#!/bin/bash

# Role Management API Test Script
echo "🧪 Testing Role Management API"
echo "================================"

BASE_URL="http://localhost:8080"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"

# Function to get admin token
get_admin_token() {
    echo "🔑 Getting admin token..."
    
    # First register admin user (ignore if exists)
    curl -s -X POST "$BASE_URL/api/v1/auth/register" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"admin\",
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\"
        }" > /dev/null 2>&1

    # Login to get token
    RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\"
        }")
    
    TOKEN=$(echo $RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    
    if [ -z "$TOKEN" ]; then
        echo "❌ Failed to get admin token"
        echo "Response: $RESPONSE"
        exit 1
    fi
    
    echo "✅ Admin token obtained"
    echo "$TOKEN"
}

# Function to test create role
test_create_role() {
    echo ""
    echo "📝 Testing Create Role..."
    
    RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/admin/role-management" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $1" \
        -d '{
            "name": "test_manager",
            "description": "Test manager role for API testing",
            "is_active": true
        }')
    
    echo "Response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q "created successfully"; then
        echo "✅ Role created successfully"
    else
        echo "❌ Failed to create role"
    fi
}

# Function to test list roles
test_list_roles() {
    echo ""
    echo "📋 Testing List Roles..."
    
    RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/admin/role-management?page=1&page_size=5" \
        -H "Authorization: Bearer $1")
    
    echo "Response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q "pagination"; then
        echo "✅ Roles listed successfully"
    else
        echo "❌ Failed to list roles"
    fi
}

# Function to test search roles
test_search_roles() {
    echo ""
    echo "🔍 Testing Search Roles..."
    
    RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name&search_filters[0][operator]=like&search_filters[0][value]=test" \
        -H "Authorization: Bearer $1")
    
    echo "Response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q "test_manager"; then
        echo "✅ Role search successful"
    else
        echo "❌ Role search failed"
    fi
}

# Function to test get role
test_get_role() {
    echo ""
    echo "🔍 Testing Get Role..."
    
    # First get role ID from list
    LIST_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name&search_filters[0][operator]=eq&search_filters[0][value]=test_manager" \
        -H "Authorization: Bearer $1")
    
    ROLE_ID=$(echo $LIST_RESPONSE | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    
    if [ -z "$ROLE_ID" ]; then
        echo "❌ Could not find test_manager role ID"
        return
    fi
    
    echo "Found role ID: $ROLE_ID"
    
    RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/admin/role-management/$ROLE_ID" \
        -H "Authorization: Bearer $1")
    
    echo "Response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q "test_manager"; then
        echo "✅ Role retrieved successfully"
    else
        echo "❌ Failed to retrieve role"
    fi
}

# Function to test update role
test_update_role() {
    echo ""
    echo "✏️ Testing Update Role..."
    
    # Get role ID
    LIST_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name&search_filters[0][operator]=eq&search_filters[0][value]=test_manager" \
        -H "Authorization: Bearer $1")
    
    ROLE_ID=$(echo $LIST_RESPONSE | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    
    if [ -z "$ROLE_ID" ]; then
        echo "❌ Could not find test_manager role ID"
        return
    fi
    
    RESPONSE=$(curl -s -X PUT "$BASE_URL/api/v1/admin/role-management/$ROLE_ID" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $1" \
        -d '{
            "name": "updated_test_manager",
            "description": "Updated test manager role",
            "is_active": false
        }')
    
    echo "Response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q "updated successfully"; then
        echo "✅ Role updated successfully"
    else
        echo "❌ Failed to update role"
    fi
}

# Function to test delete role
test_delete_role() {
    echo ""
    echo "🗑️ Testing Delete Role..."
    
    # Get role ID
    LIST_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name&search_filters[0][operator]=eq&search_filters[0][value]=updated_test_manager" \
        -H "Authorization: Bearer $1")
    
    ROLE_ID=$(echo $LIST_RESPONSE | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    
    if [ -z "$ROLE_ID" ]; then
        echo "❌ Could not find updated_test_manager role ID"
        return
    fi
    
    RESPONSE=$(curl -s -X DELETE "$BASE_URL/api/v1/admin/role-management/$ROLE_ID" \
        -H "Authorization: Bearer $1")
    
    echo "Response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q "deleted successfully"; then
        echo "✅ Role deleted successfully"
    else
        echo "❌ Failed to delete role"
    fi
}

# Main execution
main() {
    # Get admin token
    TOKEN=$(get_admin_token)
    
    if [ -z "$TOKEN" ]; then
        echo "❌ Cannot proceed without admin token"
        exit 1
    fi
    
    # Run tests
    test_create_role "$TOKEN"
    test_list_roles "$TOKEN"
    test_search_roles "$TOKEN"
    test_get_role "$TOKEN"
    test_update_role "$TOKEN"
    test_delete_role "$TOKEN"
    
    echo ""
    echo "✅ All Role Management API tests completed!"
}

# Check if server is running
if ! curl -s "$BASE_URL/swagger/index.html" > /dev/null; then
    echo "❌ Server is not running at $BASE_URL"
    echo "Please start the server with: go run main.go"
    exit 1
fi

main
