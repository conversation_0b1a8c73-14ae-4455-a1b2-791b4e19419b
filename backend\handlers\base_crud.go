package handlers

import (
	"fmt"
	"net/http"
	"reflect"
	"strconv"

	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/services"
	"jwt-auth-backend/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// BaseCRUDHandler provides generic HTTP handlers for CRUD operations
type BaseCRUDHandler struct {
	Service   *services.BaseCRUDService
	Logger    *zap.Logger
	ModelType reflect.Type
	ModelName string
}

// NewBaseCRUDHandler creates a new instance of BaseCRUDHandler
func NewBaseCRUDHandler(modelExample interface{}) *BaseCRUDHandler {
	modelType := reflect.TypeOf(modelExample)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	return &BaseCRUDHandler{
		Service:   services.NewBaseCRUDService(database.DB),
		Logger:    utils.GetLogger(),
		ModelType: modelType,
		ModelName: modelType.Name(),
	}
}

// <PERSON><PERSON> handles POST requests to create a new record
// @Summary Create a new record
// @Description Create a new record of the specified type
// @Accept json
// @Produce json
// @Param data body object true "Record data"
// @Success 201 {object} models.CRUDResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
func (h *BaseCRUDHandler) Create(c *gin.Context) {
	h.Logger.Info("Create request received", zap.String("model", h.ModelName))

	// Create new instance of the model
	model := reflect.New(h.ModelType).Interface()

	// Bind JSON to model
	if err := c.ShouldBindJSON(model); err != nil {
		h.Logger.Error("Invalid request data",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	// Create record
	if err := h.Service.Create(model); err != nil {
		h.Logger.Error("Failed to create record",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to create record",
			Details: err.Error(),
		})
		return
	}

	h.Logger.Info("Record created successfully", zap.String("model", h.ModelName))
	c.JSON(http.StatusCreated, models.CRUDResponse{
		Data:    model,
		Message: h.ModelName + " created successfully",
	})
}

// GetByID handles GET requests to retrieve a record by ID
// @Summary Get record by ID
// @Description Retrieve a record by its ID
// @Produce json
// @Param id path int true "Record ID"
// @Success 200 {object} models.CRUDResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
func (h *BaseCRUDHandler) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	h.Logger.Info("GetByID request received",
		zap.String("model", h.ModelName),
		zap.String("id", idStr),
	)

	// Parse ID
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.Logger.Error("Invalid ID parameter",
			zap.Error(err),
			zap.String("id", idStr),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid ID parameter",
			Details: "ID must be a positive integer",
		})
		return
	}

	// Create new instance of the model
	model := reflect.New(h.ModelType).Interface()

	// Get record
	if err := h.Service.GetByID(model, uint(id)); err != nil {
		if err == gorm.ErrRecordNotFound {
			h.Logger.Warn("Record not found",
				zap.String("model", h.ModelName),
				zap.Uint64("id", id),
			)
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: h.ModelName + " not found",
			})
		} else {
			h.Logger.Error("Failed to retrieve record",
				zap.Error(err),
				zap.String("model", h.ModelName),
				zap.Uint64("id", id),
			)
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "Failed to retrieve record",
				Details: err.Error(),
			})
		}
		return
	}

	h.Logger.Info("Record retrieved successfully",
		zap.String("model", h.ModelName),
		zap.Uint64("id", id),
	)
	c.JSON(http.StatusOK, models.CRUDResponse{
		Data: model,
	})
}

// Update handles PUT requests to update a record
// @Summary Update record
// @Description Update an existing record
// @Accept json
// @Produce json
// @Param id path int true "Record ID"
// @Param data body object true "Updated record data"
// @Success 200 {object} models.CRUDResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
func (h *BaseCRUDHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	h.Logger.Info("Update request received",
		zap.String("model", h.ModelName),
		zap.String("id", idStr),
	)

	// Parse ID
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.Logger.Error("Invalid ID parameter",
			zap.Error(err),
			zap.String("id", idStr),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid ID parameter",
			Details: "ID must be a positive integer",
		})
		return
	}

	// Create new instance of the model for updates
	updates := reflect.New(h.ModelType).Interface()

	// Bind JSON to updates
	if err := c.ShouldBindJSON(updates); err != nil {
		h.Logger.Error("Invalid request data",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	// Create model instance for the update operation
	model := reflect.New(h.ModelType).Interface()

	// Update record
	if err := h.Service.Update(model, uint(id), updates); err != nil {
		if err == gorm.ErrRecordNotFound {
			h.Logger.Warn("Record not found for update",
				zap.String("model", h.ModelName),
				zap.Uint64("id", id),
			)
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: h.ModelName + " not found",
			})
		} else {
			h.Logger.Error("Failed to update record",
				zap.Error(err),
				zap.String("model", h.ModelName),
				zap.Uint64("id", id),
			)
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "Failed to update record",
				Details: err.Error(),
			})
		}
		return
	}

	// Get updated record to return
	if err := h.Service.GetByID(model, uint(id)); err != nil {
		h.Logger.Error("Failed to retrieve updated record",
			zap.Error(err),
			zap.String("model", h.ModelName),
			zap.Uint64("id", id),
		)
		// Still return success since update worked
		c.JSON(http.StatusOK, models.CRUDResponse{
			Message: h.ModelName + " updated successfully",
		})
		return
	}

	h.Logger.Info("Record updated successfully",
		zap.String("model", h.ModelName),
		zap.Uint64("id", id),
	)
	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:    model,
		Message: h.ModelName + " updated successfully",
	})
}

// Delete handles DELETE requests to delete a record
// @Summary Delete record
// @Description Delete a record by ID
// @Produce json
// @Param id path int true "Record ID"
// @Success 200 {object} models.CRUDResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
func (h *BaseCRUDHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	h.Logger.Info("Delete request received",
		zap.String("model", h.ModelName),
		zap.String("id", idStr),
	)

	// Parse ID
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.Logger.Error("Invalid ID parameter",
			zap.Error(err),
			zap.String("id", idStr),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid ID parameter",
			Details: "ID must be a positive integer",
		})
		return
	}

	// Create model instance for the delete operation
	model := reflect.New(h.ModelType).Interface()

	// Delete record
	if err := h.Service.Delete(model, uint(id)); err != nil {
		if err == gorm.ErrRecordNotFound {
			h.Logger.Warn("Record not found for deletion",
				zap.String("model", h.ModelName),
				zap.Uint64("id", id),
			)
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: h.ModelName + " not found",
			})
		} else {
			h.Logger.Error("Failed to delete record",
				zap.Error(err),
				zap.String("model", h.ModelName),
				zap.Uint64("id", id),
			)
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "Failed to delete record",
				Details: err.Error(),
			})
		}
		return
	}

	h.Logger.Info("Record deleted successfully",
		zap.String("model", h.ModelName),
		zap.Uint64("id", id),
	)
	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: h.ModelName + " deleted successfully",
	})
}

// List handles GET requests to retrieve records with pagination and search
// @Summary List records
// @Description Retrieve records with pagination and search filters
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 10, max: 100)"
// @Param order_by query string false "Order by field"
// @Param order_dir query string false "Order direction (asc/desc, default: asc)"
// @Success 200 {object} models.CRUDListResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
func (h *BaseCRUDHandler) List(c *gin.Context) {
	h.Logger.Info("List request received", zap.String("model", h.ModelName))

	// Parse request parameters
	var req models.CRUDRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.Logger.Error("Invalid query parameters",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	// Parse search filters from JSON if provided
	filtersJSON := c.Query("filters")
	if filtersJSON != "" {
		// This would require additional JSON parsing logic
		// For now, we'll support simple query parameters
	}

	// Create model instance for the list operation
	model := reflect.New(h.ModelType).Interface()

	// Get records
	data, pagination, err := h.Service.List(model, req)
	if err != nil {
		h.Logger.Error("Failed to retrieve records",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to retrieve records",
			Details: err.Error(),
		})
		return
	}

	h.Logger.Info("Records retrieved successfully",
		zap.String("model", h.ModelName),
		zap.Int64("total", pagination.Total),
		zap.Int("page", pagination.Page),
	)

	c.JSON(http.StatusOK, models.CRUDListResponse{
		Data:       data,
		Pagination: pagination,
	})
}

// Search handles POST requests for advanced search with filters
// @Summary Search records
// @Description Search records with advanced filters
// @Accept json
// @Produce json
// @Param data body models.CRUDRequest true "Search criteria"
// @Success 200 {object} models.CRUDListResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
func (h *BaseCRUDHandler) Search(c *gin.Context) {
	h.Logger.Info("Search request received", zap.String("model", h.ModelName))

	// Parse request body
	var req models.CRUDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.Logger.Error("Invalid search request",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid search request",
			Details: err.Error(),
		})
		return
	}

	// Validate search filters
	for _, filter := range req.Filters {
		if err := filter.Validate(); err != nil {
			h.Logger.Error("Invalid search filter",
				zap.Error(err),
				zap.String("model", h.ModelName),
				zap.String("field", filter.Field),
			)
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "Invalid search filter",
				Details: err.Error(),
			})
			return
		}
	}

	// Create model instance for the search operation
	model := reflect.New(h.ModelType).Interface()

	// Search records
	data, pagination, err := h.Service.List(model, req)
	if err != nil {
		h.Logger.Error("Failed to search records",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to search records",
			Details: err.Error(),
		})
		return
	}

	h.Logger.Info("Search completed successfully",
		zap.String("model", h.ModelName),
		zap.Int64("total", pagination.Total),
		zap.Int("filters", len(req.Filters)),
	)

	c.JSON(http.StatusOK, models.CRUDListResponse{
		Data:       data,
		Pagination: pagination,
	})
}

// GetFieldNames handles GET requests to retrieve available field names for the model
// @Summary Get field names
// @Description Get available field names for search and ordering
// @Produce json
// @Success 200 {object} map[string]interface{}
func (h *BaseCRUDHandler) GetFieldNames(c *gin.Context) {
	h.Logger.Info("GetFieldNames request received", zap.String("model", h.ModelName))

	// Create model instance
	model := reflect.New(h.ModelType).Interface()

	// Get field names
	fields := h.Service.GetFieldNames(model)

	h.Logger.Info("Field names retrieved successfully",
		zap.String("model", h.ModelName),
		zap.Int("field_count", len(fields)),
	)

	c.JSON(http.StatusOK, gin.H{
		"model":  h.ModelName,
		"fields": fields,
	})
}

// BatchDelete handles DELETE requests to delete multiple records
// @Summary Batch delete records
// @Description Delete multiple records by IDs
// @Accept json
// @Produce json
// @Param data body map[string][]uint true "Array of IDs to delete"
// @Success 200 {object} models.CRUDResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
func (h *BaseCRUDHandler) BatchDelete(c *gin.Context) {
	h.Logger.Info("BatchDelete request received", zap.String("model", h.ModelName))

	// Parse request body
	var req struct {
		IDs []uint `json:"ids" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.Logger.Error("Invalid batch delete request",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid batch delete request",
			Details: err.Error(),
		})
		return
	}

	// Create model instance
	model := reflect.New(h.ModelType).Interface()

	// Batch delete records
	if err := h.Service.BatchDelete(model, req.IDs); err != nil {
		h.Logger.Error("Failed to batch delete records",
			zap.Error(err),
			zap.String("model", h.ModelName),
		)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to batch delete records",
			Details: err.Error(),
		})
		return
	}

	h.Logger.Info("Records batch deleted successfully",
		zap.String("model", h.ModelName),
		zap.Int("count", len(req.IDs)),
	)

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: fmt.Sprintf("%d %s records deleted successfully", len(req.IDs), h.ModelName),
	})
}

// SetupCRUDRoutes sets up standard CRUD routes for a model
func (h *BaseCRUDHandler) SetupCRUDRoutes(group *gin.RouterGroup, basePath string) {
	// Standard CRUD routes
	group.POST(basePath, h.Create)          // POST /resource
	group.GET(basePath+"/:id", h.GetByID)   // GET /resource/:id
	group.PUT(basePath+"/:id", h.Update)    // PUT /resource/:id
	group.DELETE(basePath+"/:id", h.Delete) // DELETE /resource/:id
	group.GET(basePath, h.List)             // GET /resource

	// Advanced routes
	group.POST(basePath+"/search", h.Search)       // POST /resource/search
	group.GET(basePath+"/fields", h.GetFieldNames) // GET /resource/fields
	group.DELETE(basePath+"/batch", h.BatchDelete) // DELETE /resource/batch

	h.Logger.Info("CRUD routes setup completed",
		zap.String("model", h.ModelName),
		zap.String("base_path", basePath),
	)
}
