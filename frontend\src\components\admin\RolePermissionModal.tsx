import React, { useState, useEffect } from 'react';
import { roleService, permissionService } from '../../services';
import { Role, Permission } from '../../types';

interface RolePermissionModalProps {
  role: Role;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

const RolePermissionModal: React.FC<RolePermissionModalProps> = ({
  role,
  isOpen,
  onClose,
  onSuccess,
  onError
}) => {
  const [allPermissions, setAllPermissions] = useState<Permission[]>([]);
  const [rolePermissions, setRolePermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Load data when modal opens
  useEffect(() => {
    if (isOpen) {
      loadPermissions();
      loadRolePermissions();
    }
  }, [isOpen, role.id]);

  // Load all permissions
  const loadPermissions = async () => {
    try {
      const response = await permissionService.list({ page: 1, page_size: 100 });
      setAllPermissions(response.data.data);
    } catch (err: any) {
      onError('Failed to load permissions');
      console.error('Error loading permissions:', err);
    }
  };

  // Load role's current permissions
  const loadRolePermissions = async () => {
    try {
      setLoading(true);
      const response = await roleService.getRoleWithPermissions(role.id);
      if (response.data.data && response.data.data.permissions) {
        setRolePermissions(response.data.data.permissions);
      } else {
        setRolePermissions([]);
      }
    } catch (err: any) {
      console.error('Error loading role permissions:', err);
      setRolePermissions([]);
    } finally {
      setLoading(false);
    }
  };

  // Check if permission is assigned to role
  const isPermissionAssigned = (permission: Permission): boolean => {
    return rolePermissions.some(rp => rp.id === permission.id);
  };

  // Handle permission assignment
  const handleAssignPermission = async (permission: Permission) => {
    try {
      setLoading(true);
      await roleService.assignPermissionById(role.id, permission.id);
      setRolePermissions(prev => [...prev, permission]);
      onSuccess(`Permission "${permission.name}" assigned to role "${role.name}"`);
    } catch (err: any) {
      onError(err.message || 'Failed to assign permission');
    } finally {
      setLoading(false);
    }
  };

  // Handle permission removal
  const handleRemovePermission = async (permission: Permission) => {
    try {
      setLoading(true);
      await roleService.removePermissionById(role.id, permission.id);
      setRolePermissions(prev => prev.filter(rp => rp.id !== permission.id));
      onSuccess(`Permission "${permission.name}" removed from role "${role.name}"`);
    } catch (err: any) {
      onError(err.message || 'Failed to remove permission');
    } finally {
      setLoading(false);
    }
  };

  // Filter permissions based on search and category
  const filteredPermissions = allPermissions.filter(permission => {
    const matchesSearch = searchTerm === '' ||
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (permission.description && permission.description.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = categoryFilter === 'all' ||
      permission.name.toLowerCase().startsWith(categoryFilter.toLowerCase());

    return matchesSearch && matchesCategory;
  });

  // Get unique categories from permissions
  const categories = Array.from(new Set(
    allPermissions.map(p => p.name.split('_')[0]).filter(Boolean)
  )).sort();

  if (!isOpen) return null;

  return (
    <div className="modal fade show" style={{ display: 'block' }} tabIndex={-1}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              Manage Permissions for Role: <strong>{role.name}</strong>
            </h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>

          <div className="modal-body">
            {/* Search and Filter */}
            <div className="row mb-3">
              <div className="col-md-8">
                <input
                  type="text"
                  className="form-control"
                  placeholder="Search permissions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="col-md-4">
                <select
                  className="form-select"
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Permission Statistics */}
            <div className="row mb-3">
              <div className="col-12">
                <div className="alert alert-info">
                  <strong>Role Statistics:</strong> {rolePermissions.length} of {allPermissions.length} permissions assigned
                </div>
              </div>
            </div>

            {/* Permissions List */}
            <div className="row">
              <div className="col-12">
                {loading ? (
                  <div className="text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : (
                  <div className="table-responsive" style={{ maxHeight: '400px', overflowY: 'auto' }}>
                    <table className="table table-sm table-hover">
                      <thead className="table-light sticky-top">
                        <tr>
                          <th>Permission</th>
                          <th>Description</th>
                          <th>Status</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredPermissions.length === 0 ? (
                          <tr>
                            <td colSpan={4} className="text-center py-3">
                              No permissions found
                            </td>
                          </tr>
                        ) : (
                          filteredPermissions.map((permission) => {
                            const isAssigned = isPermissionAssigned(permission);
                            return (
                              <tr key={permission.id}>
                                <td>
                                  <strong>{permission.name}</strong>
                                </td>
                                <td>
                                  <small className="text-muted">
                                    {permission.description || 'No description'}
                                  </small>
                                </td>
                                <td>
                                  <span className={`badge ${isAssigned ? 'bg-success' : 'bg-secondary'}`}>
                                    {isAssigned ? 'Assigned' : 'Not Assigned'}
                                  </span>
                                </td>
                                <td>
                                  {isAssigned ? (
                                    <button
                                      className="btn btn-sm btn-outline-danger"
                                      onClick={() => handleRemovePermission(permission)}
                                      disabled={loading}
                                    >
                                      <i className="fas fa-minus me-1"></i>
                                      Remove
                                    </button>
                                  ) : (
                                    <button
                                      className="btn btn-sm btn-outline-success"
                                      onClick={() => handleAssignPermission(permission)}
                                      disabled={loading}
                                    >
                                      <i className="fas fa-plus me-1"></i>
                                      Assign
                                    </button>
                                  )}
                                </td>
                              </tr>
                            );
                          })
                        )}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>

            {/* Bulk Actions */}
            <div className="row mt-3">
              <div className="col-12">
                <div className="d-flex gap-2">
                  <button
                    className="btn btn-sm btn-outline-success"
                    onClick={async () => {
                      for (const permission of filteredPermissions) {
                        if (!isPermissionAssigned(permission)) {
                          await handleAssignPermission(permission);
                        }
                      }
                    }}
                    disabled={loading}
                  >
                    <i className="fas fa-check-double me-1"></i>
                    Assign All Filtered
                  </button>
                  <button
                    className="btn btn-sm btn-outline-danger"
                    onClick={async () => {
                      for (const permission of filteredPermissions) {
                        if (isPermissionAssigned(permission)) {
                          await handleRemovePermission(permission);
                        }
                      }
                    }}
                    disabled={loading}
                  >
                    <i className="fas fa-times me-1"></i>
                    Remove All Filtered
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RolePermissionModal;
