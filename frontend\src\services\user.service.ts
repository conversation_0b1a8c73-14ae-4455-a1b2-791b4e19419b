import { AxiosResponse } from 'axios';
import { User } from '../types';
import { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  is_active?: boolean;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  password?: string;
  is_active?: boolean;
}

export interface UserWithRoles {
  id: number;
  username: string;
  email: string;
  roles: string[];
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

class UserService extends BaseCRUDService<User, CreateUserRequest, UpdateUserRequest> {
  constructor() {
    super('/admin/user-management');
  }

  // Get users with roles (from old admin API)
  getUsersWithRoles(): Promise<AxiosResponse<UserWithRoles[]>> {
    return api.get('/admin/users');
  }

  // Get user roles
  getUserRoles(userId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`/admin/user/${userId}/roles`);
  }

  // Get user permissions
  getUserPermissions(userId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`/admin/user/${userId}/permissions`);
  }

  // Get user direct permissions
  getUserDirectPermissions(userId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`/admin/user/${userId}/direct-permissions`);
  }

  // Assign role to user
  assignRole(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {
    return api.post('/admin/assign-role', { user_id: userId, role: roleName });
  }

  // Remove role from user
  removeRole(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {
    return api.post('/admin/remove-role', { user_id: userId, role: roleName });
  }

  // Assign permission directly to user
  assignPermission(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return api.post('/admin/assign-user-permission', { user_id: userId, permission: permissionName });
  }

  // Remove permission from user
  removePermission(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return api.post('/admin/remove-user-permission', { user_id: userId, permission: permissionName });
  }

  // Assign role to user using CRUD API
  assignRoleCRUD(userId: number, roleId: number): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/${userId}/roles`, { role_id: roleId });
  }

  // Remove role from user using CRUD API
  removeRoleCRUD(userId: number, roleId: number): Promise<AxiosResponse<{ message: string }>> {
    return api.delete(`${this.baseUrl}/${userId}/roles/${roleId}`);
  }

  // Assign permission to user using CRUD API
  assignPermissionCRUD(userId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/${userId}/permissions`, { permission_id: permissionId });
  }

  // Remove permission from user using CRUD API
  removePermissionCRUD(userId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {
    return api.delete(`${this.baseUrl}/${userId}/permissions/${permissionId}`);
  }
}

export const userService = new UserService();
export default userService;
