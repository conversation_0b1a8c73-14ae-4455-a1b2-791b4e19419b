package services

import (
	"errors"
	"fmt"
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type UserService struct {
	*BaseCRUDService
}

func NewUserService() *UserService {
	baseCRUDService := NewBaseCRUDService(database.DB)
	return &UserService{
		BaseCRUDService: baseCRUDService,
	}
}

// Create<PERSON><PERSON> creates a new user with password hashing
func (s *UserService) CreateUser(req *models.CreateUserRequest) (*models.User, error) {
	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		utils.Logger.Error("Failed to hash password", zap.Error(err))
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user model
	user := &models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		IsActive: true,
	}

	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	// Validate unique constraints
	var existingUser models.User
	if err := s.DB.Where("username = ? OR email = ?", user.Username, user.Email).First(&existingUser).Error; err == nil {
		if existingUser.Username == user.Username {
			return nil, errors.New("username already exists")
		}
		if existingUser.Email == user.Email {
			return nil, errors.New("email already exists")
		}
	}

	// Create user using base service
	if err := s.BaseCRUDService.Create(user); err != nil {
		return nil, err
	}

	utils.Logger.Info("User created successfully",
		zap.Uint("id", user.ID),
		zap.String("username", user.Username),
		zap.String("email", user.Email))

	return user, nil
}

// UpdateUser updates user with optional password hashing
func (s *UserService) UpdateUser(id uint, req *models.UpdateUserRequest) (*models.User, error) {
	// Get existing user
	var existingUser models.User
	if err := s.BaseCRUDService.GetByID(&existingUser, id); err != nil {
		return nil, err
	}

	// Update fields
	if req.Username != "" {
		// Check username uniqueness
		var userWithSameUsername models.User
		if err := s.DB.Where("username = ? AND id != ?", req.Username, id).First(&userWithSameUsername).Error; err == nil {
			return nil, errors.New("username already exists")
		}
		existingUser.Username = req.Username
	}

	if req.Email != "" {
		// Check email uniqueness
		var userWithSameEmail models.User
		if err := s.DB.Where("email = ? AND id != ?", req.Email, id).First(&userWithSameEmail).Error; err == nil {
			return nil, errors.New("email already exists")
		}
		existingUser.Email = req.Email
	}

	if req.Password != "" {
		hashedPassword, err := utils.HashPassword(req.Password)
		if err != nil {
			utils.Logger.Error("Failed to hash password", zap.Error(err))
			return nil, fmt.Errorf("failed to hash password: %w", err)
		}
		existingUser.Password = hashedPassword
	}

	if req.IsActive != nil {
		existingUser.IsActive = *req.IsActive
	}

	// Update using base service
	if err := s.BaseCRUDService.Update(&existingUser, id, &existingUser); err != nil {
		return nil, err
	}

	utils.Logger.Info("User updated successfully",
		zap.Uint("id", existingUser.ID),
		zap.String("username", existingUser.Username))

	return &existingUser, nil
}

// GetByID gets a user by ID
func (s *UserService) GetByID(id uint) (*models.User, error) {
	var user models.User
	if err := s.BaseCRUDService.GetByID(&user, id); err != nil {
		return nil, err
	}
	return &user, nil
}

// List lists users with pagination and search
func (s *UserService) List(req *models.CRUDRequest) (*models.CRUDResponse, error) {
	var user models.User
	results, pagination, err := s.BaseCRUDService.List(&user, *req)
	if err != nil {
		return nil, err
	}

	return &models.CRUDResponse{
		Data:       results,
		Pagination: &pagination,
	}, nil
}

// Delete deletes a user
func (s *UserService) Delete(id uint) error {
	var user models.User
	return s.BaseCRUDService.Delete(&user, id)
}

// AssignRoleToUser assigns a role to user
func (s *UserService) AssignRoleToUser(userID, roleID uint) error {
	// Check if user exists
	var user models.User
	if err := s.BaseCRUDService.GetByID(&user, userID); err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	// Check if role exists
	var role models.Role
	if err := s.DB.First(&role, roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("role not found")
		}
		return fmt.Errorf("failed to find role: %w", err)
	}

	// Check if assignment already exists
	var userRole models.UserRole
	if err := s.DB.Where("user_id = ? AND role_id = ?", userID, roleID).First(&userRole).Error; err == nil {
		return errors.New("user already has this role")
	}

	// Create assignment
	userRole = models.UserRole{
		UserID: userID,
		RoleID: roleID,
	}

	if err := s.DB.Create(&userRole).Error; err != nil {
		utils.Logger.Error("Failed to assign role to user", zap.Error(err))
		return fmt.Errorf("failed to assign role: %w", err)
	}

	utils.Logger.Info("Role assigned to user successfully",
		zap.Uint("user_id", userID),
		zap.Uint("role_id", roleID),
		zap.String("username", user.Username),
		zap.String("role", role.Name))

	return nil
}

// RemoveRoleFromUser removes a role from user
func (s *UserService) RemoveRoleFromUser(userID, roleID uint) error {
	// Check if assignment exists
	var userRole models.UserRole
	if err := s.DB.Where("user_id = ? AND role_id = ?", userID, roleID).First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user does not have this role")
		}
		return fmt.Errorf("failed to find role assignment: %w", err)
	}

	// Remove assignment
	if err := s.DB.Delete(&userRole).Error; err != nil {
		utils.Logger.Error("Failed to remove role from user", zap.Error(err))
		return fmt.Errorf("failed to remove role: %w", err)
	}

	utils.Logger.Info("Role removed from user successfully",
		zap.Uint("user_id", userID),
		zap.Uint("role_id", roleID))

	return nil
}

// AssignPermissionToUser assigns a permission to user
func (s *UserService) AssignPermissionToUser(userID, permissionID uint) error {
	// Check if user exists
	var user models.User
	if err := s.BaseCRUDService.GetByID(&user, userID); err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	// Check if permission exists
	var permission models.Permission
	if err := s.DB.First(&permission, permissionID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("permission not found")
		}
		return fmt.Errorf("failed to find permission: %w", err)
	}

	// Check if assignment already exists
	var userPermission models.UserPermission
	if err := s.DB.Where("user_id = ? AND permission_id = ?", userID, permissionID).First(&userPermission).Error; err == nil {
		return errors.New("user already has this permission")
	}

	// Create assignment
	userPermission = models.UserPermission{
		UserID:       userID,
		PermissionID: permissionID,
	}

	if err := s.DB.Create(&userPermission).Error; err != nil {
		utils.Logger.Error("Failed to assign permission to user", zap.Error(err))
		return fmt.Errorf("failed to assign permission: %w", err)
	}

	utils.Logger.Info("Permission assigned to user successfully",
		zap.Uint("user_id", userID),
		zap.Uint("permission_id", permissionID),
		zap.String("username", user.Username),
		zap.String("permission", permission.Name))

	return nil
}

// RemovePermissionFromUser removes a permission from user
func (s *UserService) RemovePermissionFromUser(userID, permissionID uint) error {
	// Check if assignment exists
	var userPermission models.UserPermission
	if err := s.DB.Where("user_id = ? AND permission_id = ?", userID, permissionID).First(&userPermission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user does not have this permission")
		}
		return fmt.Errorf("failed to find permission assignment: %w", err)
	}

	// Remove assignment
	if err := s.DB.Delete(&userPermission).Error; err != nil {
		utils.Logger.Error("Failed to remove permission from user", zap.Error(err))
		return fmt.Errorf("failed to remove permission: %w", err)
	}

	utils.Logger.Info("Permission removed from user successfully",
		zap.Uint("user_id", userID),
		zap.Uint("permission_id", permissionID))

	return nil
}

// GetUserWithRolesAndPermissions gets user with preloaded roles and permissions
func (s *UserService) GetUserWithRolesAndPermissions(id uint) (*models.User, error) {
	var user models.User
	if err := s.DB.Preload("Roles").Preload("Permissions").First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}
