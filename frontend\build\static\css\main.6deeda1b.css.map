{"version": 3, "file": "static/css/main.6deeda1b.css", "mappings": "AAAA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,mIAMF,CAEA,KACE,uEAEF,CAEA,WAEE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAEA,gBAIE,eAAiB,CACjB,kBAAmB,CACnB,8BAAwC,CAJxC,gBAAiB,CADjB,eAAgB,CAEhB,YAIF,CAEA,YACE,kBACF,CAEA,kBAIE,UAAW,CAHX,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,kBAGE,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAHf,YAAa,CADb,UAKF,CAEA,wBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,KAGE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAIZ,cAAe,CADf,cAAe,CALf,YAAa,CAOb,+BAAiC,CARjC,UASF,CAEA,WACE,wBACF,CAEA,eACE,wBACF,CAEA,qBACE,wBACF,CAEA,OAIE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CALlB,aAMF,CAEA,gBAPE,kBAAmB,CACnB,YAaF,CAPA,SAIE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CALlB,aAMF,CAEA,QACE,qBAAsB,CAEtB,UAAY,CADZ,cAEF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEA,WAEE,cAAe,CADf,QAEF,CAEA,eACE,eAAgB,CAChB,qBAAuB,CAGvB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CAFf,gBAGF,CAEA,qBACE,qBAAuB,CACvB,UACF,CAEA,cACE,eAAiB,CAEjB,kBAAmB,CACnB,8BAAwC,CACxC,eAAgB,CAHhB,YAIF,CAEA,iBAEE,UAAW,CADX,kBAEF,CAEA,cACE,kBACF,CAEA,qBACE,UAAW,CACX,iBACF,CAEA,aAEE,eAAgB,CADhB,iBAEF,CAEA,oBACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,cAAe,CACf,yBACF,CAEA,0BACE,aACF", "sources": ["index.css"], "sourcesContent": ["* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.auth-container {\r\n  max-width: 400px;\r\n  margin: 50px auto;\r\n  padding: 30px;\r\n  background: white;\r\n  border-radius: 10px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.form-group input {\r\n  width: 100%;\r\n  padding: 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 5px;\r\n  font-size: 16px;\r\n}\r\n\r\n.form-group input:focus {\r\n  outline: none;\r\n  border-color: #007bff;\r\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\r\n}\r\n\r\n.btn {\r\n  width: 100%;\r\n  padding: 12px;\r\n  background-color: #007bff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.btn:hover {\r\n  background-color: #0056b3;\r\n}\r\n\r\n.btn-secondary {\r\n  background-color: #6c757d;\r\n}\r\n\r\n.btn-secondary:hover {\r\n  background-color: #545b62;\r\n}\r\n\r\n.error {\r\n  color: #dc3545;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: #f8d7da;\r\n  border: 1px solid #f5c6cb;\r\n  border-radius: 5px;\r\n}\r\n\r\n.success {\r\n  color: #155724;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: #d4edda;\r\n  border: 1px solid #c3e6cb;\r\n  border-radius: 5px;\r\n}\r\n\r\n.navbar {\r\n  background-color: #333;\r\n  padding: 15px 0;\r\n  color: white;\r\n}\r\n\r\n.navbar .container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.navbar h1 {\r\n  margin: 0;\r\n  font-size: 24px;\r\n}\r\n\r\n.navbar button {\r\n  background: none;\r\n  border: 1px solid white;\r\n  color: white;\r\n  padding: 8px 16px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n}\r\n\r\n.navbar button:hover {\r\n  background-color: white;\r\n  color: #333;\r\n}\r\n\r\n.profile-card {\r\n  background: white;\r\n  padding: 30px;\r\n  border-radius: 10px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  margin-top: 30px;\r\n}\r\n\r\n.profile-card h2 {\r\n  margin-bottom: 20px;\r\n  color: #333;\r\n}\r\n\r\n.profile-info {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.profile-info strong {\r\n  color: #666;\r\n  margin-right: 10px;\r\n}\r\n\r\n.toggle-form {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.toggle-form button {\r\n  background: none;\r\n  border: none;\r\n  color: #007bff;\r\n  cursor: pointer;\r\n  text-decoration: underline;\r\n}\r\n\r\n.toggle-form button:hover {\r\n  color: #0056b3;\r\n} "], "names": [], "sourceRoot": ""}