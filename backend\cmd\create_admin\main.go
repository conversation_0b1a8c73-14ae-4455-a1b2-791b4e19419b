package main

import (
	"fmt"
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"
	"log"
)

func main() {
	fmt.Println("Creating admin user...")

	// Initialize logger
	utils.InitLogger()

	// Initialize database
	database.InitDB()

	// Create admin user
	adminUser := models.User{
		Username: "admin",
		Email:    "<EMAIL>",
		Password: "admin123",
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(adminUser.Password)
	if err != nil {
		log.Fatalf("Failed to hash password: %v", err)
	}
	adminUser.Password = hashedPassword

	// Find existing admin user by username or email
	var existingUser models.User
	result := database.DB.Where("username = ? OR email = ?", adminUser.Username, adminUser.Email).First(&existingUser)

	if result.Error != nil {
		// User doesn't exist, create new
		if err := database.DB.Create(&adminUser).Error; err != nil {
			log.Fatalf("Failed to create admin user: %v", err)
		}
		fmt.Printf("Admin user created with ID: %d\n", adminUser.ID)
	} else {
		// User exists, update password and email
		existingUser.Password = hashedPassword
		existingUser.Email = adminUser.Email
		if err := database.DB.Save(&existingUser).Error; err != nil {
			log.Fatalf("Failed to update admin user: %v", err)
		}
		adminUser = existingUser
		fmt.Printf("Admin user updated with ID: %d\n", adminUser.ID)
	}

	// Assign admin role
	var adminRole models.Role
	if err := database.DB.Where("name = ?", "admin").First(&adminRole).Error; err != nil {
		log.Fatalf("Admin role not found: %v", err)
	}

	// Check if user already has admin role
	var userRole models.UserRole
	result = database.DB.Where("user_id = ? AND role_id = ?", adminUser.ID, adminRole.ID).First(&userRole)

	if result.Error != nil {
		// Assign admin role
		userRole = models.UserRole{
			UserID: adminUser.ID,
			RoleID: adminRole.ID,
		}
		if err := database.DB.Create(&userRole).Error; err != nil {
			log.Fatalf("Failed to assign admin role: %v", err)
		}
		fmt.Println("Admin role assigned successfully")
	} else {
		fmt.Println("User already has admin role")
	}

	fmt.Println("Admin user setup completed!")
	fmt.Printf("Email: %s\n", adminUser.Email)
	fmt.Printf("Password: admin123\n")
}
