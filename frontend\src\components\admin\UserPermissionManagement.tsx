import React, { useState, useEffect } from 'react';
import { User, Role, Permission } from '../../types';
import {
  userManagementAPI,
  roleManagementAPI,
  permissionManagementAPI,
  adminAPI,
  PaginationInfo,
  SearchRequest
} from '../../services/api';
import UserModal from './UserModal';
import RoleModal from './RoleModal';
import PermissionModal from './PermissionModal';
import AssignmentsTab from './AssignmentsTab';

interface UserPermissionManagementProps { }

const UserPermissionManagement: React.FC<UserPermissionManagementProps> = () => {
  // State for active tab
  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'permissions' | 'assignments'>('users');

  // Users state
  const [users, setUsers] = useState<User[]>([]);
  const [usersPagination, setUsersPagination] = useState<PaginationInfo>({
    page: 1,
    page_size: 10,
    total_items: 0,
    total_pages: 0
  });
  const [usersLoading, setUsersLoading] = useState(false);
  const [usersSearchTerm, setUsersSearchTerm] = useState('');

  // Roles state
  const [roles, setRoles] = useState<Role[]>([]);
  const [rolesPagination, setRolesPagination] = useState<PaginationInfo>({
    page: 1,
    page_size: 10,
    total_items: 0,
    total_pages: 0
  });
  const [rolesLoading, setRolesLoading] = useState(false);
  const [rolesSearchTerm, setRolesSearchTerm] = useState('');

  // Permissions state
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionsPagination, setPermissionsPagination] = useState<PaginationInfo>({
    page: 1,
    page_size: 10,
    total_items: 0,
    total_pages: 0
  });
  const [permissionsLoading, setPermissionsLoading] = useState(false);
  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');

  // General state
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  // Modal states
  const [showUserModal, setShowUserModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);

  // Load data on component mount and tab change
  useEffect(() => {
    switch (activeTab) {
      case 'users':
        loadUsers();
        break;
      case 'roles':
        loadRoles();
        break;
      case 'permissions':
        loadPermissions();
        break;
      case 'assignments':
        loadUsers(); // Load users for assignment interface
        loadRoles(); // Load roles for assignment interface
        break;
    }
  }, [activeTab]);

  // Users functions
  const loadUsers = async (page = 1, searchTerm = '') => {
    setUsersLoading(true);
    setError('');
    try {
      let response;
      if (searchTerm.trim()) {
        const searchRequest: SearchRequest = {
          page,
          page_size: usersPagination.page_size,
          filters: [
            {
              field: 'username',
              operator: 'ilike',
              value: `%${searchTerm}%`
            },
            {
              field: 'email',
              operator: 'ilike',
              value: `%${searchTerm}%`
            }
          ],
          order_by: 'username',
          order_dir: 'asc'
        };
        response = await userManagementAPI.search(searchRequest);
      } else {
        response = await userManagementAPI.list({
          page,
          page_size: usersPagination.page_size,
          order_by: 'username',
          order_dir: 'asc'
        });
      }

      setUsers(response.data.data);
      setUsersPagination(response.data.pagination);
    } catch (err: any) {
      setError(`Failed to load users: ${err.response?.data?.error || err.message}`);
    } finally {
      setUsersLoading(false);
    }
  };

  const handleUsersSearch = () => {
    setUsersPagination(prev => ({ ...prev, page: 1 }));
    loadUsers(1, usersSearchTerm);
  };

  const handleUsersPageChange = (newPage: number) => {
    setUsersPagination(prev => ({ ...prev, page: newPage }));
    loadUsers(newPage, usersSearchTerm);
  };

  // Roles functions
  const loadRoles = async (page = 1, searchTerm = '') => {
    setRolesLoading(true);
    setError('');
    try {
      let response;
      if (searchTerm.trim()) {
        const searchRequest: SearchRequest = {
          page,
          page_size: rolesPagination.page_size,
          filters: [
            {
              field: 'name',
              operator: 'ilike',
              value: `%${searchTerm}%`
            }
          ],
          order_by: 'name',
          order_dir: 'asc'
        };
        response = await roleManagementAPI.search(searchRequest);
      } else {
        response = await roleManagementAPI.list({
          page,
          page_size: rolesPagination.page_size,
          order_by: 'name',
          order_dir: 'asc'
        });
      }

      setRoles(response.data.data);
      setRolesPagination(response.data.pagination);
    } catch (err: any) {
      setError(`Failed to load roles: ${err.response?.data?.error || err.message}`);
    } finally {
      setRolesLoading(false);
    }
  };

  const handleRolesSearch = () => {
    setRolesPagination(prev => ({ ...prev, page: 1 }));
    loadRoles(1, rolesSearchTerm);
  };

  const handleRolesPageChange = (newPage: number) => {
    setRolesPagination(prev => ({ ...prev, page: newPage }));
    loadRoles(newPage, rolesSearchTerm);
  };

  // Permissions functions
  const loadPermissions = async (page = 1, searchTerm = '') => {
    setPermissionsLoading(true);
    setError('');
    try {
      let response;
      if (searchTerm.trim()) {
        const searchRequest: SearchRequest = {
          page,
          page_size: permissionsPagination.page_size,
          filters: [
            {
              field: 'name',
              operator: 'ilike',
              value: `%${searchTerm}%`
            }
          ],
          order_by: 'name',
          order_dir: 'asc'
        };
        response = await permissionManagementAPI.search(searchRequest);
      } else {
        response = await permissionManagementAPI.list({
          page,
          page_size: permissionsPagination.page_size,
          order_by: 'name',
          order_dir: 'asc'
        });
      }

      setPermissions(response.data.data);
      setPermissionsPagination(response.data.pagination);
    } catch (err: any) {
      setError(`Failed to load permissions: ${err.response?.data?.error || err.message}`);
    } finally {
      setPermissionsLoading(false);
    }
  };

  const handlePermissionsSearch = () => {
    setPermissionsPagination(prev => ({ ...prev, page: 1 }));
    loadPermissions(1, permissionsSearchTerm);
  };

  const handlePermissionsPageChange = (newPage: number) => {
    setPermissionsPagination(prev => ({ ...prev, page: newPage }));
    loadPermissions(newPage, permissionsSearchTerm);
  };

  // Delete functions
  const handleDeleteUser = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this user?')) return;

    try {
      await userManagementAPI.delete(id);
      setMessage('User deleted successfully');
      loadUsers(usersPagination.page, usersSearchTerm);
    } catch (err: any) {
      setError(`Failed to delete user: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleDeleteRole = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this role?')) return;

    try {
      await roleManagementAPI.delete(id);
      setMessage('Role deleted successfully');
      loadRoles(rolesPagination.page, rolesSearchTerm);
    } catch (err: any) {
      setError(`Failed to delete role: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleDeletePermission = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this permission?')) return;

    try {
      await permissionManagementAPI.delete(id);
      setMessage('Permission deleted successfully');
      loadPermissions(permissionsPagination.page, permissionsSearchTerm);
    } catch (err: any) {
      setError(`Failed to delete permission: ${err.response?.data?.error || err.message}`);
    }
  };

  // Render pagination
  const renderPagination = (pagination: PaginationInfo, onPageChange: (page: number) => void) => {
    const pages = [];
    for (let i = 1; i <= pagination.total_pages; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => onPageChange(i)}
          className={`px-3 py-1 mx-1 rounded ${i === pagination.page
            ? 'bg-blue-500 text-white'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
        >
          {i}
        </button>
      );
    }

    return (
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-gray-600">
          Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
          {Math.min(pagination.page * pagination.page_size, pagination.total_items)} of{' '}
          {pagination.total_items} entries
        </div>
        <div className="flex items-center">
          <button
            onClick={() => onPageChange(pagination.page - 1)}
            disabled={pagination.page <= 1}
            className="px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50"
          >
            Previous
          </button>
          {pages}
          <button
            onClick={() => onPageChange(pagination.page + 1)}
            disabled={pagination.page >= pagination.total_pages}
            className="px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">User Permission Management</h1>

      {/* Messages */}
      {message && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {message}
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'users', label: 'Users' },
            { key: 'roles', label: 'Roles' },
            { key: 'permissions', label: 'Permissions' },
            { key: 'assignments', label: 'Assignments' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'users' && (
        <div>
          {/* Users Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">User Management</h2>
            <button
              onClick={() => {
                setEditingUser(null);
                setShowUserModal(true);
              }}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Add User
            </button>
          </div>

          {/* Users Search */}
          <div className="mb-4 flex gap-2">
            <input
              type="text"
              placeholder="Search users by username or email..."
              value={usersSearchTerm}
              onChange={(e) => setUsersSearchTerm(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleUsersSearch()}
            />
            <button
              onClick={handleUsersSearch}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
            >
              Search
            </button>
            <button
              onClick={() => {
                setUsersSearchTerm('');
                loadUsers(1, '');
              }}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            >
              Clear
            </button>
          </div>

          {/* Users Table */}
          {usersLoading ? (
            <div className="text-center py-8">Loading users...</div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Username
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {users.map((user) => (
                      <tr key={user.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {user.username}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.email}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {user.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setEditingUser(user);
                              setShowUserModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Users Pagination */}
              {renderPagination(usersPagination, handleUsersPageChange)}
            </>
          )}
        </div>
      )}

      {/* Roles Tab */}
      {activeTab === 'roles' && (
        <div>
          {/* Roles Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Role Management</h2>
            <button
              onClick={() => {
                setEditingRole(null);
                setShowRoleModal(true);
              }}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Add Role
            </button>
          </div>

          {/* Roles Search */}
          <div className="mb-4 flex gap-2">
            <input
              type="text"
              placeholder="Search roles by name..."
              value={rolesSearchTerm}
              onChange={(e) => setRolesSearchTerm(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleRolesSearch()}
            />
            <button
              onClick={handleRolesSearch}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
            >
              Search
            </button>
            <button
              onClick={() => {
                setRolesSearchTerm('');
                loadRoles(1, '');
              }}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            >
              Clear
            </button>
          </div>

          {/* Roles Table */}
          {rolesLoading ? (
            <div className="text-center py-8">Loading roles...</div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {roles.map((role) => (
                      <tr key={role.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {role.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {role.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                          {role.description || 'No description'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {role.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setEditingRole(role);
                              setShowRoleModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteRole(role.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Roles Pagination */}
              {renderPagination(rolesPagination, handleRolesPageChange)}
            </>
          )}
        </div>
      )}

      {/* Permissions Tab */}
      {activeTab === 'permissions' && (
        <div>
          {/* Permissions Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Permission Management</h2>
            <button
              onClick={() => {
                setEditingPermission(null);
                setShowPermissionModal(true);
              }}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Add Permission
            </button>
          </div>

          {/* Permissions Search */}
          <div className="mb-4 flex gap-2">
            <input
              type="text"
              placeholder="Search permissions by name..."
              value={permissionsSearchTerm}
              onChange={(e) => setPermissionsSearchTerm(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handlePermissionsSearch()}
            />
            <button
              onClick={handlePermissionsSearch}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
            >
              Search
            </button>
            <button
              onClick={() => {
                setPermissionsSearchTerm('');
                loadPermissions(1, '');
              }}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            >
              Clear
            </button>
          </div>

          {/* Permissions Table */}
          {permissionsLoading ? (
            <div className="text-center py-8">Loading permissions...</div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {permissions.map((permission) => (
                      <tr key={permission.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {permission.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {permission.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                          {permission.description || 'No description'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {permission.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setEditingPermission(permission);
                              setShowPermissionModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeletePermission(permission.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Permissions Pagination */}
              {renderPagination(permissionsPagination, handlePermissionsPageChange)}
            </>
          )}
        </div>
      )}

      {/* Assignments Tab */}
      {activeTab === 'assignments' && (
        <AssignmentsTab
          users={users}
          roles={roles}
          onMessage={setMessage}
          onError={setError}
        />
      )}

      {/* Modals */}
      <UserModal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        user={editingUser}
        onSuccess={() => {
          setMessage(editingUser ? 'User updated successfully' : 'User created successfully');
          loadUsers(usersPagination.page, usersSearchTerm);
        }}
      />

      <RoleModal
        isOpen={showRoleModal}
        onClose={() => setShowRoleModal(false)}
        role={editingRole}
        onSuccess={() => {
          setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');
          loadRoles(rolesPagination.page, rolesSearchTerm);
        }}
      />

      <PermissionModal
        isOpen={showPermissionModal}
        onClose={() => setShowPermissionModal(false)}
        permission={editingPermission}
        onSuccess={() => {
          setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');
          loadPermissions(permissionsPagination.page, permissionsSearchTerm);
        }}
      />
    </div>
  );
};

export default UserPermissionManagement;
