# JWT Authentication Project (RBAC Edition)

Project này bao gồm:
- Backend API với Golang + Gin framework
- Frontend với ReactJS
- Database PostgreSQL chạy trên Docker
- **<PERSON>ệ thống phân quyền RBAC (Role-Based Access Control) chuẩn**
- Chức năng đăng nhập/đăng ký với JWT authentication
- Quản trị user, role, permission trực quan trên frontend
- **<PERSON><PERSON> thống logging với Zap (structured logging)**
- **Hỗ trợ Refresh Token, Blacklist Token với Redis**

## Cấu trúc Project

```
Build-Project/
├── backend/          # Golang API server
├── frontend/         # ReactJS application
├── docker-compose.yml # PostgreSQL Docker configuration
└── README.md
```

## Tính năng nổi bật
- Đăng ký/đăng nhập với JWT
- Phân quyền theo role (RBAC): user có thể có nhiều role
- Quản lý permission chi tiết cho từng role
- <PERSON><PERSON>/xóa role cho user, gán/xóa permission cho role
- Trang quản trị user/role/permission cho admin
- Tìm kiếm user, badge màu cho role/permission, modal quản lý quyền
- API RESTful cho quản trị user/role/permission
- **Structured logging với Zap cho monitoring và debugging**

## Tính năng bảo mật nâng cao
- **Refresh Token:**
  - Khi đăng nhập (`/api/v1/auth/login`), backend trả về cả access token và refresh token.
  - Dùng refresh token để lấy access token mới qua API `/api/v1/auth/refresh`.
- **Blacklist Token:**
  - Khi logout (`/api/v1/auth/logout`), access token sẽ bị revoke (blacklist) trong Redis, không thể sử dụng lại.
  - Middleware sẽ kiểm tra access token có bị blacklist không trước khi cho phép truy cập API.
- **Redis:**
  - Lưu refresh token và blacklist access token, đảm bảo hiệu năng và bảo mật cao.
  - Cấu hình Redis trong file `backend/config.env`:
    ```
    REDIS_ADDR=localhost:6379
    REDIS_PASSWORD=
    ```

## API Authentication nâng cao
- `POST /api/v1/auth/login` — Đăng nhập, trả về access token và refresh token
- `POST /api/v1/auth/refresh` — Nhận refresh token, trả về access token mới
- `POST /api/v1/auth/logout` — Xóa refresh token và revoke access token (cần truyền cả access token và refresh token)

### Ví dụ login:
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "yourpassword"
}
```
**Response:**
```json
{
  "token": "<access_token>",
  "refresh_token": "<refresh_token>",
  "user": { ... }
}
```

### Ví dụ refresh token:
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "user_id": 1,
  "refresh_token": "<refresh_token>"
}
```
**Response:**
```json
{
  "token": "<new_access_token>"
}
```

### Ví dụ logout:
```http
POST /api/v1/auth/logout
Content-Type: application/json

{
  "user_id": 1,
  "refresh_token": "<refresh_token>",
  "access_token": "<access_token>"
}
```

## Lưu ý
- Access token bị revoke sẽ không thể sử dụng lại (dù còn hạn).
- Refresh token chỉ dùng được 1 lần, hết hạn hoặc logout sẽ bị xóa khỏi Redis.
- Tất cả các API bảo vệ đều kiểm tra blacklist token.

## Hệ thống Logging

### Zap Logger
Backend sử dụng [Zap](https://github.com/uber-go/zap) - một thư viện logging nhanh và structured cho Go:

#### Tính năng:
- **Structured JSON logging** với timestamp, level, caller
- **Multiple log levels**: DEBUG, INFO, WARN, ERROR
- **Performance optimized** - nhanh hơn log chuẩn của Go
- **Production ready** với stack trace cho errors
- **Configurable log level** qua environment variable

#### Log Levels:
- `DEBUG`: Thông tin chi tiết cho development
- `INFO`: Thông tin chung về hoạt động hệ thống
- `WARN`: Cảnh báo, có thể gây vấn đề
- `ERROR`: Lỗi nghiêm trọng cần xử lý

#### Cấu hình:
```env
LOG_LEVEL=info  # Có thể là: debug, info, warn, error
```

#### Ví dụ log output:
```json
{
  "level": "INFO",
  "timestamp": "2025-06-25T11:18:40.263+0700",
  "caller": "backend/handlers/auth.go:25",
  "msg": "User logged in successfully",
  "user_id": 123,
  "email": "<EMAIL>",
  "username": "testuser"
}
```

#### Logging trong các component:

**Authentication:**
- Login attempts (success/failure)
- Registration events
- Token validation errors
- Logout events

**Database:**
- Connection status
- Migration events
- Seed data creation
- Query errors

**Admin Operations:**
- Role assignments/removals
- Permission management
- User operations
- Access control violations

**HTTP Requests:**
- Request details (method, path, status, latency)
- Client IP and user agent
- Error responses

#### Sử dụng trong code:
```go
logger := utils.GetLogger()

// Info logging
logger.Info("User action", 
    zap.String("action", "login"),
    zap.Uint("user_id", userID),
    zap.String("ip", clientIP),
)

// Error logging
logger.Error("Database error", 
    zap.Error(err),
    zap.String("operation", "create_user"),
)

// Debug logging
logger.Debug("Processing request", 
    zap.String("path", c.Request.URL.Path),
)
```

## Hệ thống RBAC
- **Bảng roles**: Lưu các role (admin, user, ...)
- **Bảng user_roles**: Liên kết user với nhiều role
- **Bảng permissions**: Lưu các quyền (assign_role, edit_user, ...)
- **Bảng role_permissions**: Gán quyền cho role
- **API và UI quản trị đầy đủ**

## API Backend (admin)

### User/Role/Permission
- `GET /api/admin/users` — Lấy danh sách user kèm role
- `GET /api/admin/roles` — Lấy danh sách role
- `GET /api/admin/permissions` — Lấy danh sách permission
- `GET /api/admin/user/:id/roles` — Lấy role của user
- `GET /api/admin/user/:id/permissions` — Lấy permission của user
- `POST /api/admin/assign-role` — Gán role cho user
- `POST /api/admin/remove-role` — Xóa role khỏi user

### Permission cho role
- `GET /api/admin/role/:id/permissions` — Lấy permission của role
- `POST /api/admin/assign-permission` — Gán permission cho role
- `POST /api/admin/remove-permission` — Xóa permission khỏi role

### Demo permission
- `GET /api/edit-user-demo` — Chỉ user có permission `edit_user` mới truy cập được

## Trang quản trị frontend
- Đăng nhập bằng tài khoản admin
- Truy cập `/admin/users` hoặc click menu **Admin**
- Tìm kiếm user theo tên/email
- Gán role cho user (dropdown + Assign)
- Xóa role khỏi user (nút × cạnh badge role)
- Quản lý permission của role (nút **Manage Permissions** cạnh mỗi role, mở modal)
- Gán/xóa permission cho role trong modal
- Xem permission của user (nút View)

## Hướng dẫn sử dụng
1. **Khởi động PostgreSQL:**
   ```bash
   start-postgres.bat
   ```
2. **Chạy backend:**
   ```bash
   run-backend.bat
   ```
3. **Chạy frontend:**
   ```bash
   run-frontend.bat
   ```
4. **Đăng nhập bằng tài khoản admin** (gán role admin cho user qua database hoặc API nếu cần)
5. **Quản trị user/role/permission tại `/admin/users`**

## Seed mặc định
- Role: `admin`, `user`
- Permission: `assign_role`, `view_user`, `edit_user`, `delete_user`
- Role admin có sẵn quyền `assign_role`

## Mở rộng
- Có thể thêm/xóa role, permission qua database hoặc API
- Có thể mở rộng UI để quản lý danh mục role/permission
- Có thể thêm phân trang, xuất file, mobile UI...

## Demo API gán role cho user
```http
POST /api/admin/assign-role
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "user_id": 2,
  "role": "admin"
}
```

## Demo API gán permission cho role
```http
POST /api/admin/assign-permission
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "role_id": 1,
  "permission": "edit_user"
}
```

## Đóng góp & mở rộng
- Hệ thống đã sẵn sàng để mở rộng cho mọi nhu cầu RBAC thực tế!
- Nếu cần thêm tính năng, chỉ cần bổ sung vào backend hoặc frontend theo mẫu sẵn có.

## Yêu cầu hệ thống

- Go (version 1.19+)
- Node.js (version 16+)
- Docker & Docker Compose
- Git

## Setup Database (PostgreSQL)

1. **Khởi động PostgreSQL với Docker:**
   ```bash
   start-postgres.bat
   ```
   
   Hoặc sử dụng Docker Compose trực tiếp:
   ```bash
   docker-compose up -d postgres
   ```

2. **Kiểm tra PostgreSQL đã chạy:**
   ```bash
   docker ps
   ```

3. **Dừng PostgreSQL (khi cần):**
   ```bash
   stop-postgres.bat
   ```

## Setup Backend

1. **Đảm bảo PostgreSQL đã chạy** (sử dụng `start-postgres.bat`)
2. **Di chuyển vào thư mục backend:**
   ```bash
   cd backend
   ```
3. **Cài đặt dependencies:**
   ```bash
   go mod tidy
   ```
4. **Chạy server:**
   ```bash
   go run main.go
   ```

   Hoặc sử dụng script Windows:
   ```bash
   run-backend.bat
   ```

Server sẽ chạy tại: http://localhost:8080

## Setup Frontend

1. **Di chuyển vào thư mục frontend:**
   ```bash
   cd frontend
   ```
2. **Cài đặt dependencies:**
   ```bash
   npm install
   ```
3. **Chạy development server:**
   ```bash
   npm start
   ```

   Hoặc sử dụng script Windows:
   ```bash
   run-frontend.bat
   ```

Frontend sẽ chạy tại: http://localhost:3000

## Database Configuration

PostgreSQL được cấu hình với:
- **Database:** jwt_auth_db
- **User:** jwt_user
- **Password:** jwt_password
- **Port:** 5432
- **Host:** localhost

Cấu hình có thể thay đổi trong file `backend/config.env`

## API Endpoints

- `POST /api/auth/register` - Đăng ký user mới
- `POST /api/auth/login` - Đăng nhập
- `GET /api/user/profile` - Lấy thông tin user (cần JWT token)
- `POST /api/auth/logout` - Đăng xuất

## Tính năng

- JWT Authentication
- User Registration/Login
- Protected Routes
- Token Refresh
- Password Hashing
- CORS Support
- PostgreSQL Database
- Docker Integration

## Troubleshooting

### PostgreSQL Issues
1. **PostgreSQL không khởi động:**
   - Kiểm tra Docker đã cài đặt và chạy
   - Chạy `docker-compose logs postgres` để xem logs

2. **Backend không kết nối được database:**
   - Đảm bảo PostgreSQL đã chạy: `docker ps`
   - Kiểm tra port 5432 không bị chiếm
   - Kiểm tra file `backend/config.env`

3. **Reset database:**
   ```bash
   docker-compose down -v
   docker-compose up -d postgres
   ```

### Port Conflicts
- **Port 5432:** PostgreSQL
- **Port 8080:** Backend API
- **Port 3000:** Frontend

## Docker Commands

```bash
# Khởi động PostgreSQL
docker-compose up -d postgres

# Xem logs PostgreSQL
docker-compose logs postgres

# Dừng PostgreSQL
docker-compose down

# Dừng và xóa volume (reset database)
docker-compose down -v
``` 