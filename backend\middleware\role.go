package middleware

import (
	"net/http"
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"github.com/gin-gonic/gin"
)

// RequireRoles nhận danh sách role hợp lệ, chỉ cho phép user có role này truy cập
func RequireRoles(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		var userRoles []models.Role
		err := database.DB.
			Table("roles").
			Select("roles.name").
			Joins("join user_roles on user_roles.role_id = roles.id").
			Where("user_roles.user_id = ?", userID).
			Scan(&userRoles).Error
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "Cannot get user roles"})
			c.Abort()
			return
		}

		for _, ur := range userRoles {
			for _, r := range roles {
				if ur.Name == r {
					c.Next()
					return
				}
			}
		}
		c.<PERSON>(http.StatusForbidden, gin.H{"error": "Permission denied"})
		c.Abort()
	}
} 