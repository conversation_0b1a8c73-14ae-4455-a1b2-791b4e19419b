# Hướng dẫn Setup Project JWT Authentication với PostgreSQL

## <PERSON><PERSON><PERSON> c<PERSON>u hệ thống

### Backend (Golang)
- Go version 1.19 hoặc cao hơn
- Git

### Frontend (ReactJS)
- Node.js version 16 hoặc cao hơn
- npm hoặc yarn

### Database (PostgreSQL)
- Docker & Docker Compose

## Cài đặt và chạy

### 1. Setup Database (PostgreSQL)

1. **Đ<PERSON>m bảo Docker đã cài đặt và chạy**
2. **Khởi động PostgreSQL:**
   ```bash
   start-postgres.bat
   ```
   
   Hoặc sử dụng Docker Compose trực tiếp:
   ```bash
   docker-compose up -d postgres
   ```

3. **Kiểm tra PostgreSQL đã chạy:**
   ```bash
   docker ps
   ```
   
   Bạn sẽ thấy container `jwt-auth-postgres` đang chạy.

4. **Xem logs PostgreSQL (nếu cần):**
   ```bash
   docker-compose logs postgres
   ```

### 2. Setup Backend

1. **<PERSON><PERSON><PERSON> bảo PostgreSQL đã chạy** (sử dụng `start-postgres.bat`)
2. **<PERSON> chuyển vào thư mục backend:**
   ```bash
   cd backend
   ```

3. **Cài đặt dependencies:**
   ```bash
   go mod tidy
   ```

4. **Chạy server:**
   ```bash
   go run main.go
   ```

   Hoặc sử dụng script Windows:
   ```bash
   run-backend.bat
   ```

5. **Server sẽ chạy tại:** http://localhost:8080

### 3. Setup Frontend

1. **Di chuyển vào thư mục frontend:**
   ```bash
   cd frontend
   ```

2. **Cài đặt dependencies:**
   ```bash
   npm install
   ```

3. **Chạy development server:**
   ```bash
   npm start
   ```

   Hoặc sử dụng script Windows:
   ```bash
   run-frontend.bat
   ```

4. **Frontend sẽ chạy tại:** http://localhost:3000

## Sử dụng ứng dụng

### Đăng ký tài khoản mới
1. Mở http://localhost:3000
2. Click "Don't have an account? Register"
3. Điền thông tin:
   - Username (tối thiểu 3 ký tự)
   - Email (định dạng email hợp lệ)
   - Password (tối thiểu 6 ký tự)
4. Click "Register"

### Đăng nhập
1. Điền email và password
2. Click "Login"

### Xem thông tin profile
- Sau khi đăng nhập thành công, bạn sẽ thấy thông tin profile
- Click "Logout" để đăng xuất

## API Endpoints

### Authentication
- `POST /api/auth/register` - Đăng ký user mới
- `POST /api/auth/login` - Đăng nhập
- `POST /api/auth/logout` - Đăng xuất

### User
- `GET /api/user/profile` - Lấy thông tin user (cần JWT token)

## Cấu trúc Database

PostgreSQL database sẽ được tạo tự động với bảng `users`:

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);
```

## Database Configuration

PostgreSQL được cấu hình với:
- **Database:** jwt_auth_db
- **User:** jwt_user
- **Password:** jwt_password
- **Port:** 5432
- **Host:** localhost

Cấu hình có thể thay đổi trong file `backend/config.env`

## Troubleshooting

### Lỗi thường gặp

1. **PostgreSQL không khởi động:**
   - Kiểm tra Docker đã cài đặt và chạy
   - Chạy `docker-compose logs postgres` để xem logs
   - Kiểm tra port 5432 không bị chiếm

2. **Backend không kết nối được database:**
   - Đảm bảo PostgreSQL đã chạy: `docker ps`
   - Kiểm tra cấu hình trong `backend/config.env`
   - Đợi vài giây để PostgreSQL khởi động hoàn toàn

3. **Port 8080 đã được sử dụng:**
   - Thay đổi port trong file `backend/config.env`
   - Hoặc kill process đang sử dụng port 8080

4. **Port 3000 đã được sử dụng:**
   - React sẽ tự động hỏi để sử dụng port khác
   - Hoặc kill process đang sử dụng port 3000

5. **Lỗi CORS:**
   - Backend đã được cấu hình CORS để cho phép frontend
   - Đảm bảo frontend chạy trên port 3000

6. **Reset database:**
   ```bash
   docker-compose down -v
   docker-compose up -d postgres
   ```

### Kiểm tra logs

- **PostgreSQL logs:** `docker-compose logs postgres`
- **Backend logs:** Hiển thị trong terminal chạy backend
- **Frontend logs:** Mở Developer Tools (F12) trong browser

## Docker Commands

```bash
# Khởi động PostgreSQL
docker-compose up -d postgres

# Xem logs PostgreSQL
docker-compose logs postgres

# Dừng PostgreSQL
docker-compose down

# Dừng và xóa volume (reset database)
docker-compose down -v

# Xem containers đang chạy
docker ps

# Xem volumes
docker volume ls
```

## Tính năng bảo mật

- **Password Hashing:** Sử dụng bcrypt để hash password
- **JWT Tokens:** Token có thời hạn 24 giờ
- **CORS Protection:** Chỉ cho phép frontend truy cập API
- **Input Validation:** Validate dữ liệu đầu vào
- **Error Handling:** Xử lý lỗi an toàn
- **Database Security:** PostgreSQL với user authentication

## Mở rộng

Để mở rộng project, bạn có thể:

1. **Thêm refresh token**
2. **Thêm email verification**
3. **Thêm password reset**
4. **Thêm role-based access control**
5. **Thêm rate limiting**
6. **Thêm logging**
7. **Thêm unit tests**
8. **Thêm database migrations**
9. **Thêm connection pooling**
10. **Thêm database backup** 