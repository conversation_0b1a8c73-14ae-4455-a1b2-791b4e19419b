import React, { useState, useEffect } from 'react';
import { userAPI, authAPI, clearTokens } from '../services/api';
import { User } from '../types';

interface ProfileProps {
  onLogout: () => void;
}

const Profile: React.FC<ProfileProps> = ({ onLogout }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line
  }, []);

  const fetchData = async () => {
    setLoading(true);
    setError('');
    setMessage('');
    try {
      const response = await userAPI.getProfile();
      const { user } = response.data;
      localStorage.setItem('user', JSON.stringify(user));
      setUser(user);
    } catch (err) {
      setError('Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await authAPI.logout();
      setMessage('Logged out successfully!');
    } catch (err) {
      setError('Logout error');
    } finally {
      clearTokens();
      onLogout();
    }
  };

  const isAdmin = user && user.roles && user.roles.includes('admin');

  if (loading) {
    return (
      <div className="container">
        <div className="profile-card">
          <p>Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container">
        <div className="profile-card">
          <div className="error">{error}</div>
          <button className="btn" onClick={fetchData}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="profile-card">
        <h2>User Profile</h2>
        {message && <div className="success">{message}</div>}
        {user && (
          <div>
            <div className="profile-info">
              <strong>ID:</strong> {user.id}
            </div>
            <div className="profile-info">
              <strong>Username:</strong> {user.username}
            </div>
            <div className="profile-info">
              <strong>Email:</strong> {user.email}
            </div>
            <div className="profile-info">
              <strong>Created:</strong> {new Date(user.created_at).toLocaleDateString()}
            </div>
            <div className="profile-info">
              <strong>Roles:</strong> {user.roles && user.roles.length > 0 ? user.roles.join(', ') : 'None'}
            </div>
          </div>
        )}
        <button className="btn btn-secondary" onClick={handleLogout} style={{ marginTop: '20px' }}>
          Logout
        </button>
      </div>
    </div>
  );
};

export default Profile; 