package utils

import (
	"context"
	"os"
	"sync"

	"github.com/redis/go-redis/v9"
)

var (
	RedisClient *redis.Client
	once        sync.Once
)

// InitRedis khởi tạo Redis client (singleton)
func InitRedis() {
	once.Do(func() {
		addr := os.Getenv("REDIS_ADDR")
		if addr == "" {
			addr = "localhost:6379"
		}
		password := os.Getenv("REDIS_PASSWORD")
		RedisClient = redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: password,
			DB:       0,
		})
	})
}

// GetRedisClient trả về Redis client
func GetRedisClient() *redis.Client {
	if RedisClient == nil {
		InitRedis()
	}
	return RedisClient
}

// PingRedis kiểm tra kết nối
func PingRedis(ctx context.Context) error {
	return GetRedisClient().Ping(ctx).Err()
} 