{"ast": null, "code": "import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8080/api/v1';// Create axios instance\nconst api=axios.create({baseURL:API_BASE_URL,headers:{'Content-Type':'application/json'}});// Request interceptor to add auth token\napi.interceptors.request.use(config=>{const token=localStorage.getItem('access_token');if(token){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// Response interceptor for automatic token refresh\napi.interceptors.response.use(response=>response,async error=>{var _error$response;const originalRequest=error.config;if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401&&!originalRequest._retry){originalRequest._retry=true;try{const refreshToken=localStorage.getItem('refresh_token');if(!refreshToken){throw new Error('No refresh token available');}const response=await axios.post(\"\".concat(API_BASE_URL,\"/auth/refresh\"),{refresh_token:refreshToken});const{token:newAccessToken,refresh_token:newRefreshToken}=response.data;localStorage.setItem('access_token',newAccessToken);localStorage.setItem('refresh_token',newRefreshToken);// Retry the original request with new token\noriginalRequest.headers.Authorization=\"Bearer \".concat(newAccessToken);return api(originalRequest);}catch(refreshError){// Refresh failed, redirect to login\nlocalStorage.removeItem('access_token');localStorage.removeItem('refresh_token');localStorage.removeItem('user');window.location.href='/login';return Promise.reject(refreshError);}}return Promise.reject(error);});export const authService={// Login user\nlogin:credentials=>{return api.post('/auth/login',credentials);},// Register user\nregister:userData=>{return api.post('/auth/register',userData);},// Refresh token\nrefreshToken:refreshToken=>{return api.post('/auth/refresh',{refresh_token:refreshToken});},// Logout user\nlogout:()=>{return api.post('/auth/logout');},// Revoke all tokens\nrevokeAllTokens:()=>{return api.post('/auth/revoke-all');},// Get user profile\ngetProfile:()=>{return api.get('/user/profile');},// Check if user is authenticated\nisAuthenticated:()=>{const token=localStorage.getItem('access_token');return!!token;},// Get current user from localStorage\ngetCurrentUser:()=>{const userStr=localStorage.getItem('user');return userStr?JSON.parse(userStr):null;},// Check if user has specific role\nhasRole:role=>{var _user$roles;const user=authService.getCurrentUser();return(user===null||user===void 0?void 0:(_user$roles=user.roles)===null||_user$roles===void 0?void 0:_user$roles.includes(role))||false;},// Check if user is admin\nisAdmin:()=>{return authService.hasRole('admin');},// Clear authentication data\nclearAuth:()=>{localStorage.removeItem('access_token');localStorage.removeItem('refresh_token');localStorage.removeItem('user');},// Store authentication data\nstoreAuth:authData=>{localStorage.setItem('access_token',authData.token);localStorage.setItem('refresh_token',authData.refresh_token);localStorage.setItem('user',JSON.stringify(authData.user));}};export default authService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "Error", "post", "refresh_token", "newAccessToken", "newRefreshToken", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "authService", "login", "credentials", "register", "userData", "logout", "revokeAllTokens", "getProfile", "get", "isAuthenticated", "getCurrentUser", "userStr", "JSON", "parse", "hasRole", "role", "_user$roles", "user", "roles", "includes", "isAdmin", "clearAuth", "storeAuth", "authData", "stringify"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/auth.service.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\nimport { User, AuthResponse } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for automatic token refresh\napi.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refresh_token');\n        if (!refreshToken) {\n          throw new Error('No refresh token available');\n        }\n\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refresh_token: refreshToken,\n        });\n\n        const { token: newAccessToken, refresh_token: newRefreshToken } = response.data;\n        \n        localStorage.setItem('access_token', newAccessToken);\n        localStorage.setItem('refresh_token', newRefreshToken);\n\n        // Retry the original request with new token\n        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;\n        return api(originalRequest);\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n}\n\nexport const authService = {\n  // Login user\n  login: (credentials: LoginRequest): Promise<AxiosResponse<AuthResponse>> => {\n    return api.post('/auth/login', credentials);\n  },\n\n  // Register user\n  register: (userData: RegisterRequest): Promise<AxiosResponse<AuthResponse>> => {\n    return api.post('/auth/register', userData);\n  },\n\n  // Refresh token\n  refreshToken: (refreshToken: string): Promise<AxiosResponse<AuthResponse>> => {\n    return api.post('/auth/refresh', { refresh_token: refreshToken });\n  },\n\n  // Logout user\n  logout: (): Promise<AxiosResponse<{ message: string }>> => {\n    return api.post('/auth/logout');\n  },\n\n  // Revoke all tokens\n  revokeAllTokens: (): Promise<AxiosResponse<{ message: string }>> => {\n    return api.post('/auth/revoke-all');\n  },\n\n  // Get user profile\n  getProfile: (): Promise<AxiosResponse<{ user: User; roles: string[] }>> => {\n    return api.get('/user/profile');\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: (): boolean => {\n    const token = localStorage.getItem('access_token');\n    return !!token;\n  },\n\n  // Get current user from localStorage\n  getCurrentUser: (): User | null => {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  },\n\n  // Check if user has specific role\n  hasRole: (role: string): boolean => {\n    const user = authService.getCurrentUser();\n    return user?.roles?.includes(role) || false;\n  },\n\n  // Check if user is admin\n  isAdmin: (): boolean => {\n    return authService.hasRole('admin');\n  },\n\n  // Clear authentication data\n  clearAuth: (): void => {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n    localStorage.removeItem('user');\n  },\n\n  // Store authentication data\n  storeAuth: (authData: AuthResponse): void => {\n    localStorage.setItem('access_token', authData.token);\n    localStorage.setItem('refresh_token', authData.refresh_token);\n    localStorage.setItem('user', JSON.stringify(authData.user));\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAyB,OAAO,CAG5C,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,8BAA8B,CAEpF;AACA,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAClD,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAM,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAb,GAAG,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,EAAKA,QAAQ,CACtB,KAAO,CAAAH,KAAK,EAAK,KAAAI,eAAA,CACf,KAAM,CAAAC,eAAe,CAAGL,KAAK,CAACN,MAAM,CAEpC,GAAI,EAAAU,eAAA,CAAAJ,KAAK,CAACG,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBE,MAAM,IAAK,GAAG,EAAI,CAACD,eAAe,CAACE,MAAM,CAAE,CAC7DF,eAAe,CAACE,MAAM,CAAG,IAAI,CAE7B,GAAI,CACF,KAAM,CAAAC,YAAY,CAAGZ,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAC1D,GAAI,CAACW,YAAY,CAAE,CACjB,KAAM,IAAI,CAAAC,KAAK,CAAC,4BAA4B,CAAC,CAC/C,CAEA,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAArB,KAAK,CAAC4B,IAAI,IAAAX,MAAA,CAAIhB,YAAY,kBAAiB,CAChE4B,aAAa,CAAEH,YACjB,CAAC,CAAC,CAEF,KAAM,CAAEb,KAAK,CAAEiB,cAAc,CAAED,aAAa,CAAEE,eAAgB,CAAC,CAAGV,QAAQ,CAACW,IAAI,CAE/ElB,YAAY,CAACmB,OAAO,CAAC,cAAc,CAAEH,cAAc,CAAC,CACpDhB,YAAY,CAACmB,OAAO,CAAC,eAAe,CAAEF,eAAe,CAAC,CAEtD;AACAR,eAAe,CAACf,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaa,cAAc,CAAE,CAClE,MAAO,CAAAzB,GAAG,CAACkB,eAAe,CAAC,CAC7B,CAAE,MAAOW,YAAY,CAAE,CACrB;AACApB,YAAY,CAACqB,UAAU,CAAC,cAAc,CAAC,CACvCrB,YAAY,CAACqB,UAAU,CAAC,eAAe,CAAC,CACxCrB,YAAY,CAACqB,UAAU,CAAC,MAAM,CAAC,CAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CAC/B,MAAO,CAAAnB,OAAO,CAACC,MAAM,CAACc,YAAY,CAAC,CACrC,CACF,CAEA,MAAO,CAAAf,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAaD,MAAO,MAAM,CAAAqB,WAAW,CAAG,CACzB;AACAC,KAAK,CAAGC,WAAyB,EAA2C,CAC1E,MAAO,CAAApC,GAAG,CAACuB,IAAI,CAAC,aAAa,CAAEa,WAAW,CAAC,CAC7C,CAAC,CAED;AACAC,QAAQ,CAAGC,QAAyB,EAA2C,CAC7E,MAAO,CAAAtC,GAAG,CAACuB,IAAI,CAAC,gBAAgB,CAAEe,QAAQ,CAAC,CAC7C,CAAC,CAED;AACAjB,YAAY,CAAGA,YAAoB,EAA2C,CAC5E,MAAO,CAAArB,GAAG,CAACuB,IAAI,CAAC,eAAe,CAAE,CAAEC,aAAa,CAAEH,YAAa,CAAC,CAAC,CACnE,CAAC,CAED;AACAkB,MAAM,CAAEA,CAAA,GAAmD,CACzD,MAAO,CAAAvC,GAAG,CAACuB,IAAI,CAAC,cAAc,CAAC,CACjC,CAAC,CAED;AACAiB,eAAe,CAAEA,CAAA,GAAmD,CAClE,MAAO,CAAAxC,GAAG,CAACuB,IAAI,CAAC,kBAAkB,CAAC,CACrC,CAAC,CAED;AACAkB,UAAU,CAAEA,CAAA,GAA+D,CACzE,MAAO,CAAAzC,GAAG,CAAC0C,GAAG,CAAC,eAAe,CAAC,CACjC,CAAC,CAED;AACAC,eAAe,CAAEA,CAAA,GAAe,CAC9B,KAAM,CAAAnC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAClD,MAAO,CAAC,CAACF,KAAK,CAChB,CAAC,CAED;AACAoC,cAAc,CAAEA,CAAA,GAAmB,CACjC,KAAM,CAAAC,OAAO,CAAGpC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAC5C,MAAO,CAAAmC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC,CAAG,IAAI,CAC7C,CAAC,CAED;AACAG,OAAO,CAAGC,IAAY,EAAc,KAAAC,WAAA,CAClC,KAAM,CAAAC,IAAI,CAAGjB,WAAW,CAACU,cAAc,CAAC,CAAC,CACzC,MAAO,CAAAO,IAAI,SAAJA,IAAI,kBAAAD,WAAA,CAAJC,IAAI,CAAEC,KAAK,UAAAF,WAAA,iBAAXA,WAAA,CAAaG,QAAQ,CAACJ,IAAI,CAAC,GAAI,KAAK,CAC7C,CAAC,CAED;AACAK,OAAO,CAAEA,CAAA,GAAe,CACtB,MAAO,CAAApB,WAAW,CAACc,OAAO,CAAC,OAAO,CAAC,CACrC,CAAC,CAED;AACAO,SAAS,CAAEA,CAAA,GAAY,CACrB9C,YAAY,CAACqB,UAAU,CAAC,cAAc,CAAC,CACvCrB,YAAY,CAACqB,UAAU,CAAC,eAAe,CAAC,CACxCrB,YAAY,CAACqB,UAAU,CAAC,MAAM,CAAC,CACjC,CAAC,CAED;AACA0B,SAAS,CAAGC,QAAsB,EAAW,CAC3ChD,YAAY,CAACmB,OAAO,CAAC,cAAc,CAAE6B,QAAQ,CAACjD,KAAK,CAAC,CACpDC,YAAY,CAACmB,OAAO,CAAC,eAAe,CAAE6B,QAAQ,CAACjC,aAAa,CAAC,CAC7Df,YAAY,CAACmB,OAAO,CAAC,MAAM,CAAEkB,IAAI,CAACY,SAAS,CAACD,QAAQ,CAACN,IAAI,CAAC,CAAC,CAC7D,CACF,CAAC,CAED,cAAe,CAAAjB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}