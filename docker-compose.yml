version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: jwt-auth-postgres
    environment:
      POSTGRES_DB: jwt_auth_db
      POSTGRES_USER: jwt_user
      POSTGRES_PASSWORD: jwt_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jwt_user -d jwt_auth_db"]
      interval: 10s
      timeout: 5s
      retries: 5
  redis:
    image: redis:7-alpine
    container_name: jwt-auth-redis
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data: 