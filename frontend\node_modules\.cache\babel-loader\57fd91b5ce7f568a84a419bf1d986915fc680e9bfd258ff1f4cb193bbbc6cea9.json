{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  user,\n  onLogout\n}) => {\n  const isAdmin = user && user.roles && user.roles.includes('admin');\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"JWT Auth App\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: user && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/tokens\",\n            style: {\n              color: 'white',\n              marginRight: 20,\n              textDecoration: 'underline'\n            },\n            children: \"Tokens\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 15\n          }, this), isAdmin && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/users\",\n              style: {\n                color: 'white',\n                marginRight: 20,\n                textDecoration: 'underline'\n              },\n              children: \"Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/permissions\",\n              style: {\n                color: 'white',\n                marginRight: 20,\n                textDecoration: 'underline'\n              },\n              children: \"Permissions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: '15px'\n            },\n            children: [\"Welcome, \", user.username, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onLogout,\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "user", "onLogout", "isAdmin", "roles", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "style", "color", "marginRight", "textDecoration", "username", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/Navbar.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { User } from '../types';\r\n\r\ninterface NavbarProps {\r\n  user: User | null;\r\n  onLogout: () => void;\r\n}\r\n\r\nconst Navbar: React.FC<NavbarProps> = ({ user, onLogout }) => {\r\n  const isAdmin = user && user.roles && user.roles.includes('admin');\r\n  return (\r\n    <nav className=\"navbar\">\r\n      <div className=\"container\">\r\n        <h1>JWT Auth App</h1>\r\n        <div>\r\n          {user && (\r\n            <>\r\n              <Link to=\"/tokens\" style={{ color: 'white', marginRight: 20, textDecoration: 'underline' }}>\r\n                Tokens\r\n              </Link>\r\n              {isAdmin && (\r\n                <>\r\n                  <Link to=\"/admin/users\" style={{ color: 'white', marginRight: 20, textDecoration: 'underline' }}>\r\n                    Users\r\n                  </Link>\r\n                  <Link to=\"/admin/permissions\" style={{ color: 'white', marginRight: 20, textDecoration: 'underline' }}>\r\n                    Permissions\r\n                  </Link>\r\n                </>\r\n              )}\r\n              <span style={{ marginRight: '15px' }}>\r\n                Welcome, {user.username}!\r\n              </span>\r\n              <button onClick={onLogout}>Logout</button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navbar; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQxC,MAAMC,MAA6B,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAC5D,MAAMC,OAAO,GAAGF,IAAI,IAAIA,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACG,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC;EAClE,oBACER,OAAA;IAAKS,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACrBV,OAAA;MAAKS,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBV,OAAA;QAAAU,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBd,OAAA;QAAAU,QAAA,EACGN,IAAI,iBACHJ,OAAA,CAAAE,SAAA;UAAAQ,QAAA,gBACEV,OAAA,CAACF,IAAI;YAACiB,EAAE,EAAC,SAAS;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE,OAAO;cAAEC,WAAW,EAAE,EAAE;cAAEC,cAAc,EAAE;YAAY,CAAE;YAAAT,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNR,OAAO,iBACNN,OAAA,CAAAE,SAAA;YAAAQ,QAAA,gBACEV,OAAA,CAACF,IAAI;cAACiB,EAAE,EAAC,cAAc;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE,OAAO;gBAAEC,WAAW,EAAE,EAAE;gBAAEC,cAAc,EAAE;cAAY,CAAE;cAAAT,QAAA,EAAC;YAEjG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPd,OAAA,CAACF,IAAI;cAACiB,EAAE,EAAC,oBAAoB;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE,OAAO;gBAAEC,WAAW,EAAE,EAAE;gBAAEC,cAAc,EAAE;cAAY,CAAE;cAAAT,QAAA,EAAC;YAEvG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH,eACDd,OAAA;YAAMgB,KAAK,EAAE;cAAEE,WAAW,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAC,WAC3B,EAACN,IAAI,CAACgB,QAAQ,EAAC,GAC1B;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPd,OAAA;YAAQqB,OAAO,EAAEhB,QAAS;YAAAK,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eAC1C;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,EAAA,GAhCInB,MAA6B;AAkCnC,eAAeA,MAAM;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}