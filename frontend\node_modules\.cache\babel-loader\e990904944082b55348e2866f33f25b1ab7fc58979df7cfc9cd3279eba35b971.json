{"ast": null, "code": "// Export all services\nexport { authService } from './auth.service';\nexport { userService } from './user.service';\nexport { roleService } from './role.service';\nexport { permissionService } from './permission.service';\nexport { adminService } from './admin.service';\n\n// Export base service and types\nexport { BaseCRUDService, api } from './base.service';\n\n// Export service-specific types\n\n// Default exports for convenience\nimport authService from './auth.service';\nimport userService from './user.service';\nimport roleService from './role.service';\nimport permissionService from './permission.service';\nimport adminService from './admin.service';\nexport default {\n  auth: authService,\n  user: userService,\n  role: roleService,\n  permission: permissionService,\n  admin: adminService\n};", "map": {"version": 3, "names": ["authService", "userService", "roleService", "permissionService", "adminService", "BaseCRUDService", "api", "auth", "user", "role", "permission", "admin"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/index.ts"], "sourcesContent": ["// Export all services\nexport { authService } from './auth.service';\nexport { userService } from './user.service';\nexport { roleService } from './role.service';\nexport { permissionService } from './permission.service';\nexport { adminService } from './admin.service';\n\n// Export base service and types\nexport { BaseCRUDService, api } from './base.service';\nexport type { \n  PaginationInfo, \n  CRUDListResponse, \n  CRUDResponse, \n  SearchRequest, \n  ErrorResponse \n} from './base.service';\n\n// Export service-specific types\nexport type { \n  LoginRequest, \n  RegisterRequest \n} from './auth.service';\n\nexport type { \n  CreateUserRequest, \n  UpdateUserRequest, \n  UserWithRoles \n} from './user.service';\n\nexport type { \n  CreateRoleRequest, \n  UpdateRoleRequest \n} from './role.service';\n\nexport type { \n  CreatePermissionRequest, \n  UpdatePermissionRequest \n} from './permission.service';\n\nexport type { \n  DashboardStats,\n  AssignRoleRequest,\n  RemoveRoleRequest,\n  AssignPermissionRequest,\n  RemovePermissionRequest,\n  AssignUserPermissionRequest,\n  RemoveUserPermissionRequest\n} from './admin.service';\n\n// Default exports for convenience\nimport authService from './auth.service';\nimport userService from './user.service';\nimport roleService from './role.service';\nimport permissionService from './permission.service';\nimport adminService from './admin.service';\n\nexport default {\n  auth: authService,\n  user: userService,\n  role: roleService,\n  permission: permissionService,\n  admin: adminService\n};\n"], "mappings": "AAAA;AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AACA,SAASC,eAAe,EAAEC,GAAG,QAAQ,gBAAgB;;AASrD;;AAgCA;AACA,OAAON,WAAW,MAAM,gBAAgB;AACxC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,YAAY,MAAM,iBAAiB;AAE1C,eAAe;EACbG,IAAI,EAAEP,WAAW;EACjBQ,IAAI,EAAEP,WAAW;EACjBQ,IAAI,EAAEP,WAAW;EACjBQ,UAAU,EAAEP,iBAAiB;EAC7BQ,KAAK,EAAEP;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}