[{"C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Navbar.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Profile.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\AuthForm.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserManagement.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\TokenManager.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserPermissionManagement.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleModal.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\PermissionModal.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserModal.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\AssignmentsTab.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\index.ts": "14", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\user.service.ts": "15", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\auth.service.ts": "16", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\permission.service.ts": "17", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\base.service.ts": "18", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\admin.service.ts": "19", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\role.service.ts": "20", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleManagement.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RolePermissionModal.tsx": "22"}, {"size": 276, "mtime": 1750820674923, "results": "23", "hashOfConfig": "24"}, {"size": 2501, "mtime": 1750951478101, "results": "25", "hashOfConfig": "24"}, {"size": 1534, "mtime": 1750933404551, "results": "26", "hashOfConfig": "24"}, {"size": 2887, "mtime": 1750845887636, "results": "27", "hashOfConfig": "24"}, {"size": 3411, "mtime": 1750839310233, "results": "28", "hashOfConfig": "24"}, {"size": 4405, "mtime": 1750839531789, "results": "29", "hashOfConfig": "24"}, {"size": 9993, "mtime": 1750930541246, "results": "30", "hashOfConfig": "24"}, {"size": 5681, "mtime": 1750930053542, "results": "31", "hashOfConfig": "24"}, {"size": 28240, "mtime": 1750932081153, "results": "32", "hashOfConfig": "24"}, {"size": 4768, "mtime": 1750930701922, "results": "33", "hashOfConfig": "24"}, {"size": 4934, "mtime": 1750930727271, "results": "34", "hashOfConfig": "24"}, {"size": 5716, "mtime": 1750930674289, "results": "35", "hashOfConfig": "24"}, {"size": 10792, "mtime": 1750932131313, "results": "36", "hashOfConfig": "24"}, {"size": 1507, "mtime": 1750932179852, "results": "37", "hashOfConfig": "24"}, {"size": 3273, "mtime": 1750931767041, "results": "38", "hashOfConfig": "24"}, {"size": 4168, "mtime": 1750931721123, "results": "39", "hashOfConfig": "24"}, {"size": 3714, "mtime": 1750931813007, "results": "40", "hashOfConfig": "24"}, {"size": 3584, "mtime": 1750931964561, "results": "41", "hashOfConfig": "24"}, {"size": 4313, "mtime": 1750931902538, "results": "42", "hashOfConfig": "24"}, {"size": 3951, "mtime": 1750933518050, "results": "43", "hashOfConfig": "24"}, {"size": 20320, "mtime": 1750951202783, "results": "44", "hashOfConfig": "24"}, {"size": 11024, "mtime": 1750951281145, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rgau7r", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Profile.tsx", ["112"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\AuthForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserManagement.tsx", ["113", "114", "115"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\api.ts", ["116"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\TokenManager.tsx", ["117"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserPermissionManagement.tsx", ["118", "119"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\PermissionModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\AssignmentsTab.tsx", ["120", "121", "122"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\index.ts", ["123"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\user.service.ts", ["124", "125"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\auth.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\permission.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\base.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\admin.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\role.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleManagement.tsx", ["126", "127"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RolePermissionModal.tsx", ["128"], [], {"ruleId": "129", "severity": 1, "message": "130", "line": 48, "column": 9, "nodeType": "131", "messageId": "132", "endLine": 48, "endColumn": 16}, {"ruleId": "129", "severity": 1, "message": "133", "line": 2, "column": 8, "nodeType": "131", "messageId": "132", "endLine": 2, "endColumn": 11}, {"ruleId": "129", "severity": 1, "message": "134", "line": 3, "column": 16, "nodeType": "131", "messageId": "132", "endLine": 3, "endColumn": 20}, {"ruleId": "129", "severity": 1, "message": "135", "line": 3, "column": 22, "nodeType": "131", "messageId": "132", "endLine": 3, "endColumn": 32}, {"ruleId": "129", "severity": 1, "message": "136", "line": 1, "column": 37, "nodeType": "131", "messageId": "132", "endLine": 1, "endColumn": 50}, {"ruleId": "129", "severity": 1, "message": "137", "line": 55, "column": 13, "nodeType": "131", "messageId": "132", "endLine": 55, "endColumn": 21}, {"ruleId": "129", "severity": 1, "message": "138", "line": 7, "column": 3, "nodeType": "131", "messageId": "132", "endLine": 7, "endColumn": 15}, {"ruleId": "139", "severity": 1, "message": "140", "line": 84, "column": 6, "nodeType": "141", "endLine": 84, "endColumn": 17, "suggestions": "142"}, {"ruleId": "129", "severity": 1, "message": "143", "line": 17, "column": 10, "nodeType": "131", "messageId": "132", "endLine": 17, "endColumn": 30}, {"ruleId": "139", "severity": 1, "message": "144", "line": 26, "column": 6, "nodeType": "141", "endLine": 26, "endColumn": 20, "suggestions": "145"}, {"ruleId": "129", "severity": 1, "message": "146", "line": 81, "column": 9, "nodeType": "131", "messageId": "132", "endLine": 81, "endColumn": 31}, {"ruleId": "147", "severity": 1, "message": "148", "line": 58, "column": 1, "nodeType": "149", "endLine": 64, "endColumn": 3}, {"ruleId": "129", "severity": 1, "message": "150", "line": 3, "column": 27, "nodeType": "131", "messageId": "132", "endLine": 3, "endColumn": 43}, {"ruleId": "129", "severity": 1, "message": "151", "line": 3, "column": 45, "nodeType": "131", "messageId": "132", "endLine": 3, "endColumn": 57}, {"ruleId": "129", "severity": 1, "message": "152", "line": 11, "column": 10, "nodeType": "131", "messageId": "132", "endLine": 11, "endColumn": 21}, {"ruleId": "139", "severity": 1, "message": "153", "line": 47, "column": 6, "nodeType": "141", "endLine": 47, "endColumn": 71, "suggestions": "154"}, {"ruleId": "139", "severity": 1, "message": "155", "line": 33, "column": 6, "nodeType": "141", "endLine": 33, "endColumn": 24, "suggestions": "156"}, "@typescript-eslint/no-unused-vars", "'isAdmin' is assigned a value but never used.", "Identifier", "unusedVar", "'api' is defined but never used.", "'Role' is defined but never used.", "'Permission' is defined but never used.", "'AxiosResponse' is defined but never used.", "'response' is assigned a value but never used.", "'adminService' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadPermissions', 'loadRoles', and 'loadUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["157"], "'availablePermissions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadUserDetails'. Either include it or remove the dependency array.", ["158"], "'handleAssignPermission' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'CRUDListResponse' is defined but never used.", "'CRUDResponse' is defined but never used.", "'permissions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadRoles'. Either include it or remove the dependency array.", ["159"], "React Hook useEffect has missing dependencies: 'loadPermissions', 'loadRolePermissions', and 'role'. Either include them or remove the dependency array.", ["160"], {"desc": "161", "fix": "162"}, {"desc": "163", "fix": "164"}, {"desc": "165", "fix": "166"}, {"desc": "167", "fix": "168"}, "Update the dependencies array to be: [activeTab, loadPermissions, loadRoles, loadUsers]", {"range": "169", "text": "170"}, "Update the dependencies array to be: [loadUserDetails, selectedUser]", {"range": "171", "text": "172"}, "Update the dependencies array to be: [loadRoles, pagination.page, pagination.page_size, searchTerm, statusFilter]", {"range": "173", "text": "174"}, "Update the dependencies array to be: [isOpen, loadPermissions, loadRolePermissions, role, role.id]", {"range": "175", "text": "176"}, [2748, 2759], "[activeTab, loadPermissions, loadRoles, loadUsers]", [1027, 1041], "[loadUserDetails, selectedUser]", [1526, 1591], "[loadRoles, pagination.page, pagination.page_size, searchTerm, statusFilter]", [955, 973], "[isOpen, loadPermissions, loadRolePermissions, role, role.id]"]