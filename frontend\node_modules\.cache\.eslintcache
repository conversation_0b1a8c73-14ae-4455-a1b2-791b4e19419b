[{"C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Navbar.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Profile.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\AuthForm.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserManagement.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\TokenManager.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserPermissionManagement.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleModal.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\PermissionModal.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserModal.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\AssignmentsTab.tsx": "13"}, {"size": 276, "mtime": 1750820674923, "results": "14", "hashOfConfig": "15"}, {"size": 2153, "mtime": 1750930918349, "results": "16", "hashOfConfig": "15"}, {"size": 1363, "mtime": 1750930934228, "results": "17", "hashOfConfig": "15"}, {"size": 2887, "mtime": 1750845887636, "results": "18", "hashOfConfig": "15"}, {"size": 3411, "mtime": 1750839310233, "results": "19", "hashOfConfig": "15"}, {"size": 4405, "mtime": 1750839531789, "results": "20", "hashOfConfig": "15"}, {"size": 9993, "mtime": 1750930541246, "results": "21", "hashOfConfig": "15"}, {"size": 5681, "mtime": 1750930053542, "results": "22", "hashOfConfig": "15"}, {"size": 29349, "mtime": 1750931447367, "results": "23", "hashOfConfig": "15"}, {"size": 4768, "mtime": 1750930701922, "results": "24", "hashOfConfig": "15"}, {"size": 4934, "mtime": 1750930727271, "results": "25", "hashOfConfig": "15"}, {"size": 5716, "mtime": 1750930674289, "results": "26", "hashOfConfig": "15"}, {"size": 10762, "mtime": 1750931260162, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rgau7r", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Profile.tsx", ["67"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\AuthForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserManagement.tsx", ["68", "69", "70"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\api.ts", ["71"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\TokenManager.tsx", ["72"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserPermissionManagement.tsx", ["73", "74"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\PermissionModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\AssignmentsTab.tsx", ["75", "76", "77"], [], {"ruleId": "78", "severity": 1, "message": "79", "line": 48, "column": 9, "nodeType": "80", "messageId": "81", "endLine": 48, "endColumn": 16}, {"ruleId": "78", "severity": 1, "message": "82", "line": 2, "column": 8, "nodeType": "80", "messageId": "81", "endLine": 2, "endColumn": 11}, {"ruleId": "78", "severity": 1, "message": "83", "line": 3, "column": 16, "nodeType": "80", "messageId": "81", "endLine": 3, "endColumn": 20}, {"ruleId": "78", "severity": 1, "message": "84", "line": 3, "column": 22, "nodeType": "80", "messageId": "81", "endLine": 3, "endColumn": 32}, {"ruleId": "78", "severity": 1, "message": "85", "line": 1, "column": 37, "nodeType": "80", "messageId": "81", "endLine": 1, "endColumn": 50}, {"ruleId": "78", "severity": 1, "message": "86", "line": 55, "column": 13, "nodeType": "80", "messageId": "81", "endLine": 55, "endColumn": 21}, {"ruleId": "78", "severity": 1, "message": "87", "line": 7, "column": 3, "nodeType": "80", "messageId": "81", "endLine": 7, "endColumn": 11}, {"ruleId": "88", "severity": 1, "message": "89", "line": 84, "column": 6, "nodeType": "90", "endLine": 84, "endColumn": 17, "suggestions": "91"}, {"ruleId": "78", "severity": 1, "message": "92", "line": 17, "column": 10, "nodeType": "80", "messageId": "81", "endLine": 17, "endColumn": 30}, {"ruleId": "88", "severity": 1, "message": "93", "line": 26, "column": 6, "nodeType": "90", "endLine": 26, "endColumn": 20, "suggestions": "94"}, {"ruleId": "78", "severity": 1, "message": "95", "line": 81, "column": 9, "nodeType": "80", "messageId": "81", "endLine": 81, "endColumn": 31}, "@typescript-eslint/no-unused-vars", "'isAdmin' is assigned a value but never used.", "Identifier", "unusedVar", "'api' is defined but never used.", "'Role' is defined but never used.", "'Permission' is defined but never used.", "'AxiosResponse' is defined but never used.", "'response' is assigned a value but never used.", "'adminAPI' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadPermissions', 'loadRoles', and 'loadUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["96"], "'availablePermissions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadUserDetails'. Either include it or remove the dependency array.", ["97"], "'handleAssignPermission' is assigned a value but never used.", {"desc": "98", "fix": "99"}, {"desc": "100", "fix": "101"}, "Update the dependencies array to be: [activeTab, loadPermissions, loadRoles, loadUsers]", {"range": "102", "text": "103"}, "Update the dependencies array to be: [loadUserDetails, selectedUser]", {"range": "104", "text": "105"}, [2766, 2777], "[activeTab, loadPermissions, loadRoles, loadUsers]", [1027, 1041], "[loadUserDetails, selectedUser]"]