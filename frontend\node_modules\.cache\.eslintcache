[{"C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Navbar.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Profile.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\AuthForm.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserManagement.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\TokenManager.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserPermissionManagement.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleModal.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\PermissionModal.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserModal.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\AssignmentsTab.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\index.ts": "14", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\user.service.ts": "15", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\auth.service.ts": "16", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\permission.service.ts": "17", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\base.service.ts": "18", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\admin.service.ts": "19", "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\role.service.ts": "20"}, {"size": 276, "mtime": 1750820674923, "results": "21", "hashOfConfig": "22"}, {"size": 2153, "mtime": 1750930918349, "results": "23", "hashOfConfig": "22"}, {"size": 1363, "mtime": 1750930934228, "results": "24", "hashOfConfig": "22"}, {"size": 2887, "mtime": 1750845887636, "results": "25", "hashOfConfig": "22"}, {"size": 3411, "mtime": 1750839310233, "results": "26", "hashOfConfig": "22"}, {"size": 4405, "mtime": 1750839531789, "results": "27", "hashOfConfig": "22"}, {"size": 9993, "mtime": 1750930541246, "results": "28", "hashOfConfig": "22"}, {"size": 5681, "mtime": 1750930053542, "results": "29", "hashOfConfig": "22"}, {"size": 28240, "mtime": 1750932081153, "results": "30", "hashOfConfig": "22"}, {"size": 4768, "mtime": 1750930701922, "results": "31", "hashOfConfig": "22"}, {"size": 4934, "mtime": 1750930727271, "results": "32", "hashOfConfig": "22"}, {"size": 5716, "mtime": 1750930674289, "results": "33", "hashOfConfig": "22"}, {"size": 10792, "mtime": 1750932131313, "results": "34", "hashOfConfig": "22"}, {"size": 1507, "mtime": 1750932179852, "results": "35", "hashOfConfig": "22"}, {"size": 3273, "mtime": 1750931767041, "results": "36", "hashOfConfig": "22"}, {"size": 4168, "mtime": 1750931721123, "results": "37", "hashOfConfig": "22"}, {"size": 3714, "mtime": 1750931813007, "results": "38", "hashOfConfig": "22"}, {"size": 3584, "mtime": 1750931964561, "results": "39", "hashOfConfig": "22"}, {"size": 4313, "mtime": 1750931902538, "results": "40", "hashOfConfig": "22"}, {"size": 3170, "mtime": 1750931789413, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rgau7r", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\Profile.tsx", ["102"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\AuthForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserManagement.tsx", ["103", "104", "105"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\api.ts", ["106"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\TokenManager.tsx", ["107"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserPermissionManagement.tsx", ["108", "109"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\RoleModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\PermissionModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\UserModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\components\\admin\\AssignmentsTab.tsx", ["110", "111", "112"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\index.ts", ["113"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\user.service.ts", ["114", "115"], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\auth.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\permission.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\base.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\admin.service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Build-Project\\frontend\\src\\services\\role.service.ts", [], [], {"ruleId": "116", "severity": 1, "message": "117", "line": 48, "column": 9, "nodeType": "118", "messageId": "119", "endLine": 48, "endColumn": 16}, {"ruleId": "116", "severity": 1, "message": "120", "line": 2, "column": 8, "nodeType": "118", "messageId": "119", "endLine": 2, "endColumn": 11}, {"ruleId": "116", "severity": 1, "message": "121", "line": 3, "column": 16, "nodeType": "118", "messageId": "119", "endLine": 3, "endColumn": 20}, {"ruleId": "116", "severity": 1, "message": "122", "line": 3, "column": 22, "nodeType": "118", "messageId": "119", "endLine": 3, "endColumn": 32}, {"ruleId": "116", "severity": 1, "message": "123", "line": 1, "column": 37, "nodeType": "118", "messageId": "119", "endLine": 1, "endColumn": 50}, {"ruleId": "116", "severity": 1, "message": "124", "line": 55, "column": 13, "nodeType": "118", "messageId": "119", "endLine": 55, "endColumn": 21}, {"ruleId": "116", "severity": 1, "message": "125", "line": 7, "column": 3, "nodeType": "118", "messageId": "119", "endLine": 7, "endColumn": 15}, {"ruleId": "126", "severity": 1, "message": "127", "line": 84, "column": 6, "nodeType": "128", "endLine": 84, "endColumn": 17, "suggestions": "129"}, {"ruleId": "116", "severity": 1, "message": "130", "line": 17, "column": 10, "nodeType": "118", "messageId": "119", "endLine": 17, "endColumn": 30}, {"ruleId": "126", "severity": 1, "message": "131", "line": 26, "column": 6, "nodeType": "128", "endLine": 26, "endColumn": 20, "suggestions": "132"}, {"ruleId": "116", "severity": 1, "message": "133", "line": 81, "column": 9, "nodeType": "118", "messageId": "119", "endLine": 81, "endColumn": 31}, {"ruleId": "134", "severity": 1, "message": "135", "line": 58, "column": 1, "nodeType": "136", "endLine": 64, "endColumn": 3}, {"ruleId": "116", "severity": 1, "message": "137", "line": 3, "column": 27, "nodeType": "118", "messageId": "119", "endLine": 3, "endColumn": 43}, {"ruleId": "116", "severity": 1, "message": "138", "line": 3, "column": 45, "nodeType": "118", "messageId": "119", "endLine": 3, "endColumn": 57}, "@typescript-eslint/no-unused-vars", "'isAdmin' is assigned a value but never used.", "Identifier", "unusedVar", "'api' is defined but never used.", "'Role' is defined but never used.", "'Permission' is defined but never used.", "'AxiosResponse' is defined but never used.", "'response' is assigned a value but never used.", "'adminService' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadPermissions', 'loadRoles', and 'loadUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["139"], "'availablePermissions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadUserDetails'. Either include it or remove the dependency array.", ["140"], "'handleAssignPermission' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'CRUDListResponse' is defined but never used.", "'CRUDResponse' is defined but never used.", {"desc": "141", "fix": "142"}, {"desc": "143", "fix": "144"}, "Update the dependencies array to be: [activeTab, loadPermissions, loadRoles, loadUsers]", {"range": "145", "text": "146"}, "Update the dependencies array to be: [loadUserDetails, selectedUser]", {"range": "147", "text": "148"}, [2748, 2759], "[activeTab, loadPermissions, loadRoles, loadUsers]", [1027, 1041], "[loadUserDetails, selectedUser]"]