{"level":"INFO","timestamp":"2025-06-25T14:03:47.423+0700","caller":"backend/main.go:29","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.425+0700","caller":"backend/main.go:33","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.425+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.500+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.501+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.215+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.216+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.219+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.225+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.232+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.232+0700","caller":"backend/main.go:37","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.243+0700","caller":"backend/main.go:54","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.117+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.166+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.179+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0018258,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.186+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0069804,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.402+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.434+0700","caller":"backend/main.go:29","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.435+0700","caller":"backend/main.go:33","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.440+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.478+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.478+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.871+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.871+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.875+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.880+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.887+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.887+0700","caller":"backend/main.go:37","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.898+0700","caller":"backend/main.go:54","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.464+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0011337,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.481+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.487+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0005259,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.487+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0005259,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.717+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.750+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T14:07:44.092+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/admin/assign-user-permission"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.908+0700","caller":"backend/main.go:29","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.910+0700","caller":"backend/main.go:33","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.910+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.939+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.939+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.185+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.185+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.186+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.191+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.195+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.195+0700","caller":"backend/main.go:37","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.203+0700","caller":"backend/main.go:54","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.655+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005566,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.671+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0001662,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.673+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0010017,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.673+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0022904,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.992+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.0005549,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:52.029+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:26:02.376+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/logout","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:26:10.833+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/api/user/profile","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.792+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.815+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.818+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0.0005185,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.818+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.988+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.000918,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:26.000+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.860+0700","caller":"backend/main.go:36","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.860+0700","caller":"backend/main.go:40","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.860+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.883+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.884+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.166+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.166+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.172+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.176+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.181+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.181+0700","caller":"backend/main.go:44","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.184+0700","caller":"backend/main.go:61","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.010+0700","caller":"backend/main.go:36","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.011+0700","caller":"backend/main.go:40","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.012+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.047+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.048+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.305+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.306+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.315+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.321+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.327+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.327+0700","caller":"backend/main.go:44","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.333+0700","caller":"backend/main.go:61","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.676+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005325,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.695+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0010004,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.701+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0005201,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.703+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0017013,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.824+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.853+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:44.226+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:30:05.810+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.495+0700","caller":"backend/main.go:36","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.496+0700","caller":"backend/main.go:40","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.496+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.521+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.521+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.793+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.793+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.794+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.797+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.800+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.800+0700","caller":"backend/main.go:44","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.807+0700","caller":"backend/main.go:61","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.558+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0006241,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.576+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.591+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0029174,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.595+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0041081,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.842+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.866+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:33:13.571+0700","caller":"v1/auth.go:119","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T14:33:13.646+0700","caller":"v1/auth.go:147","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T14:33:33.526+0700","caller":"v1/admin.go:144","msg":"Permissions listed","count":4}
{"level":"INFO","timestamp":"2025-06-25T14:35:49.336+0700","caller":"v1/admin.go:128","msg":"Roles listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T14:36:03.008+0700","caller":"v1/admin.go:280","msg":"Role permissions retrieved","role_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T14:53:54.746+0700","caller":"v1/auth.go:119","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T14:53:54.819+0700","caller":"v1/auth.go:147","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.882+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.888+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.905+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.909+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.931+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.933+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.934+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.937+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.940+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.943+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.825+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.826+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.827+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.860+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.861+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.243+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.243+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.247+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.253+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.259+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.260+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"FATAL","timestamp":"2025-06-25T14:54:47.333+0700","caller":"backend/main.go:50","msg":"Failed to connect to Redis","error":"dial tcp [::1]:6379: connectex: No connection could be made because the target machine actively refused it.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:50\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.548+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.548+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.549+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.577+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.577+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.866+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.866+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.869+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.872+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.880+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.880+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"FATAL","timestamp":"2025-06-25T14:55:33.956+0700","caller":"backend/main.go:50","msg":"Failed to connect to Redis","error":"dial tcp [::1]:6379: connectex: No connection could be made because the target machine actively refused it.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:50\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.562+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.562+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.562+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.590+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.590+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.876+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.876+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.879+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.881+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.885+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.885+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.890+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.894+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T15:04:41.352+0700","caller":"v1/auth.go:118","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:04:41.492+0700","caller":"v1/auth.go:149","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.307+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.323+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.339+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.352+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.368+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.378+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.379+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.382+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.385+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.388+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.085+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.093+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.135+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.145+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.164+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.165+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.195+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.198+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.203+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.218+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.875+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.884+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.935+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.947+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.968+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.975+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.979+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.989+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.989+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.998+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:54.491+0700","caller":"v1/auth.go:118","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:05:54.570+0700","caller":"v1/auth.go:149","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.857+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.859+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.859+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.900+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.900+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.191+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.192+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.195+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.197+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.201+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.201+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.204+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.211+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"WARN","timestamp":"2025-06-25T15:19:02.533+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:19:05.253+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:19:05.342+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:19:40.623+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:19:42.184+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:19:42.263+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:19:48.416+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:19:49.392+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:19:49.518+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:21:53.182+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:21:53.398+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:21:57.657+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:21:58.520+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:21:58.625+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:21:59.847+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:22:01.677+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:22:01.758+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.051+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.061+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.073+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.083+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.094+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.095+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.100+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.105+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.109+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.119+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.465+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.465+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.466+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.492+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.492+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.757+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.758+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.762+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.767+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.772+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.772+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.776+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.780+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.363+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005688,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.394+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.423+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0011967,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.424+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0026686,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.711+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.0012886,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.752+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.495+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.519+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.520+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.521+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.641+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.658+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:07.999+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005611,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.025+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.027+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.027+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.103+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.130+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.205+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0006252,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.235+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.240+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.240+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.339+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.000533,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.362+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T15:47:18.747+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:20.765+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:21.281+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:21.468+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:21.644+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:22.255+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:22.764+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.168+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.341+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.490+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.642+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.786+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.927+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:24.075+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:24.707+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:24.848+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:25.042+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"INFO","timestamp":"2025-06-25T15:48:28.473+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:29.617+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:29.799+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:30.071+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:30.255+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:30.399+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:45.418+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:53.113+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:54.536+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:55.247+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:55.912+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.212+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.213+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.213+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.234+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.234+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.540+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.540+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.548+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.552+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.555+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.555+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.563+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.570+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T15:52:36.122+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:36.983+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:41.498+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.175+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.384+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.555+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.736+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"WARN","timestamp":"2025-06-25T15:55:06.039+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:55:09.911+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:55:09.994+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.431+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.440+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.454+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.466+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.478+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.484+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.484+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.489+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.492+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.498+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:56:29.475+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:56:29.545+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.173+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005262,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.210+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0007948,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.219+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.220+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0010477,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.399+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T15:57:15.577+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:57:17.067+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:57:17.143+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:57:39.887+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:57:45.307+0700","caller":"v1/auth.go:33","msg":"User registration attempt","email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T15:57:45.313+0700","caller":"v1/auth.go:41","msg":"Registration failed - user already exists","email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T15:57:49.337+0700","caller":"v1/auth.go:33","msg":"User registration attempt","email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T15:57:49.442+0700","caller":"v1/auth.go:97","msg":"User registered successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.709+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.710+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.710+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.736+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.736+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.013+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.014+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.019+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.021+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.025+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.025+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.029+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.032+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.426+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005342,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.455+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.466+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0024003,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.467+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0044912,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.810+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.848+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T16:18:15.322+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T16:18:16.584+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:18:16.669+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:19:58.048+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"WARN","timestamp":"2025-06-25T16:19:58.052+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"INFO","timestamp":"2025-06-25T16:19:58.096+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:20:01.961+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:20:02.035+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:20:33.531+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"WARN","timestamp":"2025-06-25T16:20:33.535+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"INFO","timestamp":"2025-06-25T16:20:33.592+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:25:59.966+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:26:00.041+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:26:16.091+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:26:16.165+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:26:22.945+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:26:23.052+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:27:59.538+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:27:59.655+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:28:08.208+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:28:08.291+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.207+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.208+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.208+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.232+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.232+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.467+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.467+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.470+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.473+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.478+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.478+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.481+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.487+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T16:31:14.971+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:31:15.092+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:31:19.375+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:31:19.448+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:32:33.024+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:32:33.103+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:32:36.384+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:32:36.451+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:35:43.449+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:43.540+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:35:46.424+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:46.495+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:35:49.527+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T16:35:50.239+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:50.307+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:35:51.076+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T16:35:51.678+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:51.746+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:36:13.745+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:36:13.824+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:36:18.646+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:36:18.721+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:38:19.218+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/user/profile"}
{"level":"INFO","timestamp":"2025-06-25T16:38:29.673+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:38:29.746+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:38:32.167+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:38:32.242+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:38:37.147+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:38:37.217+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.771+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.786+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.794+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.803+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.823+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.825+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.826+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.833+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.835+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.837+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.840+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.842+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.845+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.852+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:40:01.267+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:40:01.351+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:42:16.317+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:42:16.390+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:42:19.432+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:42:19.502+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:45:26.265+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:45:26.355+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:45:38.915+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:45:38.992+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:45:59.609+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:45:59.715+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:47:15.490+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:47:15.605+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:47:17.822+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.103+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0010807,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.125+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.129+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0011045,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.130+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0012373,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.348+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.369+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T16:48:19.214+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/user/profile"}
{"level":"INFO","timestamp":"2025-06-25T16:49:23.279+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:49:23.390+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.945+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.946+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.946+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.977+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.977+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.230+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.230+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.234+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.236+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.239+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.240+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.243+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.246+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.367+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0006032,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.402+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0005115,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.403+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0019359,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.403+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0016337,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.776+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.0005292,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.809+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:59.886+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:57:59.964+0700","caller":"v1/auth.go:167","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:58:01.350+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:58:04.828+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"WARN","timestamp":"2025-06-25T16:59:03.217+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/user/profile"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.004+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.005+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.006+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.046+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.046+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.309+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.309+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.312+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.314+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.318+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.318+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.321+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.325+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T17:02:15.231+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:02:15.309+0700","caller":"v1/auth.go:167","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T17:02:17.299+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/api/v1/auth/refresh-info","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:02:19.501+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:02:20.876+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/api/v1/auth/refresh-info","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:02:24.147+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:02:24.212+0700","caller":"v1/auth.go:167","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T17:04:48.283+0700","caller":"middleware/auth.go:56","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"INFO","timestamp":"2025-06-25T17:04:48.292+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.761+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.762+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.763+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.797+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.797+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.044+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.044+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.048+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.053+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.057+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.057+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.061+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.066+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"FATAL","timestamp":"2025-06-25T17:05:13.066+0700","caller":"backend/main.go:73","msg":"Server failed to start","error":"listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:73\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.294+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.294+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.295+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.316+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.316+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.563+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.563+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.569+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.573+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.577+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.577+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.591+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.597+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T17:06:24.282+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:24.353+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T17:06:32.693+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T17:06:34.351+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:34.418+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T17:06:38.500+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T17:06:39.157+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:39.227+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.427+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.435+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.444+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.451+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.460+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.460+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.460+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.466+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.468+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.479+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.479+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.483+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.484+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.485+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:45.718+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:45.787+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T13:34:58.942+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"FATAL","timestamp":"2025-06-26T13:34:58.963+0700","caller":"database/database.go:61","msg":"Failed to connect to database","error":"failed to connect to `host=localhost user=jwt_user database=jwt_auth_db`: dial error (dial tcp 127.0.0.1:5432: connectex: No connection could be made because the target machine actively refused it.)","stacktrace":"jwt-auth-backend/database.InitDB\n\tC:/Users/<USER>/Desktop/Build-Project/backend/database/database.go:61\nmain.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/cmd/test_crud/main.go:45\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.084+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.120+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.120+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.490+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.491+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.494+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.497+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:48:32.500+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.505+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.506+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.506+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.532+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.532+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.782+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.782+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.786+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.789+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.795+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.795+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T13:49:44.804+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T13:50:13.870+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T13:50:13.870+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T13:50:13.871+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T13:50:13.900+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:50:13.900+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.139+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.139+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.141+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.146+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.149+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.149+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.153+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T13:50:14.158+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-26T13:50:26.239+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005349,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-26T13:50:26.279+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0020003,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-26T13:50:26.280+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0039982,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-26T13:50:26.282+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0009996,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-26T13:50:26.544+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-26T13:54:08.313+0700","caller":"v1/auth.go:33","msg":"User registration attempt","email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-26T13:54:08.317+0700","caller":"v1/auth.go:41","msg":"Registration failed - user already exists","email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T13:54:08.366+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"WARN","timestamp":"2025-06-26T13:54:08.371+0700","caller":"v1/auth.go:130","msg":"Login failed - user not found","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T13:54:48.733+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T13:54:48.761+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:54:48.761+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T13:54:49.001+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T13:54:49.001+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T13:54:49.004+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:54:49.006+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:54:49.010+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:56:10.962+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T13:56:10.990+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:56:10.990+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T13:56:11.228+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T13:56:11.228+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T13:56:11.229+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:56:11.231+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:56:11.236+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.329+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.353+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.353+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.584+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.584+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.587+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.589+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T13:56:29.594+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T13:56:36.449+0700","caller":"v1/auth.go:33","msg":"User registration attempt","email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-26T13:56:36.451+0700","caller":"v1/auth.go:41","msg":"Registration failed - user already exists","email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T13:56:36.463+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T13:56:36.531+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T13:56:36.634+0700","caller":"services/base_crud.go:41","msg":"Record created successfully","type":"*models.Role"}
{"level":"INFO","timestamp":"2025-06-26T13:56:36.635+0700","caller":"services/role_service.go:52","msg":"Role created successfully","id":3,"name":"test_manager"}
{"level":"INFO","timestamp":"2025-06-26T13:56:36.653+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Role","total":3,"returned":3}
{"level":"WARN","timestamp":"2025-06-26T13:56:36.745+0700","caller":"handlers/role_handler.go:164","msg":"Invalid list roles request","error":"Key: 'CRUDRequest.PaginationRequest.Page' Error:Field validation for 'Page' failed on the 'min' tag\nKey: 'CRUDRequest.PaginationRequest.PageSize' Error:Field validation for 'PageSize' failed on the 'min' tag"}
{"level":"INFO","timestamp":"2025-06-26T13:57:06.329+0700","caller":"v1/auth.go:33","msg":"User registration attempt","email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-26T13:57:06.333+0700","caller":"v1/auth.go:41","msg":"Registration failed - user already exists","email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T13:57:06.345+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T13:57:06.409+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-26T13:57:06.463+0700","caller":"services/role_service.go:36","msg":"Role creation failed - name already exists","name":"test_manager"}
{"level":"INFO","timestamp":"2025-06-26T13:57:06.469+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Role","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T13:57:06.488+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Role","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T14:21:15.760+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T14:21:15.761+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T14:21:15.761+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.326+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.326+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.595+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.595+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.597+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.599+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.603+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.603+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.615+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T14:21:16.620+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"FATAL","timestamp":"2025-06-26T14:21:16.621+0700","caller":"backend/main.go:73","msg":"Server failed to start","error":"listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:73\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.561+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.561+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.561+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.594+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.594+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.874+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.874+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.876+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.878+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.882+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.882+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.886+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T14:21:43.890+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"WARN","timestamp":"2025-06-26T14:22:27.338+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/role-management"}
{"level":"ERROR","timestamp":"2025-06-26T14:22:37.957+0700","caller":"v1/auth.go:123","msg":"Invalid login request","error":"invalid character ':' in string escape code","stacktrace":"jwt-auth-backend/handlers/v1.Login\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:123\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.543+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.544+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.544+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.603+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.603+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.891+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.891+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.893+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.896+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.902+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.902+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.915+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T15:38:01.921+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-26T15:38:31.632+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T15:38:31.715+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T15:38:41.345+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T15:38:41.410+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T16:29:13.224+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/favicon.ico","query":"","ip":"127.0.0.1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-26T16:29:50.109+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"WARN","timestamp":"2025-06-26T16:29:50.110+0700","caller":"v1/auth.go:221","msg":"Invalid or expired refresh token","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:29:50.117+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"WARN","timestamp":"2025-06-26T16:29:50.117+0700","caller":"v1/auth.go:221","msg":"Invalid or expired refresh token","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:29:58.547+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"WARN","timestamp":"2025-06-26T16:29:58.547+0700","caller":"v1/auth.go:221","msg":"Invalid or expired refresh token","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:29:58.550+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"WARN","timestamp":"2025-06-26T16:29:58.551+0700","caller":"v1/auth.go:221","msg":"Invalid or expired refresh token","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:04.751+0700","caller":"v1/auth.go:311","msg":"Revoking all refresh tokens","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:04.763+0700","caller":"v1/auth.go:320","msg":"All refresh tokens revoked successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:07.482+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:30:07.565+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.085+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.098+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.115+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.121+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.136+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.142+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.153+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.157+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.157+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.159+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.161+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.165+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.170+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:10.178+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:14.842+0700","caller":"v1/auth.go:311","msg":"Revoking all refresh tokens","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:14.844+0700","caller":"v1/auth.go:320","msg":"All refresh tokens revoked successfully","user_id":1}
{"level":"WARN","timestamp":"2025-06-26T16:30:16.571+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-26T16:30:16.575+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"INFO","timestamp":"2025-06-26T16:30:17.811+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:30:17.879+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T16:30:21.657+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:21.661+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:25.563+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:25.566+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:26.472+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:26.476+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:26.660+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:26.662+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:27.061+0700","caller":"v1/auth.go:311","msg":"Revoking all refresh tokens","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:27.061+0700","caller":"v1/auth.go:320","msg":"All refresh tokens revoked successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:31.335+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:30:31.402+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.449+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.458+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.466+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.469+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.473+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.475+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.485+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.488+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.492+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.494+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.495+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.501+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.510+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:33.513+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T16:30:51.945+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:30:52.014+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T16:31:04.629+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:04.633+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:05.386+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:05.388+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:05.593+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:05.596+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:05.753+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:05.756+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:06.433+0700","caller":"v1/auth.go:311","msg":"Revoking all refresh tokens","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:06.434+0700","caller":"v1/auth.go:320","msg":"All refresh tokens revoked successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:31:09.643+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:31:09.709+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T16:34:11.959+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-26T16:34:12.830+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:34:12.970+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T16:34:15.530+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:15.534+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:16.095+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:16.099+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:16.279+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:16.282+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:16.969+0700","caller":"v1/auth.go:311","msg":"Revoking all refresh tokens","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:16.970+0700","caller":"v1/auth.go:320","msg":"All refresh tokens revoked successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T16:34:19.939+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:34:20.033+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T16:34:21.663+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"WARN","timestamp":"2025-06-26T16:35:42.728+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/user/profile"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.352+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.353+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.354+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.433+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.433+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.838+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.838+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.841+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.846+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.852+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.852+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.871+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T16:52:05.883+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"FATAL","timestamp":"2025-06-26T16:52:05.884+0700","caller":"backend/main.go:73","msg":"Server failed to start","error":"listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:73\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-26T16:53:08.359+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:53:08.444+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T16:53:17.052+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:53:17.120+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-26T16:53:17.154+0700","caller":"handlers/role_handler.go:164","msg":"Invalid list roles request","error":"Key: 'CRUDRequest.PaginationRequest.Page' Error:Field validation for 'Page' failed on the 'min' tag\nKey: 'CRUDRequest.PaginationRequest.PageSize' Error:Field validation for 'PageSize' failed on the 'min' tag"}
{"level":"INFO","timestamp":"2025-06-26T16:53:26.720+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:53:26.788+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T16:53:26.825+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Role","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T16:53:35.166+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T16:53:35.238+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T16:53:37.346+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.User","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T16:53:37.361+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.User","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T17:03:40.212+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:03:40.284+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T17:03:40.324+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Permission","total":4,"returned":4}
{"level":"INFO","timestamp":"2025-06-26T17:03:40.387+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:03:40.456+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T17:03:41.857+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-26T17:03:43.035+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:03:43.103+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T17:03:45.177+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"WARN","timestamp":"2025-06-26T17:03:45.179+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"ERROR","timestamp":"2025-06-26T17:03:45.223+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"ERROR","timestamp":"2025-06-26T17:03:45.222+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"INFO","timestamp":"2025-06-26T17:03:46.715+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:03:46.790+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T17:03:47.829+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"WARN","timestamp":"2025-06-26T17:03:47.832+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"ERROR","timestamp":"2025-06-26T17:03:47.837+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"ERROR","timestamp":"2025-06-26T17:03:47.841+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"INFO","timestamp":"2025-06-26T17:03:49.243+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:03:49.310+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.486+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.497+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.517+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.524+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.532+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.536+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.544+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.546+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.553+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.553+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.561+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.563+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.565+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:03:50.570+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.170+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.171+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.171+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.198+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.198+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.445+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.445+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.448+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.454+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.458+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.458+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.465+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T17:06:59.471+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"FATAL","timestamp":"2025-06-26T17:06:59.471+0700","caller":"backend/main.go:73","msg":"Server failed to start","error":"listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:73\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.176+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.177+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.177+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.209+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.209+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.500+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.500+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.503+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.506+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.511+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.511+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.518+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T17:07:24.588+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-26T17:09:08.570+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:09:08.639+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T17:09:08.861+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Permission","total":4,"returned":4}
{"level":"INFO","timestamp":"2025-06-26T17:09:19.751+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:09:19.817+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T17:09:19.840+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Permission","total":4,"returned":4}
{"level":"INFO","timestamp":"2025-06-26T17:10:10.229+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:10:10.294+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T17:10:10.325+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.Role","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T17:10:19.287+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:10:19.351+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T17:10:19.384+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.User","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.063+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.063+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.064+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.086+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.086+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.325+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.325+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.328+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.331+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.336+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.336+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.340+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-26T17:11:21.347+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-26T17:11:40.001+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:11:40.070+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-26T17:11:40.097+0700","caller":"services/base_crud.go:204","msg":"Records retrieved successfully","type":"*models.User","total":3,"returned":3}
{"level":"INFO","timestamp":"2025-06-26T17:18:01.705+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/favicon.ico","query":"","ip":"127.0.0.1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-26T17:18:26.519+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:18:26.583+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T17:18:29.273+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"WARN","timestamp":"2025-06-26T17:18:29.274+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"ERROR","timestamp":"2025-06-26T17:18:29.283+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"ERROR","timestamp":"2025-06-26T17:18:29.283+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"INFO","timestamp":"2025-06-26T17:18:30.690+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:18:30.755+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.430+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.439+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.445+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.454+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.458+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.466+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.471+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.474+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.479+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.479+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.484+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.485+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.487+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:18:31.493+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":1}
{"level":"INFO","timestamp":"2025-06-26T17:18:34.018+0700","caller":"v1/auth.go:217","msg":"Refresh token attempt","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T17:18:34.024+0700","caller":"v1/auth.go:249","msg":"Access token refreshed successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T17:18:34.652+0700","caller":"v1/auth.go:311","msg":"Revoking all refresh tokens","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T17:18:34.657+0700","caller":"v1/auth.go:320","msg":"All refresh tokens revoked successfully","user_id":1}
{"level":"INFO","timestamp":"2025-06-26T17:30:24.261+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:30:24.336+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T17:30:29.034+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/favicon.ico","query":"","ip":"127.0.0.1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"WARN","timestamp":"2025-06-26T17:31:07.213+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-26T17:31:15.897+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"WARN","timestamp":"2025-06-26T17:31:15.963+0700","caller":"v1/auth.go:154","msg":"Login failed - invalid password","email":"<EMAIL>","user_id":3}
{"level":"INFO","timestamp":"2025-06-26T17:31:16.804+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:31:16.975+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T17:31:30.186+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-26T17:31:31.895+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:31:31.963+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-26T17:31:33.384+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"WARN","timestamp":"2025-06-26T17:31:33.386+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/user-management"}
{"level":"ERROR","timestamp":"2025-06-26T17:31:33.398+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"ERROR","timestamp":"2025-06-26T17:31:33.399+0700","caller":"v1/auth.go:212","msg":"Invalid refresh token request","error":"Key: 'RefreshTokenRequest.UserID' Error:Field validation for 'UserID' failed on the 'required' tag","stacktrace":"jwt-auth-backend/handlers/v1.RefreshToken\n\tC:/Users/<USER>/Desktop/Build-Project/backend/handlers/v1/auth.go:212\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\njwt-auth-backend/routes.SetupRoutes.func1\n\tC:/Users/<USER>/Desktop/Build-Project/backend/routes/routes.go:26\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go/src/net/http/server.go:2102"}
{"level":"INFO","timestamp":"2025-06-26T17:31:34.730+0700","caller":"v1/auth.go:146","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-26T17:31:34.838+0700","caller":"v1/auth.go:187","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.151+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.180+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.180+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.460+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.460+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.462+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.464+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-26T22:16:31.468+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
