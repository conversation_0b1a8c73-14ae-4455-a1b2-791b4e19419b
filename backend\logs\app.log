{"level":"INFO","timestamp":"2025-06-25T14:03:47.423+0700","caller":"backend/main.go:29","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.425+0700","caller":"backend/main.go:33","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.425+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.500+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:03:47.501+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.215+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.216+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.219+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.225+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.232+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.232+0700","caller":"backend/main.go:37","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:03:48.243+0700","caller":"backend/main.go:54","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.117+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.166+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.179+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0018258,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.186+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0069804,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:04:19.402+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.434+0700","caller":"backend/main.go:29","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.435+0700","caller":"backend/main.go:33","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.440+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.478+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.478+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.871+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.871+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.875+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.880+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.887+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.887+0700","caller":"backend/main.go:37","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:07:05.898+0700","caller":"backend/main.go:54","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.464+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0011337,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.481+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.487+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0005259,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.487+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0005259,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.717+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:07:25.750+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T14:07:44.092+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/admin/assign-user-permission"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.908+0700","caller":"backend/main.go:29","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.910+0700","caller":"backend/main.go:33","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.910+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.939+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:25:43.939+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.185+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.185+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.186+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.191+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.195+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.195+0700","caller":"backend/main.go:37","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:25:44.203+0700","caller":"backend/main.go:54","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.655+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005566,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.671+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0001662,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.673+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0010017,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.673+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0022904,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:51.992+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.0005549,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:25:52.029+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:26:02.376+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/logout","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:26:10.833+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/api/user/profile","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.792+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.815+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.818+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0.0005185,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.818+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:25.988+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.000918,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:26.000+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.860+0700","caller":"backend/main.go:36","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.860+0700","caller":"backend/main.go:40","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.860+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.883+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:41.884+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.166+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.166+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.172+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.176+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.181+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.181+0700","caller":"backend/main.go:44","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:42.184+0700","caller":"backend/main.go:61","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.010+0700","caller":"backend/main.go:36","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.011+0700","caller":"backend/main.go:40","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.012+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.047+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.048+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.305+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.306+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.315+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.321+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.327+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.327+0700","caller":"backend/main.go:44","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:28:55.333+0700","caller":"backend/main.go:61","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.676+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005325,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.695+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0010004,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.701+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0005201,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.703+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0017013,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.824+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:15.853+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:29:44.226+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:30:05.810+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.495+0700","caller":"backend/main.go:36","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.496+0700","caller":"backend/main.go:40","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.496+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.521+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.521+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.793+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.793+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.794+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.797+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.800+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.800+0700","caller":"backend/main.go:44","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:32:36.807+0700","caller":"backend/main.go:61","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.558+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0006241,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.576+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.591+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0029174,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.595+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0041081,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.842+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:32:54.866+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T14:33:13.571+0700","caller":"v1/auth.go:119","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T14:33:13.646+0700","caller":"v1/auth.go:147","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T14:33:33.526+0700","caller":"v1/admin.go:144","msg":"Permissions listed","count":4}
{"level":"INFO","timestamp":"2025-06-25T14:35:49.336+0700","caller":"v1/admin.go:128","msg":"Roles listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T14:36:03.008+0700","caller":"v1/admin.go:280","msg":"Role permissions retrieved","role_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T14:53:54.746+0700","caller":"v1/auth.go:119","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T14:53:54.819+0700","caller":"v1/auth.go:147","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.882+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.888+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.905+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.909+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.931+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.933+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.934+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.937+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.940+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:53:56.943+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.825+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.826+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.827+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.860+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:54:46.861+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.243+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.243+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.247+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.253+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.259+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:54:47.260+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"FATAL","timestamp":"2025-06-25T14:54:47.333+0700","caller":"backend/main.go:50","msg":"Failed to connect to Redis","error":"dial tcp [::1]:6379: connectex: No connection could be made because the target machine actively refused it.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:50\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.548+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.548+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.549+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.577+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.577+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.866+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.866+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.869+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.872+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.880+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T14:55:33.880+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"FATAL","timestamp":"2025-06-25T14:55:33.956+0700","caller":"backend/main.go:50","msg":"Failed to connect to Redis","error":"dial tcp [::1]:6379: connectex: No connection could be made because the target machine actively refused it.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:50\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.562+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.562+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.562+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.590+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.590+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.876+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.876+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.879+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.881+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.885+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.885+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.890+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:04:16.894+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T15:04:41.352+0700","caller":"v1/auth.go:118","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:04:41.492+0700","caller":"v1/auth.go:149","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.307+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.323+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.339+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.352+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.368+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.378+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.379+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.382+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.385+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:40.388+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.085+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.093+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.135+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.145+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.164+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.165+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.195+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.198+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.203+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:47.218+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.875+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.884+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.935+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.947+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.968+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.975+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.979+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.989+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.989+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:49.998+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:05:54.491+0700","caller":"v1/auth.go:118","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:05:54.570+0700","caller":"v1/auth.go:149","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.857+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.859+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.859+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.900+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:17:36.900+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.191+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.192+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.195+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.197+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.201+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.201+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.204+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:17:37.211+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"WARN","timestamp":"2025-06-25T15:19:02.533+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:19:05.253+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:19:05.342+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:19:40.623+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:19:42.184+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:19:42.263+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:19:48.416+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:19:49.392+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:19:49.518+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:21:53.182+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:21:53.398+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:21:57.657+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:21:58.520+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:21:58.625+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:21:59.847+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:22:01.677+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:22:01.758+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.051+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.061+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.073+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.083+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.094+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.095+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.100+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.105+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.109+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:22:05.119+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.465+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.465+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.466+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.492+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.492+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.757+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.758+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.762+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.767+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.772+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.772+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.776+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:46:35.780+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.363+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005688,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.394+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.423+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0011967,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.424+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0026686,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.711+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.0012886,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:46:59.752+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.495+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.519+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.520+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.521+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.641+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:04.658+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:07.999+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005611,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.025+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.027+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.027+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.103+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:08.130+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.205+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0006252,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.235+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.240+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.240+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.339+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.000533,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:47:13.362+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":304,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T15:47:18.747+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:20.765+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:21.281+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:21.468+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:21.644+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:22.255+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:22.764+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.168+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.341+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.490+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.642+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.786+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:23.927+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:24.075+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:24.707+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:24.848+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"WARN","timestamp":"2025-06-25T15:47:25.042+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/admin/users"}
{"level":"INFO","timestamp":"2025-06-25T15:48:28.473+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:29.617+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:29.799+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:30.071+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:30.255+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:30.399+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:48:45.418+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:53.113+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:54.536+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:55.247+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:51:55.912+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.212+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.213+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.213+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.234+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.234+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.540+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.540+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.548+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.552+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.555+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.555+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.563+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T15:52:16.570+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T15:52:36.122+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:36.983+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:41.498+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.175+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.384+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.555+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:52:42.736+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/auth/login","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"WARN","timestamp":"2025-06-25T15:55:06.039+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:55:09.911+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:55:09.994+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.431+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.440+0700","caller":"v1/admin.go:112","msg":"Users listed","count":2}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.454+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.466+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.478+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.484+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.484+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.489+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.492+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:55:17.498+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T15:56:29.475+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:56:29.545+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.173+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005262,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.210+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0007948,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.219+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.220+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0010477,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T15:56:41.399+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T15:57:15.577+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:57:17.067+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T15:57:17.143+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T15:57:39.887+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T15:57:45.307+0700","caller":"v1/auth.go:33","msg":"User registration attempt","email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T15:57:45.313+0700","caller":"v1/auth.go:41","msg":"Registration failed - user already exists","email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T15:57:49.337+0700","caller":"v1/auth.go:33","msg":"User registration attempt","email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T15:57:49.442+0700","caller":"v1/auth.go:97","msg":"User registered successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.709+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.710+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.710+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.736+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:11:35.736+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.013+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.014+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.019+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.021+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.025+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.025+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.029+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T16:11:36.032+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.426+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0005342,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.455+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.466+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0024003,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.467+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0044912,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.810+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:11:51.848+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T16:18:15.322+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T16:18:16.584+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:18:16.669+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:19:58.048+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"WARN","timestamp":"2025-06-25T16:19:58.052+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"INFO","timestamp":"2025-06-25T16:19:58.096+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:20:01.961+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:20:02.035+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:20:33.531+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"WARN","timestamp":"2025-06-25T16:20:33.535+0700","caller":"middleware/auth.go:55","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"INFO","timestamp":"2025-06-25T16:20:33.592+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:25:59.966+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:26:00.041+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:26:16.091+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:26:16.165+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:26:22.945+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:26:23.052+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:27:59.538+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:27:59.655+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:28:08.208+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:28:08.291+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.207+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.208+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.208+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.232+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.232+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.467+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.467+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.470+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.473+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.478+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.478+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.481+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T16:28:37.487+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T16:31:14.971+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:31:15.092+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:31:19.375+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:31:19.448+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:32:33.024+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:32:33.103+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:32:36.384+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:32:36.451+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:35:43.449+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:43.540+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:35:46.424+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:46.495+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:35:49.527+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T16:35:50.239+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:50.307+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:35:51.076+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T16:35:51.678+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:35:51.746+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:36:13.745+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:36:13.824+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:36:18.646+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:36:18.721+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"WARN","timestamp":"2025-06-25T16:38:19.218+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/user/profile"}
{"level":"INFO","timestamp":"2025-06-25T16:38:29.673+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:38:29.746+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:38:32.167+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:38:32.242+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":3,"email":"<EMAIL>","username":"admin"}
{"level":"INFO","timestamp":"2025-06-25T16:38:37.147+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:38:37.217+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.771+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.786+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.794+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.803+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.823+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.825+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.826+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.833+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.835+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.837+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.840+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.842+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.845+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:38:38.852+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T16:40:01.267+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:40:01.351+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:42:16.317+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:42:16.390+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:42:19.432+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:42:19.502+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:45:26.265+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:45:26.355+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:45:38.915+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:45:38.992+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:45:59.609+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:45:59.715+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:47:15.490+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:47:15.605+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:47:17.822+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.103+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0010807,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.125+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.129+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0011045,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.130+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0012373,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.348+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:47:37.369+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"WARN","timestamp":"2025-06-25T16:48:19.214+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/user/profile"}
{"level":"INFO","timestamp":"2025-06-25T16:49:23.279+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:49:23.390+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.945+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.946+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.946+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.977+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:57:24.977+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.230+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.230+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.234+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.236+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.239+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.240+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.243+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T16:57:25.246+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.367+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/index.html","query":"","ip":"::1","status":200,"latency":0.0006032,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.402+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","status":200,"latency":0.0005115,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.403+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","status":200,"latency":0.0019359,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.403+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","status":200,"latency":0.0016337,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.776+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","status":200,"latency":0.0005292,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:41.809+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","status":200,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:57:59.886+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T16:57:59.964+0700","caller":"v1/auth.go:167","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T16:58:01.350+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T16:58:04.828+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"WARN","timestamp":"2025-06-25T16:59:03.217+0700","caller":"middleware/auth.go:19","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/user/profile"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.004+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.005+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.006+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.046+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.046+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.309+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.309+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.312+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.314+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.318+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.318+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.321+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T17:02:04.325+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T17:02:15.231+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:02:15.309+0700","caller":"v1/auth.go:167","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T17:02:17.299+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/api/v1/auth/refresh-info","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:02:19.501+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:02:20.876+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"GET","path":"/api/v1/auth/refresh-info","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:02:24.147+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:02:24.212+0700","caller":"v1/auth.go:167","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T17:04:48.283+0700","caller":"middleware/auth.go:56","msg":"Authentication failed - invalid or expired token","ip":"::1","path":"/api/v1/user/profile","error":"token has invalid claims: token is expired"}
{"level":"INFO","timestamp":"2025-06-25T17:04:48.292+0700","caller":"middleware/logging.go:60","msg":"HTTP Request","method":"POST","path":"/api/v1/auth/refresh","query":"","ip":"::1","status":404,"latency":0,"user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","error":""}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.761+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.762+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.763+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.797+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:05:12.797+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.044+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.044+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.048+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.053+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.057+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.057+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.061+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T17:05:13.066+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"FATAL","timestamp":"2025-06-25T17:05:13.066+0700","caller":"backend/main.go:73","msg":"Server failed to start","error":"listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","stacktrace":"main.main\n\tC:/Users/<USER>/Desktop/Build-Project/backend/main.go:73\nruntime.main\n\tC:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.294+0700","caller":"backend/main.go:37","msg":"Starting JWT Auth Backend"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.294+0700","caller":"backend/main.go:41","msg":"JWT initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.295+0700","caller":"database/database.go:47","msg":"Connecting to database","host":"localhost","port":"5432","database":"jwt_auth_db","user":"jwt_user"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.316+0700","caller":"database/database.go:64","msg":"Database connected successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.316+0700","caller":"database/database.go:67","msg":"Starting database migration"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.563+0700","caller":"database/database.go:72","msg":"Database migration completed"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.563+0700","caller":"database/database.go:75","msg":"Seeding roles"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.569+0700","caller":"database/database.go:89","msg":"Seeding permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.573+0700","caller":"database/database.go:103","msg":"Setting up admin role permissions"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.577+0700","caller":"database/database.go:122","msg":"Database initialization completed successfully"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.577+0700","caller":"backend/main.go:45","msg":"Database initialized"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.591+0700","caller":"backend/main.go:52","msg":"Redis connected"}
{"level":"INFO","timestamp":"2025-06-25T17:06:14.597+0700","caller":"backend/main.go:72","msg":"Server starting","port":"8080"}
{"level":"INFO","timestamp":"2025-06-25T17:06:24.282+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:24.353+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T17:06:32.693+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T17:06:34.351+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:34.418+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"WARN","timestamp":"2025-06-25T17:06:38.500+0700","caller":"middleware/auth.go:18","msg":"Authentication failed - missing authorization header","ip":"::1","path":"/api/v1/auth/logout"}
{"level":"INFO","timestamp":"2025-06-25T17:06:39.157+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:39.227+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.427+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.435+0700","caller":"v1/admin.go:112","msg":"Users listed","count":3}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.444+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.451+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"1","permission_count":1}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.460+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.460+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.460+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.466+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"1","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.468+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.479+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.479+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.483+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.484+0700","caller":"v1/admin.go:416","msg":"User direct permissions retrieved","user_id":"2","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:40.485+0700","caller":"v1/admin.go:194","msg":"User permissions retrieved","user_id":"3","permission_count":0}
{"level":"INFO","timestamp":"2025-06-25T17:06:45.718+0700","caller":"v1/auth.go:127","msg":"User login attempt","email":"<EMAIL>"}
{"level":"INFO","timestamp":"2025-06-25T17:06:45.787+0700","caller":"v1/auth.go:168","msg":"User logged in successfully","user_id":1,"email":"<EMAIL>","username":"cong"}
