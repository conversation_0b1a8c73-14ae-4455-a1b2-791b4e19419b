import React, { useState, useEffect } from 'react';
import Navbar from './components/Navbar';
import AuthForm from './components/AuthForm';
import Profile from './components/Profile';
import TokenManager from './components/TokenManager';
import UserManagement from './components/admin/UserManagement';
import UserPermissionManagement from './components/admin/UserPermissionManagement';
import RoleManagement from './components/admin/RoleManagement';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { User } from './types';

const App: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    const savedUser = localStorage.getItem('user');
    if (token && savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (err) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    }
    setLoading(false);
  }, []);

  const handleAuthSuccess = (userData: User) => {
    setUser(userData);
  };

  const handleLogout = () => {
    setUser(null);
  };

  // Kiểm tra user có phải admin không
  const isAdmin = user && user.roles && user.roles.includes('admin');

  // Debug logging
  console.log('Current user:', user);
  console.log('Is admin:', isAdmin);

  if (loading) {
    return (
      <div className="container">
        <div style={{ textAlign: 'center', marginTop: '50px' }}>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <Navbar user={user} onLogout={handleLogout} />
      <Routes>
        <Route path="/" element={user ? <Profile onLogout={handleLogout} /> : <AuthForm onAuthSuccess={handleAuthSuccess} />} />
        <Route path="/tokens" element={user ? <TokenManager /> : <Navigate to="/" />} />
        {isAdmin ? (
          <>
            <Route path="/admin/users" element={<UserManagement />} />
            <Route path="/admin/roles" element={<RoleManagement />} />
            <Route path="/admin/permissions" element={<UserPermissionManagement />} />
          </>
        ) : (
          <>
            <Route path="/admin/*" element={<Navigate to="/" />} />
          </>
        )}
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
    </Router>
  );
};

export default App; 