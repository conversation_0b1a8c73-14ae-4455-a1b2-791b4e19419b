package utils

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"time"

	"jwt-auth-backend/models"

	"github.com/golang-jwt/jwt/v5"
)

var jwtSecret []byte

func InitJWT() {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = "default-secret-key"
	}
	jwtSecret = []byte(secret)
}

func GenerateToken(user models.User) (string, error) {
	claims := jwt.MapClaims{
		"user_id":  user.ID,
		"username": user.Username,
		"email":    user.Email,
		"exp":      time.Now().Add(time.Hour * 24).Unix(), // 24 hours
		"iat":      time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

func ValidateToken(tokenString string) (*jwt.Token, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	return token, nil
}

func ExtractUserFromToken(tokenString string) (uint, string, error) {
	token, err := ValidateToken(tokenString)
	if err != nil {
		return 0, "", err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return 0, "", errors.New("invalid token claims")
	}

	userID, ok := claims["user_id"].(float64)
	if !ok {
		return 0, "", errors.New("invalid user_id in token")
	}

	username, ok := claims["username"].(string)
	if !ok {
		return 0, "", errors.New("invalid username in token")
	}

	return uint(userID), username, nil
}

// GenerateRefreshToken sinh refresh token ngẫu nhiên
func GenerateRefreshToken() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// SaveRefreshTokenToRedis lưu refresh token vào Redis
func SaveRefreshTokenToRedis(userID uint, refreshToken string, expires time.Duration) error {
	key := fmt.Sprintf("refresh:%d:%s", userID, refreshToken)
	return GetRedisClient().Set(context.Background(), key, 1, expires).Err()
}

// ValidateRefreshToken kiểm tra refresh token hợp lệ
func ValidateRefreshToken(userID uint, refreshToken string) bool {
	key := fmt.Sprintf("refresh:%d:%s", userID, refreshToken)
	val, err := GetRedisClient().Get(context.Background(), key).Result()
	return err == nil && val == "1"
}

// DeleteRefreshToken xóa refresh token khỏi Redis
func DeleteRefreshToken(userID uint, refreshToken string) error {
	key := fmt.Sprintf("refresh:%d:%s", userID, refreshToken)
	return GetRedisClient().Del(context.Background(), key).Err()
}

// BlacklistAccessToken lưu access token vào blacklist Redis
func BlacklistAccessToken(token string, expires time.Duration) error {
	key := "blacklist:" + token
	return GetRedisClient().Set(context.Background(), key, 1, expires).Err()
}

// IsAccessTokenBlacklisted kiểm tra access token có bị blacklist không
func IsAccessTokenBlacklisted(token string) bool {
	key := "blacklist:" + token
	val, err := GetRedisClient().Get(context.Background(), key).Result()
	return err == nil && val == "1"
}

// RevokeAllRefreshTokens xóa tất cả refresh tokens của một user
func RevokeAllRefreshTokens(userID uint) error {
	pattern := fmt.Sprintf("refresh:%d:*", userID)
	keys, err := GetRedisClient().Keys(context.Background(), pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		return GetRedisClient().Del(context.Background(), keys...).Err()
	}
	return nil
}
