{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\RolePermissionModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { roleService, permissionService } from '../../services';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RolePermissionModal = ({\n  role,\n  isOpen,\n  onClose,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  // State management\n  const [allPermissions, setAllPermissions] = useState([]);\n  const [rolePermissions, setRolePermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n\n  // Load data when modal opens\n  useEffect(() => {\n    if (isOpen && role) {\n      loadPermissions();\n      loadRolePermissions();\n    }\n  }, [isOpen, role === null || role === void 0 ? void 0 : role.id]);\n\n  // Early return if role is null\n  if (!role) {\n    return null;\n  }\n\n  // Load all permissions\n  const loadPermissions = async () => {\n    try {\n      const response = await permissionService.list({\n        page: 1,\n        page_size: 100\n      });\n      setAllPermissions(response.data.data);\n    } catch (err) {\n      onError('Failed to load permissions');\n      console.error('Error loading permissions:', err);\n    }\n  };\n\n  // Load role's current permissions\n  const loadRolePermissions = async () => {\n    try {\n      setLoading(true);\n      const response = await roleService.getRoleWithPermissions(role.id);\n      if (response.data.data && response.data.data.permissions) {\n        setRolePermissions(response.data.data.permissions);\n      } else {\n        setRolePermissions([]);\n      }\n    } catch (err) {\n      console.error('Error loading role permissions:', err);\n      setRolePermissions([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Check if permission is assigned to role\n  const isPermissionAssigned = permission => {\n    return rolePermissions.some(rp => rp.id === permission.id);\n  };\n\n  // Handle permission assignment\n  const handleAssignPermission = async permission => {\n    try {\n      setLoading(true);\n      await roleService.assignPermissionById(role.id, permission.id);\n      setRolePermissions(prev => [...prev, permission]);\n      onSuccess(`Permission \"${permission.name}\" assigned to role \"${role.name}\"`);\n    } catch (err) {\n      onError(err.message || 'Failed to assign permission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle permission removal\n  const handleRemovePermission = async permission => {\n    try {\n      setLoading(true);\n      await roleService.removePermissionById(role.id, permission.id);\n      setRolePermissions(prev => prev.filter(rp => rp.id !== permission.id));\n      onSuccess(`Permission \"${permission.name}\" removed from role \"${role.name}\"`);\n    } catch (err) {\n      onError(err.message || 'Failed to remove permission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filter permissions based on search and category\n  const filteredPermissions = allPermissions.filter(permission => {\n    const matchesSearch = searchTerm === '' || permission.name.toLowerCase().includes(searchTerm.toLowerCase()) || permission.description && permission.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || permission.name.toLowerCase().startsWith(categoryFilter.toLowerCase());\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get unique categories from permissions\n  const categories = Array.from(new Set(allPermissions.map(p => p.name.split('_')[0]).filter(Boolean))).sort();\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal fade show\",\n    style: {\n      display: 'block'\n    },\n    tabIndex: -1,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-dialog modal-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"modal-title\",\n            children: [\"Manage Permissions for Role: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: role.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 44\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn-close\",\n            onClick: onClose\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-8\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control\",\n                placeholder: \"Search permissions...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-4\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: categoryFilter,\n                onChange: e => setCategoryFilter(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category.charAt(0).toUpperCase() + category.slice(1)\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row mb-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert alert-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Role Statistics:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), \" \", rolePermissions.length, \" of \", allPermissions.length, \" permissions assigned\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"spinner-border\",\n                  role: \"status\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"visually-hidden\",\n                    children: \"Loading...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-responsive\",\n                style: {\n                  maxHeight: '400px',\n                  overflowY: 'auto'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-sm table-hover\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    className: \"table-light sticky-top\",\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Permission\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Description\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 185,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 186,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: filteredPermissions.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: /*#__PURE__*/_jsxDEV(\"td\", {\n                        colSpan: 4,\n                        className: \"text-center py-3\",\n                        children: \"No permissions found\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 192,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 27\n                    }, this) : filteredPermissions.map(permission => {\n                      const isAssigned = isPermissionAssigned(permission);\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: permission.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 202,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 201,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted\",\n                            children: permission.description || 'No description'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 205,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 204,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `badge ${isAssigned ? 'bg-success' : 'bg-secondary'}`,\n                            children: isAssigned ? 'Assigned' : 'Not Assigned'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 210,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 209,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: isAssigned ? /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"btn btn-sm btn-outline-danger\",\n                            onClick: () => handleRemovePermission(permission),\n                            disabled: loading,\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-minus me-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 221,\n                              columnNumber: 39\n                            }, this), \"Remove\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 216,\n                            columnNumber: 37\n                          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"btn btn-sm btn-outline-success\",\n                            onClick: () => handleAssignPermission(permission),\n                            disabled: loading,\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-plus me-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 230,\n                              columnNumber: 39\n                            }, this), \"Assign\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 225,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 214,\n                          columnNumber: 33\n                        }, this)]\n                      }, permission.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 31\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row mt-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-sm btn-outline-success\",\n                  onClick: async () => {\n                    for (const permission of filteredPermissions) {\n                      if (!isPermissionAssigned(permission)) {\n                        await handleAssignPermission(permission);\n                      }\n                    }\n                  },\n                  disabled: loading,\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-check-double me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), \"Assign All Filtered\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-sm btn-outline-danger\",\n                  onClick: async () => {\n                    for (const permission of filteredPermissions) {\n                      if (isPermissionAssigned(permission)) {\n                        await handleRemovePermission(permission);\n                      }\n                    }\n                  },\n                  disabled: loading,\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-times me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this), \"Remove All Filtered\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: onClose,\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(RolePermissionModal, \"38xsFvDqVERD8l6KKLXx7VX0sqs=\");\n_c = RolePermissionModal;\nexport default RolePermissionModal;\nvar _c;\n$RefreshReg$(_c, \"RolePermissionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "roleService", "permissionService", "jsxDEV", "_jsxDEV", "RolePermissionModal", "role", "isOpen", "onClose", "onSuccess", "onError", "_s", "allPermissions", "setAllPermissions", "rolePermissions", "setRolePermissions", "loading", "setLoading", "searchTerm", "setSearchTerm", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadPermissions", "loadRolePermissions", "id", "response", "list", "page", "page_size", "data", "err", "console", "error", "getRoleWithPermissions", "permissions", "isPermissionAssigned", "permission", "some", "rp", "handleAssignPermission", "assignPermissionById", "prev", "name", "message", "handleRemovePermission", "removePermissionById", "filter", "filteredPermissions", "matchesSearch", "toLowerCase", "includes", "description", "matchesCategory", "startsWith", "categories", "Array", "from", "Set", "map", "p", "split", "Boolean", "sort", "className", "style", "display", "tabIndex", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "placeholder", "value", "onChange", "e", "target", "category", "char<PERSON>t", "toUpperCase", "slice", "length", "maxHeight", "overflowY", "colSpan", "isAssigned", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/RolePermissionModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { roleService, permissionService } from '../../services';\nimport { Role, Permission } from '../../types';\n\ninterface RolePermissionModalProps {\n  role: Role;\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: (message: string) => void;\n  onError: (message: string) => void;\n}\n\nconst RolePermissionModal: React.FC<RolePermissionModalProps> = ({\n  role,\n  isOpen,\n  onClose,\n  onSuccess,\n  onError\n}) => {\n  // State management\n  const [allPermissions, setAllPermissions] = useState<Permission[]>([]);\n  const [rolePermissions, setRolePermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState<string>('all');\n\n  // Load data when modal opens\n  useEffect(() => {\n    if (isOpen && role) {\n      loadPermissions();\n      loadRolePermissions();\n    }\n  }, [isOpen, role?.id]);\n\n  // Early return if role is null\n  if (!role) {\n    return null;\n  }\n\n  // Load all permissions\n  const loadPermissions = async () => {\n    try {\n      const response = await permissionService.list({ page: 1, page_size: 100 });\n      setAllPermissions(response.data.data);\n    } catch (err: any) {\n      onError('Failed to load permissions');\n      console.error('Error loading permissions:', err);\n    }\n  };\n\n  // Load role's current permissions\n  const loadRolePermissions = async () => {\n    try {\n      setLoading(true);\n      const response = await roleService.getRoleWithPermissions(role.id);\n      if (response.data.data && response.data.data.permissions) {\n        setRolePermissions(response.data.data.permissions);\n      } else {\n        setRolePermissions([]);\n      }\n    } catch (err: any) {\n      console.error('Error loading role permissions:', err);\n      setRolePermissions([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Check if permission is assigned to role\n  const isPermissionAssigned = (permission: Permission): boolean => {\n    return rolePermissions.some(rp => rp.id === permission.id);\n  };\n\n  // Handle permission assignment\n  const handleAssignPermission = async (permission: Permission) => {\n    try {\n      setLoading(true);\n      await roleService.assignPermissionById(role.id, permission.id);\n      setRolePermissions(prev => [...prev, permission]);\n      onSuccess(`Permission \"${permission.name}\" assigned to role \"${role.name}\"`);\n    } catch (err: any) {\n      onError(err.message || 'Failed to assign permission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle permission removal\n  const handleRemovePermission = async (permission: Permission) => {\n    try {\n      setLoading(true);\n      await roleService.removePermissionById(role.id, permission.id);\n      setRolePermissions(prev => prev.filter(rp => rp.id !== permission.id));\n      onSuccess(`Permission \"${permission.name}\" removed from role \"${role.name}\"`);\n    } catch (err: any) {\n      onError(err.message || 'Failed to remove permission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filter permissions based on search and category\n  const filteredPermissions = allPermissions.filter(permission => {\n    const matchesSearch = searchTerm === '' ||\n      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (permission.description && permission.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    const matchesCategory = categoryFilter === 'all' ||\n      permission.name.toLowerCase().startsWith(categoryFilter.toLowerCase());\n\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get unique categories from permissions\n  const categories = Array.from(new Set(\n    allPermissions.map(p => p.name.split('_')[0]).filter(Boolean)\n  )).sort();\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal fade show\" style={{ display: 'block' }} tabIndex={-1}>\n      <div className=\"modal-dialog modal-lg\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header\">\n            <h5 className=\"modal-title\">\n              Manage Permissions for Role: <strong>{role.name}</strong>\n            </h5>\n            <button type=\"button\" className=\"btn-close\" onClick={onClose}></button>\n          </div>\n\n          <div className=\"modal-body\">\n            {/* Search and Filter */}\n            <div className=\"row mb-3\">\n              <div className=\"col-md-8\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Search permissions...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n              <div className=\"col-md-4\">\n                <select\n                  className=\"form-select\"\n                  value={categoryFilter}\n                  onChange={(e) => setCategoryFilter(e.target.value)}\n                >\n                  <option value=\"all\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category} value={category}>\n                      {category.charAt(0).toUpperCase() + category.slice(1)}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Permission Statistics */}\n            <div className=\"row mb-3\">\n              <div className=\"col-12\">\n                <div className=\"alert alert-info\">\n                  <strong>Role Statistics:</strong> {rolePermissions.length} of {allPermissions.length} permissions assigned\n                </div>\n              </div>\n            </div>\n\n            {/* Permissions List */}\n            <div className=\"row\">\n              <div className=\"col-12\">\n                {loading ? (\n                  <div className=\"text-center py-4\">\n                    <div className=\"spinner-border\" role=\"status\">\n                      <span className=\"visually-hidden\">Loading...</span>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"table-responsive\" style={{ maxHeight: '400px', overflowY: 'auto' }}>\n                    <table className=\"table table-sm table-hover\">\n                      <thead className=\"table-light sticky-top\">\n                        <tr>\n                          <th>Permission</th>\n                          <th>Description</th>\n                          <th>Status</th>\n                          <th>Action</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {filteredPermissions.length === 0 ? (\n                          <tr>\n                            <td colSpan={4} className=\"text-center py-3\">\n                              No permissions found\n                            </td>\n                          </tr>\n                        ) : (\n                          filteredPermissions.map((permission) => {\n                            const isAssigned = isPermissionAssigned(permission);\n                            return (\n                              <tr key={permission.id}>\n                                <td>\n                                  <strong>{permission.name}</strong>\n                                </td>\n                                <td>\n                                  <small className=\"text-muted\">\n                                    {permission.description || 'No description'}\n                                  </small>\n                                </td>\n                                <td>\n                                  <span className={`badge ${isAssigned ? 'bg-success' : 'bg-secondary'}`}>\n                                    {isAssigned ? 'Assigned' : 'Not Assigned'}\n                                  </span>\n                                </td>\n                                <td>\n                                  {isAssigned ? (\n                                    <button\n                                      className=\"btn btn-sm btn-outline-danger\"\n                                      onClick={() => handleRemovePermission(permission)}\n                                      disabled={loading}\n                                    >\n                                      <i className=\"fas fa-minus me-1\"></i>\n                                      Remove\n                                    </button>\n                                  ) : (\n                                    <button\n                                      className=\"btn btn-sm btn-outline-success\"\n                                      onClick={() => handleAssignPermission(permission)}\n                                      disabled={loading}\n                                    >\n                                      <i className=\"fas fa-plus me-1\"></i>\n                                      Assign\n                                    </button>\n                                  )}\n                                </td>\n                              </tr>\n                            );\n                          })\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Bulk Actions */}\n            <div className=\"row mt-3\">\n              <div className=\"col-12\">\n                <div className=\"d-flex gap-2\">\n                  <button\n                    className=\"btn btn-sm btn-outline-success\"\n                    onClick={async () => {\n                      for (const permission of filteredPermissions) {\n                        if (!isPermissionAssigned(permission)) {\n                          await handleAssignPermission(permission);\n                        }\n                      }\n                    }}\n                    disabled={loading}\n                  >\n                    <i className=\"fas fa-check-double me-1\"></i>\n                    Assign All Filtered\n                  </button>\n                  <button\n                    className=\"btn btn-sm btn-outline-danger\"\n                    onClick={async () => {\n                      for (const permission of filteredPermissions) {\n                        if (isPermissionAssigned(permission)) {\n                          await handleRemovePermission(permission);\n                        }\n                      }\n                    }}\n                    disabled={loading}\n                  >\n                    <i className=\"fas fa-times me-1\"></i>\n                    Remove All Filtered\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"modal-footer\">\n            <button type=\"button\" className=\"btn btn-secondary\" onClick={onClose}>\n              Close\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RolePermissionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWhE,MAAMC,mBAAuD,GAAGA,CAAC;EAC/DC,IAAI;EACJC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAe,EAAE,CAAC;EACtE,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAe,EAAE,CAAC;EACxE,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAS,KAAK,CAAC;;EAEnE;EACAC,SAAS,CAAC,MAAM;IACd,IAAIO,MAAM,IAAID,IAAI,EAAE;MAClBgB,eAAe,CAAC,CAAC;MACjBC,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAChB,MAAM,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,EAAE,CAAC,CAAC;;EAEtB;EACA,IAAI,CAAClB,IAAI,EAAE;IACT,OAAO,IAAI;EACb;;EAEA;EACA,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMvB,iBAAiB,CAACwB,IAAI,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAI,CAAC,CAAC;MAC1Ef,iBAAiB,CAACY,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;IACvC,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBpB,OAAO,CAAC,4BAA4B,CAAC;MACrCqB,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMP,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMxB,WAAW,CAACgC,sBAAsB,CAAC3B,IAAI,CAACkB,EAAE,CAAC;MAClE,IAAIC,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACK,WAAW,EAAE;QACxDnB,kBAAkB,CAACU,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACK,WAAW,CAAC;MACpD,CAAC,MAAM;QACLnB,kBAAkB,CAAC,EAAE,CAAC;MACxB;IACF,CAAC,CAAC,OAAOe,GAAQ,EAAE;MACjBC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEF,GAAG,CAAC;MACrDf,kBAAkB,CAAC,EAAE,CAAC;IACxB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkB,oBAAoB,GAAIC,UAAsB,IAAc;IAChE,OAAOtB,eAAe,CAACuB,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACd,EAAE,KAAKY,UAAU,CAACZ,EAAE,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMe,sBAAsB,GAAG,MAAOH,UAAsB,IAAK;IAC/D,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMhB,WAAW,CAACuC,oBAAoB,CAAClC,IAAI,CAACkB,EAAE,EAAEY,UAAU,CAACZ,EAAE,CAAC;MAC9DT,kBAAkB,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEL,UAAU,CAAC,CAAC;MACjD3B,SAAS,CAAC,eAAe2B,UAAU,CAACM,IAAI,uBAAuBpC,IAAI,CAACoC,IAAI,GAAG,CAAC;IAC9E,CAAC,CAAC,OAAOZ,GAAQ,EAAE;MACjBpB,OAAO,CAACoB,GAAG,CAACa,OAAO,IAAI,6BAA6B,CAAC;IACvD,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2B,sBAAsB,GAAG,MAAOR,UAAsB,IAAK;IAC/D,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMhB,WAAW,CAAC4C,oBAAoB,CAACvC,IAAI,CAACkB,EAAE,EAAEY,UAAU,CAACZ,EAAE,CAAC;MAC9DT,kBAAkB,CAAC0B,IAAI,IAAIA,IAAI,CAACK,MAAM,CAACR,EAAE,IAAIA,EAAE,CAACd,EAAE,KAAKY,UAAU,CAACZ,EAAE,CAAC,CAAC;MACtEf,SAAS,CAAC,eAAe2B,UAAU,CAACM,IAAI,wBAAwBpC,IAAI,CAACoC,IAAI,GAAG,CAAC;IAC/E,CAAC,CAAC,OAAOZ,GAAQ,EAAE;MACjBpB,OAAO,CAACoB,GAAG,CAACa,OAAO,IAAI,6BAA6B,CAAC;IACvD,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8B,mBAAmB,GAAGnC,cAAc,CAACkC,MAAM,CAACV,UAAU,IAAI;IAC9D,MAAMY,aAAa,GAAG9B,UAAU,KAAK,EAAE,IACrCkB,UAAU,CAACM,IAAI,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,IAC/Db,UAAU,CAACe,WAAW,IAAIf,UAAU,CAACe,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAE;IAErG,MAAMG,eAAe,GAAGhC,cAAc,KAAK,KAAK,IAC9CgB,UAAU,CAACM,IAAI,CAACO,WAAW,CAAC,CAAC,CAACI,UAAU,CAACjC,cAAc,CAAC6B,WAAW,CAAC,CAAC,CAAC;IAExE,OAAOD,aAAa,IAAII,eAAe;EACzC,CAAC,CAAC;;EAEF;EACA,MAAME,UAAU,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACnC7C,cAAc,CAAC8C,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,IAAI,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACd,MAAM,CAACe,OAAO,CAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;EAET,IAAI,CAACvD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEH,OAAA;IAAK2D,SAAS,EAAC,iBAAiB;IAACC,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAQ,CAAE;IAACC,QAAQ,EAAE,CAAC,CAAE;IAAAC,QAAA,eACzE/D,OAAA;MAAK2D,SAAS,EAAC,uBAAuB;MAAAI,QAAA,eACpC/D,OAAA;QAAK2D,SAAS,EAAC,eAAe;QAAAI,QAAA,gBAC5B/D,OAAA;UAAK2D,SAAS,EAAC,cAAc;UAAAI,QAAA,gBAC3B/D,OAAA;YAAI2D,SAAS,EAAC,aAAa;YAAAI,QAAA,GAAC,+BACG,eAAA/D,OAAA;cAAA+D,QAAA,EAAS7D,IAAI,CAACoC;YAAI;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACLnE,OAAA;YAAQoE,IAAI,EAAC,QAAQ;YAACT,SAAS,EAAC,WAAW;YAACU,OAAO,EAAEjE;UAAQ;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENnE,OAAA;UAAK2D,SAAS,EAAC,YAAY;UAAAI,QAAA,gBAEzB/D,OAAA;YAAK2D,SAAS,EAAC,UAAU;YAAAI,QAAA,gBACvB/D,OAAA;cAAK2D,SAAS,EAAC,UAAU;cAAAI,QAAA,eACvB/D,OAAA;gBACEoE,IAAI,EAAC,MAAM;gBACXT,SAAS,EAAC,cAAc;gBACxBW,WAAW,EAAC,uBAAuB;gBACnCC,KAAK,EAAEzD,UAAW;gBAClB0D,QAAQ,EAAGC,CAAC,IAAK1D,aAAa,CAAC0D,CAAC,CAACC,MAAM,CAACH,KAAK;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnE,OAAA;cAAK2D,SAAS,EAAC,UAAU;cAAAI,QAAA,eACvB/D,OAAA;gBACE2D,SAAS,EAAC,aAAa;gBACvBY,KAAK,EAAEvD,cAAe;gBACtBwD,QAAQ,EAAGC,CAAC,IAAKxD,iBAAiB,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAAAR,QAAA,gBAEnD/D,OAAA;kBAAQuE,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1CjB,UAAU,CAACI,GAAG,CAACqB,QAAQ,iBACtB3E,OAAA;kBAAuBuE,KAAK,EAAEI,QAAS;kBAAAZ,QAAA,EACpCY,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAAC,CAAC;gBAAC,GAD1CH,QAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnE,OAAA;YAAK2D,SAAS,EAAC,UAAU;YAAAI,QAAA,eACvB/D,OAAA;cAAK2D,SAAS,EAAC,QAAQ;cAAAI,QAAA,eACrB/D,OAAA;gBAAK2D,SAAS,EAAC,kBAAkB;gBAAAI,QAAA,gBAC/B/D,OAAA;kBAAA+D,QAAA,EAAQ;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzD,eAAe,CAACqE,MAAM,EAAC,MAAI,EAACvE,cAAc,CAACuE,MAAM,EAAC,uBACvF;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnE,OAAA;YAAK2D,SAAS,EAAC,KAAK;YAAAI,QAAA,eAClB/D,OAAA;cAAK2D,SAAS,EAAC,QAAQ;cAAAI,QAAA,EACpBnD,OAAO,gBACNZ,OAAA;gBAAK2D,SAAS,EAAC,kBAAkB;gBAAAI,QAAA,eAC/B/D,OAAA;kBAAK2D,SAAS,EAAC,gBAAgB;kBAACzD,IAAI,EAAC,QAAQ;kBAAA6D,QAAA,eAC3C/D,OAAA;oBAAM2D,SAAS,EAAC,iBAAiB;oBAAAI,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENnE,OAAA;gBAAK2D,SAAS,EAAC,kBAAkB;gBAACC,KAAK,EAAE;kBAAEoB,SAAS,EAAE,OAAO;kBAAEC,SAAS,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,eACjF/D,OAAA;kBAAO2D,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,gBAC3C/D,OAAA;oBAAO2D,SAAS,EAAC,wBAAwB;oBAAAI,QAAA,eACvC/D,OAAA;sBAAA+D,QAAA,gBACE/D,OAAA;wBAAA+D,QAAA,EAAI;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnBnE,OAAA;wBAAA+D,QAAA,EAAI;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpBnE,OAAA;wBAAA+D,QAAA,EAAI;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACfnE,OAAA;wBAAA+D,QAAA,EAAI;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRnE,OAAA;oBAAA+D,QAAA,EACGpB,mBAAmB,CAACoC,MAAM,KAAK,CAAC,gBAC/B/E,OAAA;sBAAA+D,QAAA,eACE/D,OAAA;wBAAIkF,OAAO,EAAE,CAAE;wBAACvB,SAAS,EAAC,kBAAkB;wBAAAI,QAAA,EAAC;sBAE7C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GAELxB,mBAAmB,CAACW,GAAG,CAAEtB,UAAU,IAAK;sBACtC,MAAMmD,UAAU,GAAGpD,oBAAoB,CAACC,UAAU,CAAC;sBACnD,oBACEhC,OAAA;wBAAA+D,QAAA,gBACE/D,OAAA;0BAAA+D,QAAA,eACE/D,OAAA;4BAAA+D,QAAA,EAAS/B,UAAU,CAACM;0BAAI;4BAAA0B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC,eACLnE,OAAA;0BAAA+D,QAAA,eACE/D,OAAA;4BAAO2D,SAAS,EAAC,YAAY;4BAAAI,QAAA,EAC1B/B,UAAU,CAACe,WAAW,IAAI;0BAAgB;4BAAAiB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC,eACLnE,OAAA;0BAAA+D,QAAA,eACE/D,OAAA;4BAAM2D,SAAS,EAAE,SAASwB,UAAU,GAAG,YAAY,GAAG,cAAc,EAAG;4BAAApB,QAAA,EACpEoB,UAAU,GAAG,UAAU,GAAG;0BAAc;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACLnE,OAAA;0BAAA+D,QAAA,EACGoB,UAAU,gBACTnF,OAAA;4BACE2D,SAAS,EAAC,+BAA+B;4BACzCU,OAAO,EAAEA,CAAA,KAAM7B,sBAAsB,CAACR,UAAU,CAAE;4BAClDoD,QAAQ,EAAExE,OAAQ;4BAAAmD,QAAA,gBAElB/D,OAAA;8BAAG2D,SAAS,EAAC;4BAAmB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEvC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,gBAETnE,OAAA;4BACE2D,SAAS,EAAC,gCAAgC;4BAC1CU,OAAO,EAAEA,CAAA,KAAMlC,sBAAsB,CAACH,UAAU,CAAE;4BAClDoD,QAAQ,EAAExE,OAAQ;4BAAAmD,QAAA,gBAElB/D,OAAA;8BAAG2D,SAAS,EAAC;4BAAkB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEtC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBACT;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA,GAlCEnC,UAAU,CAACZ,EAAE;wBAAA4C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAmClB,CAAC;oBAET,CAAC;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnE,OAAA;YAAK2D,SAAS,EAAC,UAAU;YAAAI,QAAA,eACvB/D,OAAA;cAAK2D,SAAS,EAAC,QAAQ;cAAAI,QAAA,eACrB/D,OAAA;gBAAK2D,SAAS,EAAC,cAAc;gBAAAI,QAAA,gBAC3B/D,OAAA;kBACE2D,SAAS,EAAC,gCAAgC;kBAC1CU,OAAO,EAAE,MAAAA,CAAA,KAAY;oBACnB,KAAK,MAAMrC,UAAU,IAAIW,mBAAmB,EAAE;sBAC5C,IAAI,CAACZ,oBAAoB,CAACC,UAAU,CAAC,EAAE;wBACrC,MAAMG,sBAAsB,CAACH,UAAU,CAAC;sBAC1C;oBACF;kBACF,CAAE;kBACFoD,QAAQ,EAAExE,OAAQ;kBAAAmD,QAAA,gBAElB/D,OAAA;oBAAG2D,SAAS,EAAC;kBAA0B;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,uBAE9C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnE,OAAA;kBACE2D,SAAS,EAAC,+BAA+B;kBACzCU,OAAO,EAAE,MAAAA,CAAA,KAAY;oBACnB,KAAK,MAAMrC,UAAU,IAAIW,mBAAmB,EAAE;sBAC5C,IAAIZ,oBAAoB,CAACC,UAAU,CAAC,EAAE;wBACpC,MAAMQ,sBAAsB,CAACR,UAAU,CAAC;sBAC1C;oBACF;kBACF,CAAE;kBACFoD,QAAQ,EAAExE,OAAQ;kBAAAmD,QAAA,gBAElB/D,OAAA;oBAAG2D,SAAS,EAAC;kBAAmB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,uBAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnE,OAAA;UAAK2D,SAAS,EAAC,cAAc;UAAAI,QAAA,eAC3B/D,OAAA;YAAQoE,IAAI,EAAC,QAAQ;YAACT,SAAS,EAAC,mBAAmB;YAACU,OAAO,EAAEjE,OAAQ;YAAA2D,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAvRIN,mBAAuD;AAAAoF,EAAA,GAAvDpF,mBAAuD;AAyR7D,eAAeA,mBAAmB;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}