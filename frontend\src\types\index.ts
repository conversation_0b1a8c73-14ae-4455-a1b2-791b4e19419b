export interface User {
  id: number;
  username: string;
  email: string;
  roles?: string[];
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface Role {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  permissions?: Permission[];
}

export interface Permission {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface AuthResponse {
  token: string;
  refresh_token: string;
  user: User;
  roles: string[];
}

export interface PaginationInfo {
  page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next?: boolean;
  has_prev?: boolean;
}