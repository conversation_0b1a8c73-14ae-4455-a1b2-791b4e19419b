export interface User {
  id: number;
  username: string;
  email: string;
  roles?: string[];
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface Role {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Permission {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface AuthResponse {
  token: string;
  refresh_token: string;
  user: User;
  roles: string[];
} 