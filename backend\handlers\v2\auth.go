package v2

import (
	"github.com/gin-gonic/gin"
)

// @Summary Đăng ký tài <PERSON> (v2)
// @Description Đăng ký user mới cho API v2
// @Tags Auth v2
// @Accept json
// @Produce json
// @Param data body object true "Thông tin đăng ký"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v2/auth/register [post]
func Register(c *gin.Context) {
	c.JSON(201, gin.H{"message": "Đăng ký user v2 thành công (demo)", "version": "v2"})
}

// @Summary Đăng nhập (v2)
// @Description Đăng nhập và nhận JWT cho API v2
// @Tags Auth v2
// @Accept json
// @Produce json
// @Param data body object true "Thông tin đăng nhập"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v2/auth/login [post]
func Login(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Đăng nhập v2 thành công (demo)", "version": "v2"})
}

// @Summary Đăng xuất (v2)
// @Description Đăng xuất user cho API v2
// @Tags Auth v2
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v2/auth/logout [post]
func Logout(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Đăng xuất v2 thành công (demo)", "version": "v2"})
} 