{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\UserPermissionManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport AssignmentsTab from './AssignmentsTab';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserPermissionManagement = () => {\n  _s();\n  // State for active tab\n  const [activeTab, setActiveTab] = useState('users');\n\n  // Users state\n  const [users, setUsers] = useState([]);\n  const [usersPagination, setUsersPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState([]);\n  const [rolesPagination, setRolesPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState([]);\n  const [permissionsPagination, setPermissionsPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [editingRole, setEditingRole] = useState(null);\n  const [editingPermission, setEditingPermission] = useState(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          filters: [{\n            field: 'username',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }, {\n            field: 'email',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'username',\n          order_dir: 'asc'\n        };\n        response = await userManagementAPI.search(searchRequest);\n      } else {\n        response = await userManagementAPI.list({\n          page,\n          page_size: usersPagination.page_size,\n          order_by: 'username',\n          order_dir: 'asc'\n        });\n      }\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(`Failed to load users: ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadUsers(1, usersSearchTerm);\n  };\n  const handleUsersPageChange = newPage => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: rolesPagination.page_size,\n          filters: [{\n            field: 'name',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await roleManagementAPI.search(searchRequest);\n      } else {\n        response = await roleManagementAPI.list({\n          page,\n          page_size: rolesPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(`Failed to load roles: ${((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadRoles(1, rolesSearchTerm);\n  };\n  const handleRolesPageChange = newPage => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: permissionsPagination.page_size,\n          filters: [{\n            field: 'name',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await permissionManagementAPI.search(searchRequest);\n      } else {\n        response = await permissionManagementAPI.list({\n          page,\n          page_size: permissionsPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(`Failed to load permissions: ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n  const handlePermissionsPageChange = newPage => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async id => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n    try {\n      await userManagementAPI.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      setError(`Failed to delete user: ${((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n    }\n  };\n  const handleDeleteRole = async id => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n    try {\n      await roleManagementAPI.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      setError(`Failed to delete role: ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || err.message}`);\n    }\n  };\n  const handleDeletePermission = async id => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n    try {\n      await permissionManagementAPI.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      setError(`Failed to delete permission: ${((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error) || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination, onPageChange) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(i),\n        className: `px-3 py-1 mx-1 rounded ${i === pagination.page ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n        children: i\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600\",\n        children: [\"Showing \", (pagination.page - 1) * pagination.page_size + 1, \" to\", ' ', Math.min(pagination.page * pagination.page_size, pagination.total_items), \" of\", ' ', pagination.total_items, \" entries\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page - 1),\n          disabled: pagination.page <= 1,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), pages, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page + 1),\n          disabled: pagination.page >= pagination.total_pages,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-8\",\n      children: \"User Permission Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b border-gray-200 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"-mb-px flex space-x-8\",\n        children: [{\n          key: 'users',\n          label: 'Users'\n        }, {\n          key: 'roles',\n          label: 'Roles'\n        }, {\n          key: 'permissions',\n          label: 'Permissions'\n        }, {\n          key: 'assignments',\n          label: 'Assignments'\n        }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n          children: tab.label\n        }, tab.key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingUser(null);\n            setShowUserModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search users by username or email...\",\n          value: usersSearchTerm,\n          onChange: e => setUsersSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleUsersSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleUsersSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setUsersSearchTerm('');\n            loadUsers(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), usersLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading users...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: user.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingUser(user);\n                      setShowUserModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteUser(user.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 25\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 15\n        }, this), renderPagination(usersPagination, handleUsersPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 9\n    }, this), activeTab === 'roles' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Role Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingRole(null);\n            setShowRoleModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Role\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search roles by name...\",\n          value: rolesSearchTerm,\n          onChange: e => setRolesSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleRolesSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRolesSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setRolesSearchTerm('');\n            loadRoles(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 11\n      }, this), rolesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading roles...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: roles.map(role => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: role.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: role.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: role.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: role.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRole(role);\n                      setShowRoleModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteRole(role.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 25\n                }, this)]\n              }, role.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 15\n        }, this), renderPagination(rolesPagination, handleRolesPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 9\n    }, this), activeTab === 'permissions' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Permission Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingPermission(null);\n            setShowPermissionModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Permission\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search permissions by name...\",\n          value: permissionsSearchTerm,\n          onChange: e => setPermissionsSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handlePermissionsSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePermissionsSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setPermissionsSearchTerm('');\n            loadPermissions(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 11\n      }, this), permissionsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading permissions...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: permissions.map(permission => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: permission.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: permission.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: permission.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: permission.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingPermission(permission);\n                      setShowPermissionModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeletePermission(permission.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 25\n                }, this)]\n              }, permission.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 15\n        }, this), renderPagination(permissionsPagination, handlePermissionsPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 9\n    }, this), activeTab === 'assignments' && /*#__PURE__*/_jsxDEV(AssignmentsTab, {\n      users: users,\n      roles: roles,\n      onMessage: setMessage,\n      onError: setError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 740,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(UserModal, {\n      isOpen: showUserModal,\n      onClose: () => setShowUserModal(false),\n      user: editingUser,\n      onSuccess: () => {\n        setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n        loadUsers(usersPagination.page, usersSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RoleModal, {\n      isOpen: showRoleModal,\n      onClose: () => setShowRoleModal(false),\n      role: editingRole,\n      onSuccess: () => {\n        setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n        loadRoles(rolesPagination.page, rolesSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PermissionModal, {\n      isOpen: showPermissionModal,\n      onClose: () => setShowPermissionModal(false),\n      permission: editingPermission,\n      onSuccess: () => {\n        setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n        loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 769,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 322,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPermissionManagement, \"EXXLj32ClIuSM8b9dg4KjmQqFqU=\");\n_c = UserPermissionManagement;\nexport default UserPermissionManagement;\nvar _c;\n$RefreshReg$(_c, \"UserPermissionManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "UserModal", "RoleModal", "PermissionModal", "AssignmentsTab", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserPermissionManagement", "_s", "activeTab", "setActiveTab", "users", "setUsers", "usersPagination", "setUsersPagination", "page", "page_size", "total_items", "total_pages", "usersLoading", "setUsersLoading", "usersSearchTerm", "setUsersSearchTerm", "roles", "setRoles", "rolesPagination", "setRolesPagination", "rolesLoading", "setRolesLoading", "rolesSearchTerm", "setRolesSearchTerm", "permissions", "setPermissions", "permissionsPagination", "setPermissionsPagination", "permissionsLoading", "setPermissionsLoading", "permissionsSearchTerm", "setPermissionsSearchTerm", "message", "setMessage", "error", "setError", "showUserModal", "setShowUserModal", "showRoleModal", "setShowRoleModal", "showPermissionModal", "setShowPermissionModal", "editingUser", "setEditingUser", "editingRole", "setEditingRole", "editingPermission", "setEditingPermission", "loadUsers", "loadRoles", "loadPermissions", "searchTerm", "response", "trim", "searchRequest", "filters", "field", "operator", "value", "order_by", "order_dir", "userManagementAPI", "search", "list", "data", "pagination", "err", "_err$response", "_err$response$data", "handleUsersSearch", "prev", "handleUsersPageChange", "newPage", "roleManagementAPI", "_err$response2", "_err$response2$data", "handleRolesSearch", "handleRolesPageChange", "permissionManagementAPI", "_err$response3", "_err$response3$data", "handlePermissionsSearch", "handlePermissionsPageChange", "handleDeleteUser", "id", "window", "confirm", "delete", "_err$response4", "_err$response4$data", "handleDeleteRole", "_err$response5", "_err$response5$data", "handleDeletePermission", "_err$response6", "_err$response6$data", "renderPagination", "onPageChange", "pages", "i", "push", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Math", "min", "disabled", "key", "label", "map", "tab", "type", "placeholder", "onChange", "e", "target", "onKeyPress", "user", "username", "email", "is_active", "created_at", "Date", "toLocaleDateString", "role", "name", "description", "permission", "onMessage", "onError", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/UserPermissionManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport {\n  userService,\n  roleService,\n  permissionService,\n  adminService,\n  PaginationInfo,\n  SearchRequest\n} from '../../services';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport AssignmentsTab from './AssignmentsTab';\n\ninterface UserPermissionManagementProps { }\n\nconst UserPermissionManagement: React.FC<UserPermissionManagementProps> = () => {\n  // State for active tab\n  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'permissions' | 'assignments'>('users');\n\n  // Users state\n  const [users, setUsers] = useState<User[]>([]);\n  const [usersPagination, setUsersPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [rolesPagination, setRolesPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [permissionsPagination, setPermissionsPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          filters: [\n            {\n              field: 'username',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            },\n            {\n              field: 'email',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'username',\n          order_dir: 'asc'\n        };\n        response = await userManagementAPI.search(searchRequest);\n      } else {\n        response = await userManagementAPI.list({\n          page,\n          page_size: usersPagination.page_size,\n          order_by: 'username',\n          order_dir: 'asc'\n        });\n      }\n\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load users: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({ ...prev, page: 1 }));\n    loadUsers(1, usersSearchTerm);\n  };\n\n  const handleUsersPageChange = (newPage: number) => {\n    setUsersPagination(prev => ({ ...prev, page: newPage }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: rolesPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await roleManagementAPI.search(searchRequest);\n      } else {\n        response = await roleManagementAPI.list({\n          page,\n          page_size: rolesPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load roles: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({ ...prev, page: 1 }));\n    loadRoles(1, rolesSearchTerm);\n  };\n\n  const handleRolesPageChange = (newPage: number) => {\n    setRolesPagination(prev => ({ ...prev, page: newPage }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: permissionsPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await permissionManagementAPI.search(searchRequest);\n      } else {\n        response = await permissionManagementAPI.list({\n          page,\n          page_size: permissionsPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load permissions: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({ ...prev, page: 1 }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n\n  const handlePermissionsPageChange = (newPage: number) => {\n    setPermissionsPagination(prev => ({ ...prev, page: newPage }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n\n    try {\n      await userManagementAPI.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete user: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeleteRole = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n\n    try {\n      await roleManagementAPI.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeletePermission = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n\n    try {\n      await permissionManagementAPI.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination: PaginationInfo, onPageChange: (page: number) => void) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(\n        <button\n          key={i}\n          onClick={() => onPageChange(i)}\n          className={`px-3 py-1 mx-1 rounded ${i === pagination.page\n            ? 'bg-blue-500 text-white'\n            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n        >\n          {i}\n        </button>\n      );\n    }\n\n    return (\n      <div className=\"flex items-center justify-between mt-4\">\n        <div className=\"text-sm text-gray-600\">\n          Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}\n          {Math.min(pagination.page * pagination.page_size, pagination.total_items)} of{' '}\n          {pagination.total_items} entries\n        </div>\n        <div className=\"flex items-center\">\n          <button\n            onClick={() => onPageChange(pagination.page - 1)}\n            disabled={pagination.page <= 1}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Previous\n          </button>\n          {pages}\n          <button\n            onClick={() => onPageChange(pagination.page + 1)}\n            disabled={pagination.page >= pagination.total_pages}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Next\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">User Permission Management</h1>\n\n      {/* Messages */}\n      {message && (\n        <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n          {message}\n        </div>\n      )}\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {error}\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {[\n            { key: 'users', label: 'Users' },\n            { key: 'roles', label: 'Roles' },\n            { key: 'permissions', label: 'Permissions' },\n            { key: 'assignments', label: 'Assignments' }\n          ].map((tab) => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'users' && (\n        <div>\n          {/* Users Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">User Management</h2>\n            <button\n              onClick={() => {\n                setEditingUser(null);\n                setShowUserModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add User\n            </button>\n          </div>\n\n          {/* Users Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search users by username or email...\"\n              value={usersSearchTerm}\n              onChange={(e) => setUsersSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleUsersSearch()}\n            />\n            <button\n              onClick={handleUsersSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setUsersSearchTerm('');\n                loadUsers(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Users Table */}\n          {usersLoading ? (\n            <div className=\"text-center py-8\">Loading users...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Username\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Email\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <tr key={user.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {user.username}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.email}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingUser(user);\n                              setShowUserModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteUser(user.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Users Pagination */}\n              {renderPagination(usersPagination, handleUsersPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Roles Tab */}\n      {activeTab === 'roles' && (\n        <div>\n          {/* Roles Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Role Management</h2>\n            <button\n              onClick={() => {\n                setEditingRole(null);\n                setShowRoleModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Role\n            </button>\n          </div>\n\n          {/* Roles Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search roles by name...\"\n              value={rolesSearchTerm}\n              onChange={(e) => setRolesSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleRolesSearch()}\n            />\n            <button\n              onClick={handleRolesSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setRolesSearchTerm('');\n                loadRoles(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Roles Table */}\n          {rolesLoading ? (\n            <div className=\"text-center py-8\">Loading roles...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {roles.map((role) => (\n                      <tr key={role.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {role.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {role.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {role.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingRole(role);\n                              setShowRoleModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteRole(role.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Roles Pagination */}\n              {renderPagination(rolesPagination, handleRolesPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Permissions Tab */}\n      {activeTab === 'permissions' && (\n        <div>\n          {/* Permissions Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Permission Management</h2>\n            <button\n              onClick={() => {\n                setEditingPermission(null);\n                setShowPermissionModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Permission\n            </button>\n          </div>\n\n          {/* Permissions Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search permissions by name...\"\n              value={permissionsSearchTerm}\n              onChange={(e) => setPermissionsSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handlePermissionsSearch()}\n            />\n            <button\n              onClick={handlePermissionsSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setPermissionsSearchTerm('');\n                loadPermissions(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Permissions Table */}\n          {permissionsLoading ? (\n            <div className=\"text-center py-8\">Loading permissions...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {permissions.map((permission) => (\n                      <tr key={permission.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {permission.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {permission.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {permission.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingPermission(permission);\n                              setShowPermissionModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeletePermission(permission.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Permissions Pagination */}\n              {renderPagination(permissionsPagination, handlePermissionsPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Assignments Tab */}\n      {activeTab === 'assignments' && (\n        <AssignmentsTab\n          users={users}\n          roles={roles}\n          onMessage={setMessage}\n          onError={setError}\n        />\n      )}\n\n      {/* Modals */}\n      <UserModal\n        isOpen={showUserModal}\n        onClose={() => setShowUserModal(false)}\n        user={editingUser}\n        onSuccess={() => {\n          setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n          loadUsers(usersPagination.page, usersSearchTerm);\n        }}\n      />\n\n      <RoleModal\n        isOpen={showRoleModal}\n        onClose={() => setShowRoleModal(false)}\n        role={editingRole}\n        onSuccess={() => {\n          setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n          loadRoles(rolesPagination.page, rolesSearchTerm);\n        }}\n      />\n\n      <PermissionModal\n        isOpen={showPermissionModal}\n        onClose={() => setShowPermissionModal(false)}\n        permission={editingPermission}\n        onSuccess={() => {\n          setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n          loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n        }}\n      />\n    </div>\n  );\n};\n\nexport default UserPermissionManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAUlD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI9C,MAAMC,wBAAiE,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9E;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAoD,OAAO,CAAC;;EAEtG;EACA,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAiB;IACrEkB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAiB;IACrEkB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACoC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrC,QAAQ,CAAiB;IACjFkB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACwC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAEtE;EACA,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACwD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzD,QAAQ,CAAoB,IAAI,CAAC;;EAEnF;EACAC,SAAS,CAAC,MAAM;IACd,QAAQW,SAAS;MACf,KAAK,OAAO;QACV8C,SAAS,CAAC,CAAC;QACX;MACF,KAAK,OAAO;QACVC,SAAS,CAAC,CAAC;QACX;MACF,KAAK,aAAa;QAChBC,eAAe,CAAC,CAAC;QACjB;MACF,KAAK,aAAa;QAChBF,SAAS,CAAC,CAAC,CAAC,CAAC;QACbC,SAAS,CAAC,CAAC,CAAC,CAAC;QACb;IACJ;EACF,CAAC,EAAE,CAAC/C,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM8C,SAAS,GAAG,MAAAA,CAAOxC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrDtC,eAAe,CAAC,IAAI,CAAC;IACrBsB,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG,SAAS;UACpC8C,OAAO,EAAE,CACP;YACEC,KAAK,EAAE,UAAU;YACjBC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,EACD;YACEK,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,CACF;UACDQ,QAAQ,EAAE,UAAU;UACpBC,SAAS,EAAE;QACb,CAAC;QACDR,QAAQ,GAAG,MAAMS,iBAAiB,CAACC,MAAM,CAACR,aAAa,CAAC;MAC1D,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAMS,iBAAiB,CAACE,IAAI,CAAC;UACtCvD,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG,SAAS;UACpCkD,QAAQ,EAAE,UAAU;UACpBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEAvD,QAAQ,CAAC+C,QAAQ,CAACY,IAAI,CAACA,IAAI,CAAC;MAC5BzD,kBAAkB,CAAC6C,QAAQ,CAACY,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBjC,QAAQ,CAAC,yBAAyB,EAAAgC,aAAA,GAAAD,GAAG,CAACd,QAAQ,cAAAe,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBlC,KAAK,KAAIgC,GAAG,CAAClC,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRnB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMwD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9D,kBAAkB,CAAC+D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDwC,SAAS,CAAC,CAAC,EAAElC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMyD,qBAAqB,GAAIC,OAAe,IAAK;IACjDjE,kBAAkB,CAAC+D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9D,IAAI,EAAEgE;IAAQ,CAAC,CAAC,CAAC;IACxDxB,SAAS,CAACwB,OAAO,EAAE1D,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAMmC,SAAS,GAAG,MAAAA,CAAOzC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrD9B,eAAe,CAAC,IAAI,CAAC;IACrBc,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAES,eAAe,CAACT,SAAS;UACpC8C,OAAO,EAAE,CACP;YACEC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,CACF;UACDQ,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC;QACDR,QAAQ,GAAG,MAAMqB,iBAAiB,CAACX,MAAM,CAACR,aAAa,CAAC;MAC1D,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAMqB,iBAAiB,CAACV,IAAI,CAAC;UACtCvD,IAAI;UACJC,SAAS,EAAES,eAAe,CAACT,SAAS;UACpCkD,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEA3C,QAAQ,CAACmC,QAAQ,CAACY,IAAI,CAACA,IAAI,CAAC;MAC5B7C,kBAAkB,CAACiC,QAAQ,CAACY,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAQ,cAAA,EAAAC,mBAAA;MACjBxC,QAAQ,CAAC,yBAAyB,EAAAuC,cAAA,GAAAR,GAAG,CAACd,QAAQ,cAAAsB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcV,IAAI,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBzC,KAAK,KAAIgC,GAAG,CAAClC,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRX,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMuD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,kBAAkB,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDyC,SAAS,CAAC,CAAC,EAAE3B,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMuD,qBAAqB,GAAIL,OAAe,IAAK;IACjDrD,kBAAkB,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9D,IAAI,EAAEgE;IAAQ,CAAC,CAAC,CAAC;IACxDvB,SAAS,CAACuB,OAAO,EAAElD,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAG,MAAAA,CAAO1C,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IAC3DtB,qBAAqB,CAAC,IAAI,CAAC;IAC3BM,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAEiB,qBAAqB,CAACjB,SAAS;UAC1C8C,OAAO,EAAE,CACP;YACEC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,CACF;UACDQ,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC;QACDR,QAAQ,GAAG,MAAM0B,uBAAuB,CAAChB,MAAM,CAACR,aAAa,CAAC;MAChE,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAM0B,uBAAuB,CAACf,IAAI,CAAC;UAC5CvD,IAAI;UACJC,SAAS,EAAEiB,qBAAqB,CAACjB,SAAS;UAC1CkD,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEAnC,cAAc,CAAC2B,QAAQ,CAACY,IAAI,CAACA,IAAI,CAAC;MAClCrC,wBAAwB,CAACyB,QAAQ,CAACY,IAAI,CAACC,UAAU,CAAC;IACpD,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACjB7C,QAAQ,CAAC,+BAA+B,EAAA4C,cAAA,GAAAb,GAAG,CAACd,QAAQ,cAAA2B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcf,IAAI,cAAAgB,mBAAA,uBAAlBA,mBAAA,CAAoB9C,KAAK,KAAIgC,GAAG,CAAClC,OAAO,EAAE,CAAC;IACrF,CAAC,SAAS;MACRH,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMoD,uBAAuB,GAAGA,CAAA,KAAM;IACpCtD,wBAAwB,CAAC2C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IACxD0C,eAAe,CAAC,CAAC,EAAEpB,qBAAqB,CAAC;EAC3C,CAAC;EAED,MAAMoD,2BAA2B,GAAIV,OAAe,IAAK;IACvD7C,wBAAwB,CAAC2C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9D,IAAI,EAAEgE;IAAQ,CAAC,CAAC,CAAC;IAC9DtB,eAAe,CAACsB,OAAO,EAAE1C,qBAAqB,CAAC;EACjD,CAAC;;EAED;EACA,MAAMqD,gBAAgB,GAAG,MAAOC,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAMzB,iBAAiB,CAAC0B,MAAM,CAACH,EAAE,CAAC;MAClCnD,UAAU,CAAC,2BAA2B,CAAC;MACvCe,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;IAClD,CAAC,CAAC,OAAOoD,GAAQ,EAAE;MAAA,IAAAsB,cAAA,EAAAC,mBAAA;MACjBtD,QAAQ,CAAC,0BAA0B,EAAAqD,cAAA,GAAAtB,GAAG,CAACd,QAAQ,cAAAoC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxB,IAAI,cAAAyB,mBAAA,uBAAlBA,mBAAA,CAAoBvD,KAAK,KAAIgC,GAAG,CAAClC,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAM0D,gBAAgB,GAAG,MAAON,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAMb,iBAAiB,CAACc,MAAM,CAACH,EAAE,CAAC;MAClCnD,UAAU,CAAC,2BAA2B,CAAC;MACvCgB,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;IAClD,CAAC,CAAC,OAAO4C,GAAQ,EAAE;MAAA,IAAAyB,cAAA,EAAAC,mBAAA;MACjBzD,QAAQ,CAAC,0BAA0B,EAAAwD,cAAA,GAAAzB,GAAG,CAACd,QAAQ,cAAAuC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc3B,IAAI,cAAA4B,mBAAA,uBAAlBA,mBAAA,CAAoB1D,KAAK,KAAIgC,GAAG,CAAClC,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAM6D,sBAAsB,GAAG,MAAOT,EAAU,IAAK;IACnD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;IAEzE,IAAI;MACF,MAAMR,uBAAuB,CAACS,MAAM,CAACH,EAAE,CAAC;MACxCnD,UAAU,CAAC,iCAAiC,CAAC;MAC7CiB,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;IACpE,CAAC,CAAC,OAAOoC,GAAQ,EAAE;MAAA,IAAA4B,cAAA,EAAAC,mBAAA;MACjB5D,QAAQ,CAAC,gCAAgC,EAAA2D,cAAA,GAAA5B,GAAG,CAACd,QAAQ,cAAA0C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc9B,IAAI,cAAA+B,mBAAA,uBAAlBA,mBAAA,CAAoB7D,KAAK,KAAIgC,GAAG,CAAClC,OAAO,EAAE,CAAC;IACtF;EACF,CAAC;;EAED;EACA,MAAMgE,gBAAgB,GAAGA,CAAC/B,UAA0B,EAAEgC,YAAoC,KAAK;IAC7F,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIlC,UAAU,CAACtD,WAAW,EAAEwF,CAAC,EAAE,EAAE;MAChDD,KAAK,CAACE,IAAI,cACRvG,OAAA;QAEEwG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACE,CAAC,CAAE;QAC/BG,SAAS,EAAE,0BAA0BH,CAAC,KAAKlC,UAAU,CAACzD,IAAI,GACtD,wBAAwB,GACxB,6CAA6C,EAC5C;QAAA+F,QAAA,EAEJJ;MAAC,GAPGA,CAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQA,CACV,CAAC;IACH;IAEA,oBACE9G,OAAA;MAAKyG,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD1G,OAAA;QAAKyG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC7B,EAAE,CAACtC,UAAU,CAACzD,IAAI,GAAG,CAAC,IAAIyD,UAAU,CAACxD,SAAS,GAAI,CAAC,EAAC,KAAG,EAAC,GAAG,EAClEmG,IAAI,CAACC,GAAG,CAAC5C,UAAU,CAACzD,IAAI,GAAGyD,UAAU,CAACxD,SAAS,EAAEwD,UAAU,CAACvD,WAAW,CAAC,EAAC,KAAG,EAAC,GAAG,EAChFuD,UAAU,CAACvD,WAAW,EAAC,UAC1B;MAAA;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN9G,OAAA;QAAKyG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1G,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAAChC,UAAU,CAACzD,IAAI,GAAG,CAAC,CAAE;UACjDsG,QAAQ,EAAE7C,UAAU,CAACzD,IAAI,IAAI,CAAE;UAC/B8F,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRT,KAAK,eACNrG,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAAChC,UAAU,CAACzD,IAAI,GAAG,CAAC,CAAE;UACjDsG,QAAQ,EAAE7C,UAAU,CAACzD,IAAI,IAAIyD,UAAU,CAACtD,WAAY;UACpD2F,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACE9G,OAAA;IAAKyG,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1C1G,OAAA;MAAIyG,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAGtE3E,OAAO,iBACNnC,OAAA;MAAKyG,SAAS,EAAC,4EAA4E;MAAAC,QAAA,EACxFvE;IAAO;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACAzE,KAAK,iBACJrC,OAAA;MAAKyG,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClFrE;IAAK;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9G,OAAA;MAAKyG,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5C1G,OAAA;QAAKyG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACnC,CACC;UAAEQ,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,EAC5C;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,CAC7C,CAACC,GAAG,CAAEC,GAAG,iBACRrH,OAAA;UAEEwG,OAAO,EAAEA,CAAA,KAAMlG,YAAY,CAAC+G,GAAG,CAACH,GAAU,CAAE;UAC5CT,SAAS,EAAE,4CAA4CpG,SAAS,KAAKgH,GAAG,CAACH,GAAG,GACxE,+BAA+B,GAC/B,4EAA4E,EAC3E;UAAAR,QAAA,EAEJW,GAAG,CAACF;QAAK,GAPLE,GAAG,CAACH,GAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzG,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAA0G,QAAA,gBAEE1G,OAAA;QAAKyG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1G,OAAA;UAAIyG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1D9G,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAM;YACb1D,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACFiE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN9G,OAAA;QAAKyG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1G,OAAA;UACEsH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,sCAAsC;UAClD1D,KAAK,EAAE5C,eAAgB;UACvBuG,QAAQ,EAAGC,CAAC,IAAKvG,kBAAkB,CAACuG,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;UACpD4C,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAI1C,iBAAiB,CAAC;QAAE;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF9G,OAAA;UACEwG,OAAO,EAAEhC,iBAAkB;UAC3BiC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAM;YACbtF,kBAAkB,CAAC,EAAE,CAAC;YACtBiC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFsD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/F,YAAY,gBACXf,OAAA;QAAKyG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExD9G,OAAA,CAAAE,SAAA;QAAAwG,QAAA,gBACE1G,OAAA;UAAKyG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1G,OAAA;YAAOyG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3D1G,OAAA;cAAOyG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B1G,OAAA;gBAAA0G,QAAA,gBACE1G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9G,OAAA;cAAOyG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDnG,KAAK,CAAC6G,GAAG,CAAEQ,IAAI,iBACd5H,OAAA;gBAAA0G,QAAA,gBACE1G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACrC;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EkB,IAAI,CAACC;gBAAQ;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACE;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC1G,OAAA;oBAAMyG,SAAS,EAAE,4DAA4DmB,IAAI,CAACG,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAArB,QAAA,EACFkB,IAAI,CAACG,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACI,UAAU,GAAG,IAAIC,IAAI,CAACL,IAAI,CAACI,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7D1G,OAAA;oBACEwG,OAAO,EAAEA,CAAA,KAAM;sBACb1D,cAAc,CAAC8E,IAAI,CAAC;sBACpBpF,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACFiE,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9G,OAAA;oBACEwG,OAAO,EAAEA,CAAA,KAAMlB,gBAAgB,CAACsC,IAAI,CAACrC,EAAE,CAAE;oBACzCkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEc,IAAI,CAACrC,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAAC1F,eAAe,EAAEiE,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGAzG,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAA0G,QAAA,gBAEE1G,OAAA;QAAKyG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1G,OAAA;UAAIyG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1D9G,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAM;YACbxD,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACF+D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN9G,OAAA;QAAKyG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1G,OAAA;UACEsH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,yBAAyB;UACrC1D,KAAK,EAAEpC,eAAgB;UACvB+F,QAAQ,EAAGC,CAAC,IAAK/F,kBAAkB,CAAC+F,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;UACpD4C,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAInC,iBAAiB,CAAC;QAAE;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF9G,OAAA;UACEwG,OAAO,EAAEzB,iBAAkB;UAC3B0B,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAM;YACb9E,kBAAkB,CAAC,EAAE,CAAC;YACtB0B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFqD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLvF,YAAY,gBACXvB,OAAA;QAAKyG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExD9G,OAAA,CAAAE,SAAA;QAAAwG,QAAA,gBACE1G,OAAA;UAAKyG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1G,OAAA;YAAOyG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3D1G,OAAA;cAAOyG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B1G,OAAA;gBAAA0G,QAAA,gBACE1G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9G,OAAA;cAAOyG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDvF,KAAK,CAACiG,GAAG,CAAEe,IAAI,iBACdnI,OAAA;gBAAA0G,QAAA,gBACE1G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAAC5C;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EyB,IAAI,CAACC;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAACE,WAAW,IAAI;gBAAgB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC1G,OAAA;oBAAMyG,SAAS,EAAE,4DAA4D0B,IAAI,CAACJ,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAArB,QAAA,EACFyB,IAAI,CAACJ,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAACH,UAAU,GAAG,IAAIC,IAAI,CAACE,IAAI,CAACH,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7D1G,OAAA;oBACEwG,OAAO,EAAEA,CAAA,KAAM;sBACbxD,cAAc,CAACmF,IAAI,CAAC;sBACpBzF,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACF+D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9G,OAAA;oBACEwG,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACsC,IAAI,CAAC5C,EAAE,CAAE;oBACzCkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEqB,IAAI,CAAC5C,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAAC9E,eAAe,EAAE2D,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGAzG,SAAS,KAAK,aAAa,iBAC1BL,OAAA;MAAA0G,QAAA,gBAEE1G,OAAA;QAAKyG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1G,OAAA;UAAIyG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE9G,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAM;YACbtD,oBAAoB,CAAC,IAAI,CAAC;YAC1BN,sBAAsB,CAAC,IAAI,CAAC;UAC9B,CAAE;UACF6D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN9G,OAAA;QAAKyG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1G,OAAA;UACEsH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,+BAA+B;UAC3C1D,KAAK,EAAE5B,qBAAsB;UAC7BuF,QAAQ,EAAGC,CAAC,IAAKvF,wBAAwB,CAACuF,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;UAC1D4C,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAI9B,uBAAuB,CAAC;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACF9G,OAAA;UACEwG,OAAO,EAAEpB,uBAAwB;UACjCqB,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UACEwG,OAAO,EAAEA,CAAA,KAAM;YACbtE,wBAAwB,CAAC,EAAE,CAAC;YAC5BmB,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC;UACxB,CAAE;UACFoD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/E,kBAAkB,gBACjB/B,OAAA;QAAKyG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAE9D9G,OAAA,CAAAE,SAAA;QAAAwG,QAAA,gBACE1G,OAAA;UAAKyG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1G,OAAA;YAAOyG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3D1G,OAAA;cAAOyG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B1G,OAAA;gBAAA0G,QAAA,gBACE1G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9G,OAAA;cAAOyG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjD/E,WAAW,CAACyF,GAAG,CAAEkB,UAAU,iBAC1BtI,OAAA;gBAAA0G,QAAA,gBACE1G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAAC/C;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1E4B,UAAU,CAACF;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAACD,WAAW,IAAI;gBAAgB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC1G,OAAA;oBAAMyG,SAAS,EAAE,4DAA4D6B,UAAU,CAACP,SAAS,GAC7F,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAArB,QAAA,EACF4B,UAAU,CAACP,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAACN,UAAU,GAAG,IAAIC,IAAI,CAACK,UAAU,CAACN,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACL9G,OAAA;kBAAIyG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7D1G,OAAA;oBACEwG,OAAO,EAAEA,CAAA,KAAM;sBACbtD,oBAAoB,CAACoF,UAAU,CAAC;sBAChC1F,sBAAsB,CAAC,IAAI,CAAC;oBAC9B,CAAE;oBACF6D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9G,OAAA;oBACEwG,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACsC,UAAU,CAAC/C,EAAE,CAAE;oBACrDkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEwB,UAAU,CAAC/C,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsClB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAACtE,qBAAqB,EAAEwD,2BAA2B,CAAC;MAAA,eACrE,CACH;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGAzG,SAAS,KAAK,aAAa,iBAC1BL,OAAA,CAACF,cAAc;MACbS,KAAK,EAAEA,KAAM;MACbY,KAAK,EAAEA,KAAM;MACboH,SAAS,EAAEnG,UAAW;MACtBoG,OAAO,EAAElG;IAAS;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF,eAGD9G,OAAA,CAACL,SAAS;MACR8I,MAAM,EAAElG,aAAc;MACtBmG,OAAO,EAAEA,CAAA,KAAMlG,gBAAgB,CAAC,KAAK,CAAE;MACvCoF,IAAI,EAAE/E,WAAY;MAClB8F,SAAS,EAAEA,CAAA,KAAM;QACfvG,UAAU,CAACS,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFM,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;MAClD;IAAE;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF9G,OAAA,CAACJ,SAAS;MACR6I,MAAM,EAAEhG,aAAc;MACtBiG,OAAO,EAAEA,CAAA,KAAMhG,gBAAgB,CAAC,KAAK,CAAE;MACvCyF,IAAI,EAAEpF,WAAY;MAClB4F,SAAS,EAAEA,CAAA,KAAM;QACfvG,UAAU,CAACW,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFK,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;MAClD;IAAE;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF9G,OAAA,CAACH,eAAe;MACd4I,MAAM,EAAE9F,mBAAoB;MAC5B+F,OAAO,EAAEA,CAAA,KAAM9F,sBAAsB,CAAC,KAAK,CAAE;MAC7C0F,UAAU,EAAErF,iBAAkB;MAC9B0F,SAAS,EAAEA,CAAA,KAAM;QACfvG,UAAU,CAACa,iBAAiB,GAAG,iCAAiC,GAAG,iCAAiC,CAAC;QACrGI,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;MACpE;IAAE;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1G,EAAA,CA1vBID,wBAAiE;AAAAyI,EAAA,GAAjEzI,wBAAiE;AA4vBvE,eAAeA,wBAAwB;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}