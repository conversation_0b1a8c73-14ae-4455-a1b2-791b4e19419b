package middleware

import (
	"net/http"
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"github.com/gin-gonic/gin"
)

func RequirePermissions(perms ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		var userPerms []models.Permission
		err := database.DB.
			Table("permissions").
			Select("permissions.name").
			Joins("join role_permissions on role_permissions.permission_id = permissions.id").
			Joins("join user_roles on user_roles.role_id = role_permissions.role_id").
			Where("user_roles.user_id = ?", userID).
			Scan(&userPerms).Error
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "Cannot get user permissions"})
			c.Abort()
			return
		}

		for _, up := range userPerms {
			for _, p := range perms {
				if up.Name == p {
					c.Next()
					return
				}
			}
		}

		c.<PERSON>(http.StatusForbidden, gin.H{"error": "Permission denied"})
		c.Abort()
	}
} 