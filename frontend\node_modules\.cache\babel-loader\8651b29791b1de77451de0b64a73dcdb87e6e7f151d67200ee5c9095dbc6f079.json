{"ast": null, "code": "import{BaseCRUDService,api}from'./base.service';class PermissionService extends BaseCRUDService{constructor(){super('/admin/permission-management');}// Get all permissions (from old admin API)\ngetAllPermissions(){return api.get('/admin/permissions');}// Get active permissions only\ngetActivePermissions(){return this.list({active_only:true});}// Search permissions with custom filters\nsearchPermissions(searchTerm){let page=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;let pageSize=arguments.length>2&&arguments[2]!==undefined?arguments[2]:10;return this.search({page,page_size:pageSize,search_term:searchTerm,filters:{is_active:true}});}// Get permissions by name pattern\ngetPermissionsByName(namePattern){return this.list({page:1,page_size:100,name_like:namePattern});}// Toggle permission active status\ntoggleActiveStatus(permissionId,isActive){return this.update(permissionId,{is_active:isActive});}// Get permissions by category (if implemented in backend)\ngetPermissionsByCategory(category){return this.list({page:1,page_size:100,category:category});}// Bulk operations\nbulkDelete(permissionIds){return api.post(\"\".concat(this.baseUrl,\"/bulk-delete\"),{ids:permissionIds});}bulkToggleStatus(permissionIds,isActive){return api.post(\"\".concat(this.baseUrl,\"/bulk-toggle-status\"),{ids:permissionIds,is_active:isActive});}// Get permissions that are not assigned to a specific role\ngetUnassignedPermissions(roleId){return this.list({page:1,page_size:100,exclude_role:roleId,is_active:true});}// Get permissions that are not directly assigned to a specific user\ngetUnassignedUserPermissions(userId){return this.list({page:1,page_size:100,exclude_user:userId,is_active:true});}// Advanced search with multiple filters\nadvancedSearch(filters){return this.search({page:filters.page||1,page_size:filters.page_size||10,search_term:filters.search_term,filters:{is_active:filters.is_active,category:filters.category,created_after:filters.created_after,created_before:filters.created_before}});}}export const permissionService=new PermissionService();export default permissionService;", "map": {"version": 3, "names": ["BaseCRUDService", "api", "PermissionService", "constructor", "getAllPermissions", "get", "getActivePermissions", "list", "active_only", "searchPermissions", "searchTerm", "page", "arguments", "length", "undefined", "pageSize", "search", "page_size", "search_term", "filters", "is_active", "getPermissionsByName", "namePattern", "name_like", "toggleActiveStatus", "permissionId", "isActive", "update", "getPermissionsByCategory", "category", "bulkDelete", "permissionIds", "post", "concat", "baseUrl", "ids", "bulkToggleStatus", "getUnassignedPermissions", "roleId", "exclude_role", "getUnassignedUserPermissions", "userId", "exclude_user", "advancedSearch", "created_after", "created_before", "permissionService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/permission.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { Permission } from '../types';\nimport { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';\n\nexport interface CreatePermissionRequest {\n  name: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nexport interface UpdatePermissionRequest {\n  name?: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nclass PermissionService extends BaseCRUDService<Permission, CreatePermissionRequest, UpdatePermissionRequest> {\n  constructor() {\n    super('/admin/permission-management');\n  }\n\n  // Get all permissions (from old admin API)\n  getAllPermissions(): Promise<AxiosResponse<Permission[]>> {\n    return api.get('/admin/permissions');\n  }\n\n  // Get active permissions only\n  getActivePermissions(): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({ active_only: true });\n  }\n\n  // Search permissions with custom filters\n  searchPermissions(searchTerm: string, page: number = 1, pageSize: number = 10): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.search({\n      page,\n      page_size: pageSize,\n      search_term: searchTerm,\n      filters: {\n        is_active: true\n      }\n    });\n  }\n\n  // Get permissions by name pattern\n  getPermissionsByName(namePattern: string): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      name_like: namePattern\n    });\n  }\n\n  // Toggle permission active status\n  toggleActiveStatus(permissionId: number, isActive: boolean): Promise<AxiosResponse<CRUDResponse<Permission>>> {\n    return this.update(permissionId, { is_active: isActive });\n  }\n\n  // Get permissions by category (if implemented in backend)\n  getPermissionsByCategory(category: string): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      category: category\n    });\n  }\n\n  // Bulk operations\n  bulkDelete(permissionIds: number[]): Promise<AxiosResponse<{ message: string; deleted_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-delete`, { ids: permissionIds });\n  }\n\n  bulkToggleStatus(permissionIds: number[], isActive: boolean): Promise<AxiosResponse<{ message: string; updated_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-toggle-status`, { ids: permissionIds, is_active: isActive });\n  }\n\n  // Get permissions that are not assigned to a specific role\n  getUnassignedPermissions(roleId: number): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      exclude_role: roleId,\n      is_active: true\n    });\n  }\n\n  // Get permissions that are not directly assigned to a specific user\n  getUnassignedUserPermissions(userId: number): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      exclude_user: userId,\n      is_active: true\n    });\n  }\n\n  // Advanced search with multiple filters\n  advancedSearch(filters: {\n    search_term?: string;\n    is_active?: boolean;\n    category?: string;\n    created_after?: string;\n    created_before?: string;\n    page?: number;\n    page_size?: number;\n  }): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.search({\n      page: filters.page || 1,\n      page_size: filters.page_size || 10,\n      search_term: filters.search_term,\n      filters: {\n        is_active: filters.is_active,\n        category: filters.category,\n        created_after: filters.created_after,\n        created_before: filters.created_before\n      }\n    });\n  }\n}\n\nexport const permissionService = new PermissionService();\nexport default permissionService;\n"], "mappings": "AAEA,OAASA,eAAe,CAAkCC,GAAG,KAAQ,gBAAgB,CAcrF,KAAM,CAAAC,iBAAiB,QAAS,CAAAF,eAA8E,CAC5GG,WAAWA,CAAA,CAAG,CACZ,KAAK,CAAC,8BAA8B,CAAC,CACvC,CAEA;AACAC,iBAAiBA,CAAA,CAAyC,CACxD,MAAO,CAAAH,GAAG,CAACI,GAAG,CAAC,oBAAoB,CAAC,CACtC,CAEA;AACAC,oBAAoBA,CAAA,CAAyD,CAC3E,MAAO,KAAI,CAACC,IAAI,CAAC,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CACzC,CAEA;AACAC,iBAAiBA,CAACC,UAAkB,CAAiG,IAA/F,CAAAC,IAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,QAAgB,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC3E,MAAO,KAAI,CAACI,MAAM,CAAC,CACjBL,IAAI,CACJM,SAAS,CAAEF,QAAQ,CACnBG,WAAW,CAAER,UAAU,CACvBS,OAAO,CAAE,CACPC,SAAS,CAAE,IACb,CACF,CAAC,CAAC,CACJ,CAEA;AACAC,oBAAoBA,CAACC,WAAmB,CAAwD,CAC9F,MAAO,KAAI,CAACf,IAAI,CAAC,CACfI,IAAI,CAAE,CAAC,CACPM,SAAS,CAAE,GAAG,CACdM,SAAS,CAAED,WACb,CAAC,CAAC,CACJ,CAEA;AACAE,kBAAkBA,CAACC,YAAoB,CAAEC,QAAiB,CAAoD,CAC5G,MAAO,KAAI,CAACC,MAAM,CAACF,YAAY,CAAE,CAAEL,SAAS,CAAEM,QAAS,CAAC,CAAC,CAC3D,CAEA;AACAE,wBAAwBA,CAACC,QAAgB,CAAwD,CAC/F,MAAO,KAAI,CAACtB,IAAI,CAAC,CACfI,IAAI,CAAE,CAAC,CACPM,SAAS,CAAE,GAAG,CACdY,QAAQ,CAAEA,QACZ,CAAC,CAAC,CACJ,CAEA;AACAC,UAAUA,CAACC,aAAuB,CAAsE,CACtG,MAAO,CAAA9B,GAAG,CAAC+B,IAAI,IAAAC,MAAA,CAAI,IAAI,CAACC,OAAO,iBAAgB,CAAEC,GAAG,CAAEJ,aAAc,CAAC,CAAC,CACxE,CAEAK,gBAAgBA,CAACL,aAAuB,CAAEL,QAAiB,CAAsE,CAC/H,MAAO,CAAAzB,GAAG,CAAC+B,IAAI,IAAAC,MAAA,CAAI,IAAI,CAACC,OAAO,wBAAuB,CAAEC,GAAG,CAAEJ,aAAa,CAAEX,SAAS,CAAEM,QAAS,CAAC,CAAC,CACpG,CAEA;AACAW,wBAAwBA,CAACC,MAAc,CAAwD,CAC7F,MAAO,KAAI,CAAC/B,IAAI,CAAC,CACfI,IAAI,CAAE,CAAC,CACPM,SAAS,CAAE,GAAG,CACdsB,YAAY,CAAED,MAAM,CACpBlB,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAEA;AACAoB,4BAA4BA,CAACC,MAAc,CAAwD,CACjG,MAAO,KAAI,CAAClC,IAAI,CAAC,CACfI,IAAI,CAAE,CAAC,CACPM,SAAS,CAAE,GAAG,CACdyB,YAAY,CAAED,MAAM,CACpBrB,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAEA;AACAuB,cAAcA,CAACxB,OAQd,CAAwD,CACvD,MAAO,KAAI,CAACH,MAAM,CAAC,CACjBL,IAAI,CAAEQ,OAAO,CAACR,IAAI,EAAI,CAAC,CACvBM,SAAS,CAAEE,OAAO,CAACF,SAAS,EAAI,EAAE,CAClCC,WAAW,CAAEC,OAAO,CAACD,WAAW,CAChCC,OAAO,CAAE,CACPC,SAAS,CAAED,OAAO,CAACC,SAAS,CAC5BS,QAAQ,CAAEV,OAAO,CAACU,QAAQ,CAC1Be,aAAa,CAAEzB,OAAO,CAACyB,aAAa,CACpCC,cAAc,CAAE1B,OAAO,CAAC0B,cAC1B,CACF,CAAC,CAAC,CACJ,CACF,CAEA,MAAO,MAAM,CAAAC,iBAAiB,CAAG,GAAI,CAAA5C,iBAAiB,CAAC,CAAC,CACxD,cAAe,CAAA4C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}