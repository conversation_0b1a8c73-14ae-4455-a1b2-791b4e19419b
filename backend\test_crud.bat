@echo off
REM Base CRUD Integration Test Script for Windows
echo 🚀 Base CRUD System Integration Test
echo ====================================

REM Check if Go is installed
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if config.env exists
if not exist "config.env" (
    echo ⚠️  config.env not found. Creating a sample one...
    (
        echo # Database Configuration
        echo DB_HOST=localhost
        echo DB_PORT=5432
        echo DB_USER=postgres
        echo DB_PASSWORD=password
        echo DB_NAME=jwt_auth_db
        echo DB_SSLMODE=disable
        echo.
        echo # JWT Configuration
        echo JWT_SECRET=your-super-secret-jwt-key-here
        echo JWT_EXPIRY=24h
        echo.
        echo # Redis Configuration
        echo REDIS_HOST=localhost
        echo REDIS_PORT=6379
        echo REDIS_PASSWORD=
        echo.
        echo # Server Configuration
        echo PORT=8080
        echo GIN_MODE=debug
        echo.
        echo # Log Configuration
        echo LOG_LEVEL=debug
    ) > config.env
    echo 📝 Sample config.env created. Please update with your actual database credentials.
    echo    Make sure your PostgreSQL database is running and accessible.
)

echo.
echo 📋 Prerequisites Check:
for /f "tokens=*" %%i in ('go version') do echo   ✅ Go installed: %%i

REM Check if PostgreSQL is available
psql --version >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ PostgreSQL client available
) else (
    echo   ⚠️  PostgreSQL client not found ^(optional^)
)

echo.
echo 🔧 Building and running integration test...

REM Run the integration test
go run test_crud_integration.go test-crud

REM Check the exit code
if %errorlevel% equ 0 (
    echo.
    echo 🎉 Integration test completed successfully!
    echo.
    echo 📚 Next Steps:
    echo   1. Review the test output above
    echo   2. Check the documentation in docs/base_crud_guide.md
    echo   3. See usage examples in examples/user_crud_example.go
    echo   4. Integrate CRUD routes into your main application
    echo.
    echo 🚀 To use the base CRUD system in your application:
    echo   // Create handler
    echo   userHandler := handlers.NewBaseCRUDHandler^(&models.User{}^)
    echo.
    echo   // Setup routes
    echo   v1 := r.Group^("/api/v1"^)
    echo   userHandler.SetupCRUDRoutes^(v1, "/users"^)
) else (
    echo.
    echo ❌ Integration test failed!
    echo.
    echo 🔍 Troubleshooting:
    echo   1. Check that your database is running and accessible
    echo   2. Verify config.env has correct database credentials
    echo   3. Ensure all Go dependencies are installed: go mod tidy
    echo   4. Check the error messages above for specific issues
)

echo.
echo Press any key to continue...
pause >nul
