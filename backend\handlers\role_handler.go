package handlers

import (
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/services"
	"jwt-auth-backend/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RoleHandler handles role-related HTTP requests
type RoleHandler struct {
	*BaseCRUDHandler
	roleService *services.RoleService
	logger      *zap.Logger
}

// NewRoleHandler creates a new instance of RoleHandler
func NewRoleHandler() *RoleHandler {
	logger := utils.GetLogger()
	roleService := services.NewRoleService(database.DB, logger)

	return &RoleHandler{
		BaseCRUDHandler: NewBaseCRUDHandler(&models.Role{}),
		roleService:     roleService,
		logger:          logger,
	}
}

// CreateRole handles POST /roles
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req models.RoleCreateRequest
	if err := c.<PERSON><PERSON><PERSON>(&req); err != nil {
		h.logger.Warn("Invalid role creation request", zap.Error(err))
		c.<PERSON>(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	// Set default value for IsActive if not provided
	if req.IsActive == false && req.Name != "" {
		req.IsActive = true
	}

	role, err := h.roleService.CreateRole(req)
	if err != nil {
		if err.Error() == "role with name '"+req.Name+"' already exists" {
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "Role already exists",
				Details: err.Error(),
				Code:    "ROLE_EXISTS",
			})
			return
		}

		h.logger.Error("Failed to create role", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to create role",
			Details: err.Error(),
			Code:    "CREATION_FAILED",
		})
		return
	}

	c.JSON(http.StatusCreated, models.CRUDResponse{
		Data:    role,
		Message: "Role created successfully",
	})
}

// GetRole handles GET /roles/:id
func (h *RoleHandler) GetRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid role ID",
			Details: "Role ID must be a valid number",
			Code:    "INVALID_ID",
		})
		return
	}

	// Check if we need associations
	includeAssociations := c.Query("include") == "associations"

	if includeAssociations {
		role, err := h.roleService.GetRoleWithAssociations(uint(id))
		if err != nil {
			if err.Error() == "role not found" {
				c.JSON(http.StatusNotFound, models.ErrorResponse{
					Error:   "Role not found",
					Details: err.Error(),
					Code:    "ROLE_NOT_FOUND",
				})
				return
			}

			h.logger.Error("Failed to get role with associations", zap.Error(err))
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "Failed to get role",
				Details: err.Error(),
				Code:    "RETRIEVAL_FAILED",
			})
			return
		}

		c.JSON(http.StatusOK, models.CRUDResponse{
			Data:    role,
			Message: "Role retrieved successfully",
		})
		return
	}

	// Standard get without associations
	var role models.Role
	if err := h.roleService.GetByID(&role, uint(id)); err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error:   "Role not found",
			Details: err.Error(),
			Code:    "ROLE_NOT_FOUND",
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:    role,
		Message: "Role retrieved successfully",
	})
}

// ListRoles handles GET /roles
func (h *RoleHandler) ListRoles(c *gin.Context) {
	// Check if we want only active roles
	if c.Query("active_only") == "true" {
		roles, err := h.roleService.ListActiveRoles()
		if err != nil {
			h.logger.Error("Failed to list active roles", zap.Error(err))
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "Failed to list active roles",
				Details: err.Error(),
				Code:    "RETRIEVAL_FAILED",
			})
			return
		}

		c.JSON(http.StatusOK, models.CRUDResponse{
			Data:    roles,
			Message: "Active roles retrieved successfully",
		})
		return
	}

	// Parse CRUD request for pagination and search
	var req models.CRUDRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid list roles request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request parameters",
			Details: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	// Set defaults
	req.PaginationRequest.SetDefaults()
	req.SearchRequest.SetDefaults()

	data, pagination, err := h.roleService.ListRolesWithPagination(req)
	if err != nil {
		h.logger.Error("Failed to list roles", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to list roles",
			Details: err.Error(),
			Code:    "RETRIEVAL_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDListResponse{
		Data:       data,
		Pagination: pagination,
	})
}

// UpdateRole handles PUT /roles/:id
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid role ID",
			Details: "Role ID must be a valid number",
			Code:    "INVALID_ID",
		})
		return
	}

	var req models.RoleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid role update request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	role, err := h.roleService.UpdateRole(uint(id), req)
	if err != nil {
		if err.Error() == "role not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Role not found",
				Details: err.Error(),
				Code:    "ROLE_NOT_FOUND",
			})
			return
		}

		if err.Error() == "role with name '"+req.Name+"' already exists" {
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "Role name already exists",
				Details: err.Error(),
				Code:    "ROLE_EXISTS",
			})
			return
		}

		h.logger.Error("Failed to update role", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to update role",
			Details: err.Error(),
			Code:    "UPDATE_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:    role,
		Message: "Role updated successfully",
	})
}

// DeleteRole handles DELETE /roles/:id
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid role ID",
			Details: "Role ID must be a valid number",
			Code:    "INVALID_ID",
		})
		return
	}

	err = h.roleService.DeleteRole(uint(id))
	if err != nil {
		if err.Error() == "role not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Role not found",
				Details: err.Error(),
				Code:    "ROLE_NOT_FOUND",
			})
			return
		}

		// Check if it's a constraint error (role in use)
		if err.Error()[:22] == "cannot delete role: it" {
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "Cannot delete role",
				Details: err.Error(),
				Code:    "ROLE_IN_USE",
			})
			return
		}

		h.logger.Error("Failed to delete role", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to delete role",
			Details: err.Error(),
			Code:    "DELETION_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Role deleted successfully",
	})
}

// AssignPermissionToRole handles POST /roles/:id/permissions
func (h *RoleHandler) AssignPermissionToRole(c *gin.Context) {
	idStr := c.Param("id")
	roleID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid role ID",
			Details: "Role ID must be a valid number",
			Code:    "INVALID_ID",
		})
		return
	}

	var req struct {
		PermissionID uint `json:"permission_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid permission assignment request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	err = h.roleService.AssignPermissionToRole(uint(roleID), req.PermissionID)
	if err != nil {
		if err.Error() == "role not found" || err.Error() == "permission not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Resource not found",
				Details: err.Error(),
				Code:    "RESOURCE_NOT_FOUND",
			})
			return
		}

		if err.Error() == "permission already assigned to role" {
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "Permission already assigned",
				Details: err.Error(),
				Code:    "PERMISSION_EXISTS",
			})
			return
		}

		h.logger.Error("Failed to assign permission to role", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to assign permission",
			Details: err.Error(),
			Code:    "ASSIGNMENT_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Permission assigned to role successfully",
	})
}

// RemovePermissionFromRole handles DELETE /roles/:id/permissions/:permission_id
func (h *RoleHandler) RemovePermissionFromRole(c *gin.Context) {
	idStr := c.Param("id")
	roleID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid role ID",
			Details: "Role ID must be a valid number",
			Code:    "INVALID_ID",
		})
		return
	}

	permissionIDStr := c.Param("permission_id")
	permissionID, err := strconv.ParseUint(permissionIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid permission ID",
			Details: "Permission ID must be a valid number",
			Code:    "INVALID_ID",
		})
		return
	}

	err = h.roleService.RemovePermissionFromRole(uint(roleID), uint(permissionID))
	if err != nil {
		if err.Error() == "permission assignment not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Permission assignment not found",
				Details: err.Error(),
				Code:    "ASSIGNMENT_NOT_FOUND",
			})
			return
		}

		h.logger.Error("Failed to remove permission from role", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to remove permission",
			Details: err.Error(),
			Code:    "REMOVAL_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Permission removed from role successfully",
	})
}

// SetupRoleRoutes sets up all role-related routes
func (h *RoleHandler) SetupRoleRoutes(router *gin.RouterGroup) {
	roles := router.Group("/role-management")
	{
		roles.POST("", h.CreateRole)                                                // POST /role-management
		roles.GET("", h.ListRoles)                                                  // GET /role-management
		roles.GET("/:id", h.GetRole)                                                // GET /role-management/:id
		roles.PUT("/:id", h.UpdateRole)                                             // PUT /role-management/:id
		roles.DELETE("/:id", h.DeleteRole)                                          // DELETE /role-management/:id
		roles.POST("/:id/permissions", h.AssignPermissionToRole)                    // POST /role-management/:id/permissions
		roles.DELETE("/:id/permissions/:permission_id", h.RemovePermissionFromRole) // DELETE /role-management/:id/permissions/:permission_id
	}
}
