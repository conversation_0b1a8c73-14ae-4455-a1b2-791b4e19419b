{"ast": null, "code": "import React,{useState,useEffect}from'react';import{roleService,permissionService}from'../../services';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RolePermissionModal=_ref=>{let{role,isOpen,onClose,onSuccess,onError}=_ref;// State management\nconst[allPermissions,setAllPermissions]=useState([]);const[rolePermissions,setRolePermissions]=useState([]);const[loading,setLoading]=useState(false);const[searchTerm,setSearchTerm]=useState('');const[categoryFilter,setCategoryFilter]=useState('all');// Load data when modal opens\nuseEffect(()=>{if(isOpen&&role){loadPermissions();loadRolePermissions();}},[isOpen,role===null||role===void 0?void 0:role.id]);// Early return if role is null\nif(!role){return null;}// Load all permissions\nconst loadPermissions=async()=>{try{const response=await permissionService.list({page:1,page_size:100});setAllPermissions(response.data.data);}catch(err){onError('Failed to load permissions');console.error('Error loading permissions:',err);}};// Load role's current permissions\nconst loadRolePermissions=async()=>{try{setLoading(true);const response=await roleService.getRoleWithPermissions(role.id);if(response.data.data&&response.data.data.permissions){setRolePermissions(response.data.data.permissions);}else{setRolePermissions([]);}}catch(err){console.error('Error loading role permissions:',err);setRolePermissions([]);}finally{setLoading(false);}};// Check if permission is assigned to role\nconst isPermissionAssigned=permission=>{return rolePermissions.some(rp=>rp.id===permission.id);};// Handle permission assignment\nconst handleAssignPermission=async permission=>{try{setLoading(true);await roleService.assignPermissionById(role.id,permission.id);setRolePermissions(prev=>[...prev,permission]);onSuccess(\"Permission \\\"\".concat(permission.name,\"\\\" assigned to role \\\"\").concat(role.name,\"\\\"\"));}catch(err){onError(err.message||'Failed to assign permission');}finally{setLoading(false);}};// Handle permission removal\nconst handleRemovePermission=async permission=>{try{setLoading(true);await roleService.removePermissionById(role.id,permission.id);setRolePermissions(prev=>prev.filter(rp=>rp.id!==permission.id));onSuccess(\"Permission \\\"\".concat(permission.name,\"\\\" removed from role \\\"\").concat(role.name,\"\\\"\"));}catch(err){onError(err.message||'Failed to remove permission');}finally{setLoading(false);}};// Filter permissions based on search and category\nconst filteredPermissions=allPermissions.filter(permission=>{const matchesSearch=searchTerm===''||permission.name.toLowerCase().includes(searchTerm.toLowerCase())||permission.description&&permission.description.toLowerCase().includes(searchTerm.toLowerCase());const matchesCategory=categoryFilter==='all'||permission.name.toLowerCase().startsWith(categoryFilter.toLowerCase());return matchesSearch&&matchesCategory;});// Get unique categories from permissions\nconst categories=Array.from(new Set(allPermissions.map(p=>p.name.split('_')[0]).filter(Boolean))).sort();if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"modal fade show\",style:{display:'block'},tabIndex:-1,children:/*#__PURE__*/_jsx(\"div\",{className:\"modal-dialog modal-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"modal-title\",children:[\"Manage Permissions for Role: \",/*#__PURE__*/_jsx(\"strong\",{children:role.name})]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-close\",onClick:onClose})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"row mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-md-8\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"form-control\",placeholder:\"Search permissions...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-4\",children:/*#__PURE__*/_jsxs(\"select\",{className:\"form-select\",value:categoryFilter,onChange:e=>setCategoryFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Categories\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category,children:category.charAt(0).toUpperCase()+category.slice(1)},category))]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"row mb-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-info\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Role Statistics:\"}),\" \",rolePermissions.length,\" of \",allPermissions.length,\" permissions assigned\"]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"row\",children:/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"spinner-border\",role:\"status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})})}):/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",style:{maxHeight:'400px',overflowY:'auto'},children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-sm table-hover\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"table-light sticky-top\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Permission\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Description\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Action\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredPermissions.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:4,className:\"text-center py-3\",children:\"No permissions found\"})}):filteredPermissions.map(permission=>{const isAssigned=isPermissionAssigned(permission);return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:permission.name})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:permission.description||'No description'})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"badge \".concat(isAssigned?'bg-success':'bg-secondary'),children:isAssigned?'Assigned':'Not Assigned'})}),/*#__PURE__*/_jsx(\"td\",{children:isAssigned?/*#__PURE__*/_jsxs(\"button\",{className:\"btn btn-sm btn-outline-danger\",onClick:()=>handleRemovePermission(permission),disabled:loading,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-minus me-1\"}),\"Remove\"]}):/*#__PURE__*/_jsxs(\"button\",{className:\"btn btn-sm btn-outline-success\",onClick:()=>handleAssignPermission(permission),disabled:loading,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-plus me-1\"}),\"Assign\"]})})]},permission.id);})})]})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"row mt-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"btn btn-sm btn-outline-success\",onClick:async()=>{for(const permission of filteredPermissions){if(!isPermissionAssigned(permission)){await handleAssignPermission(permission);}}},disabled:loading,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-double me-1\"}),\"Assign All Filtered\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn btn-sm btn-outline-danger\",onClick:async()=>{for(const permission of filteredPermissions){if(isPermissionAssigned(permission)){await handleRemovePermission(permission);}}},disabled:loading,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-times me-1\"}),\"Remove All Filtered\"]})]})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"modal-footer\",children:/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn btn-secondary\",onClick:onClose,children:\"Close\"})})]})})});};export default RolePermissionModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "roleService", "permissionService", "jsx", "_jsx", "jsxs", "_jsxs", "RolePermissionModal", "_ref", "role", "isOpen", "onClose", "onSuccess", "onError", "allPermissions", "setAllPermissions", "rolePermissions", "setRolePermissions", "loading", "setLoading", "searchTerm", "setSearchTerm", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadPermissions", "loadRolePermissions", "id", "response", "list", "page", "page_size", "data", "err", "console", "error", "getRoleWithPermissions", "permissions", "isPermissionAssigned", "permission", "some", "rp", "handleAssignPermission", "assignPermissionById", "prev", "concat", "name", "message", "handleRemovePermission", "removePermissionById", "filter", "filteredPermissions", "matchesSearch", "toLowerCase", "includes", "description", "matchesCategory", "startsWith", "categories", "Array", "from", "Set", "map", "p", "split", "Boolean", "sort", "className", "style", "display", "tabIndex", "children", "type", "onClick", "placeholder", "value", "onChange", "e", "target", "category", "char<PERSON>t", "toUpperCase", "slice", "length", "maxHeight", "overflowY", "colSpan", "isAssigned", "disabled"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/RolePermissionModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { roleService, permissionService } from '../../services';\nimport { Role, Permission } from '../../types';\n\ninterface RolePermissionModalProps {\n  role: Role;\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: (message: string) => void;\n  onError: (message: string) => void;\n}\n\nconst RolePermissionModal: React.FC<RolePermissionModalProps> = ({\n  role,\n  isOpen,\n  onClose,\n  onSuccess,\n  onError\n}) => {\n  // State management\n  const [allPermissions, setAllPermissions] = useState<Permission[]>([]);\n  const [rolePermissions, setRolePermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState<string>('all');\n\n  // Load data when modal opens\n  useEffect(() => {\n    if (isOpen && role) {\n      loadPermissions();\n      loadRolePermissions();\n    }\n  }, [isOpen, role?.id]);\n\n  // Early return if role is null\n  if (!role) {\n    return null;\n  }\n\n  // Load all permissions\n  const loadPermissions = async () => {\n    try {\n      const response = await permissionService.list({ page: 1, page_size: 100 });\n      setAllPermissions(response.data.data);\n    } catch (err: any) {\n      onError('Failed to load permissions');\n      console.error('Error loading permissions:', err);\n    }\n  };\n\n  // Load role's current permissions\n  const loadRolePermissions = async () => {\n    try {\n      setLoading(true);\n      const response = await roleService.getRoleWithPermissions(role.id);\n      if (response.data.data && response.data.data.permissions) {\n        setRolePermissions(response.data.data.permissions);\n      } else {\n        setRolePermissions([]);\n      }\n    } catch (err: any) {\n      console.error('Error loading role permissions:', err);\n      setRolePermissions([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Check if permission is assigned to role\n  const isPermissionAssigned = (permission: Permission): boolean => {\n    return rolePermissions.some(rp => rp.id === permission.id);\n  };\n\n  // Handle permission assignment\n  const handleAssignPermission = async (permission: Permission) => {\n    try {\n      setLoading(true);\n      await roleService.assignPermissionById(role.id, permission.id);\n      setRolePermissions(prev => [...prev, permission]);\n      onSuccess(`Permission \"${permission.name}\" assigned to role \"${role.name}\"`);\n    } catch (err: any) {\n      onError(err.message || 'Failed to assign permission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle permission removal\n  const handleRemovePermission = async (permission: Permission) => {\n    try {\n      setLoading(true);\n      await roleService.removePermissionById(role.id, permission.id);\n      setRolePermissions(prev => prev.filter(rp => rp.id !== permission.id));\n      onSuccess(`Permission \"${permission.name}\" removed from role \"${role.name}\"`);\n    } catch (err: any) {\n      onError(err.message || 'Failed to remove permission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filter permissions based on search and category\n  const filteredPermissions = allPermissions.filter(permission => {\n    const matchesSearch = searchTerm === '' ||\n      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (permission.description && permission.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    const matchesCategory = categoryFilter === 'all' ||\n      permission.name.toLowerCase().startsWith(categoryFilter.toLowerCase());\n\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get unique categories from permissions\n  const categories = Array.from(new Set(\n    allPermissions.map(p => p.name.split('_')[0]).filter(Boolean)\n  )).sort();\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal fade show\" style={{ display: 'block' }} tabIndex={-1}>\n      <div className=\"modal-dialog modal-lg\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header\">\n            <h5 className=\"modal-title\">\n              Manage Permissions for Role: <strong>{role.name}</strong>\n            </h5>\n            <button type=\"button\" className=\"btn-close\" onClick={onClose}></button>\n          </div>\n\n          <div className=\"modal-body\">\n            {/* Search and Filter */}\n            <div className=\"row mb-3\">\n              <div className=\"col-md-8\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Search permissions...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n              <div className=\"col-md-4\">\n                <select\n                  className=\"form-select\"\n                  value={categoryFilter}\n                  onChange={(e) => setCategoryFilter(e.target.value)}\n                >\n                  <option value=\"all\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category} value={category}>\n                      {category.charAt(0).toUpperCase() + category.slice(1)}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Permission Statistics */}\n            <div className=\"row mb-3\">\n              <div className=\"col-12\">\n                <div className=\"alert alert-info\">\n                  <strong>Role Statistics:</strong> {rolePermissions.length} of {allPermissions.length} permissions assigned\n                </div>\n              </div>\n            </div>\n\n            {/* Permissions List */}\n            <div className=\"row\">\n              <div className=\"col-12\">\n                {loading ? (\n                  <div className=\"text-center py-4\">\n                    <div className=\"spinner-border\" role=\"status\">\n                      <span className=\"visually-hidden\">Loading...</span>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"table-responsive\" style={{ maxHeight: '400px', overflowY: 'auto' }}>\n                    <table className=\"table table-sm table-hover\">\n                      <thead className=\"table-light sticky-top\">\n                        <tr>\n                          <th>Permission</th>\n                          <th>Description</th>\n                          <th>Status</th>\n                          <th>Action</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {filteredPermissions.length === 0 ? (\n                          <tr>\n                            <td colSpan={4} className=\"text-center py-3\">\n                              No permissions found\n                            </td>\n                          </tr>\n                        ) : (\n                          filteredPermissions.map((permission) => {\n                            const isAssigned = isPermissionAssigned(permission);\n                            return (\n                              <tr key={permission.id}>\n                                <td>\n                                  <strong>{permission.name}</strong>\n                                </td>\n                                <td>\n                                  <small className=\"text-muted\">\n                                    {permission.description || 'No description'}\n                                  </small>\n                                </td>\n                                <td>\n                                  <span className={`badge ${isAssigned ? 'bg-success' : 'bg-secondary'}`}>\n                                    {isAssigned ? 'Assigned' : 'Not Assigned'}\n                                  </span>\n                                </td>\n                                <td>\n                                  {isAssigned ? (\n                                    <button\n                                      className=\"btn btn-sm btn-outline-danger\"\n                                      onClick={() => handleRemovePermission(permission)}\n                                      disabled={loading}\n                                    >\n                                      <i className=\"fas fa-minus me-1\"></i>\n                                      Remove\n                                    </button>\n                                  ) : (\n                                    <button\n                                      className=\"btn btn-sm btn-outline-success\"\n                                      onClick={() => handleAssignPermission(permission)}\n                                      disabled={loading}\n                                    >\n                                      <i className=\"fas fa-plus me-1\"></i>\n                                      Assign\n                                    </button>\n                                  )}\n                                </td>\n                              </tr>\n                            );\n                          })\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Bulk Actions */}\n            <div className=\"row mt-3\">\n              <div className=\"col-12\">\n                <div className=\"d-flex gap-2\">\n                  <button\n                    className=\"btn btn-sm btn-outline-success\"\n                    onClick={async () => {\n                      for (const permission of filteredPermissions) {\n                        if (!isPermissionAssigned(permission)) {\n                          await handleAssignPermission(permission);\n                        }\n                      }\n                    }}\n                    disabled={loading}\n                  >\n                    <i className=\"fas fa-check-double me-1\"></i>\n                    Assign All Filtered\n                  </button>\n                  <button\n                    className=\"btn btn-sm btn-outline-danger\"\n                    onClick={async () => {\n                      for (const permission of filteredPermissions) {\n                        if (isPermissionAssigned(permission)) {\n                          await handleRemovePermission(permission);\n                        }\n                      }\n                    }}\n                    disabled={loading}\n                  >\n                    <i className=\"fas fa-times me-1\"></i>\n                    Remove All Filtered\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"modal-footer\">\n            <button type=\"button\" className=\"btn btn-secondary\" onClick={onClose}>\n              Close\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RolePermissionModal;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,iBAAiB,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWhE,KAAM,CAAAC,mBAAuD,CAAGC,IAAA,EAM1D,IAN2D,CAC/DC,IAAI,CACJC,MAAM,CACNC,OAAO,CACPC,SAAS,CACTC,OACF,CAAC,CAAAL,IAAA,CACC;AACA,KAAM,CAACM,cAAc,CAAEC,iBAAiB,CAAC,CAAGhB,QAAQ,CAAe,EAAE,CAAC,CACtE,KAAM,CAACiB,eAAe,CAAEC,kBAAkB,CAAC,CAAGlB,QAAQ,CAAe,EAAE,CAAC,CACxE,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqB,UAAU,CAAEC,aAAa,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuB,cAAc,CAAEC,iBAAiB,CAAC,CAAGxB,QAAQ,CAAS,KAAK,CAAC,CAEnE;AACAC,SAAS,CAAC,IAAM,CACd,GAAIU,MAAM,EAAID,IAAI,CAAE,CAClBe,eAAe,CAAC,CAAC,CACjBC,mBAAmB,CAAC,CAAC,CACvB,CACF,CAAC,CAAE,CAACf,MAAM,CAAED,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,EAAE,CAAC,CAAC,CAEtB;AACA,GAAI,CAACjB,IAAI,CAAE,CACT,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAe,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAzB,iBAAiB,CAAC0B,IAAI,CAAC,CAAEC,IAAI,CAAE,CAAC,CAAEC,SAAS,CAAE,GAAI,CAAC,CAAC,CAC1Ef,iBAAiB,CAACY,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC,CACvC,CAAE,MAAOC,GAAQ,CAAE,CACjBnB,OAAO,CAAC,4BAA4B,CAAC,CACrCoB,OAAO,CAACC,KAAK,CAAC,4BAA4B,CAAEF,GAAG,CAAC,CAClD,CACF,CAAC,CAED;AACA,KAAM,CAAAP,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACFN,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAA1B,WAAW,CAACkC,sBAAsB,CAAC1B,IAAI,CAACiB,EAAE,CAAC,CAClE,GAAIC,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAIJ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACK,WAAW,CAAE,CACxDnB,kBAAkB,CAACU,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACK,WAAW,CAAC,CACpD,CAAC,IAAM,CACLnB,kBAAkB,CAAC,EAAE,CAAC,CACxB,CACF,CAAE,MAAOe,GAAQ,CAAE,CACjBC,OAAO,CAACC,KAAK,CAAC,iCAAiC,CAAEF,GAAG,CAAC,CACrDf,kBAAkB,CAAC,EAAE,CAAC,CACxB,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAkB,oBAAoB,CAAIC,UAAsB,EAAc,CAChE,MAAO,CAAAtB,eAAe,CAACuB,IAAI,CAACC,EAAE,EAAIA,EAAE,CAACd,EAAE,GAAKY,UAAU,CAACZ,EAAE,CAAC,CAC5D,CAAC,CAED;AACA,KAAM,CAAAe,sBAAsB,CAAG,KAAO,CAAAH,UAAsB,EAAK,CAC/D,GAAI,CACFnB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAlB,WAAW,CAACyC,oBAAoB,CAACjC,IAAI,CAACiB,EAAE,CAAEY,UAAU,CAACZ,EAAE,CAAC,CAC9DT,kBAAkB,CAAC0B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEL,UAAU,CAAC,CAAC,CACjD1B,SAAS,iBAAAgC,MAAA,CAAgBN,UAAU,CAACO,IAAI,2BAAAD,MAAA,CAAuBnC,IAAI,CAACoC,IAAI,MAAG,CAAC,CAC9E,CAAE,MAAOb,GAAQ,CAAE,CACjBnB,OAAO,CAACmB,GAAG,CAACc,OAAO,EAAI,6BAA6B,CAAC,CACvD,CAAC,OAAS,CACR3B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA4B,sBAAsB,CAAG,KAAO,CAAAT,UAAsB,EAAK,CAC/D,GAAI,CACFnB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAlB,WAAW,CAAC+C,oBAAoB,CAACvC,IAAI,CAACiB,EAAE,CAAEY,UAAU,CAACZ,EAAE,CAAC,CAC9DT,kBAAkB,CAAC0B,IAAI,EAAIA,IAAI,CAACM,MAAM,CAACT,EAAE,EAAIA,EAAE,CAACd,EAAE,GAAKY,UAAU,CAACZ,EAAE,CAAC,CAAC,CACtEd,SAAS,iBAAAgC,MAAA,CAAgBN,UAAU,CAACO,IAAI,4BAAAD,MAAA,CAAwBnC,IAAI,CAACoC,IAAI,MAAG,CAAC,CAC/E,CAAE,MAAOb,GAAQ,CAAE,CACjBnB,OAAO,CAACmB,GAAG,CAACc,OAAO,EAAI,6BAA6B,CAAC,CACvD,CAAC,OAAS,CACR3B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA+B,mBAAmB,CAAGpC,cAAc,CAACmC,MAAM,CAACX,UAAU,EAAI,CAC9D,KAAM,CAAAa,aAAa,CAAG/B,UAAU,GAAK,EAAE,EACrCkB,UAAU,CAACO,IAAI,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAC,EAC/Dd,UAAU,CAACgB,WAAW,EAAIhB,UAAU,CAACgB,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAE,CAErG,KAAM,CAAAG,eAAe,CAAGjC,cAAc,GAAK,KAAK,EAC9CgB,UAAU,CAACO,IAAI,CAACO,WAAW,CAAC,CAAC,CAACI,UAAU,CAAClC,cAAc,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAExE,MAAO,CAAAD,aAAa,EAAII,eAAe,CACzC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,UAAU,CAAGC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CACnC9C,cAAc,CAAC+C,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACjB,IAAI,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACd,MAAM,CAACe,OAAO,CAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAET,GAAI,CAACvD,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEN,IAAA,QAAK8D,SAAS,CAAC,iBAAiB,CAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAACC,QAAQ,CAAE,CAAC,CAAE,CAAAC,QAAA,cACzElE,IAAA,QAAK8D,SAAS,CAAC,uBAAuB,CAAAI,QAAA,cACpChE,KAAA,QAAK4D,SAAS,CAAC,eAAe,CAAAI,QAAA,eAC5BhE,KAAA,QAAK4D,SAAS,CAAC,cAAc,CAAAI,QAAA,eAC3BhE,KAAA,OAAI4D,SAAS,CAAC,aAAa,CAAAI,QAAA,EAAC,+BACG,cAAAlE,IAAA,WAAAkE,QAAA,CAAS7D,IAAI,CAACoC,IAAI,CAAS,CAAC,EACvD,CAAC,cACLzC,IAAA,WAAQmE,IAAI,CAAC,QAAQ,CAACL,SAAS,CAAC,WAAW,CAACM,OAAO,CAAE7D,OAAQ,CAAS,CAAC,EACpE,CAAC,cAENL,KAAA,QAAK4D,SAAS,CAAC,YAAY,CAAAI,QAAA,eAEzBhE,KAAA,QAAK4D,SAAS,CAAC,UAAU,CAAAI,QAAA,eACvBlE,IAAA,QAAK8D,SAAS,CAAC,UAAU,CAAAI,QAAA,cACvBlE,IAAA,UACEmE,IAAI,CAAC,MAAM,CACXL,SAAS,CAAC,cAAc,CACxBO,WAAW,CAAC,uBAAuB,CACnCC,KAAK,CAAEtD,UAAW,CAClBuD,QAAQ,CAAGC,CAAC,EAAKvD,aAAa,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,cACNtE,IAAA,QAAK8D,SAAS,CAAC,UAAU,CAAAI,QAAA,cACvBhE,KAAA,WACE4D,SAAS,CAAC,aAAa,CACvBQ,KAAK,CAAEpD,cAAe,CACtBqD,QAAQ,CAAGC,CAAC,EAAKrD,iBAAiB,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAJ,QAAA,eAEnDlE,IAAA,WAAQsE,KAAK,CAAC,KAAK,CAAAJ,QAAA,CAAC,gBAAc,CAAQ,CAAC,CAC1Cb,UAAU,CAACI,GAAG,CAACiB,QAAQ,eACtB1E,IAAA,WAAuBsE,KAAK,CAAEI,QAAS,CAAAR,QAAA,CACpCQ,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGF,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC,EAD1CH,QAEL,CACT,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,cAGN1E,IAAA,QAAK8D,SAAS,CAAC,UAAU,CAAAI,QAAA,cACvBlE,IAAA,QAAK8D,SAAS,CAAC,QAAQ,CAAAI,QAAA,cACrBhE,KAAA,QAAK4D,SAAS,CAAC,kBAAkB,CAAAI,QAAA,eAC/BlE,IAAA,WAAAkE,QAAA,CAAQ,kBAAgB,CAAQ,CAAC,IAAC,CAACtD,eAAe,CAACkE,MAAM,CAAC,MAAI,CAACpE,cAAc,CAACoE,MAAM,CAAC,uBACvF,EAAK,CAAC,CACH,CAAC,CACH,CAAC,cAGN9E,IAAA,QAAK8D,SAAS,CAAC,KAAK,CAAAI,QAAA,cAClBlE,IAAA,QAAK8D,SAAS,CAAC,QAAQ,CAAAI,QAAA,CACpBpD,OAAO,cACNd,IAAA,QAAK8D,SAAS,CAAC,kBAAkB,CAAAI,QAAA,cAC/BlE,IAAA,QAAK8D,SAAS,CAAC,gBAAgB,CAACzD,IAAI,CAAC,QAAQ,CAAA6D,QAAA,cAC3ClE,IAAA,SAAM8D,SAAS,CAAC,iBAAiB,CAAAI,QAAA,CAAC,YAAU,CAAM,CAAC,CAChD,CAAC,CACH,CAAC,cAENlE,IAAA,QAAK8D,SAAS,CAAC,kBAAkB,CAACC,KAAK,CAAE,CAAEgB,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAd,QAAA,cACjFhE,KAAA,UAAO4D,SAAS,CAAC,4BAA4B,CAAAI,QAAA,eAC3ClE,IAAA,UAAO8D,SAAS,CAAC,wBAAwB,CAAAI,QAAA,cACvChE,KAAA,OAAAgE,QAAA,eACElE,IAAA,OAAAkE,QAAA,CAAI,YAAU,CAAI,CAAC,cACnBlE,IAAA,OAAAkE,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBlE,IAAA,OAAAkE,QAAA,CAAI,QAAM,CAAI,CAAC,cACflE,IAAA,OAAAkE,QAAA,CAAI,QAAM,CAAI,CAAC,EACb,CAAC,CACA,CAAC,cACRlE,IAAA,UAAAkE,QAAA,CACGpB,mBAAmB,CAACgC,MAAM,GAAK,CAAC,cAC/B9E,IAAA,OAAAkE,QAAA,cACElE,IAAA,OAAIiF,OAAO,CAAE,CAAE,CAACnB,SAAS,CAAC,kBAAkB,CAAAI,QAAA,CAAC,sBAE7C,CAAI,CAAC,CACH,CAAC,CAELpB,mBAAmB,CAACW,GAAG,CAAEvB,UAAU,EAAK,CACtC,KAAM,CAAAgD,UAAU,CAAGjD,oBAAoB,CAACC,UAAU,CAAC,CACnD,mBACEhC,KAAA,OAAAgE,QAAA,eACElE,IAAA,OAAAkE,QAAA,cACElE,IAAA,WAAAkE,QAAA,CAAShC,UAAU,CAACO,IAAI,CAAS,CAAC,CAChC,CAAC,cACLzC,IAAA,OAAAkE,QAAA,cACElE,IAAA,UAAO8D,SAAS,CAAC,YAAY,CAAAI,QAAA,CAC1BhC,UAAU,CAACgB,WAAW,EAAI,gBAAgB,CACtC,CAAC,CACN,CAAC,cACLlD,IAAA,OAAAkE,QAAA,cACElE,IAAA,SAAM8D,SAAS,UAAAtB,MAAA,CAAW0C,UAAU,CAAG,YAAY,CAAG,cAAc,CAAG,CAAAhB,QAAA,CACpEgB,UAAU,CAAG,UAAU,CAAG,cAAc,CACrC,CAAC,CACL,CAAC,cACLlF,IAAA,OAAAkE,QAAA,CACGgB,UAAU,cACThF,KAAA,WACE4D,SAAS,CAAC,+BAA+B,CACzCM,OAAO,CAAEA,CAAA,GAAMzB,sBAAsB,CAACT,UAAU,CAAE,CAClDiD,QAAQ,CAAErE,OAAQ,CAAAoD,QAAA,eAElBlE,IAAA,MAAG8D,SAAS,CAAC,mBAAmB,CAAI,CAAC,SAEvC,EAAQ,CAAC,cAET5D,KAAA,WACE4D,SAAS,CAAC,gCAAgC,CAC1CM,OAAO,CAAEA,CAAA,GAAM/B,sBAAsB,CAACH,UAAU,CAAE,CAClDiD,QAAQ,CAAErE,OAAQ,CAAAoD,QAAA,eAElBlE,IAAA,MAAG8D,SAAS,CAAC,kBAAkB,CAAI,CAAC,SAEtC,EAAQ,CACT,CACC,CAAC,GAlCE5B,UAAU,CAACZ,EAmChB,CAAC,CAET,CAAC,CACF,CACI,CAAC,EACH,CAAC,CACL,CACN,CACE,CAAC,CACH,CAAC,cAGNtB,IAAA,QAAK8D,SAAS,CAAC,UAAU,CAAAI,QAAA,cACvBlE,IAAA,QAAK8D,SAAS,CAAC,QAAQ,CAAAI,QAAA,cACrBhE,KAAA,QAAK4D,SAAS,CAAC,cAAc,CAAAI,QAAA,eAC3BhE,KAAA,WACE4D,SAAS,CAAC,gCAAgC,CAC1CM,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,IAAK,KAAM,CAAAlC,UAAU,GAAI,CAAAY,mBAAmB,CAAE,CAC5C,GAAI,CAACb,oBAAoB,CAACC,UAAU,CAAC,CAAE,CACrC,KAAM,CAAAG,sBAAsB,CAACH,UAAU,CAAC,CAC1C,CACF,CACF,CAAE,CACFiD,QAAQ,CAAErE,OAAQ,CAAAoD,QAAA,eAElBlE,IAAA,MAAG8D,SAAS,CAAC,0BAA0B,CAAI,CAAC,sBAE9C,EAAQ,CAAC,cACT5D,KAAA,WACE4D,SAAS,CAAC,+BAA+B,CACzCM,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,IAAK,KAAM,CAAAlC,UAAU,GAAI,CAAAY,mBAAmB,CAAE,CAC5C,GAAIb,oBAAoB,CAACC,UAAU,CAAC,CAAE,CACpC,KAAM,CAAAS,sBAAsB,CAACT,UAAU,CAAC,CAC1C,CACF,CACF,CAAE,CACFiD,QAAQ,CAAErE,OAAQ,CAAAoD,QAAA,eAElBlE,IAAA,MAAG8D,SAAS,CAAC,mBAAmB,CAAI,CAAC,sBAEvC,EAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cAEN9D,IAAA,QAAK8D,SAAS,CAAC,cAAc,CAAAI,QAAA,cAC3BlE,IAAA,WAAQmE,IAAI,CAAC,QAAQ,CAACL,SAAS,CAAC,mBAAmB,CAACM,OAAO,CAAE7D,OAAQ,CAAA2D,QAAA,CAAC,OAEtE,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA/D,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}