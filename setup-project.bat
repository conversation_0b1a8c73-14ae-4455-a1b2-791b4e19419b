@echo off
echo ========================================
echo JWT Auth Project Setup
echo ========================================
echo.

echo Step 1: Starting PostgreSQL...
call start-postgres.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to start PostgreSQL!
    pause
    exit /b 1
)

echo.
echo Step 2: Waiting for PostgreSQL to be ready...
timeout /t 15 /nobreak >nul

echo.
echo Step 3: Checking database connection...
call check-db.bat
if %errorlevel% neq 0 (
    echo WARNING: Database might not be ready yet.
    echo You can continue, but backend might fail to connect.
)

echo.
echo Step 4: Setting up Backend...
cd backend
echo Installing Go dependencies...
go mod tidy
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Go dependencies!
    pause
    exit /b 1
)
cd ..

echo.
echo Step 5: Setting up Frontend...
cd frontend
echo Installing Node.js dependencies...
if not exist "node_modules" (
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Node.js dependencies!
        pause
        exit /b 1
    )
) else (
    echo Dependencies already installed.
)
cd ..

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To start the application:
echo 1. Backend: run-backend.bat
echo 2. Frontend: run-frontend.bat
echo.
echo Or use the quick start scripts:
echo - quick-start-backend.bat
echo - quick-start-frontend.bat
echo.
pause 