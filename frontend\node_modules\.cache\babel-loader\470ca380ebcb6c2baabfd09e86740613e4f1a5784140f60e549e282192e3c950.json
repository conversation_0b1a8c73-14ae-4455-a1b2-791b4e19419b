{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\RoleModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { roleManagementAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleModal = ({\n  isOpen,\n  onClose,\n  role,\n  onSuccess\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    is_active: true\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    if (role) {\n      setFormData({\n        name: role.name,\n        description: role.description || '',\n        is_active: role.is_active\n      });\n    } else {\n      setFormData({\n        name: '',\n        description: '',\n        is_active: true\n      });\n    }\n    setError('');\n  }, [role, isOpen]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      if (role) {\n        // Update role\n        await roleManagementAPI.update(role.id, formData);\n      } else {\n        // Create role\n        await roleManagementAPI.create(formData);\n      }\n      onSuccess();\n      onClose();\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    const checked = e.target.checked;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: role ? 'Edit Role' : 'Add New Role'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Role Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"name\",\n              name: \"name\",\n              value: formData.name,\n              onChange: handleChange,\n              required: true,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"e.g., admin, manager, user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"description\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"description\",\n              name: \"description\",\n              value: formData.description,\n              onChange: handleChange,\n              rows: 3,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Describe the role's purpose and responsibilities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"is_active\",\n                checked: formData.is_active,\n                onChange: handleChange,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: onClose,\n              className: \"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50\",\n              children: loading ? 'Saving...' : role ? 'Update' : 'Create'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleModal, \"Z3j3bJkcSIpLQn4SsnFrHboisTc=\");\n_c = RoleModal;\nexport default RoleModal;\nvar _c;\n$RefreshReg$(_c, \"RoleModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "roleManagementAPI", "jsxDEV", "_jsxDEV", "RoleModal", "isOpen", "onClose", "role", "onSuccess", "_s", "formData", "setFormData", "name", "description", "is_active", "loading", "setLoading", "error", "setError", "handleSubmit", "e", "preventDefault", "update", "id", "create", "err", "_err$response", "_err$response$data", "response", "data", "message", "handleChange", "value", "type", "target", "checked", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "onChange", "required", "placeholder", "rows", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/RoleModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Role } from '../../types';\nimport { roleManagementAPI } from '../../services/api';\n\ninterface RoleModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  role: Role | null;\n  onSuccess: () => void;\n}\n\nconst RoleModal: React.FC<RoleModalProps> = ({ isOpen, onClose, role, onSuccess }) => {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    is_active: true\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (role) {\n      setFormData({\n        name: role.name,\n        description: role.description || '',\n        is_active: role.is_active\n      });\n    } else {\n      setFormData({\n        name: '',\n        description: '',\n        is_active: true\n      });\n    }\n    setError('');\n  }, [role, isOpen]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      if (role) {\n        // Update role\n        await roleManagementAPI.update(role.id, formData);\n      } else {\n        // Create role\n        await roleManagementAPI.create(formData);\n      }\n      \n      onSuccess();\n      onClose();\n    } catch (err: any) {\n      setError(err.response?.data?.error || err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target;\n    const checked = (e.target as HTMLInputElement).checked;\n    \n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            {role ? 'Edit Role' : 'Add New Role'}\n          </h3>\n          \n          {error && (\n            <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n              {error}\n            </div>\n          )}\n          \n          <form onSubmit={handleSubmit}>\n            <div className=\"mb-4\">\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Role Name\n              </label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"e.g., admin, manager, user\"\n              />\n            </div>\n            \n            <div className=\"mb-4\">\n              <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description\n              </label>\n              <textarea\n                id=\"description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleChange}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Describe the role's purpose and responsibilities\"\n              />\n            </div>\n            \n            <div className=\"mb-6\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  name=\"is_active\"\n                  checked={formData.is_active}\n                  onChange={handleChange}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm font-medium text-gray-700\">Active</span>\n              </label>\n            </div>\n            \n            <div className=\"flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50\"\n              >\n                {loading ? 'Saving...' : (role ? 'Update' : 'Create')}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RoleModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,iBAAiB,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASvD,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,IAAIO,IAAI,EAAE;MACRI,WAAW,CAAC;QACVC,IAAI,EAAEL,IAAI,CAACK,IAAI;QACfC,WAAW,EAAEN,IAAI,CAACM,WAAW,IAAI,EAAE;QACnCC,SAAS,EAAEP,IAAI,CAACO;MAClB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACAI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC,EAAE,CAACX,IAAI,EAAEF,MAAM,CAAC,CAAC;EAElB,MAAMc,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,IAAIX,IAAI,EAAE;QACR;QACA,MAAMN,iBAAiB,CAACqB,MAAM,CAACf,IAAI,CAACgB,EAAE,EAAEb,QAAQ,CAAC;MACnD,CAAC,MAAM;QACL;QACA,MAAMT,iBAAiB,CAACuB,MAAM,CAACd,QAAQ,CAAC;MAC1C;MAEAF,SAAS,CAAC,CAAC;MACXF,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOmB,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBT,QAAQ,CAAC,EAAAQ,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBV,KAAK,KAAIQ,GAAG,CAACK,OAAO,CAAC;IACpD,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,YAAY,GAAIX,CAA4D,IAAK;IACrF,MAAM;MAAER,IAAI;MAAEoB,KAAK;MAAEC;IAAK,CAAC,GAAGb,CAAC,CAACc,MAAM;IACtC,MAAMC,OAAO,GAAIf,CAAC,CAACc,MAAM,CAAsBC,OAAO;IAEtDxB,WAAW,CAACyB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACxB,IAAI,GAAGqB,IAAI,KAAK,UAAU,GAAGE,OAAO,GAAGH;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAI,CAAC3B,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKkC,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFnC,OAAA;MAAKkC,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFnC,OAAA;QAAKkC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnC,OAAA;UAAIkC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EACnD/B,IAAI,GAAG,WAAW,GAAG;QAAc;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAEJzB,KAAK,iBACJd,OAAA;UAAKkC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EAClFrB;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDvC,OAAA;UAAMwC,QAAQ,EAAExB,YAAa;UAAAmB,QAAA,gBAC3BnC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAOyC,OAAO,EAAC,MAAM;cAACP,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAE/E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvC,OAAA;cACE8B,IAAI,EAAC,MAAM;cACXV,EAAE,EAAC,MAAM;cACTX,IAAI,EAAC,MAAM;cACXoB,KAAK,EAAEtB,QAAQ,CAACE,IAAK;cACrBiC,QAAQ,EAAEd,YAAa;cACvBe,QAAQ;cACRT,SAAS,EAAC,wGAAwG;cAClHU,WAAW,EAAC;YAA4B;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAOyC,OAAO,EAAC,aAAa;cAACP,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEtF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvC,OAAA;cACEoB,EAAE,EAAC,aAAa;cAChBX,IAAI,EAAC,aAAa;cAClBoB,KAAK,EAAEtB,QAAQ,CAACG,WAAY;cAC5BgC,QAAQ,EAAEd,YAAa;cACvBiB,IAAI,EAAE,CAAE;cACRX,SAAS,EAAC,wGAAwG;cAClHU,WAAW,EAAC;YAAkD;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBnC,OAAA;cAAOkC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCnC,OAAA;gBACE8B,IAAI,EAAC,UAAU;gBACfrB,IAAI,EAAC,WAAW;gBAChBuB,OAAO,EAAEzB,QAAQ,CAACI,SAAU;gBAC5B+B,QAAQ,EAAEd,YAAa;gBACvBM,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACFvC,OAAA;gBAAMkC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENvC,OAAA;YAAKkC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCnC,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbgB,OAAO,EAAE3C,OAAQ;cACjB+B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvC,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbiB,QAAQ,EAAEnC,OAAQ;cAClBsB,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAE5FvB,OAAO,GAAG,WAAW,GAAIR,IAAI,GAAG,QAAQ,GAAG;YAAS;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA7IIL,SAAmC;AAAA+C,EAAA,GAAnC/C,SAAmC;AA+IzC,eAAeA,SAAS;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}