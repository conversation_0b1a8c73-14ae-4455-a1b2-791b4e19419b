{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\UserPermissionManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { userManagementAPI, roleManagementAPI, permissionManagementAPI } from '../../services/api';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserPermissionManagement = () => {\n  _s();\n  // State for active tab\n  const [activeTab, setActiveTab] = useState('users');\n\n  // Users state\n  const [users, setUsers] = useState([]);\n  const [usersPagination, setUsersPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState([]);\n  const [rolesPagination, setRolesPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState([]);\n  const [permissionsPagination, setPermissionsPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [editingRole, setEditingRole] = useState(null);\n  const [editingPermission, setEditingPermission] = useState(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          filters: [{\n            field: 'username',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }, {\n            field: 'email',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'username',\n          order_dir: 'asc'\n        };\n        response = await userManagementAPI.search(searchRequest);\n      } else {\n        response = await userManagementAPI.list({\n          page,\n          page_size: usersPagination.page_size,\n          order_by: 'username',\n          order_dir: 'asc'\n        });\n      }\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(`Failed to load users: ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadUsers(1, usersSearchTerm);\n  };\n  const handleUsersPageChange = newPage => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: rolesPagination.page_size,\n          filters: [{\n            field: 'name',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await roleManagementAPI.search(searchRequest);\n      } else {\n        response = await roleManagementAPI.list({\n          page,\n          page_size: rolesPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(`Failed to load roles: ${((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadRoles(1, rolesSearchTerm);\n  };\n  const handleRolesPageChange = newPage => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: permissionsPagination.page_size,\n          filters: [{\n            field: 'name',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await permissionManagementAPI.search(searchRequest);\n      } else {\n        response = await permissionManagementAPI.list({\n          page,\n          page_size: permissionsPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(`Failed to load permissions: ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n  const handlePermissionsPageChange = newPage => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async id => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n    try {\n      await userManagementAPI.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      setError(`Failed to delete user: ${((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n    }\n  };\n  const handleDeleteRole = async id => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n    try {\n      await roleManagementAPI.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      setError(`Failed to delete role: ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || err.message}`);\n    }\n  };\n  const handleDeletePermission = async id => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n    try {\n      await permissionManagementAPI.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      setError(`Failed to delete permission: ${((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error) || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination, onPageChange) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(i),\n        className: `px-3 py-1 mx-1 rounded ${i === pagination.page ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n        children: i\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600\",\n        children: [\"Showing \", (pagination.page - 1) * pagination.page_size + 1, \" to\", ' ', Math.min(pagination.page * pagination.page_size, pagination.total_items), \" of\", ' ', pagination.total_items, \" entries\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page - 1),\n          disabled: pagination.page <= 1,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), pages, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page + 1),\n          disabled: pagination.page >= pagination.total_pages,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-8\",\n      children: \"User Permission Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b border-gray-200 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"-mb-px flex space-x-8\",\n        children: [{\n          key: 'users',\n          label: 'Users'\n        }, {\n          key: 'roles',\n          label: 'Roles'\n        }, {\n          key: 'permissions',\n          label: 'Permissions'\n        }, {\n          key: 'assignments',\n          label: 'Assignments'\n        }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n          children: tab.label\n        }, tab.key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingUser(null);\n            setShowUserModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search users by username or email...\",\n          value: usersSearchTerm,\n          onChange: e => setUsersSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleUsersSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleUsersSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setUsersSearchTerm('');\n            loadUsers(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 11\n      }, this), usersLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading users...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: user.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: new Date(user.created_at).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingUser(user);\n                      setShowUserModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteUser(user.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 25\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 15\n        }, this), renderPagination(usersPagination, handleUsersPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 9\n    }, this), activeTab === 'roles' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Role Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingRole(null);\n            setShowRoleModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Role\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search roles by name...\",\n          value: rolesSearchTerm,\n          onChange: e => setRolesSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleRolesSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRolesSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setRolesSearchTerm('');\n            loadRoles(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 11\n      }, this), rolesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading roles...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: roles.map(role => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: role.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: role.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: role.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: role.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: new Date(role.created_at).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRole(role);\n                      setShowRoleModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteRole(role.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 25\n                }, this)]\n              }, role.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 15\n        }, this), renderPagination(rolesPagination, handleRolesPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 9\n    }, this), activeTab === 'permissions' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Permission Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingPermission(null);\n            setShowPermissionModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Permission\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search permissions by name...\",\n          value: permissionsSearchTerm,\n          onChange: e => setPermissionsSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handlePermissionsSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePermissionsSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setPermissionsSearchTerm('');\n            loadPermissions(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 11\n      }, this), permissionsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading permissions...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: permissions.map(permission => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: permission.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: permission.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: permission.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: permission.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: new Date(permission.created_at).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingPermission(permission);\n                      setShowPermissionModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeletePermission(permission.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 25\n                }, this)]\n              }, permission.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 15\n        }, this), renderPagination(permissionsPagination, handlePermissionsPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(UserModal, {\n      isOpen: showUserModal,\n      onClose: () => setShowUserModal(false),\n      user: editingUser,\n      onSuccess: () => {\n        setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n        loadUsers(usersPagination.page, usersSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RoleModal, {\n      isOpen: showRoleModal,\n      onClose: () => setShowRoleModal(false),\n      role: editingRole,\n      onSuccess: () => {\n        setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n        loadRoles(rolesPagination.page, rolesSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 748,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PermissionModal, {\n      isOpen: showPermissionModal,\n      onClose: () => setShowPermissionModal(false),\n      permission: editingPermission,\n      onSuccess: () => {\n        setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n        loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 758,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 321,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPermissionManagement, \"EXXLj32ClIuSM8b9dg4KjmQqFqU=\");\n_c = UserPermissionManagement;\nexport default UserPermissionManagement;\nvar _c;\n$RefreshReg$(_c, \"UserPermissionManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "userManagementAPI", "roleManagementAPI", "permissionManagementAPI", "UserModal", "RoleModal", "PermissionModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserPermissionManagement", "_s", "activeTab", "setActiveTab", "users", "setUsers", "usersPagination", "setUsersPagination", "page", "page_size", "total_items", "total_pages", "usersLoading", "setUsersLoading", "usersSearchTerm", "setUsersSearchTerm", "roles", "setRoles", "rolesPagination", "setRolesPagination", "rolesLoading", "setRolesLoading", "rolesSearchTerm", "setRolesSearchTerm", "permissions", "setPermissions", "permissionsPagination", "setPermissionsPagination", "permissionsLoading", "setPermissionsLoading", "permissionsSearchTerm", "setPermissionsSearchTerm", "message", "setMessage", "error", "setError", "showUserModal", "setShowUserModal", "showRoleModal", "setShowRoleModal", "showPermissionModal", "setShowPermissionModal", "editingUser", "setEditingUser", "editingRole", "setEditingRole", "editingPermission", "setEditingPermission", "loadUsers", "loadRoles", "loadPermissions", "searchTerm", "response", "trim", "searchRequest", "filters", "field", "operator", "value", "order_by", "order_dir", "search", "list", "data", "pagination", "err", "_err$response", "_err$response$data", "handleUsersSearch", "prev", "handleUsersPageChange", "newPage", "_err$response2", "_err$response2$data", "handleRolesSearch", "handleRolesPageChange", "_err$response3", "_err$response3$data", "handlePermissionsSearch", "handlePermissionsPageChange", "handleDeleteUser", "id", "window", "confirm", "delete", "_err$response4", "_err$response4$data", "handleDeleteRole", "_err$response5", "_err$response5$data", "handleDeletePermission", "_err$response6", "_err$response6$data", "renderPagination", "onPageChange", "pages", "i", "push", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Math", "min", "disabled", "key", "label", "map", "tab", "type", "placeholder", "onChange", "e", "target", "onKeyPress", "user", "username", "email", "is_active", "Date", "created_at", "toLocaleDateString", "role", "name", "description", "permission", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/UserPermissionManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport {\n  userManagementAPI,\n  roleManagementAPI,\n  permissionManagementAPI,\n  adminAPI,\n  PaginationInfo,\n  SearchRequest\n} from '../../services/api';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\n\ninterface UserPermissionManagementProps { }\n\nconst UserPermissionManagement: React.FC<UserPermissionManagementProps> = () => {\n  // State for active tab\n  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'permissions' | 'assignments'>('users');\n\n  // Users state\n  const [users, setUsers] = useState<User[]>([]);\n  const [usersPagination, setUsersPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [rolesPagination, setRolesPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [permissionsPagination, setPermissionsPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          filters: [\n            {\n              field: 'username',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            },\n            {\n              field: 'email',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'username',\n          order_dir: 'asc'\n        };\n        response = await userManagementAPI.search(searchRequest);\n      } else {\n        response = await userManagementAPI.list({\n          page,\n          page_size: usersPagination.page_size,\n          order_by: 'username',\n          order_dir: 'asc'\n        });\n      }\n\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load users: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({ ...prev, page: 1 }));\n    loadUsers(1, usersSearchTerm);\n  };\n\n  const handleUsersPageChange = (newPage: number) => {\n    setUsersPagination(prev => ({ ...prev, page: newPage }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: rolesPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await roleManagementAPI.search(searchRequest);\n      } else {\n        response = await roleManagementAPI.list({\n          page,\n          page_size: rolesPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load roles: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({ ...prev, page: 1 }));\n    loadRoles(1, rolesSearchTerm);\n  };\n\n  const handleRolesPageChange = (newPage: number) => {\n    setRolesPagination(prev => ({ ...prev, page: newPage }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: permissionsPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await permissionManagementAPI.search(searchRequest);\n      } else {\n        response = await permissionManagementAPI.list({\n          page,\n          page_size: permissionsPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load permissions: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({ ...prev, page: 1 }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n\n  const handlePermissionsPageChange = (newPage: number) => {\n    setPermissionsPagination(prev => ({ ...prev, page: newPage }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n\n    try {\n      await userManagementAPI.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete user: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeleteRole = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n\n    try {\n      await roleManagementAPI.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeletePermission = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n\n    try {\n      await permissionManagementAPI.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination: PaginationInfo, onPageChange: (page: number) => void) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(\n        <button\n          key={i}\n          onClick={() => onPageChange(i)}\n          className={`px-3 py-1 mx-1 rounded ${i === pagination.page\n            ? 'bg-blue-500 text-white'\n            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n        >\n          {i}\n        </button>\n      );\n    }\n\n    return (\n      <div className=\"flex items-center justify-between mt-4\">\n        <div className=\"text-sm text-gray-600\">\n          Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}\n          {Math.min(pagination.page * pagination.page_size, pagination.total_items)} of{' '}\n          {pagination.total_items} entries\n        </div>\n        <div className=\"flex items-center\">\n          <button\n            onClick={() => onPageChange(pagination.page - 1)}\n            disabled={pagination.page <= 1}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Previous\n          </button>\n          {pages}\n          <button\n            onClick={() => onPageChange(pagination.page + 1)}\n            disabled={pagination.page >= pagination.total_pages}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Next\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">User Permission Management</h1>\n\n      {/* Messages */}\n      {message && (\n        <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n          {message}\n        </div>\n      )}\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {error}\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {[\n            { key: 'users', label: 'Users' },\n            { key: 'roles', label: 'Roles' },\n            { key: 'permissions', label: 'Permissions' },\n            { key: 'assignments', label: 'Assignments' }\n          ].map((tab) => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'users' && (\n        <div>\n          {/* Users Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">User Management</h2>\n            <button\n              onClick={() => {\n                setEditingUser(null);\n                setShowUserModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add User\n            </button>\n          </div>\n\n          {/* Users Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search users by username or email...\"\n              value={usersSearchTerm}\n              onChange={(e) => setUsersSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleUsersSearch()}\n            />\n            <button\n              onClick={handleUsersSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setUsersSearchTerm('');\n                loadUsers(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Users Table */}\n          {usersLoading ? (\n            <div className=\"text-center py-8\">Loading users...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Username\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Email\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <tr key={user.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {user.username}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.email}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {new Date(user.created_at).toLocaleDateString()}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingUser(user);\n                              setShowUserModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteUser(user.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Users Pagination */}\n              {renderPagination(usersPagination, handleUsersPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Roles Tab */}\n      {activeTab === 'roles' && (\n        <div>\n          {/* Roles Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Role Management</h2>\n            <button\n              onClick={() => {\n                setEditingRole(null);\n                setShowRoleModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Role\n            </button>\n          </div>\n\n          {/* Roles Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search roles by name...\"\n              value={rolesSearchTerm}\n              onChange={(e) => setRolesSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleRolesSearch()}\n            />\n            <button\n              onClick={handleRolesSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setRolesSearchTerm('');\n                loadRoles(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Roles Table */}\n          {rolesLoading ? (\n            <div className=\"text-center py-8\">Loading roles...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {roles.map((role) => (\n                      <tr key={role.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {role.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {role.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {role.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {new Date(role.created_at).toLocaleDateString()}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingRole(role);\n                              setShowRoleModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteRole(role.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Roles Pagination */}\n              {renderPagination(rolesPagination, handleRolesPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Permissions Tab */}\n      {activeTab === 'permissions' && (\n        <div>\n          {/* Permissions Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Permission Management</h2>\n            <button\n              onClick={() => {\n                setEditingPermission(null);\n                setShowPermissionModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Permission\n            </button>\n          </div>\n\n          {/* Permissions Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search permissions by name...\"\n              value={permissionsSearchTerm}\n              onChange={(e) => setPermissionsSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handlePermissionsSearch()}\n            />\n            <button\n              onClick={handlePermissionsSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setPermissionsSearchTerm('');\n                loadPermissions(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Permissions Table */}\n          {permissionsLoading ? (\n            <div className=\"text-center py-8\">Loading permissions...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {permissions.map((permission) => (\n                      <tr key={permission.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {permission.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {permission.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active\n                              ? 'bg-green-100 text-green-800'\n                              : 'bg-red-100 text-red-800'\n                            }`}>\n                            {permission.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {new Date(permission.created_at).toLocaleDateString()}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingPermission(permission);\n                              setShowPermissionModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeletePermission(permission.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Permissions Pagination */}\n              {renderPagination(permissionsPagination, handlePermissionsPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Modals */}\n      <UserModal\n        isOpen={showUserModal}\n        onClose={() => setShowUserModal(false)}\n        user={editingUser}\n        onSuccess={() => {\n          setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n          loadUsers(usersPagination.page, usersSearchTerm);\n        }}\n      />\n\n      <RoleModal\n        isOpen={showRoleModal}\n        onClose={() => setShowRoleModal(false)}\n        role={editingRole}\n        onSuccess={() => {\n          setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n          loadRoles(rolesPagination.page, rolesSearchTerm);\n        }}\n      />\n\n      <PermissionModal\n        isOpen={showPermissionModal}\n        onClose={() => setShowPermissionModal(false)}\n        permission={editingPermission}\n        onSuccess={() => {\n          setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n          loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n        }}\n      />\n    </div>\n  );\n};\n\nexport default UserPermissionManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,uBAAuB,QAIlB,oBAAoB;AAC3B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIhD,MAAMC,wBAAiE,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9E;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAoD,OAAO,CAAC;;EAEtG;EACA,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAiB;IACrEoB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAiB;IACrEoB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACsC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvC,QAAQ,CAAiB;IACjFoB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;;EAEtE;EACA,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC0D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3D,QAAQ,CAAoB,IAAI,CAAC;;EAEnF;EACAC,SAAS,CAAC,MAAM;IACd,QAAQa,SAAS;MACf,KAAK,OAAO;QACV8C,SAAS,CAAC,CAAC;QACX;MACF,KAAK,OAAO;QACVC,SAAS,CAAC,CAAC;QACX;MACF,KAAK,aAAa;QAChBC,eAAe,CAAC,CAAC;QACjB;MACF,KAAK,aAAa;QAChBF,SAAS,CAAC,CAAC,CAAC,CAAC;QACbC,SAAS,CAAC,CAAC,CAAC,CAAC;QACb;IACJ;EACF,CAAC,EAAE,CAAC/C,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM8C,SAAS,GAAG,MAAAA,CAAOxC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrDtC,eAAe,CAAC,IAAI,CAAC;IACrBsB,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG,SAAS;UACpC8C,OAAO,EAAE,CACP;YACEC,KAAK,EAAE,UAAU;YACjBC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,EACD;YACEK,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,CACF;UACDQ,QAAQ,EAAE,UAAU;UACpBC,SAAS,EAAE;QACb,CAAC;QACDR,QAAQ,GAAG,MAAM9D,iBAAiB,CAACuE,MAAM,CAACP,aAAa,CAAC;MAC1D,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAM9D,iBAAiB,CAACwE,IAAI,CAAC;UACtCtD,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG,SAAS;UACpCkD,QAAQ,EAAE,UAAU;UACpBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEAvD,QAAQ,CAAC+C,QAAQ,CAACW,IAAI,CAACA,IAAI,CAAC;MAC5BxD,kBAAkB,CAAC6C,QAAQ,CAACW,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBhC,QAAQ,CAAC,yBAAyB,EAAA+B,aAAA,GAAAD,GAAG,CAACb,QAAQ,cAAAc,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBjC,KAAK,KAAI+B,GAAG,CAACjC,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRnB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMuD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7D,kBAAkB,CAAC8D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDwC,SAAS,CAAC,CAAC,EAAElC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMwD,qBAAqB,GAAIC,OAAe,IAAK;IACjDhE,kBAAkB,CAAC8D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,IAAI,EAAE+D;IAAQ,CAAC,CAAC,CAAC;IACxDvB,SAAS,CAACuB,OAAO,EAAEzD,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAMmC,SAAS,GAAG,MAAAA,CAAOzC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrD9B,eAAe,CAAC,IAAI,CAAC;IACrBc,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAES,eAAe,CAACT,SAAS;UACpC8C,OAAO,EAAE,CACP;YACEC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,CACF;UACDQ,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC;QACDR,QAAQ,GAAG,MAAM7D,iBAAiB,CAACsE,MAAM,CAACP,aAAa,CAAC;MAC1D,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAM7D,iBAAiB,CAACuE,IAAI,CAAC;UACtCtD,IAAI;UACJC,SAAS,EAAES,eAAe,CAACT,SAAS;UACpCkD,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEA3C,QAAQ,CAACmC,QAAQ,CAACW,IAAI,CAACA,IAAI,CAAC;MAC5B5C,kBAAkB,CAACiC,QAAQ,CAACW,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAO,cAAA,EAAAC,mBAAA;MACjBtC,QAAQ,CAAC,yBAAyB,EAAAqC,cAAA,GAAAP,GAAG,CAACb,QAAQ,cAAAoB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcT,IAAI,cAAAU,mBAAA,uBAAlBA,mBAAA,CAAoBvC,KAAK,KAAI+B,GAAG,CAACjC,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRX,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMqD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvD,kBAAkB,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDyC,SAAS,CAAC,CAAC,EAAE3B,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMqD,qBAAqB,GAAIJ,OAAe,IAAK;IACjDpD,kBAAkB,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,IAAI,EAAE+D;IAAQ,CAAC,CAAC,CAAC;IACxDtB,SAAS,CAACsB,OAAO,EAAEjD,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAG,MAAAA,CAAO1C,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IAC3DtB,qBAAqB,CAAC,IAAI,CAAC;IAC3BM,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAEiB,qBAAqB,CAACjB,SAAS;UAC1C8C,OAAO,EAAE,CACP;YACEC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIP,UAAU;UACvB,CAAC,CACF;UACDQ,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC;QACDR,QAAQ,GAAG,MAAM5D,uBAAuB,CAACqE,MAAM,CAACP,aAAa,CAAC;MAChE,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAM5D,uBAAuB,CAACsE,IAAI,CAAC;UAC5CtD,IAAI;UACJC,SAAS,EAAEiB,qBAAqB,CAACjB,SAAS;UAC1CkD,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEAnC,cAAc,CAAC2B,QAAQ,CAACW,IAAI,CAACA,IAAI,CAAC;MAClCpC,wBAAwB,CAACyB,QAAQ,CAACW,IAAI,CAACC,UAAU,CAAC;IACpD,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAW,cAAA,EAAAC,mBAAA;MACjB1C,QAAQ,CAAC,+BAA+B,EAAAyC,cAAA,GAAAX,GAAG,CAACb,QAAQ,cAAAwB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcb,IAAI,cAAAc,mBAAA,uBAAlBA,mBAAA,CAAoB3C,KAAK,KAAI+B,GAAG,CAACjC,OAAO,EAAE,CAAC;IACrF,CAAC,SAAS;MACRH,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMiD,uBAAuB,GAAGA,CAAA,KAAM;IACpCnD,wBAAwB,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IACxD0C,eAAe,CAAC,CAAC,EAAEpB,qBAAqB,CAAC;EAC3C,CAAC;EAED,MAAMiD,2BAA2B,GAAIR,OAAe,IAAK;IACvD5C,wBAAwB,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,IAAI,EAAE+D;IAAQ,CAAC,CAAC,CAAC;IAC9DrB,eAAe,CAACqB,OAAO,EAAEzC,qBAAqB,CAAC;EACjD,CAAC;;EAED;EACA,MAAMkD,gBAAgB,GAAG,MAAOC,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAM7F,iBAAiB,CAAC8F,MAAM,CAACH,EAAE,CAAC;MAClChD,UAAU,CAAC,2BAA2B,CAAC;MACvCe,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;IAClD,CAAC,CAAC,OAAOmD,GAAQ,EAAE;MAAA,IAAAoB,cAAA,EAAAC,mBAAA;MACjBnD,QAAQ,CAAC,0BAA0B,EAAAkD,cAAA,GAAApB,GAAG,CAACb,QAAQ,cAAAiC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAActB,IAAI,cAAAuB,mBAAA,uBAAlBA,mBAAA,CAAoBpD,KAAK,KAAI+B,GAAG,CAACjC,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAMuD,gBAAgB,GAAG,MAAON,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAM5F,iBAAiB,CAAC6F,MAAM,CAACH,EAAE,CAAC;MAClChD,UAAU,CAAC,2BAA2B,CAAC;MACvCgB,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;IAClD,CAAC,CAAC,OAAO2C,GAAQ,EAAE;MAAA,IAAAuB,cAAA,EAAAC,mBAAA;MACjBtD,QAAQ,CAAC,0BAA0B,EAAAqD,cAAA,GAAAvB,GAAG,CAACb,QAAQ,cAAAoC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczB,IAAI,cAAA0B,mBAAA,uBAAlBA,mBAAA,CAAoBvD,KAAK,KAAI+B,GAAG,CAACjC,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAM0D,sBAAsB,GAAG,MAAOT,EAAU,IAAK;IACnD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;IAEzE,IAAI;MACF,MAAM3F,uBAAuB,CAAC4F,MAAM,CAACH,EAAE,CAAC;MACxChD,UAAU,CAAC,iCAAiC,CAAC;MAC7CiB,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;IACpE,CAAC,CAAC,OAAOmC,GAAQ,EAAE;MAAA,IAAA0B,cAAA,EAAAC,mBAAA;MACjBzD,QAAQ,CAAC,gCAAgC,EAAAwD,cAAA,GAAA1B,GAAG,CAACb,QAAQ,cAAAuC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoB1D,KAAK,KAAI+B,GAAG,CAACjC,OAAO,EAAE,CAAC;IACtF;EACF,CAAC;;EAED;EACA,MAAM6D,gBAAgB,GAAGA,CAAC7B,UAA0B,EAAE8B,YAAoC,KAAK;IAC7F,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIhC,UAAU,CAACrD,WAAW,EAAEqF,CAAC,EAAE,EAAE;MAChDD,KAAK,CAACE,IAAI,cACRpG,OAAA;QAEEqG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACE,CAAC,CAAE;QAC/BG,SAAS,EAAE,0BAA0BH,CAAC,KAAKhC,UAAU,CAACxD,IAAI,GACtD,wBAAwB,GACxB,6CAA6C,EAC5C;QAAA4F,QAAA,EAEJJ;MAAC,GAPGA,CAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQA,CACV,CAAC;IACH;IAEA,oBACE3G,OAAA;MAAKsG,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDvG,OAAA;QAAKsG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC7B,EAAE,CAACpC,UAAU,CAACxD,IAAI,GAAG,CAAC,IAAIwD,UAAU,CAACvD,SAAS,GAAI,CAAC,EAAC,KAAG,EAAC,GAAG,EAClEgG,IAAI,CAACC,GAAG,CAAC1C,UAAU,CAACxD,IAAI,GAAGwD,UAAU,CAACvD,SAAS,EAAEuD,UAAU,CAACtD,WAAW,CAAC,EAAC,KAAG,EAAC,GAAG,EAChFsD,UAAU,CAACtD,WAAW,EAAC,UAC1B;MAAA;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN3G,OAAA;QAAKsG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvG,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAAC9B,UAAU,CAACxD,IAAI,GAAG,CAAC,CAAE;UACjDmG,QAAQ,EAAE3C,UAAU,CAACxD,IAAI,IAAI,CAAE;UAC/B2F,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRT,KAAK,eACNlG,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAAC9B,UAAU,CAACxD,IAAI,GAAG,CAAC,CAAE;UACjDmG,QAAQ,EAAE3C,UAAU,CAACxD,IAAI,IAAIwD,UAAU,CAACrD,WAAY;UACpDwF,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACE3G,OAAA;IAAKsG,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1CvG,OAAA;MAAIsG,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAGtExE,OAAO,iBACNnC,OAAA;MAAKsG,SAAS,EAAC,4EAA4E;MAAAC,QAAA,EACxFpE;IAAO;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACAtE,KAAK,iBACJrC,OAAA;MAAKsG,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClFlE;IAAK;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3G,OAAA;MAAKsG,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CvG,OAAA;QAAKsG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACnC,CACC;UAAEQ,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,EAC5C;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,CAC7C,CAACC,GAAG,CAAEC,GAAG,iBACRlH,OAAA;UAEEqG,OAAO,EAAEA,CAAA,KAAM/F,YAAY,CAAC4G,GAAG,CAACH,GAAU,CAAE;UAC5CT,SAAS,EAAE,4CAA4CjG,SAAS,KAAK6G,GAAG,CAACH,GAAG,GACxE,+BAA+B,GAC/B,4EAA4E,EAC3E;UAAAR,QAAA,EAEJW,GAAG,CAACF;QAAK,GAPLE,GAAG,CAACH,GAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtG,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAAuG,QAAA,gBAEEvG,OAAA;QAAKsG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDvG,OAAA;UAAIsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1D3G,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAM;YACbvD,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACF8D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN3G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvG,OAAA;UACEmH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,sCAAsC;UAClDvD,KAAK,EAAE5C,eAAgB;UACvBoG,QAAQ,EAAGC,CAAC,IAAKpG,kBAAkB,CAACoG,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;UACpDyC,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAIxC,iBAAiB,CAAC;QAAE;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF3G,OAAA;UACEqG,OAAO,EAAE9B,iBAAkB;UAC3B+B,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3G,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAM;YACbnF,kBAAkB,CAAC,EAAE,CAAC;YACtBiC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFmD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL5F,YAAY,gBACXf,OAAA;QAAKsG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExD3G,OAAA,CAAAE,SAAA;QAAAqG,QAAA,gBACEvG,OAAA;UAAKsG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvG,OAAA;YAAOsG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3DvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3BvG,OAAA;gBAAAuG,QAAA,gBACEvG,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR3G,OAAA;cAAOsG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDhG,KAAK,CAAC0G,GAAG,CAAEQ,IAAI,iBACdzH,OAAA;gBAAAuG,QAAA,gBACEvG,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACrC;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EkB,IAAI,CAACC;gBAAQ;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACE;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCvG,OAAA;oBAAMsG,SAAS,EAAE,4DAA4DmB,IAAI,CAACG,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAArB,QAAA,EACFkB,IAAI,CAACG,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D,IAAIsB,IAAI,CAACJ,IAAI,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7DvG,OAAA;oBACEqG,OAAO,EAAEA,CAAA,KAAM;sBACbvD,cAAc,CAAC2E,IAAI,CAAC;sBACpBjF,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACF8D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3G,OAAA;oBACEqG,OAAO,EAAEA,CAAA,KAAMlB,gBAAgB,CAACsC,IAAI,CAACrC,EAAE,CAAE;oBACzCkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEc,IAAI,CAACrC,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAACvF,eAAe,EAAEgE,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGAtG,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAAuG,QAAA,gBAEEvG,OAAA;QAAKsG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDvG,OAAA;UAAIsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1D3G,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAM;YACbrD,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACF4D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN3G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvG,OAAA;UACEmH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,yBAAyB;UACrCvD,KAAK,EAAEpC,eAAgB;UACvB4F,QAAQ,EAAGC,CAAC,IAAK5F,kBAAkB,CAAC4F,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;UACpDyC,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAIlC,iBAAiB,CAAC;QAAE;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF3G,OAAA;UACEqG,OAAO,EAAExB,iBAAkB;UAC3ByB,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3G,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAM;YACb3E,kBAAkB,CAAC,EAAE,CAAC;YACtB0B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFkD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLpF,YAAY,gBACXvB,OAAA;QAAKsG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExD3G,OAAA,CAAAE,SAAA;QAAAqG,QAAA,gBACEvG,OAAA;UAAKsG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvG,OAAA;YAAOsG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3DvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3BvG,OAAA;gBAAAuG,QAAA,gBACEvG,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR3G,OAAA;cAAOsG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDpF,KAAK,CAAC8F,GAAG,CAAEe,IAAI,iBACdhI,OAAA;gBAAAuG,QAAA,gBACEvG,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAAC5C;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EyB,IAAI,CAACC;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAACE,WAAW,IAAI;gBAAgB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCvG,OAAA;oBAAMsG,SAAS,EAAE,4DAA4D0B,IAAI,CAACJ,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAArB,QAAA,EACFyB,IAAI,CAACJ,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D,IAAIsB,IAAI,CAACG,IAAI,CAACF,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7DvG,OAAA;oBACEqG,OAAO,EAAEA,CAAA,KAAM;sBACbrD,cAAc,CAACgF,IAAI,CAAC;sBACpBtF,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACF4D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3G,OAAA;oBACEqG,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACsC,IAAI,CAAC5C,EAAE,CAAE;oBACzCkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEqB,IAAI,CAAC5C,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAAC3E,eAAe,EAAEyD,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGAtG,SAAS,KAAK,aAAa,iBAC1BL,OAAA;MAAAuG,QAAA,gBAEEvG,OAAA;QAAKsG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDvG,OAAA;UAAIsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE3G,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAM;YACbnD,oBAAoB,CAAC,IAAI,CAAC;YAC1BN,sBAAsB,CAAC,IAAI,CAAC;UAC9B,CAAE;UACF0D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN3G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvG,OAAA;UACEmH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,+BAA+B;UAC3CvD,KAAK,EAAE5B,qBAAsB;UAC7BoF,QAAQ,EAAGC,CAAC,IAAKpF,wBAAwB,CAACoF,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;UAC1DyC,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAI9B,uBAAuB,CAAC;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACF3G,OAAA;UACEqG,OAAO,EAAEpB,uBAAwB;UACjCqB,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3G,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAM;YACbnE,wBAAwB,CAAC,EAAE,CAAC;YAC5BmB,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC;UACxB,CAAE;UACFiD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL5E,kBAAkB,gBACjB/B,OAAA;QAAKsG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAE9D3G,OAAA,CAAAE,SAAA;QAAAqG,QAAA,gBACEvG,OAAA;UAAKsG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvG,OAAA;YAAOsG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3DvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3BvG,OAAA;gBAAAuG,QAAA,gBACEvG,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR3G,OAAA;cAAOsG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjD5E,WAAW,CAACsF,GAAG,CAAEkB,UAAU,iBAC1BnI,OAAA;gBAAAuG,QAAA,gBACEvG,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAAC/C;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1E4B,UAAU,CAACF;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAACD,WAAW,IAAI;gBAAgB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCvG,OAAA;oBAAMsG,SAAS,EAAE,4DAA4D6B,UAAU,CAACP,SAAS,GAC3F,6BAA6B,GAC7B,yBAAyB,EAC1B;oBAAArB,QAAA,EACF4B,UAAU,CAACP,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D,IAAIsB,IAAI,CAACM,UAAU,CAACL,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACL3G,OAAA;kBAAIsG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7DvG,OAAA;oBACEqG,OAAO,EAAEA,CAAA,KAAM;sBACbnD,oBAAoB,CAACiF,UAAU,CAAC;sBAChCvF,sBAAsB,CAAC,IAAI,CAAC;oBAC9B,CAAE;oBACF0D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3G,OAAA;oBACEqG,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACsC,UAAU,CAAC/C,EAAE,CAAE;oBACrDkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEwB,UAAU,CAAC/C,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsClB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAACnE,qBAAqB,EAAEqD,2BAA2B,CAAC;MAAA,eACrE,CACH;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD3G,OAAA,CAACJ,SAAS;MACRwI,MAAM,EAAE7F,aAAc;MACtB8F,OAAO,EAAEA,CAAA,KAAM7F,gBAAgB,CAAC,KAAK,CAAE;MACvCiF,IAAI,EAAE5E,WAAY;MAClByF,SAAS,EAAEA,CAAA,KAAM;QACflG,UAAU,CAACS,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFM,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;MAClD;IAAE;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF3G,OAAA,CAACH,SAAS;MACRuI,MAAM,EAAE3F,aAAc;MACtB4F,OAAO,EAAEA,CAAA,KAAM3F,gBAAgB,CAAC,KAAK,CAAE;MACvCsF,IAAI,EAAEjF,WAAY;MAClBuF,SAAS,EAAEA,CAAA,KAAM;QACflG,UAAU,CAACW,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFK,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;MAClD;IAAE;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF3G,OAAA,CAACF,eAAe;MACdsI,MAAM,EAAEzF,mBAAoB;MAC5B0F,OAAO,EAAEA,CAAA,KAAMzF,sBAAsB,CAAC,KAAK,CAAE;MAC7CuF,UAAU,EAAElF,iBAAkB;MAC9BqF,SAAS,EAAEA,CAAA,KAAM;QACflG,UAAU,CAACa,iBAAiB,GAAG,iCAAiC,GAAG,iCAAiC,CAAC;QACrGI,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;MACpE;IAAE;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvG,EAAA,CAhvBID,wBAAiE;AAAAoI,EAAA,GAAjEpI,wBAAiE;AAkvBvE,eAAeA,wBAAwB;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}