package handlers

import (
	"net/http"
	"strconv"

	"jwt-auth-backend/models"
	"jwt-auth-backend/services"
	"jwt-auth-backend/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserHandler struct {
	userService *services.UserService
}

func NewUserHandler() *UserHandler {
	return &UserHandler{
		userService: services.NewUserService(),
	}
}

// C<PERSON><PERSON><PERSON> creates a new user
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Logger.Warn("Invalid create user request", zap.Error(err))
		c.<PERSON>(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	user, err := h.userService.CreateUser(&req)
	if err != nil {
		utils.Logger.Error("Failed to create user", zap.Error(err))
		c.<PERSON>(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to create user",
			Details: err.Error(),
		})
		return
	}

	response := models.UserResponse{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		IsActive:  user.IsActive,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}

	c.JSON(http.StatusCreated, models.CRUDResponse{
		Data:    response,
		Message: "User created successfully",
	})
}

// ListUsers lists users with pagination and search
func (h *UserHandler) ListUsers(c *gin.Context) {
	var req models.CRUDRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Logger.Warn("Invalid list users request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	// Set default pagination if not provided
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.userService.List(&req)
	if err != nil {
		utils.Logger.Error("Failed to list users", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to retrieve users",
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	var userResponses []models.UserResponse
	if users, ok := result.Data.([]models.User); ok {
		for _, user := range users {
			userResponses = append(userResponses, models.UserResponse{
				ID:        user.ID,
				Username:  user.Username,
				Email:     user.Email,
				IsActive:  user.IsActive,
				CreatedAt: user.CreatedAt,
				UpdatedAt: user.UpdatedAt,
			})
		}
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:       userResponses,
		Pagination: result.Pagination,
	})
}

// GetUser gets a user by ID
func (h *UserHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Details: "User ID must be a valid number",
		})
		return
	}

	user, err := h.userService.GetByID(uint(id))
	if err != nil {
		utils.Logger.Error("Failed to get user", zap.Error(err))
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error:   "User not found",
			Details: err.Error(),
		})
		return
	}

	response := models.UserResponse{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		IsActive:  user.IsActive,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:    response,
		Message: "User retrieved successfully",
	})
}

// UpdateUser updates a user
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Details: "User ID must be a valid number",
		})
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Logger.Warn("Invalid update user request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	user, err := h.userService.UpdateUser(uint(id), &req)
	if err != nil {
		utils.Logger.Error("Failed to update user", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to update user",
			Details: err.Error(),
		})
		return
	}

	response := models.UserResponse{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		IsActive:  user.IsActive,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:    response,
		Message: "User updated successfully",
	})
}

// DeleteUser deletes a user
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Details: "User ID must be a valid number",
		})
		return
	}

	if err := h.userService.Delete(uint(id)); err != nil {
		utils.Logger.Error("Failed to delete user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to delete user",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "User deleted successfully",
	})
}

// AssignRoleToUser assigns a role to user
func (h *UserHandler) AssignRoleToUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Details: "User ID must be a valid number",
		})
		return
	}

	var req models.AssignRoleToUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Logger.Warn("Invalid assign role request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	if err := h.userService.AssignRoleToUser(uint(id), req.RoleID); err != nil {
		utils.Logger.Error("Failed to assign role to user", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to assign role",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Role assigned to user successfully",
	})
}

// RemoveRoleFromUser removes a role from user
func (h *UserHandler) RemoveRoleFromUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Details: "User ID must be a valid number",
		})
		return
	}

	roleIDStr := c.Param("role_id")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid role ID",
			Details: "Role ID must be a valid number",
		})
		return
	}

	if err := h.userService.RemoveRoleFromUser(uint(id), uint(roleID)); err != nil {
		utils.Logger.Error("Failed to remove role from user", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to remove role",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Role removed from user successfully",
	})
}

// AssignPermissionToUser assigns a permission to user
func (h *UserHandler) AssignPermissionToUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Details: "User ID must be a valid number",
		})
		return
	}

	var req models.AssignPermissionToUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Logger.Warn("Invalid assign permission request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	if err := h.userService.AssignPermissionToUser(uint(id), req.PermissionID); err != nil {
		utils.Logger.Error("Failed to assign permission to user", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to assign permission",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Permission assigned to user successfully",
	})
}

// RemovePermissionFromUser removes a permission from user
func (h *UserHandler) RemovePermissionFromUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Details: "User ID must be a valid number",
		})
		return
	}

	permissionIDStr := c.Param("permission_id")
	permissionID, err := strconv.ParseUint(permissionIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid permission ID",
			Details: "Permission ID must be a valid number",
		})
		return
	}

	if err := h.userService.RemovePermissionFromUser(uint(id), uint(permissionID)); err != nil {
		utils.Logger.Error("Failed to remove permission from user", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to remove permission",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Permission removed from user successfully",
	})
}

// SetupUserRoutes sets up all user-related routes
func (h *UserHandler) SetupUserRoutes(router *gin.RouterGroup) {
	users := router.Group("/user-management")
	{
		users.POST("", h.CreateUser)                                                // POST /user-management
		users.GET("", h.ListUsers)                                                  // GET /user-management
		users.GET("/:id", h.GetUser)                                                // GET /user-management/:id
		users.PUT("/:id", h.UpdateUser)                                             // PUT /user-management/:id
		users.DELETE("/:id", h.DeleteUser)                                          // DELETE /user-management/:id
		users.POST("/:id/roles", h.AssignRoleToUser)                                // POST /user-management/:id/roles
		users.DELETE("/:id/roles/:role_id", h.RemoveRoleFromUser)                   // DELETE /user-management/:id/roles/:role_id
		users.POST("/:id/permissions", h.AssignPermissionToUser)                    // POST /user-management/:id/permissions
		users.DELETE("/:id/permissions/:permission_id", h.RemovePermissionFromUser) // DELETE /user-management/:id/permissions/:permission_id
	}
}
