package v1

import (
	"net/http"

	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"github.com/gin-gonic/gin"
)
// @Summary Lấy thông tin user
// @Description Lấy thông tin user từ JWT token
// @Tags User
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} models.User
// @Failure 401 {object} map[string]interface{}
// @Router /api/v1/user/profile [get]
func GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var user models.User
	if err := database.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Lấy roles của user
	var roles []string
	database.DB.
		Table("roles").
		Select("roles.name").
		Joins("join user_roles on user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", user.ID).
		Scan(&roles)

	c.JSON(http.StatusOK, gin.H{
		"user": user,
		"roles": roles,
	})
} 