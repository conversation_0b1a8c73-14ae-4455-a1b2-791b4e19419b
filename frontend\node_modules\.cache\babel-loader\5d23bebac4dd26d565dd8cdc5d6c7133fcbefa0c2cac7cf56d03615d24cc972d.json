{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\UserPermissionManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { userService } from '../../services';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport AssignmentsTab from './AssignmentsTab';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserPermissionManagement = () => {\n  _s();\n  // State for active tab\n  const [activeTab, setActiveTab] = useState('users');\n\n  // Users state\n  const [users, setUsers] = useState([]);\n  const [usersPagination, setUsersPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState([]);\n  const [rolesPagination, setRolesPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState([]);\n  const [permissionsPagination, setPermissionsPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [editingRole, setEditingRole] = useState(null);\n  const [editingPermission, setEditingPermission] = useState(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          search_term: searchTerm,\n          filters: {\n            is_active: true\n          }\n        };\n        response = await userService.search(searchRequest);\n      } else {\n        response = await userService.list({\n          page,\n          page_size: usersPagination.page_size\n        });\n      }\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(`Failed to load users: ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadUsers(1, usersSearchTerm);\n  };\n  const handleUsersPageChange = newPage => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: rolesPagination.page_size,\n          filters: [{\n            field: 'name',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await roleManagementAPI.search(searchRequest);\n      } else {\n        response = await roleManagementAPI.list({\n          page,\n          page_size: rolesPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(`Failed to load roles: ${((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadRoles(1, rolesSearchTerm);\n  };\n  const handleRolesPageChange = newPage => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: permissionsPagination.page_size,\n          filters: [{\n            field: 'name',\n            operator: 'ilike',\n            value: `%${searchTerm}%`\n          }],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await permissionManagementAPI.search(searchRequest);\n      } else {\n        response = await permissionManagementAPI.list({\n          page,\n          page_size: permissionsPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(`Failed to load permissions: ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n  const handlePermissionsPageChange = newPage => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async id => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n    try {\n      await userManagementAPI.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      setError(`Failed to delete user: ${((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n    }\n  };\n  const handleDeleteRole = async id => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n    try {\n      await roleManagementAPI.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      setError(`Failed to delete role: ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || err.message}`);\n    }\n  };\n  const handleDeletePermission = async id => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n    try {\n      await permissionManagementAPI.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      setError(`Failed to delete permission: ${((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error) || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination, onPageChange) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(i),\n        className: `px-3 py-1 mx-1 rounded ${i === pagination.page ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n        children: i\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600\",\n        children: [\"Showing \", (pagination.page - 1) * pagination.page_size + 1, \" to\", ' ', Math.min(pagination.page * pagination.page_size, pagination.total_items), \" of\", ' ', pagination.total_items, \" entries\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page - 1),\n          disabled: pagination.page <= 1,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), pages, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page + 1),\n          disabled: pagination.page >= pagination.total_pages,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-8\",\n      children: \"User Permission Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b border-gray-200 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"-mb-px flex space-x-8\",\n        children: [{\n          key: 'users',\n          label: 'Users'\n        }, {\n          key: 'roles',\n          label: 'Roles'\n        }, {\n          key: 'permissions',\n          label: 'Permissions'\n        }, {\n          key: 'assignments',\n          label: 'Assignments'\n        }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n          children: tab.label\n        }, tab.key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingUser(null);\n            setShowUserModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search users by username or email...\",\n          value: usersSearchTerm,\n          onChange: e => setUsersSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleUsersSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleUsersSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setUsersSearchTerm('');\n            loadUsers(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this), usersLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading users...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: user.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingUser(user);\n                      setShowUserModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteUser(user.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 25\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 15\n        }, this), renderPagination(usersPagination, handleUsersPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 9\n    }, this), activeTab === 'roles' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Role Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingRole(null);\n            setShowRoleModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Role\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search roles by name...\",\n          value: rolesSearchTerm,\n          onChange: e => setRolesSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleRolesSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRolesSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setRolesSearchTerm('');\n            loadRoles(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 11\n      }, this), rolesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading roles...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: roles.map(role => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: role.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: role.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: role.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: role.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRole(role);\n                      setShowRoleModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteRole(role.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 25\n                }, this)]\n              }, role.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 15\n        }, this), renderPagination(rolesPagination, handleRolesPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 9\n    }, this), activeTab === 'permissions' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Permission Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingPermission(null);\n            setShowPermissionModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Permission\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search permissions by name...\",\n          value: permissionsSearchTerm,\n          onChange: e => setPermissionsSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handlePermissionsSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePermissionsSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setPermissionsSearchTerm('');\n            loadPermissions(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 11\n      }, this), permissionsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading permissions...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: permissions.map(permission => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: permission.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: permission.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: permission.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: permission.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingPermission(permission);\n                      setShowPermissionModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeletePermission(permission.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 25\n                }, this)]\n              }, permission.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 15\n        }, this), renderPagination(permissionsPagination, handlePermissionsPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 9\n    }, this), activeTab === 'assignments' && /*#__PURE__*/_jsxDEV(AssignmentsTab, {\n      users: users,\n      roles: roles,\n      onMessage: setMessage,\n      onError: setError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 728,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(UserModal, {\n      isOpen: showUserModal,\n      onClose: () => setShowUserModal(false),\n      user: editingUser,\n      onSuccess: () => {\n        setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n        loadUsers(usersPagination.page, usersSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 737,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RoleModal, {\n      isOpen: showRoleModal,\n      onClose: () => setShowRoleModal(false),\n      role: editingRole,\n      onSuccess: () => {\n        setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n        loadRoles(rolesPagination.page, rolesSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 747,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PermissionModal, {\n      isOpen: showPermissionModal,\n      onClose: () => setShowPermissionModal(false),\n      permission: editingPermission,\n      onSuccess: () => {\n        setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n        loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 310,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPermissionManagement, \"EXXLj32ClIuSM8b9dg4KjmQqFqU=\");\n_c = UserPermissionManagement;\nexport default UserPermissionManagement;\nvar _c;\n$RefreshReg$(_c, \"UserPermissionManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "userService", "UserModal", "RoleModal", "PermissionModal", "AssignmentsTab", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserPermissionManagement", "_s", "activeTab", "setActiveTab", "users", "setUsers", "usersPagination", "setUsersPagination", "page", "page_size", "total_items", "total_pages", "usersLoading", "setUsersLoading", "usersSearchTerm", "setUsersSearchTerm", "roles", "setRoles", "rolesPagination", "setRolesPagination", "rolesLoading", "setRolesLoading", "rolesSearchTerm", "setRolesSearchTerm", "permissions", "setPermissions", "permissionsPagination", "setPermissionsPagination", "permissionsLoading", "setPermissionsLoading", "permissionsSearchTerm", "setPermissionsSearchTerm", "message", "setMessage", "error", "setError", "showUserModal", "setShowUserModal", "showRoleModal", "setShowRoleModal", "showPermissionModal", "setShowPermissionModal", "editingUser", "setEditingUser", "editingRole", "setEditingRole", "editingPermission", "setEditingPermission", "loadUsers", "loadRoles", "loadPermissions", "searchTerm", "response", "trim", "searchRequest", "search_term", "filters", "is_active", "search", "list", "data", "pagination", "err", "_err$response", "_err$response$data", "handleUsersSearch", "prev", "handleUsersPageChange", "newPage", "field", "operator", "value", "order_by", "order_dir", "roleManagementAPI", "_err$response2", "_err$response2$data", "handleRolesSearch", "handleRolesPageChange", "permissionManagementAPI", "_err$response3", "_err$response3$data", "handlePermissionsSearch", "handlePermissionsPageChange", "handleDeleteUser", "id", "window", "confirm", "userManagementAPI", "delete", "_err$response4", "_err$response4$data", "handleDeleteRole", "_err$response5", "_err$response5$data", "handleDeletePermission", "_err$response6", "_err$response6$data", "renderPagination", "onPageChange", "pages", "i", "push", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Math", "min", "disabled", "key", "label", "map", "tab", "type", "placeholder", "onChange", "e", "target", "onKeyPress", "user", "username", "email", "created_at", "Date", "toLocaleDateString", "role", "name", "description", "permission", "onMessage", "onError", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/UserPermissionManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport {\n  userService,\n  roleService,\n  permissionService,\n  adminService,\n  PaginationInfo,\n  SearchRequest\n} from '../../services';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport AssignmentsTab from './AssignmentsTab';\n\ninterface UserPermissionManagementProps { }\n\nconst UserPermissionManagement: React.FC<UserPermissionManagementProps> = () => {\n  // State for active tab\n  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'permissions' | 'assignments'>('users');\n\n  // Users state\n  const [users, setUsers] = useState<User[]>([]);\n  const [usersPagination, setUsersPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [rolesPagination, setRolesPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [permissionsPagination, setPermissionsPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          search_term: searchTerm,\n          filters: {\n            is_active: true\n          }\n        };\n        response = await userService.search(searchRequest);\n      } else {\n        response = await userService.list({\n          page,\n          page_size: usersPagination.page_size\n        });\n      }\n\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load users: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({ ...prev, page: 1 }));\n    loadUsers(1, usersSearchTerm);\n  };\n\n  const handleUsersPageChange = (newPage: number) => {\n    setUsersPagination(prev => ({ ...prev, page: newPage }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: rolesPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await roleManagementAPI.search(searchRequest);\n      } else {\n        response = await roleManagementAPI.list({\n          page,\n          page_size: rolesPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load roles: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({ ...prev, page: 1 }));\n    loadRoles(1, rolesSearchTerm);\n  };\n\n  const handleRolesPageChange = (newPage: number) => {\n    setRolesPagination(prev => ({ ...prev, page: newPage }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: permissionsPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await permissionManagementAPI.search(searchRequest);\n      } else {\n        response = await permissionManagementAPI.list({\n          page,\n          page_size: permissionsPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load permissions: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({ ...prev, page: 1 }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n\n  const handlePermissionsPageChange = (newPage: number) => {\n    setPermissionsPagination(prev => ({ ...prev, page: newPage }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n\n    try {\n      await userManagementAPI.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete user: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeleteRole = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n\n    try {\n      await roleManagementAPI.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeletePermission = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n\n    try {\n      await permissionManagementAPI.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination: PaginationInfo, onPageChange: (page: number) => void) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(\n        <button\n          key={i}\n          onClick={() => onPageChange(i)}\n          className={`px-3 py-1 mx-1 rounded ${i === pagination.page\n            ? 'bg-blue-500 text-white'\n            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n        >\n          {i}\n        </button>\n      );\n    }\n\n    return (\n      <div className=\"flex items-center justify-between mt-4\">\n        <div className=\"text-sm text-gray-600\">\n          Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}\n          {Math.min(pagination.page * pagination.page_size, pagination.total_items)} of{' '}\n          {pagination.total_items} entries\n        </div>\n        <div className=\"flex items-center\">\n          <button\n            onClick={() => onPageChange(pagination.page - 1)}\n            disabled={pagination.page <= 1}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Previous\n          </button>\n          {pages}\n          <button\n            onClick={() => onPageChange(pagination.page + 1)}\n            disabled={pagination.page >= pagination.total_pages}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Next\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">User Permission Management</h1>\n\n      {/* Messages */}\n      {message && (\n        <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n          {message}\n        </div>\n      )}\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {error}\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {[\n            { key: 'users', label: 'Users' },\n            { key: 'roles', label: 'Roles' },\n            { key: 'permissions', label: 'Permissions' },\n            { key: 'assignments', label: 'Assignments' }\n          ].map((tab) => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'users' && (\n        <div>\n          {/* Users Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">User Management</h2>\n            <button\n              onClick={() => {\n                setEditingUser(null);\n                setShowUserModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add User\n            </button>\n          </div>\n\n          {/* Users Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search users by username or email...\"\n              value={usersSearchTerm}\n              onChange={(e) => setUsersSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleUsersSearch()}\n            />\n            <button\n              onClick={handleUsersSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setUsersSearchTerm('');\n                loadUsers(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Users Table */}\n          {usersLoading ? (\n            <div className=\"text-center py-8\">Loading users...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Username\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Email\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <tr key={user.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {user.username}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.email}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingUser(user);\n                              setShowUserModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteUser(user.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Users Pagination */}\n              {renderPagination(usersPagination, handleUsersPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Roles Tab */}\n      {activeTab === 'roles' && (\n        <div>\n          {/* Roles Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Role Management</h2>\n            <button\n              onClick={() => {\n                setEditingRole(null);\n                setShowRoleModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Role\n            </button>\n          </div>\n\n          {/* Roles Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search roles by name...\"\n              value={rolesSearchTerm}\n              onChange={(e) => setRolesSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleRolesSearch()}\n            />\n            <button\n              onClick={handleRolesSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setRolesSearchTerm('');\n                loadRoles(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Roles Table */}\n          {rolesLoading ? (\n            <div className=\"text-center py-8\">Loading roles...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {roles.map((role) => (\n                      <tr key={role.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {role.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {role.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {role.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingRole(role);\n                              setShowRoleModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteRole(role.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Roles Pagination */}\n              {renderPagination(rolesPagination, handleRolesPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Permissions Tab */}\n      {activeTab === 'permissions' && (\n        <div>\n          {/* Permissions Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Permission Management</h2>\n            <button\n              onClick={() => {\n                setEditingPermission(null);\n                setShowPermissionModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Permission\n            </button>\n          </div>\n\n          {/* Permissions Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search permissions by name...\"\n              value={permissionsSearchTerm}\n              onChange={(e) => setPermissionsSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handlePermissionsSearch()}\n            />\n            <button\n              onClick={handlePermissionsSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setPermissionsSearchTerm('');\n                loadPermissions(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Permissions Table */}\n          {permissionsLoading ? (\n            <div className=\"text-center py-8\">Loading permissions...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {permissions.map((permission) => (\n                      <tr key={permission.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {permission.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {permission.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {permission.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingPermission(permission);\n                              setShowPermissionModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeletePermission(permission.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Permissions Pagination */}\n              {renderPagination(permissionsPagination, handlePermissionsPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Assignments Tab */}\n      {activeTab === 'assignments' && (\n        <AssignmentsTab\n          users={users}\n          roles={roles}\n          onMessage={setMessage}\n          onError={setError}\n        />\n      )}\n\n      {/* Modals */}\n      <UserModal\n        isOpen={showUserModal}\n        onClose={() => setShowUserModal(false)}\n        user={editingUser}\n        onSuccess={() => {\n          setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n          loadUsers(usersPagination.page, usersSearchTerm);\n        }}\n      />\n\n      <RoleModal\n        isOpen={showRoleModal}\n        onClose={() => setShowRoleModal(false)}\n        role={editingRole}\n        onSuccess={() => {\n          setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n          loadRoles(rolesPagination.page, rolesSearchTerm);\n        }}\n      />\n\n      <PermissionModal\n        isOpen={showPermissionModal}\n        onClose={() => setShowPermissionModal(false)}\n        permission={editingPermission}\n        onSuccess={() => {\n          setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n          loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n        }}\n      />\n    </div>\n  );\n};\n\nexport default UserPermissionManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SACEC,WAAW,QAMN,gBAAgB;AACvB,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI9C,MAAMC,wBAAiE,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9E;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAoD,OAAO,CAAC;;EAEtG;EACA,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAiB;IACrEmB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAiB;IACrEmB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACqC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtC,QAAQ,CAAiB;IACjFmB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;;EAEtE;EACA,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACyD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAoB,IAAI,CAAC;;EAEnF;EACAC,SAAS,CAAC,MAAM;IACd,QAAQY,SAAS;MACf,KAAK,OAAO;QACV8C,SAAS,CAAC,CAAC;QACX;MACF,KAAK,OAAO;QACVC,SAAS,CAAC,CAAC;QACX;MACF,KAAK,aAAa;QAChBC,eAAe,CAAC,CAAC;QACjB;MACF,KAAK,aAAa;QAChBF,SAAS,CAAC,CAAC,CAAC,CAAC;QACbC,SAAS,CAAC,CAAC,CAAC,CAAC;QACb;IACJ;EACF,CAAC,EAAE,CAAC/C,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM8C,SAAS,GAAG,MAAAA,CAAOxC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrDtC,eAAe,CAAC,IAAI,CAAC;IACrBsB,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG,SAAS;UACpC8C,WAAW,EAAEJ,UAAU;UACvBK,OAAO,EAAE;YACPC,SAAS,EAAE;UACb;QACF,CAAC;QACDL,QAAQ,GAAG,MAAM7D,WAAW,CAACmE,MAAM,CAACJ,aAAa,CAAC;MACpD,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAM7D,WAAW,CAACoE,IAAI,CAAC;UAChCnD,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG;QAC7B,CAAC,CAAC;MACJ;MAEAJ,QAAQ,CAAC+C,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC5BrD,kBAAkB,CAAC6C,QAAQ,CAACQ,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjB7B,QAAQ,CAAC,yBAAyB,EAAA4B,aAAA,GAAAD,GAAG,CAACV,QAAQ,cAAAW,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoB9B,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRnB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMoD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1D,kBAAkB,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDwC,SAAS,CAAC,CAAC,EAAElC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMqD,qBAAqB,GAAIC,OAAe,IAAK;IACjD7D,kBAAkB,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE4D;IAAQ,CAAC,CAAC,CAAC;IACxDpB,SAAS,CAACoB,OAAO,EAAEtD,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAMmC,SAAS,GAAG,MAAAA,CAAOzC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrD9B,eAAe,CAAC,IAAI,CAAC;IACrBc,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAES,eAAe,CAACT,SAAS;UACpC+C,OAAO,EAAE,CACP;YACEa,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIpB,UAAU;UACvB,CAAC,CACF;UACDqB,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC;QACDrB,QAAQ,GAAG,MAAMsB,iBAAiB,CAAChB,MAAM,CAACJ,aAAa,CAAC;MAC1D,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAMsB,iBAAiB,CAACf,IAAI,CAAC;UACtCnD,IAAI;UACJC,SAAS,EAAES,eAAe,CAACT,SAAS;UACpC+D,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEAxD,QAAQ,CAACmC,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC5BzC,kBAAkB,CAACiC,QAAQ,CAACQ,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACjBzC,QAAQ,CAAC,yBAAyB,EAAAwC,cAAA,GAAAb,GAAG,CAACV,QAAQ,cAAAuB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcf,IAAI,cAAAgB,mBAAA,uBAAlBA,mBAAA,CAAoB1C,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRX,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMwD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1D,kBAAkB,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDyC,SAAS,CAAC,CAAC,EAAE3B,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMwD,qBAAqB,GAAIV,OAAe,IAAK;IACjDjD,kBAAkB,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE4D;IAAQ,CAAC,CAAC,CAAC;IACxDnB,SAAS,CAACmB,OAAO,EAAE9C,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAG,MAAAA,CAAO1C,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IAC3DtB,qBAAqB,CAAC,IAAI,CAAC;IAC3BM,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAEiB,qBAAqB,CAACjB,SAAS;UAC1C+C,OAAO,EAAE,CACP;YACEa,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE,IAAIpB,UAAU;UACvB,CAAC,CACF;UACDqB,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC;QACDrB,QAAQ,GAAG,MAAM2B,uBAAuB,CAACrB,MAAM,CAACJ,aAAa,CAAC;MAChE,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAM2B,uBAAuB,CAACpB,IAAI,CAAC;UAC5CnD,IAAI;UACJC,SAAS,EAAEiB,qBAAqB,CAACjB,SAAS;UAC1C+D,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEAhD,cAAc,CAAC2B,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAClCjC,wBAAwB,CAACyB,QAAQ,CAACQ,IAAI,CAACC,UAAU,CAAC;IACpD,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAkB,cAAA,EAAAC,mBAAA;MACjB9C,QAAQ,CAAC,+BAA+B,EAAA6C,cAAA,GAAAlB,GAAG,CAACV,QAAQ,cAAA4B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcpB,IAAI,cAAAqB,mBAAA,uBAAlBA,mBAAA,CAAoB/C,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IACrF,CAAC,SAAS;MACRH,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMqD,uBAAuB,GAAGA,CAAA,KAAM;IACpCvD,wBAAwB,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IACxD0C,eAAe,CAAC,CAAC,EAAEpB,qBAAqB,CAAC;EAC3C,CAAC;EAED,MAAMqD,2BAA2B,GAAIf,OAAe,IAAK;IACvDzC,wBAAwB,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE4D;IAAQ,CAAC,CAAC,CAAC;IAC9DlB,eAAe,CAACkB,OAAO,EAAEtC,qBAAqB,CAAC;EACjD,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAG,MAAOC,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAMC,iBAAiB,CAACC,MAAM,CAACJ,EAAE,CAAC;MAClCpD,UAAU,CAAC,2BAA2B,CAAC;MACvCe,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;IAClD,CAAC,CAAC,OAAOgD,GAAQ,EAAE;MAAA,IAAA4B,cAAA,EAAAC,mBAAA;MACjBxD,QAAQ,CAAC,0BAA0B,EAAAuD,cAAA,GAAA5B,GAAG,CAACV,QAAQ,cAAAsC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc9B,IAAI,cAAA+B,mBAAA,uBAAlBA,mBAAA,CAAoBzD,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAM4D,gBAAgB,GAAG,MAAOP,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAMb,iBAAiB,CAACe,MAAM,CAACJ,EAAE,CAAC;MAClCpD,UAAU,CAAC,2BAA2B,CAAC;MACvCgB,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;IAClD,CAAC,CAAC,OAAOwC,GAAQ,EAAE;MAAA,IAAA+B,cAAA,EAAAC,mBAAA;MACjB3D,QAAQ,CAAC,0BAA0B,EAAA0D,cAAA,GAAA/B,GAAG,CAACV,QAAQ,cAAAyC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjC,IAAI,cAAAkC,mBAAA,uBAAlBA,mBAAA,CAAoB5D,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAM+D,sBAAsB,GAAG,MAAOV,EAAU,IAAK;IACnD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;IAEzE,IAAI;MACF,MAAMR,uBAAuB,CAACU,MAAM,CAACJ,EAAE,CAAC;MACxCpD,UAAU,CAAC,iCAAiC,CAAC;MAC7CiB,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;IACpE,CAAC,CAAC,OAAOgC,GAAQ,EAAE;MAAA,IAAAkC,cAAA,EAAAC,mBAAA;MACjB9D,QAAQ,CAAC,gCAAgC,EAAA6D,cAAA,GAAAlC,GAAG,CAACV,QAAQ,cAAA4C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcpC,IAAI,cAAAqC,mBAAA,uBAAlBA,mBAAA,CAAoB/D,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IACtF;EACF,CAAC;;EAED;EACA,MAAMkE,gBAAgB,GAAGA,CAACrC,UAA0B,EAAEsC,YAAoC,KAAK;IAC7F,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIxC,UAAU,CAAClD,WAAW,EAAE0F,CAAC,EAAE,EAAE;MAChDD,KAAK,CAACE,IAAI,cACRzG,OAAA;QAEE0G,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACE,CAAC,CAAE;QAC/BG,SAAS,EAAE,0BAA0BH,CAAC,KAAKxC,UAAU,CAACrD,IAAI,GACtD,wBAAwB,GACxB,6CAA6C,EAC5C;QAAAiG,QAAA,EAEJJ;MAAC,GAPGA,CAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQA,CACV,CAAC;IACH;IAEA,oBACEhH,OAAA;MAAK2G,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD5G,OAAA;QAAK2G,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC7B,EAAE,CAAC5C,UAAU,CAACrD,IAAI,GAAG,CAAC,IAAIqD,UAAU,CAACpD,SAAS,GAAI,CAAC,EAAC,KAAG,EAAC,GAAG,EAClEqG,IAAI,CAACC,GAAG,CAAClD,UAAU,CAACrD,IAAI,GAAGqD,UAAU,CAACpD,SAAS,EAAEoD,UAAU,CAACnD,WAAW,CAAC,EAAC,KAAG,EAAC,GAAG,EAChFmD,UAAU,CAACnD,WAAW,EAAC,UAC1B;MAAA;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNhH,OAAA;QAAK2G,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5G,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACtC,UAAU,CAACrD,IAAI,GAAG,CAAC,CAAE;UACjDwG,QAAQ,EAAEnD,UAAU,CAACrD,IAAI,IAAI,CAAE;UAC/BgG,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRT,KAAK,eACNvG,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACtC,UAAU,CAACrD,IAAI,GAAG,CAAC,CAAE;UACjDwG,QAAQ,EAAEnD,UAAU,CAACrD,IAAI,IAAIqD,UAAU,CAAClD,WAAY;UACpD6F,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEhH,OAAA;IAAK2G,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1C5G,OAAA;MAAI2G,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAGtE7E,OAAO,iBACNnC,OAAA;MAAK2G,SAAS,EAAC,4EAA4E;MAAAC,QAAA,EACxFzE;IAAO;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACA3E,KAAK,iBACJrC,OAAA;MAAK2G,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClFvE;IAAK;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhH,OAAA;MAAK2G,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5C5G,OAAA;QAAK2G,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACnC,CACC;UAAEQ,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,EAC5C;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,CAC7C,CAACC,GAAG,CAAEC,GAAG,iBACRvH,OAAA;UAEE0G,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAACiH,GAAG,CAACH,GAAU,CAAE;UAC5CT,SAAS,EAAE,4CAA4CtG,SAAS,KAAKkH,GAAG,CAACH,GAAG,GACxE,+BAA+B,GAC/B,4EAA4E,EAC3E;UAAAR,QAAA,EAEJW,GAAG,CAACF;QAAK,GAPLE,GAAG,CAACH,GAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3G,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAA4G,QAAA,gBAEE5G,OAAA;QAAK2G,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5G,OAAA;UAAI2G,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DhH,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM;YACb5D,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACFmE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhH,OAAA;QAAK2G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5G,OAAA;UACEwH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,sCAAsC;UAClD/C,KAAK,EAAEzD,eAAgB;UACvByG,QAAQ,EAAGC,CAAC,IAAKzG,kBAAkB,CAACyG,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;UACpDiC,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAIhD,iBAAiB,CAAC;QAAE;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACFhH,OAAA;UACE0G,OAAO,EAAEtC,iBAAkB;UAC3BuC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThH,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM;YACbxF,kBAAkB,CAAC,EAAE,CAAC;YACtBiC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFwD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLjG,YAAY,gBACXf,OAAA;QAAK2G,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExDhH,OAAA,CAAAE,SAAA;QAAA0G,QAAA,gBACE5G,OAAA;UAAK2G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B5G,OAAA;YAAO2G,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3D5G,OAAA;cAAO2G,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B5G,OAAA;gBAAA4G,QAAA,gBACE5G,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRhH,OAAA;cAAO2G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDrG,KAAK,CAAC+G,GAAG,CAAEQ,IAAI,iBACd9H,OAAA;gBAAA4G,QAAA,gBACE5G,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACtC;gBAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EkB,IAAI,CAACC;gBAAQ;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACE;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC5G,OAAA;oBAAM2G,SAAS,EAAE,4DAA4DmB,IAAI,CAAClE,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAAgD,QAAA,EACFkB,IAAI,CAAClE,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DkB,IAAI,CAACG,UAAU,GAAG,IAAIC,IAAI,CAACJ,IAAI,CAACG,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7D5G,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM;sBACb5D,cAAc,CAACgF,IAAI,CAAC;sBACpBtF,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACFmE,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACThH,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAACuC,IAAI,CAACtC,EAAE,CAAE;oBACzCmB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEc,IAAI,CAACtC,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAAC5F,eAAe,EAAE6D,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA3G,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAA4G,QAAA,gBAEE5G,OAAA;QAAK2G,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5G,OAAA;UAAI2G,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DhH,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM;YACb1D,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACFiE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhH,OAAA;QAAK2G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5G,OAAA;UACEwH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,yBAAyB;UACrC/C,KAAK,EAAEjD,eAAgB;UACvBiG,QAAQ,EAAGC,CAAC,IAAKjG,kBAAkB,CAACiG,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;UACpDiC,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAIpC,iBAAiB,CAAC;QAAE;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACFhH,OAAA;UACE0G,OAAO,EAAE1B,iBAAkB;UAC3B2B,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThH,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM;YACbhF,kBAAkB,CAAC,EAAE,CAAC;YACtB0B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFuD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLzF,YAAY,gBACXvB,OAAA;QAAK2G,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExDhH,OAAA,CAAAE,SAAA;QAAA0G,QAAA,gBACE5G,OAAA;UAAK2G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B5G,OAAA;YAAO2G,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3D5G,OAAA;cAAO2G,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B5G,OAAA;gBAAA4G,QAAA,gBACE5G,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRhH,OAAA;cAAO2G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDzF,KAAK,CAACmG,GAAG,CAAEc,IAAI,iBACdpI,OAAA;gBAAA4G,QAAA,gBACE5G,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DwB,IAAI,CAAC5C;gBAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EwB,IAAI,CAACC;gBAAI;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DwB,IAAI,CAACE,WAAW,IAAI;gBAAgB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC5G,OAAA;oBAAM2G,SAAS,EAAE,4DAA4DyB,IAAI,CAACxE,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAAgD,QAAA,EACFwB,IAAI,CAACxE,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DwB,IAAI,CAACH,UAAU,GAAG,IAAIC,IAAI,CAACE,IAAI,CAACH,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7D5G,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM;sBACb1D,cAAc,CAACoF,IAAI,CAAC;sBACpB1F,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACFiE,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACThH,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACqC,IAAI,CAAC5C,EAAE,CAAE;oBACzCmB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEoB,IAAI,CAAC5C,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAAChF,eAAe,EAAE4D,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA3G,SAAS,KAAK,aAAa,iBAC1BL,OAAA;MAAA4G,QAAA,gBAEE5G,OAAA;QAAK2G,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5G,OAAA;UAAI2G,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEhH,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM;YACbxD,oBAAoB,CAAC,IAAI,CAAC;YAC1BN,sBAAsB,CAAC,IAAI,CAAC;UAC9B,CAAE;UACF+D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhH,OAAA;QAAK2G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5G,OAAA;UACEwH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,+BAA+B;UAC3C/C,KAAK,EAAEzC,qBAAsB;UAC7ByF,QAAQ,EAAGC,CAAC,IAAKzF,wBAAwB,CAACyF,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;UAC1DiC,SAAS,EAAC,wGAAwG;UAClHkB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAK,OAAO,IAAI/B,uBAAuB,CAAC;QAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACFhH,OAAA;UACE0G,OAAO,EAAErB,uBAAwB;UACjCsB,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThH,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM;YACbxE,wBAAwB,CAAC,EAAE,CAAC;YAC5BmB,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC;UACxB,CAAE;UACFsD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLjF,kBAAkB,gBACjB/B,OAAA;QAAK2G,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAE9DhH,OAAA,CAAAE,SAAA;QAAA0G,QAAA,gBACE5G,OAAA;UAAK2G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B5G,OAAA;YAAO2G,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3D5G,OAAA;cAAO2G,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B5G,OAAA;gBAAA4G,QAAA,gBACE5G,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRhH,OAAA;cAAO2G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDjF,WAAW,CAAC2F,GAAG,CAAEiB,UAAU,iBAC1BvI,OAAA;gBAAA4G,QAAA,gBACE5G,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D2B,UAAU,CAAC/C;gBAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1E2B,UAAU,CAACF;gBAAI;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D2B,UAAU,CAACD,WAAW,IAAI;gBAAgB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC5G,OAAA;oBAAM2G,SAAS,EAAE,4DAA4D4B,UAAU,CAAC3E,SAAS,GAC7F,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAAgD,QAAA,EACF2B,UAAU,CAAC3E,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D2B,UAAU,CAACN,UAAU,GAAG,IAAIC,IAAI,CAACK,UAAU,CAACN,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACLhH,OAAA;kBAAI2G,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7D5G,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM;sBACbxD,oBAAoB,CAACqF,UAAU,CAAC;sBAChC3F,sBAAsB,CAAC,IAAI,CAAC;oBAC9B,CAAE;oBACF+D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACThH,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACqC,UAAU,CAAC/C,EAAE,CAAE;oBACrDmB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEuB,UAAU,CAAC/C,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsClB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAACxE,qBAAqB,EAAEyD,2BAA2B,CAAC;MAAA,eACrE,CACH;IAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA3G,SAAS,KAAK,aAAa,iBAC1BL,OAAA,CAACF,cAAc;MACbS,KAAK,EAAEA,KAAM;MACbY,KAAK,EAAEA,KAAM;MACbqH,SAAS,EAAEpG,UAAW;MACtBqG,OAAO,EAAEnG;IAAS;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF,eAGDhH,OAAA,CAACL,SAAS;MACR+I,MAAM,EAAEnG,aAAc;MACtBoG,OAAO,EAAEA,CAAA,KAAMnG,gBAAgB,CAAC,KAAK,CAAE;MACvCsF,IAAI,EAAEjF,WAAY;MAClB+F,SAAS,EAAEA,CAAA,KAAM;QACfxG,UAAU,CAACS,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFM,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;MAClD;IAAE;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEFhH,OAAA,CAACJ,SAAS;MACR8I,MAAM,EAAEjG,aAAc;MACtBkG,OAAO,EAAEA,CAAA,KAAMjG,gBAAgB,CAAC,KAAK,CAAE;MACvC0F,IAAI,EAAErF,WAAY;MAClB6F,SAAS,EAAEA,CAAA,KAAM;QACfxG,UAAU,CAACW,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFK,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;MAClD;IAAE;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEFhH,OAAA,CAACH,eAAe;MACd6I,MAAM,EAAE/F,mBAAoB;MAC5BgG,OAAO,EAAEA,CAAA,KAAM/F,sBAAsB,CAAC,KAAK,CAAE;MAC7C2F,UAAU,EAAEtF,iBAAkB;MAC9B2F,SAAS,EAAEA,CAAA,KAAM;QACfxG,UAAU,CAACa,iBAAiB,GAAG,iCAAiC,GAAG,iCAAiC,CAAC;QACrGI,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;MACpE;IAAE;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC5G,EAAA,CA9uBID,wBAAiE;AAAA0I,EAAA,GAAjE1I,wBAAiE;AAgvBvE,eAAeA,wBAAwB;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}