{"ast": null, "code": "import { api } from './base.service';\nclass AdminService {\n  constructor() {\n    this.baseUrl = '/admin';\n  }\n  // Dashboard\n  getDashboard() {\n    return api.get(`${this.baseUrl}/dashboard`);\n  }\n\n  // Users\n  getUsers() {\n    return api.get(`${this.baseUrl}/users`);\n  }\n  getUserRoles(userId) {\n    return api.get(`${this.baseUrl}/user/${userId}/roles`);\n  }\n  getUserPermissions(userId) {\n    return api.get(`${this.baseUrl}/user/${userId}/permissions`);\n  }\n  getUserDirectPermissions(userId) {\n    return api.get(`${this.baseUrl}/user/${userId}/direct-permissions`);\n  }\n\n  // Roles\n  getRoles() {\n    return api.get(`${this.baseUrl}/roles`);\n  }\n  getRolePermissions(roleId) {\n    return api.get(`${this.baseUrl}/role/${roleId}/permissions`);\n  }\n\n  // Permissions\n  getPermissions() {\n    return api.get(`${this.baseUrl}/permissions`);\n  }\n\n  // Role assignments\n  assignRole(request) {\n    return api.post(`${this.baseUrl}/assign-role`, request);\n  }\n  removeRole(request) {\n    return api.post(`${this.baseUrl}/remove-role`, request);\n  }\n\n  // Permission assignments to roles\n  assignPermission(request) {\n    return api.post(`${this.baseUrl}/assign-permission`, request);\n  }\n  removePermission(request) {\n    return api.post(`${this.baseUrl}/remove-permission`, request);\n  }\n\n  // Direct permission assignments to users\n  assignUserPermission(request) {\n    return api.post(`${this.baseUrl}/assign-user-permission`, request);\n  }\n  removeUserPermission(request) {\n    return api.post(`${this.baseUrl}/remove-user-permission`, request);\n  }\n\n  // Convenience methods\n  assignRoleToUser(userId, roleName) {\n    return this.assignRole({\n      user_id: userId,\n      role: roleName\n    });\n  }\n  removeRoleFromUser(userId, roleName) {\n    return this.removeRole({\n      user_id: userId,\n      role: roleName\n    });\n  }\n  assignPermissionToRole(roleId, permissionName) {\n    return this.assignPermission({\n      role_id: roleId,\n      permission: permissionName\n    });\n  }\n  removePermissionFromRole(roleId, permissionName) {\n    return this.removePermission({\n      role_id: roleId,\n      permission: permissionName\n    });\n  }\n  assignPermissionToUser(userId, permissionName) {\n    return this.assignUserPermission({\n      user_id: userId,\n      permission: permissionName\n    });\n  }\n  removePermissionFromUser(userId, permissionName) {\n    return this.removeUserPermission({\n      user_id: userId,\n      permission: permissionName\n    });\n  }\n}\nexport const adminService = new AdminService();\nexport default adminService;", "map": {"version": 3, "names": ["api", "AdminService", "constructor", "baseUrl", "getDashboard", "get", "getUsers", "getUserRoles", "userId", "getUserPermissions", "getUserDirectPermissions", "getRoles", "getRolePermissions", "roleId", "getPermissions", "assignRole", "request", "post", "removeRole", "assignPermission", "removePermission", "assignUserPermission", "removeUserPermission", "assignRoleToUser", "<PERSON><PERSON><PERSON>", "user_id", "role", "removeRoleFromUser", "assignPermissionToRole", "permissionName", "role_id", "permission", "removePermissionFromRole", "assignPermissionToUser", "removePermissionFromUser", "adminService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/admin.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { User, Role, Permission } from '../types';\nimport { api } from './base.service';\n\nexport interface DashboardStats {\n  total_users: number;\n  total_roles: number;\n  total_permissions: number;\n  active_users: number;\n  active_roles: number;\n  active_permissions: number;\n}\n\nexport interface AssignRoleRequest {\n  user_id: number;\n  role: string;\n}\n\nexport interface RemoveRoleRequest {\n  user_id: number;\n  role: string;\n}\n\nexport interface AssignPermissionRequest {\n  role_id: number;\n  permission: string;\n}\n\nexport interface RemovePermissionRequest {\n  role_id: number;\n  permission: string;\n}\n\nexport interface AssignUserPermissionRequest {\n  user_id: number;\n  permission: string;\n}\n\nexport interface RemoveUserPermissionRequest {\n  user_id: number;\n  permission: string;\n}\n\nclass AdminService {\n  private baseUrl = '/admin';\n\n  // Dashboard\n  getDashboard(): Promise<AxiosResponse<DashboardStats>> {\n    return api.get(`${this.baseUrl}/dashboard`);\n  }\n\n  // Users\n  getUsers(): Promise<AxiosResponse<User[]>> {\n    return api.get(`${this.baseUrl}/users`);\n  }\n\n  getUserRoles(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/user/${userId}/roles`);\n  }\n\n  getUserPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/user/${userId}/permissions`);\n  }\n\n  getUserDirectPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/user/${userId}/direct-permissions`);\n  }\n\n  // Roles\n  getRoles(): Promise<AxiosResponse<Role[]>> {\n    return api.get(`${this.baseUrl}/roles`);\n  }\n\n  getRolePermissions(roleId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/role/${roleId}/permissions`);\n  }\n\n  // Permissions\n  getPermissions(): Promise<AxiosResponse<Permission[]>> {\n    return api.get(`${this.baseUrl}/permissions`);\n  }\n\n  // Role assignments\n  assignRole(request: AssignRoleRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/assign-role`, request);\n  }\n\n  removeRole(request: RemoveRoleRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/remove-role`, request);\n  }\n\n  // Permission assignments to roles\n  assignPermission(request: AssignPermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/assign-permission`, request);\n  }\n\n  removePermission(request: RemovePermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/remove-permission`, request);\n  }\n\n  // Direct permission assignments to users\n  assignUserPermission(request: AssignUserPermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/assign-user-permission`, request);\n  }\n\n  removeUserPermission(request: RemoveUserPermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/remove-user-permission`, request);\n  }\n\n  // Convenience methods\n  assignRoleToUser(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.assignRole({ user_id: userId, role: roleName });\n  }\n\n  removeRoleFromUser(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.removeRole({ user_id: userId, role: roleName });\n  }\n\n  assignPermissionToRole(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.assignPermission({ role_id: roleId, permission: permissionName });\n  }\n\n  removePermissionFromRole(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.removePermission({ role_id: roleId, permission: permissionName });\n  }\n\n  assignPermissionToUser(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.assignUserPermission({ user_id: userId, permission: permissionName });\n  }\n\n  removePermissionFromUser(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.removeUserPermission({ user_id: userId, permission: permissionName });\n  }\n}\n\nexport const adminService = new AdminService();\nexport default adminService;\n"], "mappings": "AAEA,SAASA,GAAG,QAAQ,gBAAgB;AAyCpC,MAAMC,YAAY,CAAC;EAAAC,YAAA;IAAA,KACTC,OAAO,GAAG,QAAQ;EAAA;EAE1B;EACAC,YAAYA,CAAA,EAA2C;IACrD,OAAOJ,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,YAAY,CAAC;EAC7C;;EAEA;EACAG,QAAQA,CAAA,EAAmC;IACzC,OAAON,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,QAAQ,CAAC;EACzC;EAEAI,YAAYA,CAACC,MAAc,EAAoC;IAC7D,OAAOR,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,SAASK,MAAM,QAAQ,CAAC;EACxD;EAEAC,kBAAkBA,CAACD,MAAc,EAAoC;IACnE,OAAOR,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,SAASK,MAAM,cAAc,CAAC;EAC9D;EAEAE,wBAAwBA,CAACF,MAAc,EAAoC;IACzE,OAAOR,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,SAASK,MAAM,qBAAqB,CAAC;EACrE;;EAEA;EACAG,QAAQA,CAAA,EAAmC;IACzC,OAAOX,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,QAAQ,CAAC;EACzC;EAEAS,kBAAkBA,CAACC,MAAc,EAAoC;IACnE,OAAOb,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,SAASU,MAAM,cAAc,CAAC;EAC9D;;EAEA;EACAC,cAAcA,CAAA,EAAyC;IACrD,OAAOd,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,cAAc,CAAC;EAC/C;;EAEA;EACAY,UAAUA,CAACC,OAA0B,EAA+C;IAClF,OAAOhB,GAAG,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACd,OAAO,cAAc,EAAEa,OAAO,CAAC;EACzD;EAEAE,UAAUA,CAACF,OAA0B,EAA+C;IAClF,OAAOhB,GAAG,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACd,OAAO,cAAc,EAAEa,OAAO,CAAC;EACzD;;EAEA;EACAG,gBAAgBA,CAACH,OAAgC,EAA+C;IAC9F,OAAOhB,GAAG,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACd,OAAO,oBAAoB,EAAEa,OAAO,CAAC;EAC/D;EAEAI,gBAAgBA,CAACJ,OAAgC,EAA+C;IAC9F,OAAOhB,GAAG,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACd,OAAO,oBAAoB,EAAEa,OAAO,CAAC;EAC/D;;EAEA;EACAK,oBAAoBA,CAACL,OAAoC,EAA+C;IACtG,OAAOhB,GAAG,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACd,OAAO,yBAAyB,EAAEa,OAAO,CAAC;EACpE;EAEAM,oBAAoBA,CAACN,OAAoC,EAA+C;IACtG,OAAOhB,GAAG,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACd,OAAO,yBAAyB,EAAEa,OAAO,CAAC;EACpE;;EAEA;EACAO,gBAAgBA,CAACf,MAAc,EAAEgB,QAAgB,EAA+C;IAC9F,OAAO,IAAI,CAACT,UAAU,CAAC;MAAEU,OAAO,EAAEjB,MAAM;MAAEkB,IAAI,EAAEF;IAAS,CAAC,CAAC;EAC7D;EAEAG,kBAAkBA,CAACnB,MAAc,EAAEgB,QAAgB,EAA+C;IAChG,OAAO,IAAI,CAACN,UAAU,CAAC;MAAEO,OAAO,EAAEjB,MAAM;MAAEkB,IAAI,EAAEF;IAAS,CAAC,CAAC;EAC7D;EAEAI,sBAAsBA,CAACf,MAAc,EAAEgB,cAAsB,EAA+C;IAC1G,OAAO,IAAI,CAACV,gBAAgB,CAAC;MAAEW,OAAO,EAAEjB,MAAM;MAAEkB,UAAU,EAAEF;IAAe,CAAC,CAAC;EAC/E;EAEAG,wBAAwBA,CAACnB,MAAc,EAAEgB,cAAsB,EAA+C;IAC5G,OAAO,IAAI,CAACT,gBAAgB,CAAC;MAAEU,OAAO,EAAEjB,MAAM;MAAEkB,UAAU,EAAEF;IAAe,CAAC,CAAC;EAC/E;EAEAI,sBAAsBA,CAACzB,MAAc,EAAEqB,cAAsB,EAA+C;IAC1G,OAAO,IAAI,CAACR,oBAAoB,CAAC;MAAEI,OAAO,EAAEjB,MAAM;MAAEuB,UAAU,EAAEF;IAAe,CAAC,CAAC;EACnF;EAEAK,wBAAwBA,CAAC1B,MAAc,EAAEqB,cAAsB,EAA+C;IAC5G,OAAO,IAAI,CAACP,oBAAoB,CAAC;MAAEG,OAAO,EAAEjB,MAAM;MAAEuB,UAAU,EAAEF;IAAe,CAAC,CAAC;EACnF;AACF;AAEA,OAAO,MAAMM,YAAY,GAAG,IAAIlC,YAAY,CAAC,CAAC;AAC9C,eAAekC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}