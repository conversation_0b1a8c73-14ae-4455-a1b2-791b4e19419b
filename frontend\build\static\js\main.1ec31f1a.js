/*! For license information please see main.1ec31f1a.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,l={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:l,_owner:i.current}}t.Fragment=l,t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,h(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},N={key:!0,ref:!0,__self:!0,__source:!0};function j(e,t,r){var a,l={},o=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)S.call(t,a)&&!N.hasOwnProperty(a)&&(l[a]=t[a]);var s=arguments.length-2;if(1===s)l.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:n,type:e,key:o,ref:i,props:l,_owner:k.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function C(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return o=o(s=e),e=""===l?"."+C(s,0):l,w(o)?(a="",null!=e&&(a=e.replace(_,"$&/")+"/"),P(o,t,a,"",function(e){return e})):null!=o&&(E(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(_,"$&/")+"/")+e)),t.push(o)),1;if(s=0,l=""===l?".":l+":",w(e))for(var u=0;u<e.length;u++){var c=l+C(i=e[u],u);s+=P(i,t,a,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=P(i=i.value,t,a,c=l+C(i,u++),o);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function R(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",function(e){return t.call(n,e,a++)}),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var T={current:null},L={transition:null},U={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:L,ReactCurrentOwner:k};function z(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:R,forEach:function(e,t,n){R(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return R(e,function(){t++}),t},toArray:function(e){return R(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=l,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=U,t.act=z,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),l=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=k.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)S.call(t,u)&&!N.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:l,ref:o,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=j,t.createFactory=function(e){var t=j.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=L.transition;L.transition={};try{e()}finally{L.transition=t}},t.unstable_act=z,t.useCallback=function(e,t){return T.current.useCallback(e,t)},t.useContext=function(e){return T.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return T.current.useDeferredValue(e)},t.useEffect=function(e,t){return T.current.useEffect(e,t)},t.useId=function(){return T.current.useId()},t.useImperativeHandle=function(e,t,n){return T.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return T.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return T.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return T.current.useMemo(e,t)},t.useReducer=function(e,t,n){return T.current.useReducer(e,t,n)},t.useRef=function(e){return T.current.useRef(e)},t.useState=function(e){return T.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return T.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return T.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!h)if(null!==r(u))h=!0,L(S);else{var t=r(c);null!==t&&U(w,t.startTime-e)}}function S(e,n){h=!1,g&&(g=!1,y(E),E=-1),m=!0;var l=p;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!P());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(u)&&a(u),x(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&U(w,d.startTime-n),s=!1}return s}finally{f=null,p=l,m=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,N=!1,j=null,E=-1,_=5,C=-1;function P(){return!(t.unstable_now()-C<_)}function R(){if(null!==j){var e=t.unstable_now();C=e;var n=!0;try{n=j(!0,e)}finally{n?k():(N=!1,j=null)}}else N=!1}if("function"===typeof b)k=function(){b(R)};else if("undefined"!==typeof MessageChannel){var O=new MessageChannel,T=O.port2;O.port1.onmessage=R,k=function(){T.postMessage(null)}}else k=function(){v(R,0)};function L(e){j=e,N||(N=!0,k())}function U(e,n){E=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,L(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?o+l:o:l=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(g?(y(E),E=-1):g=!0,U(w,l-o))):(e.sortIndex=i,n(u,e),h||m||(h=!0,L(S))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{var r=n(950);t.H=r.createRoot,r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function h(e,t,n,r,a,l,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new h(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new h(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new h(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new h(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new h(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(m,e)||!d.call(p,e)&&(f.test(e)?m[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),_=Symbol.for("react.context"),C=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),R=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),T=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var L=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var U=Symbol.iterator;function z(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=U&&e[U]||e["@@iterator"])?e:null}var F,D=Object.assign;function A(e){if(void 0===F)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);F=t&&t[1]||""}return"\n"+F+e}var I=!1;function M(e,t){if(!e||I)return"";I=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),l=r.stack.split("\n"),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(1!==o||1!==i)do{if(o--,0>--i||a[o]!==l[i]){var s="\n"+a[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=o&&0<=i);break}}}finally{I=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?A(e):""}function B(e){switch(e.tag){case 5:return A(e.type);case 16:return A("Lazy");case 13:return A("Suspense");case 19:return A("SuspenseList");case 0:case 2:case 15:return e=M(e.type,!1);case 11:return e=M(e.type.render,!1);case 1:return e=M(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case j:return"Profiler";case N:return"StrictMode";case P:return"Suspense";case R:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case _:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case C:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return W(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===N?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return D({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function G(e,t){Y(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return D({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function le(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){me.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ve=D({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Ne=null;function je(e){if(e=ba(e)){if("function"!==typeof Se)throw Error(l(280));var t=e.stateNode;t&&(t=wa(t),Se(e.stateNode,e.type,t))}}function Ee(e){ke?Ne?Ne.push(e):Ne=[e]:ke=e}function _e(){if(ke){var e=ke,t=Ne;if(Ne=ke=null,je(e),t)for(e=0;e<t.length;e++)je(t[e])}}function Ce(e,t){return e(t)}function Pe(){}var Re=!1;function Oe(e,t,n){if(Re)return e(t,n);Re=!0;try{return Ce(e,t,n)}finally{Re=!1,(null!==ke||null!==Ne)&&(Pe(),_e())}}function Te(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Le=!1;if(c)try{var Ue={};Object.defineProperty(Ue,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Ue,Ue),window.removeEventListener("test",Ue,Ue)}catch(ce){Le=!1}function ze(e,t,n,r,a,l,o,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Fe=!1,De=null,Ae=!1,Ie=null,Me={onError:function(e){Fe=!0,De=e}};function Be(e,t,n,r,a,l,o,i,s){Fe=!1,De=null,ze.apply(Me,arguments)}function We(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(We(e)!==e)throw Error(l(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=We(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return Ve(a),e;if(o===r)return Ve(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Je=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ye=a.unstable_now,Ge=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,lt=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/st|0)|0},it=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=268435455&n;if(0!==o){var i=o&~a;0!==i?r=dt(i):0!==(l&=o)&&(r=dt(l))}else 0!==(o=n&~a)?r=dt(o):0!==l&&(r=dt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,St,kt,Nt,jt,Et=!1,_t=[],Ct=null,Pt=null,Rt=null,Ot=new Map,Tt=new Map,Lt=[],Ut="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Rt=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Tt.delete(t.pointerId)}}function Ft(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Dt(e){var t=ya(e.target);if(null!==t){var n=We(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void jt(e.priority,function(){kt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function At(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function It(e,t,n){At(e)&&n.delete(t)}function Mt(){Et=!1,null!==Ct&&At(Ct)&&(Ct=null),null!==Pt&&At(Pt)&&(Pt=null),null!==Rt&&At(Rt)&&(Rt=null),Ot.forEach(It),Tt.forEach(It)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Et||(Et=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Mt)))}function Wt(e){function t(t){return Bt(t,e)}if(0<_t.length){Bt(_t[0],e);for(var n=1;n<_t.length;n++){var r=_t[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ct&&Bt(Ct,e),null!==Pt&&Bt(Pt,e),null!==Rt&&Bt(Rt,e),Ot.forEach(t),Tt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)Dt(n),null===n.blockedOn&&Lt.shift()}var Ht=x.ReactCurrentBatchConfig,Vt=!0;function $t(e,t,n,r){var a=bt,l=Ht.transition;Ht.transition=null;try{bt=1,Qt(e,t,n,r)}finally{bt=a,Ht.transition=l}}function qt(e,t,n,r){var a=bt,l=Ht.transition;Ht.transition=null;try{bt=4,Qt(e,t,n,r)}finally{bt=a,Ht.transition=l}}function Qt(e,t,n,r){if(Vt){var a=Jt(e,t,n,r);if(null===a)Vr(e,t,r,Kt,n),zt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Ct=Ft(Ct,e,t,n,r,a),!0;case"dragenter":return Pt=Ft(Pt,e,t,n,r,a),!0;case"mouseover":return Rt=Ft(Rt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return Ot.set(l,Ft(Ot.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Tt.set(l,Ft(Tt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(zt(e,r),4&t&&-1<Ut.indexOf(e)){for(;null!==a;){var l=ba(a);if(null!==l&&wt(l),null===(l=Jt(e,t,n,r))&&Vr(e,t,r,Kt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Vr(e,t,r,null,n)}}var Kt=null;function Jt(e,t,n,r){if(Kt=null,null!==(e=ya(e=we(r))))if(null===(t=We(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ge()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Gt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Gt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return D(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,on,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=D({},un,{view:0,detail:0}),fn=an(dn),pn=D({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(ln=e.screenX-sn.screenX,on=e.screenY-sn.screenY):on=ln=0,sn=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:on}}),mn=an(pn),hn=an(D({},pn,{dataTransfer:0})),gn=an(D({},dn,{relatedTarget:0})),vn=an(D({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=D({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),xn=an(D({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Nn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function jn(){return Nn}var En=D({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),_n=an(En),Cn=an(D({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=an(D({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jn})),Rn=an(D({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=D({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Tn=an(On),Ln=[9,13,27,32],Un=c&&"CompositionEvent"in window,zn=null;c&&"documentMode"in document&&(zn=document.documentMode);var Fn=c&&"TextEvent"in window&&!zn,Dn=c&&(!Un||zn&&8<zn&&11>=zn),An=String.fromCharCode(32),In=!1;function Mn(e,t){switch(e){case"keyup":return-1!==Ln.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Wn=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function $n(e,t,n,r){Ee(r),0<(t=qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Qn=null;function Kn(e){Ar(e,0)}function Jn(e){if(Q(xa(e)))return e}function Xn(e,t){if("change"===e)return t}var Yn=!1;if(c){var Gn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Gn=Zn}else Gn=!1;Yn=Gn&&(!document.documentMode||9<document.documentMode)}function tr(){qn&&(qn.detachEvent("onpropertychange",nr),Qn=qn=null)}function nr(e){if("value"===e.propertyName&&Jn(Qn)){var t=[];$n(t,Qn,e,we(e)),Oe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Jn(Qn)}function lr(e,t){if("click"===e)return Jn(t)}function or(e,t){if("input"===e||"change"===e)return Jn(t)}var ir="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(ir(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=cr(n,l);var o=cr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=qr(vr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},kr={},Nr={};function jr(e){if(kr[e])return kr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Nr)return kr[e]=n[t];return e}c&&(Nr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Er=jr("animationend"),_r=jr("animationiteration"),Cr=jr("animationstart"),Pr=jr("transitionend"),Rr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tr(e,t){Rr.set(e,t),s(t,[e])}for(var Lr=0;Lr<Or.length;Lr++){var Ur=Or[Lr];Tr(Ur.toLowerCase(),"on"+(Ur[0].toUpperCase()+Ur.slice(1)))}Tr(Er,"onAnimationEnd"),Tr(_r,"onAnimationIteration"),Tr(Cr,"onAnimationStart"),Tr("dblclick","onDoubleClick"),Tr("focusin","onFocus"),Tr("focusout","onBlur"),Tr(Pr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fr=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Dr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,s,u){if(Be.apply(this,arguments),Fe){if(!Fe)throw Error(l(198));var c=De;Fe=!1,De=null,Ae||(Ae=!0,Ie=c)}}(r,t,void 0,e),e.currentTarget=null}function Ar(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;Dr(a,i,u),l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,u=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;Dr(a,i,u),l=s}}}if(Ae)throw e=Ie,Ae=!1,Ie=null,e}function Ir(e,t){var n=t[ha];void 0===n&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Mr(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[Br]){e[Br]=!0,o.forEach(function(t){"selectionchange"!==t&&(Fr.has(t)||Mr(t,!1,e),Mr(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Mr("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Xt(t)){case 1:var a=$t;break;case 4:a=qt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Vr(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&((s=o.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;o=o.return}for(;null!==i;){if(null===(o=ya(i)))return;if(5===(s=o.tag)||6===s){r=l=o;continue e}i=i.parentNode}}r=r.return}Oe(function(){var r=l,a=we(n),o=[];e:{var i=Rr.get(e);if(void 0!==i){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=_n;break;case"focusin":u="focus",s=gn;break;case"focusout":u="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Pn;break;case Er:case _r:case Cr:s=vn;break;case Pr:s=Rn;break;case"scroll":s=fn;break;case"wheel":s=Tn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Cn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,m=r;null!==m;){var h=(p=m).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&(null!=(h=Te(m,f))&&c.push($r(m,h,p)))),d)break;m=m.return}0<c.length&&(i=new s(i,u,null,n,a),o.push({event:i,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!ya(u)&&!u[ma])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?ya(u):null)&&(u!==(d=We(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=mn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(c=Cn,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?i:xa(s),p=null==u?i:xa(u),(i=new c(h,m+"leave",s,n,a)).target=d,i.relatedTarget=p,h=null,ya(a)===r&&((c=new c(f,m+"enter",u,n,a)).target=p,c.relatedTarget=d,h=c),d=h,s&&u)e:{for(f=u,m=0,p=c=s;p;p=Qr(p))m++;for(p=0,h=f;h;h=Qr(h))p++;for(;0<m-p;)c=Qr(c),m--;for(;0<p-m;)f=Qr(f),p--;for(;m--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==s&&Kr(o,i,s,c,!1),null!==u&&null!==d&&Kr(o,d,u,c,!0)}if("select"===(s=(i=r?xa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=Xn;else if(Vn(i))if(Yn)g=or;else{g=ar;var v=rr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=lr);switch(g&&(g=g(e,r))?$n(o,g,n,a):(v&&v(e,i,r),"focusout"===e&&(v=i._wrapperState)&&v.controlled&&"number"===i.type&&ee(i,"number",i.value)),v=r?xa(r):window,e){case"focusin":(Vn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(o,n,a);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":xr(o,n,a)}var y;if(Un)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Wn?Mn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Dn&&"ko"!==n.locale&&(Wn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Wn&&(y=en()):(Gt="value"in(Yt=a)?Yt.value:Yt.textContent,Wn=!0)),0<(v=qr(r,b)).length&&(b=new xn(b,e,null,n,a),o.push({event:b,listeners:v}),y?b.data=y:null!==(y=Bn(n))&&(b.data=y))),(y=Fn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(In=!0,An);case"textInput":return(e=t.data)===An&&In?null:e;default:return null}}(e,n):function(e,t){if(Wn)return"compositionend"===e||!Un&&Mn(e,t)?(e=en(),Zt=Gt=Yt=null,Wn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Dn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=y))}Ar(o,t)})}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Te(e,n))&&r.unshift($r(e,l,a)),null!=(l=Te(e,t))&&r.push($r(e,l,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Te(n,l))&&o.unshift($r(n,s,i)):a||null!=(s=Te(n,l))&&o.push($r(n,s,i))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Jr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Jr,"\n").replace(Xr,"")}function Gr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(l(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,la="function"===typeof Promise?Promise:void 0,oa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof la?function(e){return la.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Wt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Wt(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ma="__reactContainer$"+da,ha="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ma]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ma])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function wa(e){return e[pa]||null}var Sa=[],ka=-1;function Na(e){return{current:e}}function ja(e){0>ka||(e.current=Sa[ka],Sa[ka]=null,ka--)}function Ea(e,t){ka++,Sa[ka]=e.current,e.current=t}var _a={},Ca=Na(_a),Pa=Na(!1),Ra=_a;function Oa(e,t){var n=e.type.contextTypes;if(!n)return _a;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Ta(e){return null!==(e=e.childContextTypes)&&void 0!==e}function La(){ja(Pa),ja(Ca)}function Ua(e,t,n){if(Ca.current!==_a)throw Error(l(168));Ea(Ca,t),Ea(Pa,n)}function za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,H(e)||"Unknown",a));return D({},n,r)}function Fa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_a,Ra=Ca.current,Ea(Ca,e),Ea(Pa,Pa.current),!0}function Da(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=za(e,t,Ra),r.__reactInternalMemoizedMergedChildContext=e,ja(Pa),ja(Ca),Ea(Ca,e)):ja(Pa),Ea(Pa,n)}var Aa=null,Ia=!1,Ma=!1;function Ba(e){null===Aa?Aa=[e]:Aa.push(e)}function Wa(){if(!Ma&&null!==Aa){Ma=!0;var e=0,t=bt;try{var n=Aa;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Aa=null,Ia=!1}catch(a){throw null!==Aa&&(Aa=Aa.slice(e+1)),Qe(Ze,Wa),a}finally{bt=t,Ma=!1}}return null}var Ha=[],Va=0,$a=null,qa=0,Qa=[],Ka=0,Ja=null,Xa=1,Ya="";function Ga(e,t){Ha[Va++]=qa,Ha[Va++]=$a,$a=e,qa=t}function Za(e,t,n){Qa[Ka++]=Xa,Qa[Ka++]=Ya,Qa[Ka++]=Ja,Ja=e;var r=Xa;e=Ya;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var l=32-ot(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xa=1<<32-ot(t)+a|n<<a|r,Ya=l+e}else Xa=1<<l|n<<a|r,Ya=e}function el(e){null!==e.return&&(Ga(e,1),Za(e,1,0))}function tl(e){for(;e===$a;)$a=Ha[--Va],Ha[Va]=null,qa=Ha[--Va],Ha[Va]=null;for(;e===Ja;)Ja=Qa[--Ka],Qa[Ka]=null,Ya=Qa[--Ka],Qa[Ka]=null,Xa=Qa[--Ka],Qa[Ka]=null}var nl=null,rl=null,al=!1,ll=null;function ol(e,t){var n=Ou(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function il(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,nl=e,rl=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,nl=e,rl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ja?{id:Xa,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ou(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nl=e,rl=null,!0);default:return!1}}function sl(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ul(e){if(al){var t=rl;if(t){var n=t;if(!il(e,t)){if(sl(e))throw Error(l(418));t=ua(n.nextSibling);var r=nl;t&&il(e,t)?ol(r,n):(e.flags=-4097&e.flags|2,al=!1,nl=e)}}else{if(sl(e))throw Error(l(418));e.flags=-4097&e.flags|2,al=!1,nl=e}}}function cl(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nl=e}function dl(e){if(e!==nl)return!1;if(!al)return cl(e),al=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=rl)){if(sl(e))throw fl(),Error(l(418));for(;t;)ol(e,t),t=ua(t.nextSibling)}if(cl(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){rl=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}rl=null}}else rl=nl?ua(e.stateNode.nextSibling):null;return!0}function fl(){for(var e=rl;e;)e=ua(e.nextSibling)}function pl(){rl=nl=null,al=!1}function ml(e){null===ll?ll=[e]:ll.push(e)}var hl=x.ReactCurrentBatchConfig;function gl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function vl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yl(e){return(0,e._init)(e._payload)}function bl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Lu(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Du(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===T&&yl(l)===t.type)?((r=a(t,n.props)).ref=gl(e,t,n),r.return=e,r):((r=Uu(n.type,n.key,n.props,null,e.mode,r)).ref=gl(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Au(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=zu(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Du(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Uu(t.type,t.key,t.props,null,e.mode,n)).ref=gl(e,null,t),n.return=e,n;case S:return(t=Au(t,e.mode,n)).return=e,t;case T:return f(e,(0,t._init)(t._payload),n)}if(te(t)||z(t))return(t=zu(t,e.mode,n,null)).return=e,t;vl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case S:return n.key===a?c(e,t,n,r):null;case T:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||z(n))return null!==a?null:d(e,t,n,r,null);vl(e,n)}return null}function m(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case T:return m(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||z(r))return d(t,e=e.get(n)||null,r,a,null);vl(t,r)}return null}function h(a,l,i,s){for(var u=null,c=null,d=l,h=l=0,g=null;null!==d&&h<i.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=p(a,d,i[h],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),l=o(v,l,h),null===c?u=v:c.sibling=v,c=v,d=g}if(h===i.length)return n(a,d),al&&Ga(a,h),u;if(null===d){for(;h<i.length;h++)null!==(d=f(a,i[h],s))&&(l=o(d,l,h),null===c?u=d:c.sibling=d,c=d);return al&&Ga(a,h),u}for(d=r(a,d);h<i.length;h++)null!==(g=m(d,a,h,i[h],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),l=o(g,l,h),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),al&&Ga(a,h),u}function g(a,i,s,u){var c=z(s);if("function"!==typeof c)throw Error(l(150));if(null==(s=c.call(s)))throw Error(l(151));for(var d=c=null,h=i,g=i=0,v=null,y=s.next();null!==h&&!y.done;g++,y=s.next()){h.index>g?(v=h,h=null):v=h.sibling;var b=p(a,h,y.value,u);if(null===b){null===h&&(h=v);break}e&&h&&null===b.alternate&&t(a,h),i=o(b,i,g),null===d?c=b:d.sibling=b,d=b,h=v}if(y.done)return n(a,h),al&&Ga(a,g),c;if(null===h){for(;!y.done;g++,y=s.next())null!==(y=f(a,y.value,u))&&(i=o(y,i,g),null===d?c=y:d.sibling=y,d=y);return al&&Ga(a,g),c}for(h=r(a,h);!y.done;g++,y=s.next())null!==(y=m(h,a,g,y.value,u))&&(e&&null!==y.alternate&&h.delete(null===y.key?g:y.key),i=o(y,i,g),null===d?c=y:d.sibling=y,d=y);return e&&h.forEach(function(e){return t(a,e)}),al&&Ga(a,g),c}return function e(r,l,o,s){if("object"===typeof o&&null!==o&&o.type===k&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var u=o.key,c=l;null!==c;){if(c.key===u){if((u=o.type)===k){if(7===c.tag){n(r,c.sibling),(l=a(c,o.props.children)).return=r,r=l;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===T&&yl(u)===c.type){n(r,c.sibling),(l=a(c,o.props)).ref=gl(r,c,o),l.return=r,r=l;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===k?((l=zu(o.props.children,r.mode,s,o.key)).return=r,r=l):((s=Uu(o.type,o.key,o.props,null,r.mode,s)).ref=gl(r,l,o),s.return=r,r=s)}return i(r);case S:e:{for(c=o.key;null!==l;){if(l.key===c){if(4===l.tag&&l.stateNode.containerInfo===o.containerInfo&&l.stateNode.implementation===o.implementation){n(r,l.sibling),(l=a(l,o.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Au(o,r.mode,s)).return=r,r=l}return i(r);case T:return e(r,l,(c=o._init)(o._payload),s)}if(te(o))return h(r,l,o,s);if(z(o))return g(r,l,o,s);vl(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,o)).return=r,r=l):(n(r,l),(l=Du(o,r.mode,s)).return=r,r=l),i(r)):n(r,l)}}var xl=bl(!0),wl=bl(!1),Sl=Na(null),kl=null,Nl=null,jl=null;function El(){jl=Nl=kl=null}function _l(e){var t=Sl.current;ja(Sl),e._currentValue=t}function Cl(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Pl(e,t){kl=e,jl=Nl=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bi=!0),e.firstContext=null)}function Rl(e){var t=e._currentValue;if(jl!==e)if(e={context:e,memoizedValue:t,next:null},null===Nl){if(null===kl)throw Error(l(308));Nl=e,kl.dependencies={lanes:0,firstContext:e}}else Nl=Nl.next=e;return t}var Ol=null;function Tl(e){null===Ol?Ol=[e]:Ol.push(e)}function Ll(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Tl(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ul(e,r)}function Ul(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var zl=!1;function Fl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Dl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Al(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Il(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Cs)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Ul(e,n)}return null===(a=r.interleaved)?(t.next=t,Tl(r)):(t.next=a.next,a.next=t),r.interleaved=t,Ul(e,n)}function Ml(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Bl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wl(e,t,n,r){var a=e.updateQueue;zl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===o?l=u:o.next=u,o=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(o=0,c=u=s=null,i=l;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var m=e,h=i;switch(f=t,p=n,h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(p,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=h.payload)?m.call(p,d,f):m)||void 0===f)break e;d=D({},d,f);break e;case 2:zl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,o|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Fs|=o,e.lanes=o,e.memoizedState=d}}function Hl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var Vl={},$l=Na(Vl),ql=Na(Vl),Ql=Na(Vl);function Kl(e){if(e===Vl)throw Error(l(174));return e}function Jl(e,t){switch(Ea(Ql,t),Ea(ql,e),Ea($l,Vl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}ja($l),Ea($l,t)}function Xl(){ja($l),ja(ql),ja(Ql)}function Yl(e){Kl(Ql.current);var t=Kl($l.current),n=se(t,e.type);t!==n&&(Ea(ql,e),Ea($l,n))}function Gl(e){ql.current===e&&(ja($l),ja(ql))}var Zl=Na(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=x.ReactCurrentDispatcher,ao=x.ReactCurrentBatchConfig,lo=0,oo=null,io=null,so=null,uo=!1,co=!1,fo=0,po=0;function mo(){throw Error(l(321))}function ho(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function go(e,t,n,r,a,o){if(lo=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Zo:ei,e=n(r,a),co){o=0;do{if(co=!1,fo=0,25<=o)throw Error(l(301));o+=1,so=io=null,t.updateQueue=null,ro.current=ti,e=n(r,a)}while(co)}if(ro.current=Go,t=null!==io&&null!==io.next,lo=0,so=io=oo=null,uo=!1,t)throw Error(l(300));return e}function vo(){var e=0!==fo;return fo=0,e}function yo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===so?oo.memoizedState=so=e:so=so.next=e,so}function bo(){if(null===io){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=io.next;var t=null===so?oo.memoizedState:so.next;if(null!==t)so=t,io=e;else{if(null===e)throw Error(l(310));e={memoizedState:(io=e).memoizedState,baseState:io.baseState,baseQueue:io.baseQueue,queue:io.queue,next:null},null===so?oo.memoizedState=so=e:so=so.next=e}return so}function xo(e,t){return"function"===typeof t?t(e):t}function wo(e){var t=bo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=io,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=i=null,u=null,c=o;do{var d=c.lane;if((lo&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,oo.lanes|=d,Fs|=d}c=c.next}while(null!==c&&c!==o);null===u?i=r:u.next=s,ir(r,t.memoizedState)||(bi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,Fs|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function So(e){var t=bo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);ir(o,t.memoizedState)||(bi=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function ko(){}function No(e,t){var n=oo,r=bo(),a=t(),o=!ir(r.memoizedState,a);if(o&&(r.memoizedState=a,bi=!0),r=r.queue,Fo(_o.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==so&&1&so.memoizedState.tag){if(n.flags|=2048,Oo(9,Eo.bind(null,n,r,a,t),void 0,null),null===Ps)throw Error(l(349));0!==(30&lo)||jo(n,t,a)}return a}function jo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Eo(e,t,n,r){t.value=n,t.getSnapshot=r,Co(t)&&Po(e)}function _o(e,t,n){return n(function(){Co(t)&&Po(e)})}function Co(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(r){return!0}}function Po(e){var t=Ul(e,1);null!==t&&nu(t,e,1,-1)}function Ro(e){var t=yo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=Ko.bind(null,oo,e),[t.memoizedState,e]}function Oo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function To(){return bo().memoizedState}function Lo(e,t,n,r){var a=yo();oo.flags|=e,a.memoizedState=Oo(1|t,n,void 0,void 0===r?null:r)}function Uo(e,t,n,r){var a=bo();r=void 0===r?null:r;var l=void 0;if(null!==io){var o=io.memoizedState;if(l=o.destroy,null!==r&&ho(r,o.deps))return void(a.memoizedState=Oo(t,n,l,r))}oo.flags|=e,a.memoizedState=Oo(1|t,n,l,r)}function zo(e,t){return Lo(8390656,8,e,t)}function Fo(e,t){return Uo(2048,8,e,t)}function Do(e,t){return Uo(4,2,e,t)}function Ao(e,t){return Uo(4,4,e,t)}function Io(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Mo(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Uo(4,4,Io.bind(null,t,e),n)}function Bo(){}function Wo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ho(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ho(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ho(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vo(e,t,n){return 0===(21&lo)?(e.baseState&&(e.baseState=!1,bi=!0),e.memoizedState=n):(ir(n,t)||(n=ht(),oo.lanes|=n,Fs|=n,e.baseState=!0),t)}function $o(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{bt=n,ao.transition=r}}function qo(){return bo().memoizedState}function Qo(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jo(e))Xo(t,n);else if(null!==(n=Ll(e,t,n,r))){nu(n,e,r,eu()),Yo(n,t,r)}}function Ko(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jo(e))Xo(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,o)){var s=t.interleaved;return null===s?(a.next=a,Tl(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Ll(e,t,a,r))&&(nu(n,e,r,a=eu()),Yo(n,t,r))}}function Jo(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Xo(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yo(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Go={readContext:Rl,useCallback:mo,useContext:mo,useEffect:mo,useImperativeHandle:mo,useInsertionEffect:mo,useLayoutEffect:mo,useMemo:mo,useReducer:mo,useRef:mo,useState:mo,useDebugValue:mo,useDeferredValue:mo,useTransition:mo,useMutableSource:mo,useSyncExternalStore:mo,useId:mo,unstable_isNewReconciler:!1},Zo={readContext:Rl,useCallback:function(e,t){return yo().memoizedState=[e,void 0===t?null:t],e},useContext:Rl,useEffect:zo,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Lo(4194308,4,Io.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Lo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Lo(4,2,e,t)},useMemo:function(e,t){var n=yo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yo().memoizedState=e},useState:Ro,useDebugValue:Bo,useDeferredValue:function(e){return yo().memoizedState=e},useTransition:function(){var e=Ro(!1),t=e[0];return e=$o.bind(null,e[1]),yo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=yo();if(al){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Ps)throw Error(l(349));0!==(30&lo)||jo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,zo(_o.bind(null,r,o,e),[e]),r.flags|=2048,Oo(9,Eo.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=yo(),t=Ps.identifierPrefix;if(al){var n=Ya;t=":"+t+"R"+(n=(Xa&~(1<<32-ot(Xa)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=po++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ei={readContext:Rl,useCallback:Wo,useContext:Rl,useEffect:Fo,useImperativeHandle:Mo,useInsertionEffect:Do,useLayoutEffect:Ao,useMemo:Ho,useReducer:wo,useRef:To,useState:function(){return wo(xo)},useDebugValue:Bo,useDeferredValue:function(e){return Vo(bo(),io.memoizedState,e)},useTransition:function(){return[wo(xo)[0],bo().memoizedState]},useMutableSource:ko,useSyncExternalStore:No,useId:qo,unstable_isNewReconciler:!1},ti={readContext:Rl,useCallback:Wo,useContext:Rl,useEffect:Fo,useImperativeHandle:Mo,useInsertionEffect:Do,useLayoutEffect:Ao,useMemo:Ho,useReducer:So,useRef:To,useState:function(){return So(xo)},useDebugValue:Bo,useDeferredValue:function(e){var t=bo();return null===io?t.memoizedState=e:Vo(t,io.memoizedState,e)},useTransition:function(){return[So(xo)[0],bo().memoizedState]},useMutableSource:ko,useSyncExternalStore:No,useId:qo,unstable_isNewReconciler:!1};function ni(e,t){if(e&&e.defaultProps){for(var n in t=D({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ri(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:D({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ai={isMounted:function(e){return!!(e=e._reactInternals)&&We(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Al(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Il(e,l,a))&&(nu(t,e,a,r),Ml(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Al(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Il(e,l,a))&&(nu(t,e,a,r),Ml(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Al(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Il(e,a,r))&&(nu(t,e,r,n),Ml(t,e,r))}};function li(e,t,n,r,a,l,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,l))}function oi(e,t,n){var r=!1,a=_a,l=t.contextType;return"object"===typeof l&&null!==l?l=Rl(l):(a=Ta(t)?Ra:Ca.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?Oa(e,a):_a),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ai,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function ii(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ai.enqueueReplaceState(t,t.state,null)}function si(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fl(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Rl(l):(l=Ta(t)?Ra:Ca.current,a.context=Oa(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(ri(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ai.enqueueReplaceState(a,a.state,null),Wl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function ui(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function di(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fi="function"===typeof WeakMap?WeakMap:Map;function pi(e,t,n){(n=Al(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vs||(Vs=!0,$s=r),di(0,t)},n}function mi(e,t,n){(n=Al(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){di(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){di(0,t),"function"!==typeof r&&(null===qs?qs=new Set([this]):qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function hi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=ju.bind(null,e,t,n),t.then(e,e))}function gi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Al(-1,1)).tag=2,Il(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var yi=x.ReactCurrentOwner,bi=!1;function xi(e,t,n,r){t.child=null===e?wl(t,null,n,r):xl(t,e.child,n,r)}function wi(e,t,n,r,a){n=n.render;var l=t.ref;return Pl(t,a),r=go(e,t,n,r,l,a),n=vo(),null===e||bi?(al&&n&&el(t),t.flags|=1,xi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vi(e,t,a))}function Si(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Tu(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Uu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,ki(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(o,r)&&e.ref===t.ref)return Vi(e,t,a)}return t.flags|=1,(e=Lu(l,r)).ref=t.ref,e.return=t,t.child=e}function ki(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(sr(l,r)&&e.ref===t.ref){if(bi=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,Vi(e,t,a);0!==(131072&e.flags)&&(bi=!0)}}return Ei(e,t,n,r,a)}function Ni(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ea(Ls,Ts),Ts|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ea(Ls,Ts),Ts|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Ea(Ls,Ts),Ts|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Ea(Ls,Ts),Ts|=r;return xi(e,t,a,n),t.child}function ji(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ei(e,t,n,r,a){var l=Ta(n)?Ra:Ca.current;return l=Oa(t,l),Pl(t,a),n=go(e,t,n,r,l,a),r=vo(),null===e||bi?(al&&r&&el(t),t.flags|=1,xi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vi(e,t,a))}function _i(e,t,n,r,a){if(Ta(n)){var l=!0;Fa(t)}else l=!1;if(Pl(t,a),null===t.stateNode)Hi(e,t),oi(t,n,r),si(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,i=t.memoizedProps;o.props=i;var s=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=Rl(u):u=Oa(t,u=Ta(n)?Ra:Ca.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==r||s!==u)&&ii(t,o,r,u),zl=!1;var f=t.memoizedState;o.state=f,Wl(t,r,o,a),s=t.memoizedState,i!==r||f!==s||Pa.current||zl?("function"===typeof c&&(ri(t,n,c,r),s=t.memoizedState),(i=zl||li(t,n,i,r,f,s,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=u,r=i):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Dl(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:ni(t.type,i),o.props=u,d=t.pendingProps,f=o.context,"object"===typeof(s=n.contextType)&&null!==s?s=Rl(s):s=Oa(t,s=Ta(n)?Ra:Ca.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==s)&&ii(t,o,r,s),zl=!1,f=t.memoizedState,o.state=f,Wl(t,r,o,a);var m=t.memoizedState;i!==d||f!==m||Pa.current||zl?("function"===typeof p&&(ri(t,n,p,r),m=t.memoizedState),(u=zl||li(t,n,u,r,f,m,s)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,m,s),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,m,s)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),o.props=r,o.state=m,o.context=s,r=u):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ci(e,t,n,r,l,a)}function Ci(e,t,n,r,a,l){ji(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&Da(t,n,!1),Vi(e,t,l);r=t.stateNode,yi.current=t;var i=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=xl(t,e.child,null,l),t.child=xl(t,null,i,l)):xi(e,t,i,l),t.memoizedState=r.state,a&&Da(t,n,!0),t.child}function Pi(e){var t=e.stateNode;t.pendingContext?Ua(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ua(0,t.context,!1),Jl(e,t.containerInfo)}function Ri(e,t,n,r,a){return pl(),ml(a),t.flags|=256,xi(e,t,n,r),t.child}var Oi,Ti,Li,Ui,zi={dehydrated:null,treeContext:null,retryLane:0};function Fi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Di(e,t,n){var r,a=t.pendingProps,o=Zl.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Ea(Zl,1&o),null===e)return ul(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Fu(s,a,0,null),e=zu(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Fi(n),t.memoizedState=zi,e):Ai(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,Ii(e,t,i,r=ci(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Fu({mode:"visible",children:r.children},a,0,null),(o=zu(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&xl(t,e.child,null,i),t.child.memoizedState=Fi(i),t.memoizedState=zi,o);if(0===(1&t.mode))return Ii(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Ii(e,t,i,r=ci(o=Error(l(419)),r,void 0))}if(s=0!==(i&e.childLanes),bi||s){if(null!==(r=Ps)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==o.retryLane&&(o.retryLane=a,Ul(e,a),nu(r,e,a,-1))}return hu(),Ii(e,t,i,r=ci(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=_u.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,rl=ua(a.nextSibling),nl=t,al=!0,ll=null,null!==e&&(Qa[Ka++]=Xa,Qa[Ka++]=Ya,Qa[Ka++]=Ja,Xa=e.id,Ya=e.overflow,Ja=t),t=Ai(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,o,n);if(i){i=a.fallback,s=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Lu(o,u)).subtreeFlags=14680064&o.subtreeFlags,null!==r?i=Lu(r,i):(i=zu(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Fi(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=zi,a}return e=(i=e.child).sibling,a=Lu(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ai(e,t){return(t=Fu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ii(e,t,n,r){return null!==r&&ml(r),xl(t,e.child,null,n),(e=Ai(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Mi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Cl(e.return,t,n)}function Bi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Wi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(xi(e,t,r.children,n),0!==(2&(r=Zl.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Mi(e,n,t);else if(19===e.tag)Mi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ea(Zl,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bi(t,!0,n,null,l);break;case"together":Bi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Fs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Lu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Lu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $i(e,t){if(!al)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qi(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qi(t),null;case 1:case 17:return Ta(t.type)&&La(),qi(t),null;case 3:return r=t.stateNode,Xl(),ja(Pa),ja(Ca),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(dl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ll&&(ou(ll),ll=null))),Ti(e,t),qi(t),null;case 5:Gl(t);var a=Kl(Ql.current);if(n=t.type,null!==e&&null!=t.stateNode)Li(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return qi(t),null}if(e=Kl($l.current),dl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[pa]=o,e=0!==(1&t.mode),n){case"dialog":Ir("cancel",r),Ir("close",r);break;case"iframe":case"object":case"embed":Ir("load",r);break;case"video":case"audio":for(a=0;a<zr.length;a++)Ir(zr[a],r);break;case"source":Ir("error",r);break;case"img":case"image":case"link":Ir("error",r),Ir("load",r);break;case"details":Ir("toggle",r);break;case"input":X(r,o),Ir("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Ir("invalid",r);break;case"textarea":ae(r,o),Ir("invalid",r)}for(var s in ye(n,o),a=null,o)if(o.hasOwnProperty(s)){var u=o[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Ir("scroll",r)}switch(n){case"input":q(r),Z(r,o,!0);break;case"textarea":q(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Oi(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Ir("cancel",e),Ir("close",e),a=r;break;case"iframe":case"object":case"embed":Ir("load",e),a=r;break;case"video":case"audio":for(a=0;a<zr.length;a++)Ir(zr[a],e);a=r;break;case"source":Ir("error",e),a=r;break;case"img":case"image":case"link":Ir("error",e),Ir("load",e),a=r;break;case"details":Ir("toggle",e),a=r;break;case"input":X(e,r),a=J(e,r),Ir("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=D({},r,{value:void 0}),Ir("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ir("invalid",e)}for(o in ye(n,a),u=a)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?ge(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(i.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Ir("scroll",e):null!=c&&b(e,o,c,s))}switch(n){case"input":q(e),Z(e,r,!1);break;case"textarea":q(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qi(t),null;case 6:if(e&&null!=t.stateNode)Ui(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=Kl(Ql.current),Kl($l.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=nl))switch(e.tag){case 3:Gr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Gr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return qi(t),null;case 13:if(ja(Zl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(al&&null!==rl&&0!==(1&t.mode)&&0===(128&t.flags))fl(),pl(),t.flags|=98560,o=!1;else if(o=dl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[fa]=t}else pl(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qi(t),o=!1}else null!==ll&&(ou(ll),ll=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Zl.current)?0===Us&&(Us=3):hu())),null!==t.updateQueue&&(t.flags|=4),qi(t),null);case 4:return Xl(),Ti(e,t),null===e&&Wr(t.stateNode.containerInfo),qi(t),null;case 10:return _l(t.type._context),qi(t),null;case 19:if(ja(Zl),null===(o=t.memoizedState))return qi(t),null;if(r=0!==(128&t.flags),null===(s=o.rendering))if(r)$i(o,!1);else{if(0!==Us||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=eo(e))){for(t.flags|=128,$i(o,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(s=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ea(Zl,1&Zl.current|2),t.child}e=e.sibling}null!==o.tail&&Ye()>Ws&&(t.flags|=128,r=!0,$i(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$i(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!al)return qi(t),null}else 2*Ye()-o.renderingStartTime>Ws&&1073741824!==n&&(t.flags|=128,r=!0,$i(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=o.last)?n.sibling=s:t.child=s,o.last=s)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ye(),t.sibling=null,n=Zl.current,Ea(Zl,r?1&n|2:1&n),t):(qi(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ts)&&(qi(t),6&t.subtreeFlags&&(t.flags|=8192)):qi(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Ki(e,t){switch(tl(t),t.tag){case 1:return Ta(t.type)&&La(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xl(),ja(Pa),ja(Ca),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Gl(t),null;case 13:if(ja(Zl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return ja(Zl),null;case 4:return Xl(),null;case 10:return _l(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Oi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ti=function(){},Li=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Kl($l.current);var l,o=null;switch(n){case"input":a=J(e,a),r=J(e,r),o=[];break;case"select":a=D({},a,{value:void 0}),r=D({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ye(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(l in s)!s.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&s[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ir("scroll",e),o||s===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},Ui=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ji=!1,Xi=!1,Yi="function"===typeof WeakSet?WeakSet:Set,Gi=null;function Zi(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Nu(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Nu(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&es(t,n,l)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ls(e){var t=e.alternate;null!==t&&(e.alternate=null,ls(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ha],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function os(e){return 5===e.tag||3===e.tag||4===e.tag}function is(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||os(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}var cs=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(lt&&"function"===typeof lt.onCommitFiberUnmount)try{lt.onCommitFiberUnmount(at,n)}catch(i){}switch(n.tag){case 5:Xi||Zi(n,t);case 6:var r=cs,a=ds;cs=null,fs(e,t,n),ds=a,null!==(cs=r)&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cs.removeChild(n.stateNode));break;case 18:null!==cs&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Wt(e)):sa(cs,n.stateNode));break;case 4:r=cs,a=ds,cs=n.stateNode.containerInfo,ds=!0,fs(e,t,n),cs=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Xi&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,void 0!==o&&(0!==(2&l)||0!==(4&l))&&es(n,t,o),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Xi&&(Zi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){Nu(n,t,i)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Xi=(r=Xi)||null!==n.memoizedState,fs(e,t,n),Xi=r):fs(e,t,n);break;default:fs(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Yi),t.forEach(function(t){var r=Cu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function hs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:cs=s.stateNode,ds=!1;break e;case 3:case 4:cs=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===cs)throw Error(l(160));ps(o,i,a),cs=null,ds=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Nu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(hs(t,e),vs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(g){Nu(e,e.return,g)}try{ns(5,e,e.return)}catch(g){Nu(e,e.return,g)}}break;case 1:hs(t,e),vs(e),512&r&&null!==n&&Zi(n,n.return);break;case 5:if(hs(t,e),vs(e),512&r&&null!==n&&Zi(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Nu(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===o.type&&null!=o.name&&Y(a,o),be(s,i);var c=be(s,o);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(s){case"input":G(a,o);break;case"textarea":le(a,o);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var m=o.value;null!=m?ne(a,!!o.multiple,m,!1):p!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[pa]=o}catch(g){Nu(e,e.return,g)}}break;case 6:if(hs(t,e),vs(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(g){Nu(e,e.return,g)}}break;case 3:if(hs(t,e),vs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Wt(t.containerInfo)}catch(g){Nu(e,e.return,g)}break;case 4:default:hs(t,e),vs(e);break;case 13:hs(t,e),vs(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Bs=Ye())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xi=(c=Xi)||d,hs(t,e),Xi=c):hs(t,e),vs(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Gi=e,d=e.child;null!==d;){for(f=Gi=d;null!==Gi;){switch(m=(p=Gi).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Zi(p,p.return);var h=p.stateNode;if("function"===typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(g){Nu(r,n,g)}}break;case 5:Zi(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==m?(m.return=p,Gi=m):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=f.stateNode,i=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=he("display",i))}catch(g){Nu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Nu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:hs(t,e),vs(e),4&r&&ms(e);case 21:}}function vs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(os(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),us(e,is(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ss(e,is(e),o);break;default:throw Error(l(161))}}catch(i){Nu(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ys(e,t,n){Gi=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Gi;){var a=Gi,l=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Ji;if(!o){var i=a.alternate,s=null!==i&&null!==i.memoizedState||Xi;i=Ji;var u=Xi;if(Ji=o,(Xi=s)&&!u)for(Gi=a;null!==Gi;)s=(o=Gi).child,22===o.tag&&null!==o.memoizedState?Ss(a):null!==s?(s.return=o,Gi=s):Ss(a);for(;null!==l;)Gi=l,bs(l,t,n),l=l.sibling;Gi=a,Ji=i,Xi=u}xs(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,Gi=l):xs(e)}}function xs(e){for(;null!==Gi;){var t=Gi;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xi||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xi)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ni(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Hl(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Hl(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Wt(f)}}}break;default:throw Error(l(163))}Xi||512&t.flags&&as(t)}catch(p){Nu(t,t.return,p)}}if(t===e){Gi=null;break}if(null!==(n=t.sibling)){n.return=t.return,Gi=n;break}Gi=t.return}}function ws(e){for(;null!==Gi;){var t=Gi;if(t===e){Gi=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Gi=n;break}Gi=t.return}}function Ss(e){for(;null!==Gi;){var t=Gi;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Nu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Nu(t,a,s)}}var l=t.return;try{as(t)}catch(s){Nu(t,l,s)}break;case 5:var o=t.return;try{as(t)}catch(s){Nu(t,o,s)}}}catch(s){Nu(t,t.return,s)}if(t===e){Gi=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Gi=i;break}Gi=t.return}}var ks,Ns=Math.ceil,js=x.ReactCurrentDispatcher,Es=x.ReactCurrentOwner,_s=x.ReactCurrentBatchConfig,Cs=0,Ps=null,Rs=null,Os=0,Ts=0,Ls=Na(0),Us=0,zs=null,Fs=0,Ds=0,As=0,Is=null,Ms=null,Bs=0,Ws=1/0,Hs=null,Vs=!1,$s=null,qs=null,Qs=!1,Ks=null,Js=0,Xs=0,Ys=null,Gs=-1,Zs=0;function eu(){return 0!==(6&Cs)?Ye():-1!==Gs?Gs:Gs=Ye()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Cs)&&0!==Os?Os&-Os:null!==hl.transition?(0===Zs&&(Zs=ht()),Zs):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nu(e,t,n,r){if(50<Xs)throw Xs=0,Ys=null,Error(l(185));vt(e,n,r),0!==(2&Cs)&&e===Ps||(e===Ps&&(0===(2&Cs)&&(Ds|=n),4===Us&&iu(e,Os)),ru(e,r),1===n&&0===Cs&&0===(1&t.mode)&&(Ws=Ye()+500,Ia&&Wa()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-ot(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=pt(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=ft(e,e===Ps?Os:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ia=!0,Ba(e)}(su.bind(null,e)):Ba(su.bind(null,e)),oa(function(){0===(6&Cs)&&Wa()}),n=null;else{switch(xt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Pu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Gs=-1,Zs=0,0!==(6&Cs))throw Error(l(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===Ps?Os:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=Cs;Cs|=2;var o=mu();for(Ps===e&&Os===t||(Hs=null,Ws=Ye()+500,fu(e,t));;)try{yu();break}catch(s){pu(e,s)}El(),js.current=o,Cs=a,null!==Rs?t=0:(Ps=null,Os=0,t=Us)}if(0!==t){if(2===t&&(0!==(a=mt(e))&&(r=a,t=lu(e,a))),1===t)throw n=zs,fu(e,0),iu(e,r),ru(e,Ye()),n;if(6===t)iu(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!ir(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gu(e,r))&&(0!==(o=mt(e))&&(r=o,t=lu(e,o))),1===t))throw n=zs,fu(e,0),iu(e,r),ru(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:wu(e,Ms,Hs);break;case 3:if(iu(e,r),(130023424&r)===r&&10<(t=Bs+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,Ms,Hs),t);break}wu(e,Ms,Hs);break;case 4:if(iu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-ot(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ns(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,Ms,Hs),r);break}wu(e,Ms,Hs);break;default:throw Error(l(329))}}}return ru(e,Ye()),e.callbackNode===n?au.bind(null,e):null}function lu(e,t){var n=Is;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Ms,Ms=n,null!==t&&ou(t)),e}function ou(e){null===Ms?Ms=e:Ms.push.apply(Ms,e)}function iu(e,t){for(t&=~As,t&=~Ds,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function su(e){if(0!==(6&Cs))throw Error(l(327));Su();var t=ft(e,0);if(0===(1&t))return ru(e,Ye()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=lu(e,r))}if(1===n)throw n=zs,fu(e,0),iu(e,t),ru(e,Ye()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Ms,Hs),ru(e,Ye()),null}function uu(e,t){var n=Cs;Cs|=1;try{return e(t)}finally{0===(Cs=n)&&(Ws=Ye()+500,Ia&&Wa())}}function cu(e){null!==Ks&&0===Ks.tag&&0===(6&Cs)&&Su();var t=Cs;Cs|=1;var n=_s.transition,r=bt;try{if(_s.transition=null,bt=1,e)return e()}finally{bt=r,_s.transition=n,0===(6&(Cs=t))&&Wa()}}function du(){Ts=Ls.current,ja(Ls)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Rs)for(n=Rs.return;null!==n;){var r=n;switch(tl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&La();break;case 3:Xl(),ja(Pa),ja(Ca),no();break;case 5:Gl(r);break;case 4:Xl();break;case 13:case 19:ja(Zl);break;case 10:_l(r.type._context);break;case 22:case 23:du()}n=n.return}if(Ps=e,Rs=e=Lu(e.current,null),Os=Ts=t,Us=0,zs=null,As=Ds=Fs=0,Ms=Is=null,null!==Ol){for(t=0;t<Ol.length;t++)if(null!==(r=(n=Ol[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var o=l.next;l.next=a,r.next=o}n.pending=r}Ol=null}return e}function pu(e,t){for(;;){var n=Rs;try{if(El(),ro.current=Go,uo){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}uo=!1}if(lo=0,so=io=oo=null,co=!1,fo=0,Es.current=null,null===n||null===n.return){Us=1,zs=t,Rs=null;break}e:{var o=e,i=n.return,s=n,u=t;if(t=Os,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=gi(i);if(null!==m){m.flags&=-257,vi(m,i,s,0,t),1&m.mode&&hi(o,c,t),u=c;var h=(t=m).updateQueue;if(null===h){var g=new Set;g.add(u),t.updateQueue=g}else h.add(u);break e}if(0===(1&t)){hi(o,c,t),hu();break e}u=Error(l(426))}else if(al&&1&s.mode){var v=gi(i);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vi(v,i,s,0,t),ml(ui(u,s));break e}}o=u=ui(u,s),4!==Us&&(Us=2),null===Is?Is=[o]:Is.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Bl(o,pi(0,u,t));break e;case 1:s=u;var y=o.type,b=o.stateNode;if(0===(128&o.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===qs||!qs.has(b)))){o.flags|=65536,t&=-t,o.lanes|=t,Bl(o,mi(o,s,t));break e}}o=o.return}while(null!==o)}xu(n)}catch(x){t=x,Rs===n&&null!==n&&(Rs=n=n.return);continue}break}}function mu(){var e=js.current;return js.current=Go,null===e?Go:e}function hu(){0!==Us&&3!==Us&&2!==Us||(Us=4),null===Ps||0===(268435455&Fs)&&0===(268435455&Ds)||iu(Ps,Os)}function gu(e,t){var n=Cs;Cs|=2;var r=mu();for(Ps===e&&Os===t||(Hs=null,fu(e,t));;)try{vu();break}catch(a){pu(e,a)}if(El(),Cs=n,js.current=r,null!==Rs)throw Error(l(261));return Ps=null,Os=0,Us}function vu(){for(;null!==Rs;)bu(Rs)}function yu(){for(;null!==Rs&&!Je();)bu(Rs)}function bu(e){var t=ks(e.alternate,e,Ts);e.memoizedProps=e.pendingProps,null===t?xu(e):Rs=t,Es.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qi(n,t,Ts)))return void(Rs=n)}else{if(null!==(n=Ki(n,t)))return n.flags&=32767,void(Rs=n);if(null===e)return Us=6,void(Rs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Rs=t);Rs=t=e}while(null!==t);0===Us&&(Us=5)}function wu(e,t,n){var r=bt,a=_s.transition;try{_s.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==Ks);if(0!==(6&Cs))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,o),e===Ps&&(Rs=Ps=null,Os=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qs||(Qs=!0,Pu(tt,function(){return Su(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=_s.transition,_s.transition=null;var i=bt;bt=1;var s=Cs;Cs|=4,Es.current=null,function(e,t){if(ea=Vt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===o&&++d===r&&(u=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Vt=!1,Gi=t;null!==Gi;)if(e=(t=Gi).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Gi=e;else for(;null!==Gi;){t=Gi;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:ni(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(l(163))}}catch(w){Nu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Gi=e;break}Gi=t.return}h=ts,ts=!1}(e,n),gs(n,e),mr(ta),Vt=!!ea,ta=ea=null,e.current=n,ys(n,e,a),Xe(),Cs=s,bt=i,_s.transition=o}else e.current=n;if(Qs&&(Qs=!1,Ks=e,Js=a),o=e.pendingLanes,0===o&&(qs=null),function(e){if(lt&&"function"===typeof lt.onCommitFiberRoot)try{lt.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Vs)throw Vs=!1,e=$s,$s=null,e;0!==(1&Js)&&0!==e.tag&&Su(),o=e.pendingLanes,0!==(1&o)?e===Ys?Xs++:(Xs=0,Ys=e):Xs=0,Wa()}(e,t,n,r)}finally{_s.transition=a,bt=r}return null}function Su(){if(null!==Ks){var e=xt(Js),t=_s.transition,n=bt;try{if(_s.transition=null,bt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Js=0,0!==(6&Cs))throw Error(l(331));var a=Cs;for(Cs|=4,Gi=e.current;null!==Gi;){var o=Gi,i=o.child;if(0!==(16&Gi.flags)){var s=o.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Gi=c;null!==Gi;){var d=Gi;switch(d.tag){case 0:case 11:case 15:ns(8,d,o)}var f=d.child;if(null!==f)f.return=d,Gi=f;else for(;null!==Gi;){var p=(d=Gi).sibling,m=d.return;if(ls(d),d===c){Gi=null;break}if(null!==p){p.return=m,Gi=p;break}Gi=m}}}var h=o.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Gi=o}}if(0!==(2064&o.subtreeFlags)&&null!==i)i.return=o,Gi=i;else e:for(;null!==Gi;){if(0!==(2048&(o=Gi).flags))switch(o.tag){case 0:case 11:case 15:ns(9,o,o.return)}var y=o.sibling;if(null!==y){y.return=o.return,Gi=y;break e}Gi=o.return}}var b=e.current;for(Gi=b;null!==Gi;){var x=(i=Gi).child;if(0!==(2064&i.subtreeFlags)&&null!==x)x.return=i,Gi=x;else e:for(i=b;null!==Gi;){if(0!==(2048&(s=Gi).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(S){Nu(s,s.return,S)}if(s===i){Gi=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Gi=w;break e}Gi=s.return}}if(Cs=a,Wa(),lt&&"function"===typeof lt.onPostCommitFiberRoot)try{lt.onPostCommitFiberRoot(at,e)}catch(S){}r=!0}return r}finally{bt=n,_s.transition=t}}return!1}function ku(e,t,n){e=Il(e,t=pi(0,t=ui(n,t),1),1),t=eu(),null!==e&&(vt(e,1,t),ru(e,t))}function Nu(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qs||!qs.has(r))){t=Il(t,e=mi(t,e=ui(n,e),1),1),e=eu(),null!==t&&(vt(t,1,e),ru(t,e));break}}t=t.return}}function ju(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Ps===e&&(Os&n)===n&&(4===Us||3===Us&&(130023424&Os)===Os&&500>Ye()-Bs?fu(e,0):As|=n),ru(e,t)}function Eu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Ul(e,t))&&(vt(e,t,n),ru(e,n))}function _u(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Eu(e,n)}function Cu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Eu(e,n)}function Pu(e,t){return Qe(e,t)}function Ru(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ou(e,t,n,r){return new Ru(e,t,n,r)}function Tu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Lu(e,t){var n=e.alternate;return null===n?((n=Ou(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Uu(e,t,n,r,a,o){var i=2;if(r=e,"function"===typeof e)Tu(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case k:return zu(n.children,a,o,t);case N:i=8,a|=8;break;case j:return(e=Ou(12,n,t,2|a)).elementType=j,e.lanes=o,e;case P:return(e=Ou(13,n,t,a)).elementType=P,e.lanes=o,e;case R:return(e=Ou(19,n,t,a)).elementType=R,e.lanes=o,e;case L:return Fu(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:i=10;break e;case _:i=9;break e;case C:i=11;break e;case O:i=14;break e;case T:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Ou(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function zu(e,t,n,r){return(e=Ou(7,e,r,t)).lanes=n,e}function Fu(e,t,n,r){return(e=Ou(22,e,r,t)).elementType=L,e.lanes=n,e.stateNode={isHidden:!1},e}function Du(e,t,n){return(e=Ou(6,e,null,t)).lanes=n,e}function Au(e,t,n){return(t=Ou(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Iu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Mu(e,t,n,r,a,l,o,i,s){return e=new Iu(e,t,n,i,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Ou(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fl(l),e}function Bu(e){if(!e)return _a;e:{if(We(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ta(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Ta(n))return za(e,n,t)}return t}function Wu(e,t,n,r,a,l,o,i,s){return(e=Mu(n,r,!0,e,0,l,0,i,s)).context=Bu(null),n=e.current,(l=Al(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Il(n,l,a),e.current.lanes=a,vt(e,a,r),ru(e,r),e}function Hu(e,t,n,r){var a=t.current,l=eu(),o=tu(a);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Al(l,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Il(a,t,o))&&(nu(e,a,o,l),Ml(e,a,o)),o}function Vu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $u(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qu(e,t){$u(e,t),(e=e.alternate)&&$u(e,t)}ks=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pa.current)bi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bi=!1,function(e,t,n){switch(t.tag){case 3:Pi(t),pl();break;case 5:Yl(t);break;case 1:Ta(t.type)&&Fa(t);break;case 4:Jl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ea(Sl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ea(Zl,1&Zl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Di(e,t,n):(Ea(Zl,1&Zl.current),null!==(e=Vi(e,t,n))?e.sibling:null);Ea(Zl,1&Zl.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Wi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ea(Zl,Zl.current),r)break;return null;case 22:case 23:return t.lanes=0,Ni(e,t,n)}return Vi(e,t,n)}(e,t,n);bi=0!==(131072&e.flags)}else bi=!1,al&&0!==(1048576&t.flags)&&Za(t,qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Hi(e,t),e=t.pendingProps;var a=Oa(t,Ca.current);Pl(t,n),a=go(null,t,r,e,a,n);var o=vo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ta(r)?(o=!0,Fa(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fl(t),a.updater=ai,t.stateNode=a,a._reactInternals=t,si(t,r,e,n),t=Ci(null,t,r,!0,o,n)):(t.tag=0,al&&o&&el(t),xi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Hi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Tu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===C)return 11;if(e===O)return 14}return 2}(r),e=ni(r,e),a){case 0:t=Ei(null,t,r,e,n);break e;case 1:t=_i(null,t,r,e,n);break e;case 11:t=wi(null,t,r,e,n);break e;case 14:t=Si(null,t,r,ni(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ei(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 1:return r=t.type,a=t.pendingProps,_i(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 3:e:{if(Pi(t),null===e)throw Error(l(387));r=t.pendingProps,a=(o=t.memoizedState).element,Dl(e,t),Wl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ri(e,t,r,n,a=ui(Error(l(423)),t));break e}if(r!==a){t=Ri(e,t,r,n,a=ui(Error(l(424)),t));break e}for(rl=ua(t.stateNode.containerInfo.firstChild),nl=t,al=!0,ll=null,n=wl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pl(),r===a){t=Vi(e,t,n);break e}xi(e,t,r,n)}t=t.child}return t;case 5:return Yl(t),null===e&&ul(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==o&&na(r,o)&&(t.flags|=32),ji(e,t),xi(e,t,i,n),t.child;case 6:return null===e&&ul(t),null;case 13:return Di(e,t,n);case 4:return Jl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xl(t,null,r,n):xi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,wi(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 7:return xi(e,t,t.pendingProps,n),t.child;case 8:case 12:return xi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,Ea(Sl,r._currentValue),r._currentValue=i,null!==o)if(ir(o.value,i)){if(o.children===a.children&&!Pa.current){t=Vi(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){i=o.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=Al(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),Cl(o.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Cl(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}xi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Pl(t,n),r=r(a=Rl(a)),t.flags|=1,xi(e,t,r,n),t.child;case 14:return a=ni(r=t.type,t.pendingProps),Si(e,t,r,a=ni(r.type,a),n);case 15:return ki(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ni(r,a),Hi(e,t),t.tag=1,Ta(r)?(e=!0,Fa(t)):e=!1,Pl(t,n),oi(t,r,a),si(t,r,a,n),Ci(null,t,r,!0,e,n);case 19:return Wi(e,t,n);case 22:return Ni(e,t,n)}throw Error(l(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Ju(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gu(){}function Zu(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if("function"===typeof a){var i=a;a=function(){var e=Vu(o);i.call(e)}}Hu(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=Vu(o);l.call(e)}}var o=Wu(t,r,e,0,null,!1,0,"",Gu);return e._reactRootContainer=o,e[ma]=o.current,Wr(8===e.nodeType?e.parentNode:e),cu(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=Vu(s);i.call(e)}}var s=Mu(e,0,!1,null,0,!1,0,"",Gu);return e._reactRootContainer=s,e[ma]=s.current,Wr(8===e.nodeType?e.parentNode:e),cu(function(){Hu(t,s,n,r)}),s}(n,t,e,a,r);return Vu(o)}Ju.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Hu(e,t,null,null)},Ju.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Hu(null,e,null,null)}),t[ma]=null}},Ju.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&Dt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ru(t,Ye()),0===(6&Cs)&&(Ws=Ye()+500,Wa()))}break;case 13:cu(function(){var t=Ul(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),qu(e,1)}},St=function(e){if(13===e.tag){var t=Ul(e,134217728);if(null!==t)nu(t,e,134217728,eu());qu(e,134217728)}},kt=function(e){if(13===e.tag){var t=tu(e),n=Ul(e,t);if(null!==n)nu(n,e,t,eu());qu(e,t)}},Nt=function(){return bt},jt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(l(90));Q(r),G(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ce=uu,Pe=cu;var ec={usingClientEntryPoint:!1,Events:[ba,xa,wa,Ee,_e,uu]},tc={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),lt=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xu(e))throw Error(l(299));var n=!1,r="",a=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Mu(e,1,!1,null,0,n,0,r,a),e[ma]=t.current,Wr(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Yu(t))throw Error(l(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xu(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Wu(t,null,e,1,null!=n?n:null,a,0,o,i),e[ma]=t.current,Wr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Ju(t)},t.render=function(e,t,n){if(!Yu(t))throw Error(l(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yu(e))throw Error(l(40));return!!e._reactRootContainer&&(cu(function(){Zu(null,null,e,!1,function(){e._reactRootContainer=null,e[ma]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yu(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var o={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach(e=>o[e]=()=>r[e]);return o.default=()=>r,n.d(l,o),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Vt,hasStandardBrowserEnv:()=>qt,hasStandardBrowserWebWorkerEnv:()=>Qt,navigator:()=>$t,origin:()=>Kt});var a,l=n(43),o=n.t(l,2),i=n(391),s=n(950),u=n.t(s,2);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(a||(a={}));const d="popstate";function f(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function p(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function m(e,t){return{usr:e.state,key:e.key,idx:t}}function h(e,t,n,r){return void 0===n&&(n=null),c({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?v(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function g(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function v(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function y(e,t,n,r){void 0===r&&(r={});let{window:l=document.defaultView,v5Compat:o=!1}=r,i=l.history,s=a.Pop,u=null,p=v();function v(){return(i.state||{idx:null}).idx}function y(){s=a.Pop;let e=v(),t=null==e?null:e-p;p=e,u&&u({action:s,location:x.location,delta:t})}function b(e){let t="null"!==l.location.origin?l.location.origin:l.location.href,n="string"===typeof e?e:g(e);return n=n.replace(/ $/,"%20"),f(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==p&&(p=0,i.replaceState(c({},i.state,{idx:p}),""));let x={get action(){return s},get location(){return e(l,i)},listen(e){if(u)throw new Error("A history only accepts one active listener");return l.addEventListener(d,y),u=e,()=>{l.removeEventListener(d,y),u=null}},createHref:e=>t(l,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s=a.Push;let r=h(x.location,e,t);n&&n(r,e),p=v()+1;let c=m(r,p),d=x.createHref(r);try{i.pushState(c,"",d)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;l.location.assign(d)}o&&u&&u({action:s,location:x.location,delta:1})},replace:function(e,t){s=a.Replace;let r=h(x.location,e,t);n&&n(r,e),p=v();let l=m(r,p),c=x.createHref(r);i.replaceState(l,"",c),o&&u&&u({action:s,location:x.location,delta:0})},go:e=>i.go(e)};return x}var b;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(b||(b={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function x(e,t,n){return void 0===n&&(n="/"),w(e,t,n,!1)}function w(e,t,n,r){let a=z(("string"===typeof t?v(t):t).pathname||"/",n);if(null==a)return null;let l=S(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(l);let o=null;for(let i=0;null==o&&i<l.length;++i){let e=U(a);o=T(l[i],e,r)}return o}function S(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let o={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(f(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let i=M([r,o.relativePath]),s=n.concat(o);e.children&&e.children.length>0&&(f(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),S(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:O(i,e.index),routesMeta:s})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of k(e.path))a(e,t,r);else a(e,t)}),t}function k(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let o=k(r.join("/")),i=[];return i.push(...o.map(e=>""===e?l:[l,e].join("/"))),a&&i.push(...o),i.map(t=>e.startsWith("/")&&""===t?"/":t)}const N=/^:[\w-]+$/,j=3,E=2,_=1,C=10,P=-2,R=e=>"*"===e;function O(e,t){let n=e.split("/"),r=n.length;return n.some(R)&&(r+=P),t&&(r+=E),n.filter(e=>!R(e)).reduce((e,t)=>e+(N.test(t)?j:""===t?_:C),r)}function T(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",o=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,u="/"===l?t:t.slice(l.length)||"/",c=L({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=L({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),o.push({params:a,pathname:M([l,c.pathname]),pathnameBase:B(M([l,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(l=M([l,c.pathnameBase]))}return o}function L(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);p("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],o=l.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";o=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:l,pathnameBase:o,pattern:e}}function U(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return p(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function z(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function F(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function D(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function A(e,t){let n=D(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function I(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=v(e):(a=c({},e),f(!a.pathname||!a.pathname.includes("?"),F("?","pathname","search",a)),f(!a.pathname||!a.pathname.includes("#"),F("#","pathname","hash",a)),f(!a.search||!a.search.includes("#"),F("#","search","hash",a)));let l,o=""===e||""===a.pathname,i=o?"/":a.pathname;if(null==i)l=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?v(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:W(r),hash:H(a)}}(a,l),u=i&&"/"!==i&&i.endsWith("/"),d=(o||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!d||(s.pathname+="/"),s}const M=e=>e.join("/").replace(/\/\/+/g,"/"),B=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),W=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",H=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function V(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const $=["post","put","patch","delete"],q=(new Set($),["get",...$]);new Set(q),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Q.apply(this,arguments)}const K=l.createContext(null);const J=l.createContext(null);const X=l.createContext(null);const Y=l.createContext(null);const G=l.createContext({outlet:null,matches:[],isDataRoute:!1});const Z=l.createContext(null);function ee(){return null!=l.useContext(Y)}function te(){return ee()||f(!1),l.useContext(Y).location}function ne(e){l.useContext(X).static||l.useLayoutEffect(e)}function re(){let{isDataRoute:e}=l.useContext(G);return e?function(){let{router:e}=pe(de.UseNavigateStable),t=he(fe.UseNavigateStable),n=l.useRef(!1);return ne(()=>{n.current=!0}),l.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,Q({fromRouteId:t},a)))},[e,t])}():function(){ee()||f(!1);let e=l.useContext(K),{basename:t,future:n,navigator:r}=l.useContext(X),{matches:a}=l.useContext(G),{pathname:o}=te(),i=JSON.stringify(A(a,n.v7_relativeSplatPath)),s=l.useRef(!1);return ne(()=>{s.current=!0}),l.useCallback(function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"===typeof n)return void r.go(n);let l=I(n,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:M([t,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)},[t,r,i,o,e])}()}function ae(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=l.useContext(X),{matches:a}=l.useContext(G),{pathname:o}=te(),i=JSON.stringify(A(a,r.v7_relativeSplatPath));return l.useMemo(()=>I(e,JSON.parse(i),o,"path"===n),[e,i,o,n])}function le(e,t,n,r){ee()||f(!1);let{navigator:o}=l.useContext(X),{matches:i}=l.useContext(G),s=i[i.length-1],u=s?s.params:{},c=(s&&s.pathname,s?s.pathnameBase:"/");s&&s.route;let d,p=te();if(t){var m;let e="string"===typeof t?v(t):t;"/"===c||(null==(m=e.pathname)?void 0:m.startsWith(c))||f(!1),d=e}else d=p;let h=d.pathname||"/",g=h;if("/"!==c){let e=c.replace(/^\//,"").split("/");g="/"+h.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=x(e,{pathname:g});let b=ce(y&&y.map(e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:M([c,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:M([c,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,r);return t&&b?l.createElement(Y.Provider,{value:{location:Q({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:a.Pop}},b):b}function oe(){let e=function(){var e;let t=l.useContext(Z),n=me(fe.UseRouteError),r=he(fe.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=V(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r};return l.createElement(l.Fragment,null,l.createElement("h2",null,"Unexpected Application Error!"),l.createElement("h3",{style:{fontStyle:"italic"}},t),n?l.createElement("pre",{style:a},n):null,null)}const ie=l.createElement(oe,null);class se extends l.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?l.createElement(G.Provider,{value:this.props.routeContext},l.createElement(Z.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ue(e){let{routeContext:t,match:n,children:r}=e,a=l.useContext(K);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),l.createElement(G.Provider,{value:t},r)}function ce(e,t,n,r){var a;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var o;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(o=r)&&o.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(a=n)?void 0:a.errors;if(null!=s){let e=i.findIndex(e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id]));e>=0||f(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let l=0;l<i.length;l++){let e=i[l];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=l),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight((e,r,a)=>{let o,d=!1,f=null,p=null;var m;n&&(o=s&&r.route.id?s[r.route.id]:void 0,f=r.route.errorElement||ie,u&&(c<0&&0===a?(m="route-fallback",!1||ge[m]||(ge[m]=!0),d=!0,p=null):c===a&&(d=!0,p=r.route.hydrateFallbackElement||null)));let h=t.concat(i.slice(0,a+1)),g=()=>{let t;return t=o?f:d?p:r.route.Component?l.createElement(r.route.Component,null):r.route.element?r.route.element:e,l.createElement(ue,{match:r,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?l.createElement(se,{location:n.location,revalidation:n.revalidation,component:f,error:o,children:g(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):g()},null)}var de=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(de||{}),fe=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(fe||{});function pe(e){let t=l.useContext(K);return t||f(!1),t}function me(e){let t=l.useContext(J);return t||f(!1),t}function he(e){let t=function(){let e=l.useContext(G);return e||f(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||f(!1),n.route.id}const ge={};function ve(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}o.startTransition;function ye(e){let{to:t,replace:n,state:r,relative:a}=e;ee()||f(!1);let{future:o,static:i}=l.useContext(X),{matches:s}=l.useContext(G),{pathname:u}=te(),c=re(),d=I(t,A(s,o.v7_relativeSplatPath),u,"path"===a),p=JSON.stringify(d);return l.useEffect(()=>c(JSON.parse(p),{replace:n,state:r,relative:a}),[c,p,a,n,r]),null}function be(e){f(!1)}function xe(e){let{basename:t="/",children:n=null,location:r,navigationType:o=a.Pop,navigator:i,static:s=!1,future:u}=e;ee()&&f(!1);let c=t.replace(/^\/*/,"/"),d=l.useMemo(()=>({basename:c,navigator:i,static:s,future:Q({v7_relativeSplatPath:!1},u)}),[c,u,i,s]);"string"===typeof r&&(r=v(r));let{pathname:p="/",search:m="",hash:h="",state:g=null,key:y="default"}=r,b=l.useMemo(()=>{let e=z(p,c);return null==e?null:{location:{pathname:e,search:m,hash:h,state:g,key:y},navigationType:o}},[c,p,m,h,g,y,o]);return null==b?null:l.createElement(X.Provider,{value:d},l.createElement(Y.Provider,{children:n,value:b}))}function we(e){let{children:t,location:n}=e;return le(Se(t),n)}new Promise(()=>{});l.Component;function Se(e,t){void 0===t&&(t=[]);let n=[];return l.Children.forEach(e,(e,r)=>{if(!l.isValidElement(e))return;let a=[...t,r];if(e.type===l.Fragment)return void n.push.apply(n,Se(e.props.children,a));e.type!==be&&f(!1),e.props.index&&e.props.children&&f(!1);let o={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=Se(e.props.children,a)),n.push(o)}),n}function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ke.apply(this,arguments)}function Ne(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const je=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(ta){}new Map;const Ee=o.startTransition;u.flushSync,o.useId;function _e(e){let{basename:t,children:n,future:r,window:a}=e,o=l.useRef();var i;null==o.current&&(o.current=(void 0===(i={window:a,v5Compat:!0})&&(i={}),y(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return h("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:g(t)},null,i)));let s=o.current,[u,c]=l.useState({action:s.action,location:s.location}),{v7_startTransition:d}=r||{},f=l.useCallback(e=>{d&&Ee?Ee(()=>c(e)):c(e)},[c,d]);return l.useLayoutEffect(()=>s.listen(f),[s,f]),l.useEffect(()=>ve(r),[r]),l.createElement(xe,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:r})}const Ce="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Pe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Re=l.forwardRef(function(e,t){let n,{onClick:r,relative:a,reloadDocument:o,replace:i,state:s,target:u,to:c,preventScrollReset:d,viewTransition:p}=e,m=Ne(e,je),{basename:h}=l.useContext(X),v=!1;if("string"===typeof c&&Pe.test(c)&&(n=c,Ce))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=z(t.pathname,h);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:v=!0}catch(ta){}let y=function(e,t){let{relative:n}=void 0===t?{}:t;ee()||f(!1);let{basename:r,navigator:a}=l.useContext(X),{hash:o,pathname:i,search:s}=ae(e,{relative:n}),u=i;return"/"!==r&&(u="/"===i?r:M([r,i])),a.createHref({pathname:u,search:s,hash:o})}(c,{relative:a}),b=function(e,t){let{target:n,replace:r,state:a,preventScrollReset:o,relative:i,viewTransition:s}=void 0===t?{}:t,u=re(),c=te(),d=ae(e,{relative:i});return l.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:g(c)===g(d);u(e,{replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:s})}},[c,u,d,r,a,n,e,o,i,s])}(c,{replace:i,state:s,target:u,preventScrollReset:d,relative:a,viewTransition:p});return l.createElement("a",ke({},m,{href:n||y,onClick:v||o?r:function(e){r&&r(e),e.defaultPrevented||b(e)},ref:t,target:u}))});var Oe,Te;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Oe||(Oe={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Te||(Te={}));var Le=n(579);const Ue=e=>{let{user:t,onLogout:n}=e;const r=t&&t.roles&&t.roles.includes("admin");return(0,Le.jsx)("nav",{className:"navbar",children:(0,Le.jsxs)("div",{className:"container",children:[(0,Le.jsx)("h1",{children:"JWT Auth App"}),(0,Le.jsx)("div",{children:t&&(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)(Re,{to:"/tokens",style:{color:"white",marginRight:20,textDecoration:"underline"},children:"Tokens"}),r&&(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)(Re,{to:"/admin/users",style:{color:"white",marginRight:20,textDecoration:"underline"},children:"Users"}),(0,Le.jsx)(Re,{to:"/admin/roles",style:{color:"white",marginRight:20,textDecoration:"underline"},children:"Roles"}),(0,Le.jsx)(Re,{to:"/admin/permissions",style:{color:"white",marginRight:20,textDecoration:"underline"},children:"Permissions"})]}),(0,Le.jsxs)("span",{style:{marginRight:"15px"},children:["Welcome, ",t.username,"!"]}),(0,Le.jsx)("button",{onClick:n,children:"Logout"})]})})]})})};function ze(e){return ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(e)}function Fe(e){var t=function(e,t){if("object"!=ze(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=ze(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ze(t)?t:t+""}function De(e,t,n){return(t=Fe(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ae(Object(n),!0).forEach(function(t){De(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ae(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Me(e,t){return function(){return e.apply(t,arguments)}}const{toString:Be}=Object.prototype,{getPrototypeOf:We}=Object,{iterator:He,toStringTag:Ve}=Symbol,$e=(qe=Object.create(null),e=>{const t=Be.call(e);return qe[t]||(qe[t]=t.slice(8,-1).toLowerCase())});var qe;const Qe=e=>(e=e.toLowerCase(),t=>$e(t)===e),Ke=e=>t=>typeof t===e,{isArray:Je}=Array,Xe=Ke("undefined");const Ye=Qe("ArrayBuffer");const Ge=Ke("string"),Ze=Ke("function"),et=Ke("number"),tt=e=>null!==e&&"object"===typeof e,nt=e=>{if("object"!==$e(e))return!1;const t=We(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Ve in e)&&!(He in e)},rt=Qe("Date"),at=Qe("File"),lt=Qe("Blob"),ot=Qe("FileList"),it=Qe("URLSearchParams"),[st,ut,ct,dt]=["ReadableStream","Request","Response","Headers"].map(Qe);function ft(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Je(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let o;for(n=0;n<l;n++)o=r[n],t.call(null,e[o],o,e)}}function pt(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const mt="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,ht=e=>!Xe(e)&&e!==mt;const gt=(vt="undefined"!==typeof Uint8Array&&We(Uint8Array),e=>vt&&e instanceof vt);var vt;const yt=Qe("HTMLFormElement"),bt=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),xt=Qe("RegExp"),wt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ft(n,(n,a)=>{let l;!1!==(l=t(n,a,e))&&(r[a]=l||n)}),Object.defineProperties(e,r)};const St=Qe("AsyncFunction"),kt=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],mt.addEventListener("message",e=>{let{source:t,data:a}=e;t===mt&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),mt.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,Ze(mt.postMessage)),Nt="undefined"!==typeof queueMicrotask?queueMicrotask.bind(mt):"undefined"!==typeof process&&process.nextTick||kt,jt={isArray:Je,isArrayBuffer:Ye,isBuffer:function(e){return null!==e&&!Xe(e)&&null!==e.constructor&&!Xe(e.constructor)&&Ze(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Ze(e.append)&&("formdata"===(t=$e(e))||"object"===t&&Ze(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Ye(e.buffer),t},isString:Ge,isNumber:et,isBoolean:e=>!0===e||!1===e,isObject:tt,isPlainObject:nt,isReadableStream:st,isRequest:ut,isResponse:ct,isHeaders:dt,isUndefined:Xe,isDate:rt,isFile:at,isBlob:lt,isRegExp:xt,isFunction:Ze,isStream:e=>tt(e)&&Ze(e.pipe),isURLSearchParams:it,isTypedArray:gt,isFileList:ot,forEach:ft,merge:function e(){const{caseless:t}=ht(this)&&this||{},n={},r=(r,a)=>{const l=t&&pt(n,a)||a;nt(n[l])&&nt(r)?n[l]=e(n[l],r):nt(r)?n[l]=e({},r):Je(r)?n[l]=r.slice():n[l]=r};for(let a=0,l=arguments.length;a<l;a++)arguments[a]&&ft(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return ft(t,(t,r)=>{n&&Ze(t)?e[r]=Me(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,l,o;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),l=a.length;l-- >0;)o=a[l],r&&!r(o,e,t)||i[o]||(t[o]=e[o],i[o]=!0);e=!1!==n&&We(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:$e,kindOfTest:Qe,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Je(e))return e;let t=e.length;if(!et(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[He]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:yt,hasOwnProperty:bt,hasOwnProp:bt,reduceDescriptors:wt,freezeMethods:e=>{wt(e,(t,n)=>{if(Ze(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Ze(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return Je(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:pt,global:mt,isContextDefined:ht,isSpecCompliantForm:function(e){return!!(e&&Ze(e.append)&&"FormData"===e[Ve]&&e[He])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(tt(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=Je(e)?[]:{};return ft(e,(e,t)=>{const l=n(e,r+1);!Xe(l)&&(a[t]=l)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:St,isThenable:e=>e&&(tt(e)||Ze(e))&&Ze(e.then)&&Ze(e.catch),setImmediate:kt,asap:Nt,isIterable:e=>null!=e&&Ze(e[He])};function Et(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}jt.inherits(Et,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:jt.toJSONObject(this.config),code:this.code,status:this.status}}});const _t=Et.prototype,Ct={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ct[e]={value:e}}),Object.defineProperties(Et,Ct),Object.defineProperty(_t,"isAxiosError",{value:!0}),Et.from=(e,t,n,r,a,l)=>{const o=Object.create(_t);return jt.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Et.call(o,e.message,t,n,r,a),o.cause=e,o.name=e.name,l&&Object.assign(o,l),o};const Pt=Et;function Rt(e){return jt.isPlainObject(e)||jt.isArray(e)}function Ot(e){return jt.endsWith(e,"[]")?e.slice(0,-2):e}function Tt(e,t,n){return e?e.concat(t).map(function(e,t){return e=Ot(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Lt=jt.toFlatObject(jt,{},null,function(e){return/^is[A-Z]/.test(e)});const Ut=function(e,t,n){if(!jt.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=jt.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!jt.isUndefined(t[e])})).metaTokens,a=n.visitor||u,l=n.dots,o=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&jt.isSpecCompliantForm(t);if(!jt.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(jt.isDate(e))return e.toISOString();if(jt.isBoolean(e))return e.toString();if(!i&&jt.isBlob(e))throw new Pt("Blob is not supported. Use a Buffer instead.");return jt.isArrayBuffer(e)||jt.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(jt.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(jt.isArray(e)&&function(e){return jt.isArray(e)&&!e.some(Rt)}(e)||(jt.isFileList(e)||jt.endsWith(n,"[]"))&&(i=jt.toArray(e)))return n=Ot(n),i.forEach(function(e,r){!jt.isUndefined(e)&&null!==e&&t.append(!0===o?Tt([n],r,l):null===o?n:n+"[]",s(e))}),!1;return!!Rt(e)||(t.append(Tt(a,n,l),s(e)),!1)}const c=[],d=Object.assign(Lt,{defaultVisitor:u,convertValue:s,isVisitable:Rt});if(!jt.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!jt.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),jt.forEach(n,function(n,l){!0===(!(jt.isUndefined(n)||null===n)&&a.call(t,n,jt.isString(l)?l.trim():l,r,d))&&e(n,r?r.concat(l):[l])}),c.pop()}}(e),t};function zt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function Ft(e,t){this._pairs=[],e&&Ut(e,this,t)}const Dt=Ft.prototype;Dt.append=function(e,t){this._pairs.push([e,t])},Dt.toString=function(e){const t=e?function(t){return e.call(this,t,zt)}:zt;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const At=Ft;function It(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Mt(e,t,n){if(!t)return e;const r=n&&n.encode||It;jt.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let l;if(l=a?a(t,n):jt.isURLSearchParams(t)?t.toString():new At(t,n).toString(r),l){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+l}return e}const Bt=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){jt.forEach(this.handlers,function(t){null!==t&&e(t)})}},Wt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ht={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:At,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Vt="undefined"!==typeof window&&"undefined"!==typeof document,$t="object"===typeof navigator&&navigator||void 0,qt=Vt&&(!$t||["ReactNative","NativeScript","NS"].indexOf($t.product)<0),Qt="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Kt=Vt&&window.location.href||"http://localhost",Jt=Ie(Ie({},r),Ht);const Xt=function(e){function t(e,n,r,a){let l=e[a++];if("__proto__"===l)return!0;const o=Number.isFinite(+l),i=a>=e.length;if(l=!l&&jt.isArray(r)?r.length:l,i)return jt.hasOwnProp(r,l)?r[l]=[r[l],n]:r[l]=n,!o;r[l]&&jt.isObject(r[l])||(r[l]=[]);return t(e,n,r[l],a)&&jt.isArray(r[l])&&(r[l]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let l;for(r=0;r<a;r++)l=n[r],t[l]=e[l];return t}(r[l])),!o}if(jt.isFormData(e)&&jt.isFunction(e.entries)){const n={};return jt.forEachEntry(e,(e,r)=>{t(function(e){return jt.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const Yt={transitional:Wt,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=jt.isObject(e);a&&jt.isHTMLForm(e)&&(e=new FormData(e));if(jt.isFormData(e))return r?JSON.stringify(Xt(e)):e;if(jt.isArrayBuffer(e)||jt.isBuffer(e)||jt.isStream(e)||jt.isFile(e)||jt.isBlob(e)||jt.isReadableStream(e))return e;if(jt.isArrayBufferView(e))return e.buffer;if(jt.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let l;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Ut(e,new Jt.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Jt.isNode&&jt.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((l=jt.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Ut(l?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(jt.isString(e))try{return(t||JSON.parse)(e),jt.trim(e)}catch(ta){if("SyntaxError"!==ta.name)throw ta}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Yt.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(jt.isResponse(e)||jt.isReadableStream(e))return e;if(e&&jt.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(ta){if(n){if("SyntaxError"===ta.name)throw Pt.from(ta,Pt.ERR_BAD_RESPONSE,this,null,this.response);throw ta}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Jt.classes.FormData,Blob:Jt.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};jt.forEach(["delete","get","head","post","put","patch"],e=>{Yt.headers[e]={}});const Gt=Yt,Zt=jt.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),en=Symbol("internals");function tn(e){return e&&String(e).trim().toLowerCase()}function nn(e){return!1===e||null==e?e:jt.isArray(e)?e.map(nn):String(e)}function rn(e,t,n,r,a){return jt.isFunction(r)?r.call(this,t,n):(a&&(t=n),jt.isString(t)?jt.isString(r)?-1!==t.indexOf(r):jt.isRegExp(r)?r.test(t):void 0:void 0)}class an{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=tn(t);if(!a)throw new Error("header name must be a non-empty string");const l=jt.findKey(r,a);(!l||void 0===r[l]||!0===n||void 0===n&&!1!==r[l])&&(r[l||t]=nn(e))}const l=(e,t)=>jt.forEach(e,(e,n)=>a(e,n,t));if(jt.isPlainObject(e)||e instanceof this.constructor)l(e,t);else if(jt.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))l((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Zt[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(jt.isObject(e)&&jt.isIterable(e)){let n,r,a={};for(const t of e){if(!jt.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?jt.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}l(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=tn(e)){const n=jt.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(jt.isFunction(t))return t.call(this,e,n);if(jt.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=tn(e)){const n=jt.findKey(this,e);return!(!n||void 0===this[n]||t&&!rn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=tn(e)){const a=jt.findKey(n,e);!a||t&&!rn(0,n[a],a,t)||(delete n[a],r=!0)}}return jt.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!rn(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return jt.forEach(this,(r,a)=>{const l=jt.findKey(n,a);if(l)return t[l]=nn(r),void delete t[a];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();o!==a&&delete t[a],t[o]=nn(r),n[o]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return jt.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&jt.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[en]=this[en]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=tn(e);t[r]||(!function(e,t){const n=jt.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return jt.isArray(e)?e.forEach(r):r(e),this}}an.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),jt.reduceDescriptors(an.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),jt.freezeMethods(an);const ln=an;function on(e,t){const n=this||Gt,r=t||n,a=ln.from(r.headers);let l=r.data;return jt.forEach(e,function(e){l=e.call(n,l,a.normalize(),t?t.status:void 0)}),a.normalize(),l}function sn(e){return!(!e||!e.__CANCEL__)}function un(e,t,n){Pt.call(this,null==e?"canceled":e,Pt.ERR_CANCELED,t,n),this.name="CanceledError"}jt.inherits(un,Pt,{__CANCEL__:!0});const cn=un;function dn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Pt("Request failed with status code "+n.status,[Pt.ERR_BAD_REQUEST,Pt.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const fn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,l=0,o=0;return t=void 0!==t?t:1e3,function(i){const s=Date.now(),u=r[o];a||(a=s),n[l]=i,r[l]=s;let c=o,d=0;for(;c!==l;)d+=n[c++],c%=e;if(l=(l+1)%e,l===o&&(o=(o+1)%e),s-a<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}};const pn=function(e,t){let n,r,a=0,l=1e3/t;const o=function(t){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=l,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,s=new Array(i),u=0;u<i;u++)s[u]=arguments[u];t>=l?o(s,e):(n=s,r||(r=setTimeout(()=>{r=null,o(n)},l-t)))},()=>n&&o(n)]},mn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=fn(50,250);return pn(n=>{const l=n.loaded,o=n.lengthComputable?n.total:void 0,i=l-r,s=a(i);r=l;e({loaded:l,total:o,progress:o?l/o:void 0,bytes:i,rate:s||void 0,estimated:s&&o&&l<=o?(o-l)/s:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0})},n)},hn=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},gn=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return jt.asap(()=>e(...n))},vn=Jt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Jt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Jt.origin),Jt.navigator&&/(msie|trident)/i.test(Jt.navigator.userAgent)):()=>!0,yn=Jt.hasStandardBrowserEnv?{write(e,t,n,r,a,l){const o=[e+"="+encodeURIComponent(t)];jt.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),jt.isString(r)&&o.push("path="+r),jt.isString(a)&&o.push("domain="+a),!0===l&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function bn(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const xn=e=>e instanceof ln?Ie({},e):e;function wn(e,t){t=t||{};const n={};function r(e,t,n,r){return jt.isPlainObject(e)&&jt.isPlainObject(t)?jt.merge.call({caseless:r},e,t):jt.isPlainObject(t)?jt.merge({},t):jt.isArray(t)?t.slice():t}function a(e,t,n,a){return jt.isUndefined(t)?jt.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function l(e,t){if(!jt.isUndefined(t))return r(void 0,t)}function o(e,t){return jt.isUndefined(t)?jt.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,l){return l in t?r(n,a):l in e?r(void 0,n):void 0}const s={url:l,method:l,data:l,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:i,headers:(e,t,n)=>a(xn(e),xn(t),0,!0)};return jt.forEach(Object.keys(Object.assign({},e,t)),function(r){const l=s[r]||a,o=l(e[r],t[r],r);jt.isUndefined(o)&&l!==i||(n[r]=o)}),n}const Sn=e=>{const t=wn({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:l,xsrfCookieName:o,headers:i,auth:s}=t;if(t.headers=i=ln.from(i),t.url=Mt(bn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),jt.isFormData(r))if(Jt.hasStandardBrowserEnv||Jt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(Jt.hasStandardBrowserEnv&&(a&&jt.isFunction(a)&&(a=a(t)),a||!1!==a&&vn(t.url))){const e=l&&o&&yn.read(o);e&&i.set(l,e)}return t},kn="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Sn(e);let a=r.data;const l=ln.from(r.headers).normalize();let o,i,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function m(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let h=new XMLHttpRequest;function g(){if(!h)return;const r=ln.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());dn(function(e){t(e),m()},function(e){n(e),m()},{data:d&&"text"!==d&&"json"!==d?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:r,config:e,request:h}),h=null}h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout,"onloadend"in h?h.onloadend=g:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(g)},h.onabort=function(){h&&(n(new Pt("Request aborted",Pt.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new Pt("Network Error",Pt.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Wt;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Pt(t,a.clarifyTimeoutError?Pt.ETIMEDOUT:Pt.ECONNABORTED,e,h)),h=null},void 0===a&&l.setContentType(null),"setRequestHeader"in h&&jt.forEach(l.toJSON(),function(e,t){h.setRequestHeader(t,e)}),jt.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),d&&"json"!==d&&(h.responseType=r.responseType),p&&([s,c]=mn(p,!0),h.addEventListener("progress",s)),f&&h.upload&&([i,u]=mn(f),h.upload.addEventListener("progress",i),h.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(o=t=>{h&&(n(!t||t.type?new cn(null,e,h):t),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const v=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);v&&-1===Jt.protocols.indexOf(v)?n(new Pt("Unsupported protocol "+v+":",Pt.ERR_BAD_REQUEST,e)):h.send(a||null)})},Nn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Pt?t:new cn(t instanceof Error?t.message:t))}};let l=t&&setTimeout(()=>{l=null,a(new Pt("timeout ".concat(t," of ms exceeded"),Pt.ETIMEDOUT))},t);const o=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:i}=r;return i.unsubscribe=()=>jt.asap(o),i}};function jn(e,t){this.v=e,this.k=t}function En(e){return function(){return new _n(e.apply(this,arguments))}}function _n(e){var t,n;function r(t,n){try{var l=e[t](n),o=l.value,i=o instanceof jn;Promise.resolve(i?o.v:o).then(function(n){if(i){var s="return"===t?"return":"next";if(!o.k||n.done)return r(s,n);n=e[s](n).value}a(l.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(l,o){var i={key:e,arg:a,resolve:l,reject:o,next:null};n?n=n.next=i:(t=n=i,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function Cn(e){return new jn(e,0)}function Pn(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new jn(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Rn(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new On(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function On(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return On=function(e){this.s=e,this.n=e.next},On.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new On(e)}_n.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},_n.prototype.next=function(e){return this._invoke("next",e)},_n.prototype.throw=function(e){return this._invoke("throw",e)},_n.prototype.return=function(e){return this._invoke("return",e)};const Tn=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Ln=function(){var e=En(function*(e,t){var n,r=!1,a=!1;try{for(var l,o=Rn(Un(e));r=!(l=yield Cn(o.next())).done;r=!1){const e=l.value;yield*Pn(Rn(Tn(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=o.return&&(yield Cn(o.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),Un=function(){var e=En(function*(e){if(e[Symbol.asyncIterator])return void(yield*Pn(Rn(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Cn(t.read());if(e)break;yield n}}finally{yield Cn(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),zn=(e,t,n,r)=>{const a=Ln(e,t);let l,o=0,i=e=>{l||(l=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let l=r.byteLength;if(n){let e=o+=l;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},Fn="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Dn=Fn&&"function"===typeof ReadableStream,An=Fn&&("function"===typeof TextEncoder?(In=new TextEncoder,e=>In.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var In;const Mn=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(ta){return!1}},Bn=Dn&&Mn(()=>{let e=!1;const t=new Request(Jt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Wn=Dn&&Mn(()=>jt.isReadableStream(new Response("").body)),Hn={stream:Wn&&(e=>e.body)};var Vn;Fn&&(Vn=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Hn[e]&&(Hn[e]=jt.isFunction(Vn[e])?t=>t[e]():(t,n)=>{throw new Pt("Response type '".concat(e,"' is not supported"),Pt.ERR_NOT_SUPPORT,n)})}));const $n=async(e,t)=>{const n=jt.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(jt.isBlob(e))return e.size;if(jt.isSpecCompliantForm(e)){const t=new Request(Jt.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return jt.isArrayBufferView(e)||jt.isArrayBuffer(e)?e.byteLength:(jt.isURLSearchParams(e)&&(e+=""),jt.isString(e)?(await An(e)).byteLength:void 0)})(t):n},qn=Fn&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:l,timeout:o,onDownloadProgress:i,onUploadProgress:s,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Sn(e);u=u?(u+"").toLowerCase():"text";let p,m=Nn([a,l&&l.toAbortSignal()],o);const h=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let g;try{if(s&&Bn&&"get"!==n&&"head"!==n&&0!==(g=await $n(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(jt.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=hn(g,mn(gn(s)));r=zn(n.body,65536,e,t)}}jt.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,Ie(Ie({},f),{},{signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let l=await fetch(p,f);const o=Wn&&("stream"===u||"response"===u);if(Wn&&(i||o&&h)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=l[t]});const t=jt.toFiniteNumber(l.headers.get("content-length")),[n,r]=i&&hn(t,mn(gn(i),!0))||[];l=new Response(zn(l.body,65536,n,()=>{r&&r(),h&&h()}),e)}u=u||"text";let v=await Hn[jt.findKey(Hn,u)||"text"](l,e);return!o&&h&&h(),await new Promise((t,n)=>{dn(t,n,{data:v,headers:ln.from(l.headers),status:l.status,statusText:l.statusText,config:e,request:p})})}catch(v){if(h&&h(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new Pt("Network Error",Pt.ERR_NETWORK,e,p),{cause:v.cause||v});throw Pt.from(v,v&&v.code,e,p)}}),Qn={http:null,xhr:kn,fetch:qn};jt.forEach(Qn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(ta){}Object.defineProperty(e,"adapterName",{value:t})}});const Kn=e=>"- ".concat(e),Jn=e=>jt.isFunction(e)||null===e||!1===e,Xn=e=>{e=jt.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let l=0;l<t;l++){let t;if(n=e[l],r=n,!Jn(n)&&(r=Qn[(t=String(n)).toLowerCase()],void 0===r))throw new Pt("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+l]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(Kn).join("\n"):" "+Kn(e[0]):"as no adapter specified";throw new Pt("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Yn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new cn(null,e)}function Gn(e){Yn(e),e.headers=ln.from(e.headers),e.data=on.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Xn(e.adapter||Gt.adapter)(e).then(function(t){return Yn(e),t.data=on.call(e,e.transformResponse,t),t.headers=ln.from(t.headers),t},function(t){return sn(t)||(Yn(e),t&&t.response&&(t.response.data=on.call(e,e.transformResponse,t.response),t.response.headers=ln.from(t.response.headers))),Promise.reject(t)})}const Zn="1.10.0",er={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{er[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const tr={};er.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Zn+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,l)=>{if(!1===e)throw new Pt(r(a," has been removed"+(t?" in "+t:"")),Pt.ERR_DEPRECATED);return t&&!tr[a]&&(tr[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,l)}},er.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const nr={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Pt("options must be an object",Pt.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const l=r[a],o=t[l];if(o){const t=e[l],n=void 0===t||o(t,l,e);if(!0!==n)throw new Pt("option "+l+" must be "+n,Pt.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new Pt("Unknown option "+l,Pt.ERR_BAD_OPTION)}},validators:er},rr=nr.validators;class ar{constructor(e){this.defaults=e||{},this.interceptors={request:new Bt,response:new Bt}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(ta){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=wn(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&nr.assertOptions(n,{silentJSONParsing:rr.transitional(rr.boolean),forcedJSONParsing:rr.transitional(rr.boolean),clarifyTimeoutError:rr.transitional(rr.boolean)},!1),null!=r&&(jt.isFunction(r)?t.paramsSerializer={serialize:r}:nr.assertOptions(r,{encode:rr.function,serialize:rr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),nr.assertOptions(t,{baseUrl:rr.spelling("baseURL"),withXsrfToken:rr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let l=a&&jt.merge(a.common,a[t.method]);a&&jt.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=ln.concat(l,a);const o=[];let i=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,o.unshift(e.fulfilled,e.rejected))});const s=[];let u;this.interceptors.response.forEach(function(e){s.push(e.fulfilled,e.rejected)});let c,d=0;if(!i){const e=[Gn.bind(this),void 0];for(e.unshift.apply(e,o),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=o.length;let f=t;for(d=0;d<c;){const e=o[d++],t=o[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=Gn.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return Mt(bn((e=wn(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}jt.forEach(["delete","get","head","options"],function(e){ar.prototype[e]=function(t,n){return this.request(wn(n||{},{method:e,url:t,data:(n||{}).data}))}}),jt.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(wn(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ar.prototype[e]=t(),ar.prototype[e+"Form"]=t(!0)});const lr=ar;class or{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new cn(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new or(function(t){e=t}),cancel:e}}}const ir=or;const sr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(sr).forEach(e=>{let[t,n]=e;sr[n]=t});const ur=sr;const cr=function e(t){const n=new lr(t),r=Me(lr.prototype.request,n);return jt.extend(r,lr.prototype,n,{allOwnKeys:!0}),jt.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(wn(t,n))},r}(Gt);cr.Axios=lr,cr.CanceledError=cn,cr.CancelToken=ir,cr.isCancel=sn,cr.VERSION=Zn,cr.toFormData=Ut,cr.AxiosError=Pt,cr.Cancel=cr.CanceledError,cr.all=function(e){return Promise.all(e)},cr.spread=function(e){return function(t){return e.apply(null,t)}},cr.isAxiosError=function(e){return jt.isObject(e)&&!0===e.isAxiosError},cr.mergeConfig=wn,cr.AxiosHeaders=ln,cr.formToJSON=e=>Xt(jt.isHTMLForm(e)?new FormData(e):e),cr.getAdapter=Xn,cr.HttpStatusCode=ur,cr.default=cr;const dr=cr,fr={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:8080/api/v1",pr=dr.create({baseURL:fr,headers:{"Content-Type":"application/json"}});function mr(e,t){localStorage.setItem("token",e),localStorage.setItem("refresh_token",t)}function hr(){localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user")}pr.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e));let gr=!1,vr=[];function yr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;vr.forEach(n=>{e?n.reject(e):n.resolve(t)}),vr=[]}pr.interceptors.response.use(e=>e,async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;const e=localStorage.getItem("refresh_token"),t=localStorage.getItem("user");if(e&&t)try{if(gr)return new Promise((e,t)=>{vr.push({resolve:e,reject:t})}).then(e=>{const t=e;return n.headers=n.headers||{},n.headers.Authorization="Bearer "+t,pr(n)}).catch(e=>Promise.reject(e));gr=!0;const{id:r}=JSON.parse(t),a=await pr.post("/auth/refresh",{user_id:r,refresh_token:e});return mr(a.data.token,e),yr(null,a.data.token),n.headers=n.headers||{},n.headers.Authorization="Bearer "+a.data.token,pr(n)}catch(r){return yr(r,null),hr(),window.location.href="/login",Promise.reject(r)}finally{gr=!1}else hr(),window.location.href="/login"}return Promise.reject(e)});const br=e=>pr.post("/auth/register",e),xr=async e=>{const t=await pr.post("/auth/login",e);mr(t.data.token,t.data.refresh_token);const n=Ie(Ie({},t.data.user),{},{roles:t.data.roles});return localStorage.setItem("user",JSON.stringify(n)),t.data.user=n,t},wr=(e,t)=>pr.post("/auth/refresh",{user_id:e,refresh_token:t}),Sr=()=>{const e=localStorage.getItem("token"),t=localStorage.getItem("refresh_token"),n=localStorage.getItem("user");let r;if(n)try{r=JSON.parse(n).id}catch(a){}return hr(),pr.post("/auth/logout",{user_id:r,refresh_token:t,access_token:e})},kr=()=>pr.post("/auth/revoke-all"),Nr=()=>pr.get("/user/profile"),jr=e=>pr.post("/admin/user-management",e),Er=(e,t)=>pr.put("/admin/user-management/".concat(e),t),_r=e=>pr.post("/admin/role-management",e),Cr=(e,t)=>pr.put("/admin/role-management/".concat(e),t),Pr=e=>pr.post("/admin/permission-management",e),Rr=(e,t)=>pr.put("/admin/permission-management/".concat(e),t),Or=()=>pr.get("/admin/users"),Tr=e=>pr.get("/admin/user/".concat(e,"/permissions")),Lr=e=>pr.get("/admin/user/".concat(e,"/direct-permissions")),Ur=e=>{let{onAuthSuccess:t}=e;const[n,r]=(0,l.useState)(!0),[a,o]=(0,l.useState)({username:"",email:"",password:""}),[i,s]=(0,l.useState)(""),[u,c]=(0,l.useState)(!1),d=e=>{o(Ie(Ie({},a),{},{[e.target.name]:e.target.value}))};return(0,Le.jsxs)("div",{className:"auth-container",children:[(0,Le.jsx)("h2",{children:n?"Login":"Register"}),i&&(0,Le.jsx)("div",{className:"error",children:i}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),s(""),c(!0);try{let e;e=n?await xr({email:a.email,password:a.password}):await br(a);const{token:r,refresh_token:l,user:o,roles:i}=e.data;mr(r,l);const s=Ie(Ie({},o),{},{roles:i});localStorage.setItem("user",JSON.stringify(s)),t(s)}catch(o){var r,l;s((null===(r=o.response)||void 0===r||null===(l=r.data)||void 0===l?void 0:l.error)||"An error occurred")}finally{c(!1)}},children:[!n&&(0,Le.jsxs)("div",{className:"form-group",children:[(0,Le.jsx)("label",{htmlFor:"username",children:"Username"}),(0,Le.jsx)("input",{type:"text",id:"username",name:"username",value:a.username,onChange:d,required:!0,minLength:3,maxLength:50})]}),(0,Le.jsxs)("div",{className:"form-group",children:[(0,Le.jsx)("label",{htmlFor:"email",children:"Email"}),(0,Le.jsx)("input",{type:"email",id:"email",name:"email",value:a.email,onChange:d,required:!0})]}),(0,Le.jsxs)("div",{className:"form-group",children:[(0,Le.jsx)("label",{htmlFor:"password",children:"Password"}),(0,Le.jsx)("input",{type:"password",id:"password",name:"password",value:a.password,onChange:d,required:!0,minLength:6})]}),(0,Le.jsx)("button",{type:"submit",className:"btn",disabled:u,children:u?"Loading...":n?"Login":"Register"})]}),(0,Le.jsx)("div",{className:"toggle-form",children:(0,Le.jsx)("button",{type:"button",onClick:()=>{r(!n),s(""),o({username:"",email:"",password:""})},children:n?"Don't have an account? Register":"Already have an account? Login"})})]})},zr=e=>{let{onLogout:t}=e;const[n,r]=(0,l.useState)(null),[a,o]=(0,l.useState)(!0),[i,s]=(0,l.useState)(""),[u,c]=(0,l.useState)("");(0,l.useEffect)(()=>{d()},[]);const d=async()=>{o(!0),s(""),c("");try{const e=await Nr(),{user:t}=e.data;localStorage.setItem("user",JSON.stringify(t)),r(t)}catch(e){s("Failed to load profile")}finally{o(!1)}};n&&n.roles&&n.roles.includes("admin");return a?(0,Le.jsx)("div",{className:"container",children:(0,Le.jsx)("div",{className:"profile-card",children:(0,Le.jsx)("p",{children:"Loading profile..."})})}):i?(0,Le.jsx)("div",{className:"container",children:(0,Le.jsxs)("div",{className:"profile-card",children:[(0,Le.jsx)("div",{className:"error",children:i}),(0,Le.jsx)("button",{className:"btn",onClick:d,children:"Retry"})]})}):(0,Le.jsx)("div",{className:"container",children:(0,Le.jsxs)("div",{className:"profile-card",children:[(0,Le.jsx)("h2",{children:"User Profile"}),u&&(0,Le.jsx)("div",{className:"success",children:u}),n&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"ID:"})," ",n.id]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Username:"})," ",n.username]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Email:"})," ",n.email]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Created:"})," ",new Date(n.created_at).toLocaleDateString()]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Roles:"})," ",n.roles&&n.roles.length>0?n.roles.join(", "):"None"]})]}),(0,Le.jsx)("button",{className:"btn btn-secondary",onClick:async()=>{try{await Sr(),c("Logged out successfully!")}catch(e){s("Logout error")}finally{hr(),t()}},style:{marginTop:"20px"},children:"Logout"})]})})},Fr=()=>{const[e,t]=(0,l.useState)(null),[n,r]=(0,l.useState)(!1),[a,o]=(0,l.useState)("");(0,l.useEffect)(()=>{i()},[]);const i=()=>{const e=localStorage.getItem("token"),n=localStorage.getItem("refresh_token"),r=localStorage.getItem("user");if(e&&n&&r)try{const a=JSON.parse(atob(e.split(".")[1])),l=new Date(1e3*a.exp);t({token:e.substring(0,20)+"...",refreshToken:n.substring(0,20)+"...",user:JSON.parse(r),tokenExpiry:l})}catch(a){console.error("Error parsing token:",a)}},s=()=>!(null===e||void 0===e||!e.tokenExpiry)&&new Date>e.tokenExpiry;return e?(0,Le.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,Le.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Token Manager"}),(0,Le.jsxs)("div",{className:"space-y-4",children:[(0,Le.jsxs)("div",{children:[(0,Le.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User:"}),(0,Le.jsxs)("p",{className:"text-sm text-gray-900",children:[e.user.username," (",e.user.email,")"]})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Access Token:"}),(0,Le.jsx)("p",{className:"text-sm text-gray-900 font-mono",children:e.token}),(0,Le.jsxs)("div",{className:"flex items-center mt-1",children:[(0,Le.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(s()?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:s()?"Expired":"Valid"}),(0,Le.jsx)("span",{className:"ml-2 text-xs text-gray-500",children:s()?"Token has expired":"Expires in ".concat((()=>{if(null===e||void 0===e||!e.tokenExpiry)return"";const t=new Date,n=e.tokenExpiry.getTime()-t.getTime();if(n<=0)return"Expired";const r=Math.floor(n/6e4),a=Math.floor(r/60);return a>0?"".concat(a,"h ").concat(r%60,"m"):"".concat(r,"m")})())})]})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Refresh Token:"}),(0,Le.jsx)("p",{className:"text-sm text-gray-900 font-mono",children:e.refreshToken})]}),(0,Le.jsxs)("div",{className:"flex space-x-3",children:[(0,Le.jsx)("button",{onClick:async()=>{var t;if(null!==e&&void 0!==e&&null!==(t=e.user)&&void 0!==t&&t.id){r(!0),o("");try{const t=localStorage.getItem("refresh_token");if(!t)throw new Error("No refresh token found");await wr(e.user.id,t);o("\u2705 Token refreshed successfully!"),i()}catch(l){var n,a;o("\u274c Failed to refresh token: ".concat((null===(n=l.response)||void 0===n||null===(a=n.data)||void 0===a?void 0:a.error)||l.message))}finally{r(!1)}}},disabled:n,className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50",children:n?"Refreshing...":"Refresh Token"}),(0,Le.jsx)("button",{onClick:async()=>{try{await kr(),o("\u2705 All refresh tokens revoked successfully!"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user"),t(null)}catch(r){var e,n;o("\u274c Failed to revoke tokens: ".concat((null===(e=r.response)||void 0===e||null===(n=e.data)||void 0===n?void 0:n.error)||r.message))}},className:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded",children:"Revoke All Tokens"}),(0,Le.jsx)("button",{onClick:i,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Reload Info"})]}),a&&(0,Le.jsx)("div",{className:"p-3 rounded ".concat(a.startsWith("\u2705")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:a})]})]}):(0,Le.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,Le.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Token Manager"}),(0,Le.jsx)("p",{className:"text-gray-600",children:"No active session found. Please login first."})]})},Dr=()=>{const[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)(!0),[a,o]=(0,l.useState)(null),[i,s]=(0,l.useState)(!1),[u,c]=(0,l.useState)("");(0,l.useEffect)(()=>{d()},[]);const d=async()=>{r(!0),c("");try{const e=(await Or()).data,n=await Promise.all(e.map(async e=>{const[t,n]=await Promise.all([Tr(e.id),Lr(e.id)]);return Ie(Ie({},e),{},{permissions:t.data,directPermissions:n.data})}));t(n)}catch(e){c("Failed to load users")}finally{r(!1)}};return(0,Le.jsxs)("div",{className:"container",children:[(0,Le.jsx)("h2",{children:"User Management"}),u&&(0,Le.jsx)("div",{className:"error",children:u}),n?(0,Le.jsx)("p",{children:"Loading users..."}):(0,Le.jsxs)("table",{className:"user-table",style:{width:"100%",borderCollapse:"collapse"},children:[(0,Le.jsx)("thead",{children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{children:"ID"}),(0,Le.jsx)("th",{children:"Username"}),(0,Le.jsx)("th",{children:"Email"}),(0,Le.jsx)("th",{children:"Role"}),(0,Le.jsx)("th",{children:"Actions"})]})}),(0,Le.jsx)("tbody",{children:e.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{children:e.id}),(0,Le.jsx)("td",{children:e.username}),(0,Le.jsx)("td",{children:e.email}),(0,Le.jsx)("td",{children:e.roles&&e.roles.length>0?e.roles.join(", "):"None"}),(0,Le.jsx)("td",{children:(0,Le.jsx)("button",{className:"btn",onClick:()=>(e=>{o(e),s(!0)})(e),children:"Xem quy\u1ec1n"})})]},e.id))})]}),i&&a&&(0,Le.jsx)("div",{className:"modal-overlay",style:{position:"fixed",top:0,left:0,width:"100vw",height:"100vh",background:"rgba(0,0,0,0.3)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,Le.jsxs)("div",{className:"modal",style:{background:"white",padding:30,borderRadius:10,minWidth:350},children:[(0,Le.jsxs)("h3",{children:["Quy\u1ec1n c\u1ee7a user: ",a.username]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("strong",{children:"Quy\u1ec1n t\u1ed5ng h\u1ee3p (qua role):"}),(0,Le.jsx)("ul",{children:a.permissions&&0!==a.permissions.length?a.permissions.map(e=>(0,Le.jsx)("li",{children:e},e)):(0,Le.jsx)("li",{children:"Kh\xf4ng c\xf3"})})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("strong",{children:"Quy\u1ec1n tr\u1ef1c ti\u1ebfp:"}),(0,Le.jsx)("ul",{children:a.directPermissions&&0!==a.directPermissions.length?a.directPermissions.map(e=>(0,Le.jsx)("li",{children:e},e)):(0,Le.jsx)("li",{children:"Kh\xf4ng c\xf3"})})]}),(0,Le.jsx)("button",{className:"btn btn-secondary",onClick:()=>{o(null),s(!1)},style:{marginTop:20},children:"\u0110\xf3ng"})]})})]})},Ar={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:8080/api/v1",Ir=dr.create({baseURL:Ar,headers:{"Content-Type":"application/json"}});Ir.interceptors.request.use(e=>{const t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),Ir.interceptors.response.use(e=>e,async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;try{const e=localStorage.getItem("refresh_token");if(!e)throw new Error("No refresh token available");const t=await dr.post("".concat(Ar,"/auth/refresh"),{refresh_token:e}),{token:r,refresh_token:a}=t.data;return localStorage.setItem("access_token",r),localStorage.setItem("refresh_token",a),n.headers.Authorization="Bearer ".concat(r),Ir(n)}catch(r){return localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user"),window.location.href="/login",Promise.reject(r)}}return Promise.reject(e)});const Mr={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:8080/api/v1",Br=dr.create({baseURL:Mr,headers:{"Content-Type":"application/json"}});Br.interceptors.request.use(e=>{const t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),Br.interceptors.response.use(e=>e,async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;try{const e=localStorage.getItem("refresh_token");if(!e)throw new Error("No refresh token available");const t=await dr.post("".concat(Mr,"/auth/refresh"),{refresh_token:e}),{token:r,refresh_token:a}=t.data;return localStorage.setItem("access_token",r),localStorage.setItem("refresh_token",a),n.headers.Authorization="Bearer ".concat(r),Br(n)}catch(r){return localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user"),window.location.href="/login",Promise.reject(r)}}return Promise.reject(e)});class Wr{constructor(e){this.baseUrl=void 0,this.baseUrl=e}list(e){return Br.get(this.baseUrl,{params:e})}search(e){return Br.post("".concat(this.baseUrl,"/search"),e)}getById(e){return Br.get("".concat(this.baseUrl,"/").concat(e))}create(e){return Br.post(this.baseUrl,e)}update(e,t){return Br.put("".concat(this.baseUrl,"/").concat(e),t)}delete(e){return Br.delete("".concat(this.baseUrl,"/").concat(e))}}const Hr=new class extends Wr{constructor(){super("/admin/user-management")}getUsersWithRoles(){return Br.get("/admin/users")}getUserRoles(e){return Br.get("/admin/user/".concat(e,"/roles"))}getUserPermissions(e){return Br.get("/admin/user/".concat(e,"/permissions"))}getUserDirectPermissions(e){return Br.get("/admin/user/".concat(e,"/direct-permissions"))}assignRole(e,t){return Br.post("/admin/assign-role",{user_id:e,role:t})}removeRole(e,t){return Br.post("/admin/remove-role",{user_id:e,role:t})}assignPermission(e,t){return Br.post("/admin/assign-user-permission",{user_id:e,permission:t})}removePermission(e,t){return Br.post("/admin/remove-user-permission",{user_id:e,permission:t})}assignRoleCRUD(e,t){return Br.post("".concat(this.baseUrl,"/").concat(e,"/roles"),{role_id:t})}removeRoleCRUD(e,t){return Br.delete("".concat(this.baseUrl,"/").concat(e,"/roles/").concat(t))}assignPermissionCRUD(e,t){return Br.post("".concat(this.baseUrl,"/").concat(e,"/permissions"),{permission_id:t})}removePermissionCRUD(e,t){return Br.delete("".concat(this.baseUrl,"/").concat(e,"/permissions/").concat(t))}};const Vr=new class extends Wr{constructor(){super("/admin/role-management")}getAllRoles(){return Br.get("/admin/roles")}getActiveRoles(){return this.list({active_only:!0})}getRolePermissions(e){return Br.get("/admin/role/".concat(e,"/permissions"))}getRoleWithPermissions(e){return this.getById(e)}assignPermission(e,t){return Br.post("/admin/assign-permission",{role_id:e,permission:t})}removePermission(e,t){return Br.post("/admin/remove-permission",{role_id:e,permission:t})}assignPermissionById(e,t){return Br.post("".concat(this.baseUrl,"/").concat(e,"/permissions"),{permission_id:t})}removePermissionById(e,t){return Br.delete("".concat(this.baseUrl,"/").concat(e,"/permissions/").concat(t))}assignPermissionCRUD(e,t){return Br.post("".concat(this.baseUrl,"/").concat(e,"/permissions"),{permission_id:t})}removePermissionCRUD(e,t){return Br.delete("".concat(this.baseUrl,"/").concat(e,"/permissions/").concat(t))}searchRoles(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return this.search({page:t,page_size:n,search_term:e,filters:{is_active:!0}})}getRolesByName(e){return this.list({page:1,page_size:100,name_like:e})}toggleActiveStatus(e,t){return this.update(e,{is_active:t})}bulkDelete(e){return Br.post("".concat(this.baseUrl,"/bulk-delete"),{ids:e})}bulkToggleStatus(e,t){return Br.post("".concat(this.baseUrl,"/bulk-toggle-status"),{ids:e,is_active:t})}};const $r=new class extends Wr{constructor(){super("/admin/permission-management")}getAllPermissions(){return Br.get("/admin/permissions")}getActivePermissions(){return this.list({active_only:!0})}searchPermissions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return this.search({page:t,page_size:n,search_term:e,filters:{is_active:!0}})}getPermissionsByName(e){return this.list({page:1,page_size:100,name_like:e})}toggleActiveStatus(e,t){return this.update(e,{is_active:t})}getPermissionsByCategory(e){return this.list({page:1,page_size:100,category:e})}bulkDelete(e){return Br.post("".concat(this.baseUrl,"/bulk-delete"),{ids:e})}bulkToggleStatus(e,t){return Br.post("".concat(this.baseUrl,"/bulk-toggle-status"),{ids:e,is_active:t})}getUnassignedPermissions(e){return this.list({page:1,page_size:100,exclude_role:e,is_active:!0})}getUnassignedUserPermissions(e){return this.list({page:1,page_size:100,exclude_user:e,is_active:!0})}advancedSearch(e){return this.search({page:e.page||1,page_size:e.page_size||10,search_term:e.search_term,filters:{is_active:e.is_active,category:e.category,created_after:e.created_after,created_before:e.created_before}})}};const qr=new class{constructor(){this.baseUrl="/admin"}getDashboard(){return Br.get("".concat(this.baseUrl,"/dashboard"))}getUsers(){return Br.get("".concat(this.baseUrl,"/users"))}getUserRoles(e){return Br.get("".concat(this.baseUrl,"/user/").concat(e,"/roles"))}getUserPermissions(e){return Br.get("".concat(this.baseUrl,"/user/").concat(e,"/permissions"))}getUserDirectPermissions(e){return Br.get("".concat(this.baseUrl,"/user/").concat(e,"/direct-permissions"))}getRoles(){return Br.get("".concat(this.baseUrl,"/roles"))}getRolePermissions(e){return Br.get("".concat(this.baseUrl,"/role/").concat(e,"/permissions"))}getPermissions(){return Br.get("".concat(this.baseUrl,"/permissions"))}assignRole(e){return Br.post("".concat(this.baseUrl,"/assign-role"),e)}removeRole(e){return Br.post("".concat(this.baseUrl,"/remove-role"),e)}assignPermission(e){return Br.post("".concat(this.baseUrl,"/assign-permission"),e)}removePermission(e){return Br.post("".concat(this.baseUrl,"/remove-permission"),e)}assignUserPermission(e){return Br.post("".concat(this.baseUrl,"/assign-user-permission"),e)}removeUserPermission(e){return Br.post("".concat(this.baseUrl,"/remove-user-permission"),e)}assignRoleToUser(e,t){return this.assignRole({user_id:e,role:t})}removeRoleFromUser(e,t){return this.removeRole({user_id:e,role:t})}assignPermissionToRole(e,t){return this.assignPermission({role_id:e,permission:t})}removePermissionFromRole(e,t){return this.removePermission({role_id:e,permission:t})}assignPermissionToUser(e,t){return this.assignUserPermission({user_id:e,permission:t})}removePermissionFromUser(e,t){return this.removeUserPermission({user_id:e,permission:t})}},Qr=e=>{let{isOpen:t,onClose:n,user:r,onSuccess:a}=e;const[o,i]=(0,l.useState)({username:"",email:"",password:"",is_active:!0}),[s,u]=(0,l.useState)(!1),[c,d]=(0,l.useState)("");(0,l.useEffect)(()=>{i(r?{username:r.username,email:r.email,password:"",is_active:r.is_active}:{username:"",email:"",password:"",is_active:!0}),d("")},[r,t]);const f=e=>{const{name:t,value:n,type:r,checked:a}=e.target;i(e=>Ie(Ie({},e),{},{[t]:"checkbox"===r?a:n}))};return t?(0,Le.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Le.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,Le.jsxs)("div",{className:"mt-3",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:r?"Edit User":"Add New User"}),c&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),u(!0),d("");try{if(r){const e={username:o.username,email:o.email,is_active:o.is_active};o.password.trim()&&(e.password=o.password),await Er(r.id,e)}else{if(!o.password.trim())return void d("Password is required for new users");await jr(o)}a(),n()}catch(i){var t,l;d((null===(t=i.response)||void 0===t||null===(l=t.data)||void 0===l?void 0:l.error)||i.message)}finally{u(!1)}},children:[(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),(0,Le.jsx)("input",{type:"text",id:"username",name:"username",value:o.username,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,Le.jsx)("input",{type:"email",id:"email",name:"email",value:o.email,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsxs)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:["Password ",r&&"(leave blank to keep current)"]}),(0,Le.jsx)("input",{type:"password",id:"password",name:"password",value:o.password,onChange:f,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,Le.jsx)("div",{className:"mb-6",children:(0,Le.jsxs)("label",{className:"flex items-center",children:[(0,Le.jsx)("input",{type:"checkbox",name:"is_active",checked:o.is_active,onChange:f,className:"mr-2"}),(0,Le.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),(0,Le.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,Le.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",disabled:s,className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50",children:s?"Saving...":r?"Update":"Create"})]})]})]})})}):null},Kr=e=>{let{isOpen:t,onClose:n,role:r,onSuccess:a}=e;const[o,i]=(0,l.useState)({name:"",description:"",is_active:!0}),[s,u]=(0,l.useState)(!1),[c,d]=(0,l.useState)("");(0,l.useEffect)(()=>{i(r?{name:r.name,description:r.description||"",is_active:r.is_active}:{name:"",description:"",is_active:!0}),d("")},[r,t]);const f=e=>{const{name:t,value:n,type:r}=e.target,a=e.target.checked;i(e=>Ie(Ie({},e),{},{[t]:"checkbox"===r?a:n}))};return t?(0,Le.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Le.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,Le.jsxs)("div",{className:"mt-3",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:r?"Edit Role":"Add New Role"}),c&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),u(!0),d("");try{r?await Cr(r.id,o):await _r(o),a(),n()}catch(i){var t,l;d((null===(t=i.response)||void 0===t||null===(l=t.data)||void 0===l?void 0:l.error)||i.message)}finally{u(!1)}},children:[(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Role Name"}),(0,Le.jsx)("input",{type:"text",id:"name",name:"name",value:o.name,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., admin, manager, user"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,Le.jsx)("textarea",{id:"description",name:"description",value:o.description,onChange:f,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe the role's purpose and responsibilities"})]}),(0,Le.jsx)("div",{className:"mb-6",children:(0,Le.jsxs)("label",{className:"flex items-center",children:[(0,Le.jsx)("input",{type:"checkbox",name:"is_active",checked:o.is_active,onChange:f,className:"mr-2"}),(0,Le.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),(0,Le.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,Le.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",disabled:s,className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50",children:s?"Saving...":r?"Update":"Create"})]})]})]})})}):null},Jr=e=>{let{isOpen:t,onClose:n,permission:r,onSuccess:a}=e;const[o,i]=(0,l.useState)({name:"",description:"",is_active:!0}),[s,u]=(0,l.useState)(!1),[c,d]=(0,l.useState)("");(0,l.useEffect)(()=>{i(r?{name:r.name,description:r.description||"",is_active:r.is_active}:{name:"",description:"",is_active:!0}),d("")},[r,t]);const f=e=>{const{name:t,value:n,type:r}=e.target,a=e.target.checked;i(e=>Ie(Ie({},e),{},{[t]:"checkbox"===r?a:n}))};return t?(0,Le.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Le.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,Le.jsxs)("div",{className:"mt-3",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:r?"Edit Permission":"Add New Permission"}),c&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),u(!0),d("");try{r?await Rr(r.id,o):await Pr(o),a(),n()}catch(i){var t,l;d((null===(t=i.response)||void 0===t||null===(l=t.data)||void 0===l?void 0:l.error)||i.message)}finally{u(!1)}},children:[(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Permission Name"}),(0,Le.jsx)("input",{type:"text",id:"name",name:"name",value:o.name,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., read_users, edit_users, delete_users"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,Le.jsx)("textarea",{id:"description",name:"description",value:o.description,onChange:f,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe what this permission allows users to do"})]}),(0,Le.jsx)("div",{className:"mb-6",children:(0,Le.jsxs)("label",{className:"flex items-center",children:[(0,Le.jsx)("input",{type:"checkbox",name:"is_active",checked:o.is_active,onChange:f,className:"mr-2"}),(0,Le.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),(0,Le.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,Le.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",disabled:s,className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50",children:s?"Saving...":r?"Update":"Create"})]})]})]})})}):null},Xr=e=>{let{users:t,roles:n,onMessage:r,onError:a}=e;const[o,i]=(0,l.useState)(null),[s,u]=(0,l.useState)([]),[c,d]=(0,l.useState)([]),[f,p]=(0,l.useState)([]),[m,h]=(0,l.useState)([]),[g,v]=(0,l.useState)(!1),[y,b]=(0,l.useState)("roles");(0,l.useEffect)(()=>{o&&x()},[o]);const x=async()=>{if(o){v(!0);try{const e=await qr.getUserRoles(o.id);u(e.data);const t=await qr.getUserPermissions(o.id);d(t.data);const r=e.data;p(n.filter(e=>!r.includes(e.name)&&e.is_active)),h([])}catch(r){var e,t;a("Failed to load user details: ".concat((null===(e=r.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.error)||r.message))}finally{v(!1)}}};return(0,Le.jsxs)("div",{children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold mb-6",children:"User Role & Permission Assignments"}),(0,Le.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,Le.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Select User"}),(0,Le.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:t.map(e=>(0,Le.jsxs)("button",{onClick:()=>i(e),className:"w-full text-left p-3 rounded border ".concat((null===o||void 0===o?void 0:o.id)===e.id?"bg-blue-100 border-blue-500":"bg-gray-50 border-gray-200 hover:bg-gray-100"),children:[(0,Le.jsx)("div",{className:"font-medium",children:e.username}),(0,Le.jsx)("div",{className:"text-sm text-gray-600",children:e.email})]},e.id))})]}),o&&(0,Le.jsx)("div",{className:"lg:col-span-2",children:(0,Le.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,Le.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,Le.jsxs)("h3",{className:"text-lg font-medium",children:["Managing: ",o.username]}),(0,Le.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,Le.jsx)("button",{onClick:()=>b("roles"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("roles"===y?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"Roles"}),(0,Le.jsx)("button",{onClick:()=>b("permissions"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("permissions"===y?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"Permissions"})]})]}),g?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading..."}):(0,Le.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("h4",{className:"font-medium mb-3",children:["Assigned ","roles"===y?"Roles":"Permissions"]}),(0,Le.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:"roles"===y?s.length>0?s.map(e=>(0,Le.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded",children:[(0,Le.jsx)("div",{children:(0,Le.jsx)("div",{className:"font-medium text-green-800",children:e})}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(o)try{await qr.removeRoleFromUser(o.id,e),r("Role removed successfully"),x()}catch(l){var t,n;a("Failed to remove role: ".concat((null===(t=l.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||l.message))}})(e),className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},e)):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No roles assigned"}):c.length>0?c.map(e=>(0,Le.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded",children:[(0,Le.jsx)("div",{children:(0,Le.jsx)("div",{className:"font-medium text-green-800",children:e})}),(0,Le.jsx)("button",{onClick:()=>(async()=>{if(o)try{a("Direct permission removal not yet implemented")}catch(n){var e,t;a("Failed to remove permission: ".concat((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.error)||n.message))}})(),className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},e)):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No permissions assigned"})})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("h4",{className:"font-medium mb-3",children:["Available ","roles"===y?"Roles":"Permissions"]}),(0,Le.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:"roles"===y?f.length>0?f.map(e=>(0,Le.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded",children:[(0,Le.jsxs)("div",{children:[(0,Le.jsx)("div",{className:"font-medium",children:e.name}),e.description&&(0,Le.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(o)try{await qr.assignRoleToUser(o.id,e),r("Role assigned successfully"),x()}catch(l){var t,n;a("Failed to assign role: ".concat((null===(t=l.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||l.message))}})(e.name),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Assign"})]},e.id)):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No available roles"}):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"Permission assignment feature coming soon"})})]})]})]})})]}),!o&&(0,Le.jsx)("div",{className:"text-center py-12 text-gray-500",children:(0,Le.jsx)("p",{children:"Select a user to manage their roles and permissions"})})]})},Yr=()=>{const[e,t]=(0,l.useState)("users"),[n,r]=(0,l.useState)([]),[a,o]=(0,l.useState)({page:1,page_size:10,total_items:0,total_pages:0}),[i,s]=(0,l.useState)(!1),[u,c]=(0,l.useState)(""),[d,f]=(0,l.useState)([]),[p,m]=(0,l.useState)({page:1,page_size:10,total_items:0,total_pages:0}),[h,g]=(0,l.useState)(!1),[v,y]=(0,l.useState)(""),[b,x]=(0,l.useState)([]),[w,S]=(0,l.useState)({page:1,page_size:10,total_items:0,total_pages:0}),[k,N]=(0,l.useState)(!1),[j,E]=(0,l.useState)(""),[_,C]=(0,l.useState)(""),[P,R]=(0,l.useState)(""),[O,T]=(0,l.useState)(!1),[L,U]=(0,l.useState)(!1),[z,F]=(0,l.useState)(!1),[D,A]=(0,l.useState)(null),[I,M]=(0,l.useState)(null),[B,W]=(0,l.useState)(null);(0,l.useEffect)(()=>{switch(e){case"users":H();break;case"roles":$();break;case"permissions":Q();break;case"assignments":H(),$()}},[e]);const H=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";s(!0),R("");try{let n;if(t.trim()){const r={page:e,page_size:a.page_size,search_term:t,filters:{is_active:!0}};n=await Hr.search(r)}else n=await Hr.list({page:e,page_size:a.page_size});r(n.data.data),o(n.data.pagination)}catch(i){var n,l;R("Failed to load users: ".concat((null===(n=i.response)||void 0===n||null===(l=n.data)||void 0===l?void 0:l.error)||i.message))}finally{s(!1)}},V=()=>{o(e=>Ie(Ie({},e),{},{page:1})),H(1,u)},$=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";g(!0),R("");try{let n;n=t.trim()?await Vr.searchRoles(t,e,p.page_size):await Vr.list({page:e,page_size:p.page_size}),f(n.data.data),m(n.data.pagination)}catch(a){var n,r;R("Failed to load roles: ".concat((null===(n=a.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.error)||a.message))}finally{g(!1)}},q=()=>{m(e=>Ie(Ie({},e),{},{page:1})),$(1,v)},Q=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";N(!0),R("");try{let n;n=t.trim()?await $r.searchPermissions(t,e,w.page_size):await $r.list({page:e,page_size:w.page_size}),x(n.data.data),S(n.data.pagination)}catch(a){var n,r;R("Failed to load permissions: ".concat((null===(n=a.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.error)||a.message))}finally{N(!1)}},K=()=>{S(e=>Ie(Ie({},e),{},{page:1})),Q(1,j)},J=(e,t)=>{const n=[];for(let r=1;r<=e.total_pages;r++)n.push((0,Le.jsx)("button",{onClick:()=>t(r),className:"px-3 py-1 mx-1 rounded ".concat(r===e.page?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:r},r));return(0,Le.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,Le.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(e.page-1)*e.page_size+1," to"," ",Math.min(e.page*e.page_size,e.total_items)," of"," ",e.total_items," entries"]}),(0,Le.jsxs)("div",{className:"flex items-center",children:[(0,Le.jsx)("button",{onClick:()=>t(e.page-1),disabled:e.page<=1,className:"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50",children:"Previous"}),n,(0,Le.jsx)("button",{onClick:()=>t(e.page+1),disabled:e.page>=e.total_pages,className:"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50",children:"Next"})]})]})};return(0,Le.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,Le.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"User Permission Management"}),_&&(0,Le.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:_}),P&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:P}),(0,Le.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,Le.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{key:"users",label:"Users"},{key:"roles",label:"Roles"},{key:"permissions",label:"Permissions"},{key:"assignments",label:"Assignments"}].map(n=>(0,Le.jsx)("button",{onClick:()=>t(n.key),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(e===n.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:n.label},n.key))})}),"users"===e&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold",children:"User Management"}),(0,Le.jsx)("button",{onClick:()=>{A(null),T(!0)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"Add User"})]}),(0,Le.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,Le.jsx)("input",{type:"text",placeholder:"Search users by username or email...",value:u,onChange:e=>c(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&V()}),(0,Le.jsx)("button",{onClick:V,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Search"}),(0,Le.jsx)("button",{onClick:()=>{c(""),H(1,"")},className:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded",children:"Clear"})]}),i?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading users..."}):(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)("div",{className:"overflow-x-auto",children:(0,Le.jsxs)("table",{className:"min-w-full bg-white border border-gray-200",children:[(0,Le.jsx)("thead",{className:"bg-gray-50",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Username"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Le.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:n.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.id}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.username}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.email}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Le.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Active":"Inactive"})}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,Le.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,Le.jsx)("button",{onClick:()=>{A(e),T(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"Edit"}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this user?"))try{await Hr.delete(e),C("User deleted successfully"),H(a.page,u)}catch(r){var t,n;R("Failed to delete user: ".concat((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||r.message))}})(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})}),J(a,e=>{o(t=>Ie(Ie({},t),{},{page:e})),H(e,u)})]})]}),"roles"===e&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold",children:"Role Management"}),(0,Le.jsx)("button",{onClick:()=>{M(null),U(!0)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"Add Role"})]}),(0,Le.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,Le.jsx)("input",{type:"text",placeholder:"Search roles by name...",value:v,onChange:e=>y(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&q()}),(0,Le.jsx)("button",{onClick:q,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Search"}),(0,Le.jsx)("button",{onClick:()=>{y(""),$(1,"")},className:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded",children:"Clear"})]}),h?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading roles..."}):(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)("div",{className:"overflow-x-auto",children:(0,Le.jsxs)("table",{className:"min-w-full bg-white border border-gray-200",children:[(0,Le.jsx)("thead",{className:"bg-gray-50",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Le.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.id}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.name}),(0,Le.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.description||"No description"}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Le.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Active":"Inactive"})}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,Le.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,Le.jsx)("button",{onClick:()=>{M(e),U(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"Edit"}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this role?"))try{await Vr.delete(e),C("Role deleted successfully"),$(p.page,v)}catch(r){var t,n;R("Failed to delete role: ".concat((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||r.message))}})(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})}),J(p,e=>{m(t=>Ie(Ie({},t),{},{page:e})),$(e,v)})]})]}),"permissions"===e&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold",children:"Permission Management"}),(0,Le.jsx)("button",{onClick:()=>{W(null),F(!0)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"Add Permission"})]}),(0,Le.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,Le.jsx)("input",{type:"text",placeholder:"Search permissions by name...",value:j,onChange:e=>E(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&K()}),(0,Le.jsx)("button",{onClick:K,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Search"}),(0,Le.jsx)("button",{onClick:()=>{E(""),Q(1,"")},className:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded",children:"Clear"})]}),k?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading permissions..."}):(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)("div",{className:"overflow-x-auto",children:(0,Le.jsxs)("table",{className:"min-w-full bg-white border border-gray-200",children:[(0,Le.jsx)("thead",{className:"bg-gray-50",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Le.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.id}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.name}),(0,Le.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.description||"No description"}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Le.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Active":"Inactive"})}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,Le.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,Le.jsx)("button",{onClick:()=>{W(e),F(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"Edit"}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this permission?"))try{await $r.delete(e),C("Permission deleted successfully"),Q(w.page,j)}catch(r){var t,n;R("Failed to delete permission: ".concat((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||r.message))}})(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})}),J(w,e=>{S(t=>Ie(Ie({},t),{},{page:e})),Q(e,j)})]})]}),"assignments"===e&&(0,Le.jsx)(Xr,{users:n,roles:d,onMessage:C,onError:R}),(0,Le.jsx)(Qr,{isOpen:O,onClose:()=>T(!1),user:D,onSuccess:()=>{C(D?"User updated successfully":"User created successfully"),H(a.page,u)}}),(0,Le.jsx)(Kr,{isOpen:L,onClose:()=>U(!1),role:I,onSuccess:()=>{C(I?"Role updated successfully":"Role created successfully"),$(p.page,v)}}),(0,Le.jsx)(Jr,{isOpen:z,onClose:()=>F(!1),permission:B,onSuccess:()=>{C(B?"Permission updated successfully":"Permission created successfully"),Q(w.page,j)}})]})},Gr=e=>{let{role:t,isOpen:n,onClose:r,onSuccess:a,onError:o}=e;const[i,s]=(0,l.useState)([]),[u,c]=(0,l.useState)([]),[d,f]=(0,l.useState)(!1),[p,m]=(0,l.useState)(""),[h,g]=(0,l.useState)("all");if((0,l.useEffect)(()=>{n&&t&&(v(),y())},[n,null===t||void 0===t?void 0:t.id]),!t)return null;const v=async()=>{try{const e=await $r.list({page:1,page_size:100});s(e.data.data)}catch(e){o("Failed to load permissions"),console.error("Error loading permissions:",e)}},y=async()=>{try{f(!0);const e=await Vr.getRoleWithPermissions(t.id);e.data.data&&e.data.data.permissions?c(e.data.data.permissions):c([])}catch(e){console.error("Error loading role permissions:",e),c([])}finally{f(!1)}},b=e=>u.some(t=>t.id===e.id),x=async e=>{try{f(!0),await Vr.assignPermissionById(t.id,e.id),c(t=>[...t,e]),a('Permission "'.concat(e.name,'" assigned to role "').concat(t.name,'"'))}catch(n){o(n.message||"Failed to assign permission")}finally{f(!1)}},w=async e=>{try{f(!0),await Vr.removePermissionById(t.id,e.id),c(t=>t.filter(t=>t.id!==e.id)),a('Permission "'.concat(e.name,'" removed from role "').concat(t.name,'"'))}catch(n){o(n.message||"Failed to remove permission")}finally{f(!1)}},S=i.filter(e=>{const t=""===p||e.name.toLowerCase().includes(p.toLowerCase())||e.description&&e.description.toLowerCase().includes(p.toLowerCase()),n="all"===h||e.name.toLowerCase().startsWith(h.toLowerCase());return t&&n}),k=Array.from(new Set(i.map(e=>e.name.split("_")[0]).filter(Boolean))).sort();return n?(0,Le.jsx)("div",{className:"modal fade show",style:{display:"block"},tabIndex:-1,children:(0,Le.jsx)("div",{className:"modal-dialog modal-lg",children:(0,Le.jsxs)("div",{className:"modal-content",children:[(0,Le.jsxs)("div",{className:"modal-header",children:[(0,Le.jsxs)("h5",{className:"modal-title",children:["Manage Permissions for Role: ",(0,Le.jsx)("strong",{children:t.name})]}),(0,Le.jsx)("button",{type:"button",className:"btn-close",onClick:r})]}),(0,Le.jsxs)("div",{className:"modal-body",children:[(0,Le.jsxs)("div",{className:"row mb-3",children:[(0,Le.jsx)("div",{className:"col-md-8",children:(0,Le.jsx)("input",{type:"text",className:"form-control",placeholder:"Search permissions...",value:p,onChange:e=>m(e.target.value)})}),(0,Le.jsx)("div",{className:"col-md-4",children:(0,Le.jsxs)("select",{className:"form-select",value:h,onChange:e=>g(e.target.value),children:[(0,Le.jsx)("option",{value:"all",children:"All Categories"}),k.map(e=>(0,Le.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]})})]}),(0,Le.jsx)("div",{className:"row mb-3",children:(0,Le.jsx)("div",{className:"col-12",children:(0,Le.jsxs)("div",{className:"alert alert-info",children:[(0,Le.jsx)("strong",{children:"Role Statistics:"})," ",u.length," of ",i.length," permissions assigned"]})})}),(0,Le.jsx)("div",{className:"row",children:(0,Le.jsx)("div",{className:"col-12",children:d?(0,Le.jsx)("div",{className:"text-center py-4",children:(0,Le.jsx)("div",{className:"spinner-border",role:"status",children:(0,Le.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}):(0,Le.jsx)("div",{className:"table-responsive",style:{maxHeight:"400px",overflowY:"auto"},children:(0,Le.jsxs)("table",{className:"table table-sm table-hover",children:[(0,Le.jsx)("thead",{className:"table-light sticky-top",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{children:"Permission"}),(0,Le.jsx)("th",{children:"Description"}),(0,Le.jsx)("th",{children:"Status"}),(0,Le.jsx)("th",{children:"Action"})]})}),(0,Le.jsx)("tbody",{children:0===S.length?(0,Le.jsx)("tr",{children:(0,Le.jsx)("td",{colSpan:4,className:"text-center py-3",children:"No permissions found"})}):S.map(e=>{const t=b(e);return(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{children:(0,Le.jsx)("strong",{children:e.name})}),(0,Le.jsx)("td",{children:(0,Le.jsx)("small",{className:"text-muted",children:e.description||"No description"})}),(0,Le.jsx)("td",{children:(0,Le.jsx)("span",{className:"badge ".concat(t?"bg-success":"bg-secondary"),children:t?"Assigned":"Not Assigned"})}),(0,Le.jsx)("td",{children:t?(0,Le.jsxs)("button",{className:"btn btn-sm btn-outline-danger",onClick:()=>w(e),disabled:d,children:[(0,Le.jsx)("i",{className:"fas fa-minus me-1"}),"Remove"]}):(0,Le.jsxs)("button",{className:"btn btn-sm btn-outline-success",onClick:()=>x(e),disabled:d,children:[(0,Le.jsx)("i",{className:"fas fa-plus me-1"}),"Assign"]})})]},e.id)})})]})})})}),(0,Le.jsx)("div",{className:"row mt-3",children:(0,Le.jsx)("div",{className:"col-12",children:(0,Le.jsxs)("div",{className:"d-flex gap-2",children:[(0,Le.jsxs)("button",{className:"btn btn-sm btn-outline-success",onClick:async()=>{for(const e of S)b(e)||await x(e)},disabled:d,children:[(0,Le.jsx)("i",{className:"fas fa-check-double me-1"}),"Assign All Filtered"]}),(0,Le.jsxs)("button",{className:"btn btn-sm btn-outline-danger",onClick:async()=>{for(const e of S)b(e)&&await w(e)},disabled:d,children:[(0,Le.jsx)("i",{className:"fas fa-times me-1"}),"Remove All Filtered"]})]})})})]}),(0,Le.jsx)("div",{className:"modal-footer",children:(0,Le.jsx)("button",{type:"button",className:"btn btn-secondary",onClick:r,children:"Close"})})]})})}):null},Zr=()=>{const[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)([]),[a,o]=(0,l.useState)(!1),[i,s]=(0,l.useState)(null),[u,c]=(0,l.useState)(null),[d,f]=(0,l.useState)({page:1,page_size:10,total_items:0,total_pages:0,has_next:!1,has_prev:!1}),[p,m]=(0,l.useState)(""),[h,g]=(0,l.useState)("all"),[v,y]=(0,l.useState)(!1),[b,x]=(0,l.useState)(!1),[w,S]=(0,l.useState)(!1),[k,N]=(0,l.useState)(null),[j,E]=(0,l.useState)({name:"",description:"",is_active:!0});(0,l.useEffect)(()=>{_(),C()},[d.page,d.page_size,p,h]);const _=async()=>{try{o(!0),s(null);const e={page:d.page,page_size:d.page_size};p.trim()&&(e.search_term=p.trim(),e.search_columns=["name","description"]),"all"!==h&&(e.is_active="active"===h);const n=await Vr.list(e);t(n.data.data);const r=n.data.pagination;f(Ie(Ie({},r),{},{has_next:r.page<r.total_pages,has_prev:r.page>1}))}catch(e){s(e.message||"Failed to load roles"),console.error("Error loading roles:",e)}finally{o(!1)}},C=async()=>{try{const e=await $r.list({page:1,page_size:100});r(e.data.data)}catch(e){console.error("Error loading permissions:",e)}},P=()=>{E({name:"",description:"",is_active:!0}),N(null)},R=()=>{s(null),c(null)};return(0,Le.jsxs)("div",{className:"container mt-4",children:[(0,Le.jsx)("div",{className:"row",children:(0,Le.jsxs)("div",{className:"col-12",children:[(0,Le.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,Le.jsx)("h2",{children:"Role Management"}),(0,Le.jsxs)("button",{className:"btn btn-primary",onClick:()=>y(!0),children:[(0,Le.jsx)("i",{className:"fas fa-plus me-2"}),"Create Role"]})]}),i&&(0,Le.jsxs)("div",{className:"alert alert-danger alert-dismissible fade show",role:"alert",children:[i,(0,Le.jsx)("button",{type:"button",className:"btn-close",onClick:R})]}),u&&(0,Le.jsxs)("div",{className:"alert alert-success alert-dismissible fade show",role:"alert",children:[u,(0,Le.jsx)("button",{type:"button",className:"btn-close",onClick:R})]}),(0,Le.jsx)("div",{className:"card mb-4",children:(0,Le.jsx)("div",{className:"card-body",children:(0,Le.jsx)("form",{onSubmit:e=>{e.preventDefault(),f(e=>Ie(Ie({},e),{},{page:1})),_()},children:(0,Le.jsxs)("div",{className:"row g-3",children:[(0,Le.jsxs)("div",{className:"col-md-6",children:[(0,Le.jsx)("label",{htmlFor:"search",className:"form-label",children:"Search Roles"}),(0,Le.jsx)("input",{type:"text",className:"form-control",id:"search",placeholder:"Search by name or description...",value:p,onChange:e=>m(e.target.value)})]}),(0,Le.jsxs)("div",{className:"col-md-3",children:[(0,Le.jsx)("label",{htmlFor:"status",className:"form-label",children:"Status"}),(0,Le.jsxs)("select",{className:"form-select",id:"status",value:h,onChange:e=>g(e.target.value),children:[(0,Le.jsx)("option",{value:"all",children:"All Status"}),(0,Le.jsx)("option",{value:"active",children:"Active"}),(0,Le.jsx)("option",{value:"inactive",children:"Inactive"})]})]}),(0,Le.jsxs)("div",{className:"col-md-3 d-flex align-items-end",children:[(0,Le.jsxs)("button",{type:"submit",className:"btn btn-outline-primary me-2",children:[(0,Le.jsx)("i",{className:"fas fa-search me-1"}),"Search"]}),(0,Le.jsx)("button",{type:"button",className:"btn btn-outline-secondary",onClick:()=>{m(""),g("all"),f(e=>Ie(Ie({},e),{},{page:1}))},children:"Clear"})]})]})})})}),(0,Le.jsx)("div",{className:"card",children:(0,Le.jsx)("div",{className:"card-body",children:a?(0,Le.jsx)("div",{className:"text-center py-4",children:(0,Le.jsx)("div",{className:"spinner-border",role:"status",children:(0,Le.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}):(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)("div",{className:"table-responsive",children:(0,Le.jsxs)("table",{className:"table table-hover",children:[(0,Le.jsx)("thead",{className:"table-light",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{children:"ID"}),(0,Le.jsx)("th",{children:"Name"}),(0,Le.jsx)("th",{children:"Description"}),(0,Le.jsx)("th",{children:"Status"}),(0,Le.jsx)("th",{children:"Created"}),(0,Le.jsx)("th",{children:"Actions"})]})}),(0,Le.jsx)("tbody",{children:0===e.length?(0,Le.jsx)("tr",{children:(0,Le.jsx)("td",{colSpan:6,className:"text-center py-4",children:"No roles found"})}):e.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{children:e.id}),(0,Le.jsx)("td",{children:(0,Le.jsx)("strong",{children:e.name})}),(0,Le.jsx)("td",{children:e.description||"-"}),(0,Le.jsx)("td",{children:(0,Le.jsx)("span",{className:"badge ".concat(e.is_active?"bg-success":"bg-secondary"),children:e.is_active?"Active":"Inactive"})}),(0,Le.jsx)("td",{children:e.created_at?new Date(e.created_at).toLocaleDateString():"-"}),(0,Le.jsx)("td",{children:(0,Le.jsxs)("div",{className:"btn-group btn-group-sm",role:"group",children:[(0,Le.jsx)("button",{className:"btn btn-outline-primary",onClick:()=>(e=>{N(e),E({name:e.name,description:e.description||"",is_active:e.is_active}),x(!0)})(e),title:"Edit Role",children:(0,Le.jsx)("i",{className:"fas fa-edit"})}),(0,Le.jsx)("button",{className:"btn btn-outline-info",onClick:()=>(e=>{N(e),S(!0)})(e),title:"Manage Permissions",children:(0,Le.jsx)("i",{className:"fas fa-key"})}),(0,Le.jsx)("button",{className:"btn btn-outline-danger",onClick:()=>(async e=>{if(window.confirm('Are you sure you want to delete role "'.concat(e.name,'"?')))try{o(!0),s(null),await Vr.delete(e.id),c("Role deleted successfully"),_()}catch(t){s(t.message||"Failed to delete role")}finally{o(!1)}})(e),title:"Delete Role",children:(0,Le.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),d.total_pages>1&&(0,Le.jsx)("nav",{"aria-label":"Roles pagination",children:(0,Le.jsxs)("ul",{className:"pagination justify-content-center mt-3",children:[(0,Le.jsx)("li",{className:"page-item ".concat(d.has_prev?"":"disabled"),children:(0,Le.jsx)("button",{className:"page-link",onClick:()=>f(e=>Ie(Ie({},e),{},{page:e.page-1})),disabled:!d.has_prev,children:"Previous"})}),Array.from({length:d.total_pages},(e,t)=>t+1).map(e=>(0,Le.jsx)("li",{className:"page-item ".concat(d.page===e?"active":""),children:(0,Le.jsx)("button",{className:"page-link",onClick:()=>f(t=>Ie(Ie({},t),{},{page:e})),children:e})},e)),(0,Le.jsx)("li",{className:"page-item ".concat(d.has_next?"":"disabled"),children:(0,Le.jsx)("button",{className:"page-link",onClick:()=>f(e=>Ie(Ie({},e),{},{page:e.page+1})),disabled:!d.has_next,children:"Next"})})]})})]})})})]})}),v&&(0,Le.jsx)("div",{className:"modal fade show",style:{display:"block"},tabIndex:-1,children:(0,Le.jsx)("div",{className:"modal-dialog",children:(0,Le.jsxs)("div",{className:"modal-content",children:[(0,Le.jsxs)("div",{className:"modal-header",children:[(0,Le.jsx)("h5",{className:"modal-title",children:"Create New Role"}),(0,Le.jsx)("button",{type:"button",className:"btn-close",onClick:()=>{y(!1),P()}})]}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{o(!0),s(null),await Vr.create(j),c("Role created successfully"),y(!1),P(),_()}catch(t){s(t.message||"Failed to create role")}finally{o(!1)}},children:[(0,Le.jsxs)("div",{className:"modal-body",children:[(0,Le.jsxs)("div",{className:"mb-3",children:[(0,Le.jsx)("label",{htmlFor:"createName",className:"form-label",children:"Role Name *"}),(0,Le.jsx)("input",{type:"text",className:"form-control",id:"createName",value:j.name,onChange:e=>E(t=>Ie(Ie({},t),{},{name:e.target.value})),required:!0})]}),(0,Le.jsxs)("div",{className:"mb-3",children:[(0,Le.jsx)("label",{htmlFor:"createDescription",className:"form-label",children:"Description"}),(0,Le.jsx)("textarea",{className:"form-control",id:"createDescription",rows:3,value:j.description,onChange:e=>E(t=>Ie(Ie({},t),{},{description:e.target.value}))})]}),(0,Le.jsxs)("div",{className:"mb-3 form-check",children:[(0,Le.jsx)("input",{type:"checkbox",className:"form-check-input",id:"createActive",checked:j.is_active,onChange:e=>E(t=>Ie(Ie({},t),{},{is_active:e.target.checked}))}),(0,Le.jsx)("label",{className:"form-check-label",htmlFor:"createActive",children:"Active"})]})]}),(0,Le.jsxs)("div",{className:"modal-footer",children:[(0,Le.jsx)("button",{type:"button",className:"btn btn-secondary",onClick:()=>{y(!1),P()},children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",className:"btn btn-primary",disabled:a,children:a?"Creating...":"Create Role"})]})]})]})})}),b&&k&&(0,Le.jsx)("div",{className:"modal fade show",style:{display:"block"},tabIndex:-1,children:(0,Le.jsx)("div",{className:"modal-dialog",children:(0,Le.jsxs)("div",{className:"modal-content",children:[(0,Le.jsxs)("div",{className:"modal-header",children:[(0,Le.jsxs)("h5",{className:"modal-title",children:["Edit Role: ",k.name]}),(0,Le.jsx)("button",{type:"button",className:"btn-close",onClick:()=>{x(!1),P()}})]}),(0,Le.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),k)try{o(!0),s(null),await Vr.update(k.id,j),c("Role updated successfully"),x(!1),P(),_()}catch(t){s(t.message||"Failed to update role")}finally{o(!1)}},children:[(0,Le.jsxs)("div",{className:"modal-body",children:[(0,Le.jsxs)("div",{className:"mb-3",children:[(0,Le.jsx)("label",{htmlFor:"editName",className:"form-label",children:"Role Name *"}),(0,Le.jsx)("input",{type:"text",className:"form-control",id:"editName",value:j.name,onChange:e=>E(t=>Ie(Ie({},t),{},{name:e.target.value})),required:!0})]}),(0,Le.jsxs)("div",{className:"mb-3",children:[(0,Le.jsx)("label",{htmlFor:"editDescription",className:"form-label",children:"Description"}),(0,Le.jsx)("textarea",{className:"form-control",id:"editDescription",rows:3,value:j.description,onChange:e=>E(t=>Ie(Ie({},t),{},{description:e.target.value}))})]}),(0,Le.jsxs)("div",{className:"mb-3 form-check",children:[(0,Le.jsx)("input",{type:"checkbox",className:"form-check-input",id:"editActive",checked:j.is_active,onChange:e=>E(t=>Ie(Ie({},t),{},{is_active:e.target.checked}))}),(0,Le.jsx)("label",{className:"form-check-label",htmlFor:"editActive",children:"Active"})]})]}),(0,Le.jsxs)("div",{className:"modal-footer",children:[(0,Le.jsx)("button",{type:"button",className:"btn btn-secondary",onClick:()=>{x(!1),P()},children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",className:"btn btn-primary",disabled:a,children:a?"Updating...":"Update Role"})]})]})]})})}),k&&(0,Le.jsx)(Gr,{role:k,isOpen:w,onClose:()=>{S(!1),N(null)},onSuccess:e=>{c(e),setTimeout(()=>c(null),5e3)},onError:e=>{s(e),setTimeout(()=>s(null),5e3)}})]})},ea=()=>{const[e,t]=(0,l.useState)(null),[n,r]=(0,l.useState)(!0);(0,l.useEffect)(()=>{const e=localStorage.getItem("token"),n=localStorage.getItem("user");if(e&&n)try{t(JSON.parse(n))}catch(a){localStorage.removeItem("token"),localStorage.removeItem("user")}r(!1)},[]);const a=()=>{t(null)},o=e&&e.roles&&e.roles.includes("admin");return n?(0,Le.jsx)("div",{className:"container",children:(0,Le.jsx)("div",{style:{textAlign:"center",marginTop:"50px"},children:(0,Le.jsx)("p",{children:"Loading..."})})}):(0,Le.jsxs)(_e,{children:[(0,Le.jsx)(Ue,{user:e,onLogout:a}),(0,Le.jsxs)(we,{children:[(0,Le.jsx)(be,{path:"/",element:e?(0,Le.jsx)(zr,{onLogout:a}):(0,Le.jsx)(Ur,{onAuthSuccess:e=>{t(e)}})}),(0,Le.jsx)(be,{path:"/tokens",element:e?(0,Le.jsx)(Fr,{}):(0,Le.jsx)(ye,{to:"/"})}),o&&(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)(be,{path:"/admin/users",element:(0,Le.jsx)(Dr,{})}),(0,Le.jsx)(be,{path:"/admin/roles",element:(0,Le.jsx)(Zr,{})}),(0,Le.jsx)(be,{path:"/admin/permissions",element:(0,Le.jsx)(Yr,{})})]}),(0,Le.jsx)(be,{path:"*",element:(0,Le.jsx)(ye,{to:"/"})})]})]})};(0,i.H)(document.getElementById("root")).render((0,Le.jsx)(l.StrictMode,{children:(0,Le.jsx)(ea,{})}))})();
//# sourceMappingURL=main.1ec31f1a.js.map