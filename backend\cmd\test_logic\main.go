package main

import (
	"fmt"
	"reflect"

	"jwt-auth-backend/models"
	"jwt-auth-backend/services"

	"gorm.io/gorm"
)

// TestProduct model for testing
type TestProduct struct {
	models.BaseModel
	Name        string  `json:"name" gorm:"not null"`
	Description string  `json:"description"`
	Price       float64 `json:"price" gorm:"not null"`
	CategoryID  uint    `json:"category_id"`
	IsActive    bool    `json:"is_active" gorm:"default:true"`
}

func main() {
	fmt.Println("🧪 Testing Base CRUD Logic (without database)")

	// Test search filter validation
	testSearchFilterValidation()

	// Test pagination calculation
	testPaginationCalculation()

	// Test field name extraction
	testFieldNameExtraction()

	// Test search operators
	testSearchOperators()

	fmt.Println("✅ All logic tests passed!")
}

func testSearchFilterValidation() {
	fmt.Println("\n🔍 Testing Search Filter Validation...")

	// Valid filter
	validFilter := models.SearchFilter{
		Field:    "name",
		Operator: models.SearchOperatorEqual,
		Value:    "test",
	}

	if err := validFilter.Validate(); err != nil {
		fmt.Printf("❌ Valid filter failed validation: %v\n", err)
		return
	}
	fmt.Println("✅ Valid filter passed validation")

	// Invalid operator
	invalidFilter := models.SearchFilter{
		Field:    "name",
		Operator: "invalid_op",
		Value:    "test",
	}

	if err := invalidFilter.Validate(); err == nil {
		fmt.Println("❌ Invalid operator should have failed validation")
		return
	}
	fmt.Println("✅ Invalid operator correctly failed validation")

	// Empty field
	emptyFieldFilter := models.SearchFilter{
		Field:    "",
		Operator: models.SearchOperatorEqual,
		Value:    "test",
	}

	if err := emptyFieldFilter.Validate(); err == nil {
		fmt.Println("❌ Empty field should have failed validation")
		return
	}
	fmt.Println("✅ Empty field correctly failed validation")
}

func testPaginationCalculation() {
	fmt.Println("\n📄 Testing Pagination Calculation...")

	// Test case 1: Normal pagination
	pagination := models.CalculatePagination(2, 10, 25)

	expected := models.PaginationResponse{
		Page:       2,
		PageSize:   10,
		Total:      25,
		TotalPages: 3,
		HasNext:    true,
		HasPrev:    true,
	}

	if !comparePagination(pagination, expected) {
		fmt.Printf("❌ Pagination calculation failed. Got: %+v, Expected: %+v\n", pagination, expected)
		return
	}
	fmt.Println("✅ Normal pagination calculation correct")

	// Test case 2: First page
	pagination = models.CalculatePagination(1, 10, 25)
	expected = models.PaginationResponse{
		Page:       1,
		PageSize:   10,
		Total:      25,
		TotalPages: 3,
		HasNext:    true,
		HasPrev:    false,
	}

	if !comparePagination(pagination, expected) {
		fmt.Printf("❌ First page calculation failed. Got: %+v, Expected: %+v\n", pagination, expected)
		return
	}
	fmt.Println("✅ First page calculation correct")

	// Test case 3: Last page
	pagination = models.CalculatePagination(3, 10, 25)
	expected = models.PaginationResponse{
		Page:       3,
		PageSize:   10,
		Total:      25,
		TotalPages: 3,
		HasNext:    false,
		HasPrev:    true,
	}

	if !comparePagination(pagination, expected) {
		fmt.Printf("❌ Last page calculation failed. Got: %+v, Expected: %+v\n", pagination, expected)
		return
	}
	fmt.Println("✅ Last page calculation correct")
}

func testFieldNameExtraction() {
	fmt.Println("\n🏷️ Testing Field Name Extraction...")

	// Create a mock service (without database)
	service := &services.BaseCRUDService{}

	// Test with TestProduct
	product := &TestProduct{}
	fields := service.GetFieldNames(product)

	fmt.Printf("Debug: Extracted fields: %v\n", fields)

	expectedFields := []string{"id", "created_at", "updated_at", "deleted_at", "name", "description", "price", "category_id", "is_active"}

	if len(fields) != len(expectedFields) {
		fmt.Printf("❌ Field count mismatch. Got %d, expected %d\n", len(fields), len(expectedFields))
		fmt.Printf("Got fields: %v\n", fields)
		fmt.Printf("Expected fields: %v\n", expectedFields)
		return
	}

	// Check if all expected fields are present
	fieldMap := make(map[string]bool)
	for _, field := range fields {
		fieldMap[field] = true
	}

	for _, expected := range expectedFields {
		if !fieldMap[expected] {
			fmt.Printf("❌ Missing expected field: %s\n", expected)
			return
		}
	}

	fmt.Printf("✅ Field extraction correct. Found fields: %v\n", fields)
}

func testSearchOperators() {
	fmt.Println("\n🔧 Testing Search Operators...")

	operators := []models.SearchOperator{
		models.SearchOperatorEqual,
		models.SearchOperatorNotEqual,
		models.SearchOperatorLike,
		models.SearchOperatorILike,
		models.SearchOperatorGreaterThan,
		models.SearchOperatorGreaterEqual,
		models.SearchOperatorLessThan,
		models.SearchOperatorLessEqual,
		models.SearchOperatorIn,
		models.SearchOperatorNotIn,
		models.SearchOperatorIsNull,
		models.SearchOperatorIsNotNull,
	}

	fmt.Printf("✅ All %d search operators defined correctly:\n", len(operators))
	for i, op := range operators {
		fmt.Printf("  %d. %s\n", i+1, op)
	}
}

func comparePagination(got, expected models.PaginationResponse) bool {
	return got.Page == expected.Page &&
		got.PageSize == expected.PageSize &&
		got.Total == expected.Total &&
		got.TotalPages == expected.TotalPages &&
		got.HasNext == expected.HasNext &&
		got.HasPrev == expected.HasPrev
}

// Test GORM scope functions
func testGORMScopes() {
	fmt.Println("\n🎯 Testing GORM Scopes...")

	// Create a mock DB (this won't actually execute, just test the function signatures)
	var db *gorm.DB

	// Test ActiveScope
	result := services.ActiveScope(db)
	if result == nil {
		fmt.Println("❌ ActiveScope returned nil")
		return
	}
	fmt.Println("✅ ActiveScope function works")

	// Test RecentScope
	result = services.RecentScope(db)
	if result == nil {
		fmt.Println("❌ RecentScope returned nil")
		return
	}
	fmt.Println("✅ RecentScope function works")

	// Test PaginateScope
	paginateFunc := services.PaginateScope(1, 10)
	if paginateFunc == nil {
		fmt.Println("❌ PaginateScope returned nil function")
		return
	}
	fmt.Println("✅ PaginateScope function works")

	// Test DateRangeScope
	dateRangeFunc := services.DateRangeScope("2023-01-01", "2023-12-31")
	if dateRangeFunc == nil {
		fmt.Println("❌ DateRangeScope returned nil function")
		return
	}
	fmt.Println("✅ DateRangeScope function works")

	// Test SearchScope
	searchFunc := services.SearchScope("test", []string{"name", "description"})
	if searchFunc == nil {
		fmt.Println("❌ SearchScope returned nil function")
		return
	}
	fmt.Println("✅ SearchScope function works")
}

// Test reflection utilities
func testReflectionUtils() {
	fmt.Println("\n🪞 Testing Reflection Utilities...")

	product := &TestProduct{}
	productType := reflect.TypeOf(product)

	if productType.Kind() == reflect.Ptr {
		productType = productType.Elem()
	}

	fmt.Printf("✅ Product type: %s\n", productType.Name())
	fmt.Printf("✅ Product package: %s\n", productType.PkgPath())
	fmt.Printf("✅ Number of fields: %d\n", productType.NumField())

	// Test slice creation
	sliceType := reflect.SliceOf(productType)
	slice := reflect.New(sliceType).Interface()

	if slice == nil {
		fmt.Println("❌ Failed to create slice")
		return
	}
	fmt.Printf("✅ Created slice type: %T\n", slice)
}
