{"ast": null, "code": "import { BaseCRUDService, api } from './base.service';\nclass UserService extends BaseCRUDService {\n  constructor() {\n    super('/admin/user-management');\n  }\n\n  // Get users with roles (from old admin API)\n  getUsersWithRoles() {\n    return api.get('/admin/users');\n  }\n\n  // Get user roles\n  getUserRoles(userId) {\n    return api.get(`/admin/user/${userId}/roles`);\n  }\n\n  // Get user permissions\n  getUserPermissions(userId) {\n    return api.get(`/admin/user/${userId}/permissions`);\n  }\n\n  // Get user direct permissions\n  getUserDirectPermissions(userId) {\n    return api.get(`/admin/user/${userId}/direct-permissions`);\n  }\n\n  // Assign role to user\n  assignRole(userId, roleName) {\n    return api.post('/admin/assign-role', {\n      user_id: userId,\n      role: roleName\n    });\n  }\n\n  // Remove role from user\n  removeRole(userId, roleName) {\n    return api.post('/admin/remove-role', {\n      user_id: userId,\n      role: roleName\n    });\n  }\n\n  // Assign permission directly to user\n  assignPermission(userId, permissionName) {\n    return api.post('/admin/assign-user-permission', {\n      user_id: userId,\n      permission: permissionName\n    });\n  }\n\n  // Remove permission from user\n  removePermission(userId, permissionName) {\n    return api.post('/admin/remove-user-permission', {\n      user_id: userId,\n      permission: permissionName\n    });\n  }\n\n  // Assign role to user using CRUD API\n  assignRoleCRUD(userId, roleId) {\n    return api.post(`${this.baseUrl}/${userId}/roles`, {\n      role_id: roleId\n    });\n  }\n\n  // Remove role from user using CRUD API\n  removeRoleCRUD(userId, roleId) {\n    return api.delete(`${this.baseUrl}/${userId}/roles/${roleId}`);\n  }\n\n  // Assign permission to user using CRUD API\n  assignPermissionCRUD(userId, permissionId) {\n    return api.post(`${this.baseUrl}/${userId}/permissions`, {\n      permission_id: permissionId\n    });\n  }\n\n  // Remove permission from user using CRUD API\n  removePermissionCRUD(userId, permissionId) {\n    return api.delete(`${this.baseUrl}/${userId}/permissions/${permissionId}`);\n  }\n}\nexport const userService = new UserService();\nexport default userService;", "map": {"version": 3, "names": ["BaseCRUDService", "api", "UserService", "constructor", "getUsersWithRoles", "get", "getUserRoles", "userId", "getUserPermissions", "getUserDirectPermissions", "assignRole", "<PERSON><PERSON><PERSON>", "post", "user_id", "role", "removeRole", "assignPermission", "permissionName", "permission", "removePermission", "assignRoleCRUD", "roleId", "baseUrl", "role_id", "removeRoleCRUD", "delete", "assignPermissionCRUD", "permissionId", "permission_id", "removePermissionCRUD", "userService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/user.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { User } from '../types';\nimport { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';\n\nexport interface CreateUserRequest {\n  username: string;\n  email: string;\n  password: string;\n  is_active?: boolean;\n}\n\nexport interface UpdateUserRequest {\n  username?: string;\n  email?: string;\n  password?: string;\n  is_active?: boolean;\n}\n\nexport interface UserWithRoles {\n  id: number;\n  username: string;\n  email: string;\n  roles: string[];\n  is_active: boolean;\n  created_at: string;\n  updated_at?: string;\n}\n\nclass UserService extends BaseCRUDService<User, CreateUserRequest, UpdateUserRequest> {\n  constructor() {\n    super('/admin/user-management');\n  }\n\n  // Get users with roles (from old admin API)\n  getUsersWithRoles(): Promise<AxiosResponse<UserWithRoles[]>> {\n    return api.get('/admin/users');\n  }\n\n  // Get user roles\n  getUserRoles(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/user/${userId}/roles`);\n  }\n\n  // Get user permissions\n  getUserPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/user/${userId}/permissions`);\n  }\n\n  // Get user direct permissions\n  getUserDirectPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/user/${userId}/direct-permissions`);\n  }\n\n  // Assign role to user\n  assignRole(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/assign-role', { user_id: userId, role: roleName });\n  }\n\n  // Remove role from user\n  removeRole(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/remove-role', { user_id: userId, role: roleName });\n  }\n\n  // Assign permission directly to user\n  assignPermission(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/assign-user-permission', { user_id: userId, permission: permissionName });\n  }\n\n  // Remove permission from user\n  removePermission(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/remove-user-permission', { user_id: userId, permission: permissionName });\n  }\n\n  // Assign role to user using CRUD API\n  assignRoleCRUD(userId: number, roleId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/${userId}/roles`, { role_id: roleId });\n  }\n\n  // Remove role from user using CRUD API\n  removeRoleCRUD(userId: number, roleId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${userId}/roles/${roleId}`);\n  }\n\n  // Assign permission to user using CRUD API\n  assignPermissionCRUD(userId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/${userId}/permissions`, { permission_id: permissionId });\n  }\n\n  // Remove permission from user using CRUD API\n  removePermissionCRUD(userId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${userId}/permissions/${permissionId}`);\n  }\n}\n\nexport const userService = new UserService();\nexport default userService;\n"], "mappings": "AAEA,SAASA,eAAe,EAAkCC,GAAG,QAAQ,gBAAgB;AA0BrF,MAAMC,WAAW,SAASF,eAAe,CAA6C;EACpFG,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,wBAAwB,CAAC;EACjC;;EAEA;EACAC,iBAAiBA,CAAA,EAA4C;IAC3D,OAAOH,GAAG,CAACI,GAAG,CAAC,cAAc,CAAC;EAChC;;EAEA;EACAC,YAAYA,CAACC,MAAc,EAAoC;IAC7D,OAAON,GAAG,CAACI,GAAG,CAAC,eAAeE,MAAM,QAAQ,CAAC;EAC/C;;EAEA;EACAC,kBAAkBA,CAACD,MAAc,EAAoC;IACnE,OAAON,GAAG,CAACI,GAAG,CAAC,eAAeE,MAAM,cAAc,CAAC;EACrD;;EAEA;EACAE,wBAAwBA,CAACF,MAAc,EAAoC;IACzE,OAAON,GAAG,CAACI,GAAG,CAAC,eAAeE,MAAM,qBAAqB,CAAC;EAC5D;;EAEA;EACAG,UAAUA,CAACH,MAAc,EAAEI,QAAgB,EAA+C;IACxF,OAAOV,GAAG,CAACW,IAAI,CAAC,oBAAoB,EAAE;MAAEC,OAAO,EAAEN,MAAM;MAAEO,IAAI,EAAEH;IAAS,CAAC,CAAC;EAC5E;;EAEA;EACAI,UAAUA,CAACR,MAAc,EAAEI,QAAgB,EAA+C;IACxF,OAAOV,GAAG,CAACW,IAAI,CAAC,oBAAoB,EAAE;MAAEC,OAAO,EAAEN,MAAM;MAAEO,IAAI,EAAEH;IAAS,CAAC,CAAC;EAC5E;;EAEA;EACAK,gBAAgBA,CAACT,MAAc,EAAEU,cAAsB,EAA+C;IACpG,OAAOhB,GAAG,CAACW,IAAI,CAAC,+BAA+B,EAAE;MAAEC,OAAO,EAAEN,MAAM;MAAEW,UAAU,EAAED;IAAe,CAAC,CAAC;EACnG;;EAEA;EACAE,gBAAgBA,CAACZ,MAAc,EAAEU,cAAsB,EAA+C;IACpG,OAAOhB,GAAG,CAACW,IAAI,CAAC,+BAA+B,EAAE;MAAEC,OAAO,EAAEN,MAAM;MAAEW,UAAU,EAAED;IAAe,CAAC,CAAC;EACnG;;EAEA;EACAG,cAAcA,CAACb,MAAc,EAAEc,MAAc,EAA+C;IAC1F,OAAOpB,GAAG,CAACW,IAAI,CAAC,GAAG,IAAI,CAACU,OAAO,IAAIf,MAAM,QAAQ,EAAE;MAAEgB,OAAO,EAAEF;IAAO,CAAC,CAAC;EACzE;;EAEA;EACAG,cAAcA,CAACjB,MAAc,EAAEc,MAAc,EAA+C;IAC1F,OAAOpB,GAAG,CAACwB,MAAM,CAAC,GAAG,IAAI,CAACH,OAAO,IAAIf,MAAM,UAAUc,MAAM,EAAE,CAAC;EAChE;;EAEA;EACAK,oBAAoBA,CAACnB,MAAc,EAAEoB,YAAoB,EAA+C;IACtG,OAAO1B,GAAG,CAACW,IAAI,CAAC,GAAG,IAAI,CAACU,OAAO,IAAIf,MAAM,cAAc,EAAE;MAAEqB,aAAa,EAAED;IAAa,CAAC,CAAC;EAC3F;;EAEA;EACAE,oBAAoBA,CAACtB,MAAc,EAAEoB,YAAoB,EAA+C;IACtG,OAAO1B,GAAG,CAACwB,MAAM,CAAC,GAAG,IAAI,CAACH,OAAO,IAAIf,MAAM,gBAAgBoB,YAAY,EAAE,CAAC;EAC5E;AACF;AAEA,OAAO,MAAMG,WAAW,GAAG,IAAI5B,WAAW,CAAC,CAAC;AAC5C,eAAe4B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}