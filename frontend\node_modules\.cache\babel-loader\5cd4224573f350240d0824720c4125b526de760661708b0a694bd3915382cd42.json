{"ast": null, "code": "import React,{useState,useEffect}from'react';import{adminAPI}from'../../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AssignmentsTab=_ref=>{let{users,roles,onMessage,onError}=_ref;const[selectedUser,setSelectedUser]=useState(null);const[userRoleNames,setUserRoleNames]=useState([]);const[userPermissionNames,setUserPermissionNames]=useState([]);const[availableRoles,setAvailableRoles]=useState([]);const[availablePermissions,setAvailablePermissions]=useState([]);const[loading,setLoading]=useState(false);const[assignmentType,setAssignmentType]=useState('roles');// Load user details when selected\nuseEffect(()=>{if(selectedUser){loadUserDetails();}},[selectedUser]);const loadUserDetails=async()=>{if(!selectedUser)return;setLoading(true);try{// Load user roles (returns string[])\nconst rolesResponse=await adminAPI.getUserRoles(selectedUser.id);setUserRoleNames(rolesResponse.data);// Load user permissions (returns string[])\nconst permissionsResponse=await adminAPI.getUserPermissions(selectedUser.id);setUserPermissionNames(permissionsResponse.data);// Set available roles (roles not assigned to user)\nconst assignedRoleNames=rolesResponse.data;setAvailableRoles(roles.filter(role=>!assignedRoleNames.includes(role.name)&&role.is_active));// Load all permissions for available permissions list\n// Note: This would need to be implemented in the backend to get all permissions\n// For now, we'll use an empty array\nsetAvailablePermissions([]);}catch(err){var _err$response,_err$response$data;onError(\"Failed to load user details: \".concat(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.error)||err.message));}finally{setLoading(false);}};const handleAssignRole=async roleName=>{if(!selectedUser)return;try{await adminAPI.assignRole(selectedUser.id,roleName);onMessage('Role assigned successfully');loadUserDetails();// Reload to update the lists\n}catch(err){var _err$response2,_err$response2$data;onError(\"Failed to assign role: \".concat(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.error)||err.message));}};const handleRemoveRole=async roleName=>{if(!selectedUser)return;try{await adminAPI.removeRole(selectedUser.id,roleName);onMessage('Role removed successfully');loadUserDetails();// Reload to update the lists\n}catch(err){var _err$response3,_err$response3$data;onError(\"Failed to remove role: \".concat(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.error)||err.message));}};const handleAssignPermission=async permissionName=>{if(!selectedUser)return;try{// Note: This would need a different API endpoint for direct user permission assignment\n// For now, we'll show an error message\nonError('Direct permission assignment not yet implemented');}catch(err){var _err$response4,_err$response4$data;onError(\"Failed to assign permission: \".concat(((_err$response4=err.response)===null||_err$response4===void 0?void 0:(_err$response4$data=_err$response4.data)===null||_err$response4$data===void 0?void 0:_err$response4$data.error)||err.message));}};const handleRemovePermission=async permissionName=>{if(!selectedUser)return;try{// Note: This would need a different API endpoint for direct user permission removal\n// For now, we'll show an error message\nonError('Direct permission removal not yet implemented');}catch(err){var _err$response5,_err$response5$data;onError(\"Failed to remove permission: \".concat(((_err$response5=err.response)===null||_err$response5===void 0?void 0:(_err$response5$data=_err$response5.data)===null||_err$response5$data===void 0?void 0:_err$response5$data.error)||err.message));}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold mb-6\",children:\"User Role & Permission Assignments\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-4 rounded-lg border\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium mb-4\",children:\"Select User\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2 max-h-96 overflow-y-auto\",children:users.map(user=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setSelectedUser(user),className:\"w-full text-left p-3 rounded border \".concat((selectedUser===null||selectedUser===void 0?void 0:selectedUser.id)===user.id?'bg-blue-100 border-blue-500':'bg-gray-50 border-gray-200 hover:bg-gray-100'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium\",children:user.username}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:user.email})]},user.id))})]}),selectedUser&&/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-4 rounded-lg border\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium\",children:[\"Managing: \",selectedUser.username]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex bg-gray-100 rounded-lg p-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setAssignmentType('roles'),className:\"px-4 py-2 rounded-md text-sm font-medium \".concat(assignmentType==='roles'?'bg-white text-blue-600 shadow-sm':'text-gray-600 hover:text-gray-900'),children:\"Roles\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setAssignmentType('permissions'),className:\"px-4 py-2 rounded-md text-sm font-medium \".concat(assignmentType==='permissions'?'bg-white text-blue-600 shadow-sm':'text-gray-600 hover:text-gray-900'),children:\"Permissions\"})]})]}),loading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8\",children:\"Loading...\"}):/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium mb-3\",children:[\"Assigned \",assignmentType==='roles'?'Roles':'Permissions']}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2 max-h-64 overflow-y-auto\",children:assignmentType==='roles'?userRoleNames.length>0?userRoleNames.map(roleName=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-green-800\",children:roleName})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleRemoveRole(roleName),className:\"text-red-600 hover:text-red-800 text-sm\",children:\"Remove\"})]},roleName)):/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-500 text-center py-4\",children:\"No roles assigned\"}):userPermissionNames.length>0?userPermissionNames.map(permissionName=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-green-800\",children:permissionName})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleRemovePermission(permissionName),className:\"text-red-600 hover:text-red-800 text-sm\",children:\"Remove\"})]},permissionName)):/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-500 text-center py-4\",children:\"No permissions assigned\"})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium mb-3\",children:[\"Available \",assignmentType==='roles'?'Roles':'Permissions']}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2 max-h-64 overflow-y-auto\",children:assignmentType==='roles'?availableRoles.length>0?availableRoles.map(role=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium\",children:role.name}),role.description&&/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:role.description})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleAssignRole(role.name),className:\"text-blue-600 hover:text-blue-800 text-sm\",children:\"Assign\"})]},role.id)):/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-500 text-center py-4\",children:\"No available roles\"}):/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-500 text-center py-4\",children:\"Permission assignment feature coming soon\"})})]})]})]})})]}),!selectedUser&&/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-12 text-gray-500\",children:/*#__PURE__*/_jsx(\"p\",{children:\"Select a user to manage their roles and permissions\"})})]});};export default AssignmentsTab;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "adminAPI", "jsx", "_jsx", "jsxs", "_jsxs", "AssignmentsTab", "_ref", "users", "roles", "onMessage", "onError", "selected<PERSON>ser", "setSelectedUser", "userRoleNames", "setUserRoleNames", "userPermissionNames", "setUserPermissionNames", "availableRoles", "setAvailableRoles", "availablePermissions", "setAvailablePermissions", "loading", "setLoading", "assignmentType", "setAssignmentType", "loadUserDetails", "rolesResponse", "getUserRoles", "id", "data", "permissionsResponse", "getUserPermissions", "assignedRoleNames", "filter", "role", "includes", "name", "is_active", "err", "_err$response", "_err$response$data", "concat", "response", "error", "message", "handleAssignRole", "<PERSON><PERSON><PERSON>", "assignRole", "_err$response2", "_err$response2$data", "handleRemoveRole", "removeRole", "_err$response3", "_err$response3$data", "handleAssignPermission", "permissionName", "_err$response4", "_err$response4$data", "handleRemovePermission", "_err$response5", "_err$response5$data", "children", "className", "map", "user", "onClick", "username", "email", "length", "description"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/AssignmentsTab.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport { adminAPI } from '../../services/api';\n\ninterface AssignmentsTabProps {\n  users: User[];\n  roles: Role[];\n  onMessage: (message: string) => void;\n  onError: (error: string) => void;\n}\n\nconst AssignmentsTab: React.FC<AssignmentsTabProps> = ({ users, roles, onMessage, onError }) => {\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [userRoleNames, setUserRoleNames] = useState<string[]>([]);\n  const [userPermissionNames, setUserPermissionNames] = useState<string[]>([]);\n  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);\n  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [assignmentType, setAssignmentType] = useState<'roles' | 'permissions'>('roles');\n\n  // Load user details when selected\n  useEffect(() => {\n    if (selectedUser) {\n      loadUserDetails();\n    }\n  }, [selectedUser]);\n\n  const loadUserDetails = async () => {\n    if (!selectedUser) return;\n\n    setLoading(true);\n    try {\n      // Load user roles (returns string[])\n      const rolesResponse = await adminAPI.getUserRoles(selectedUser.id);\n      setUserRoleNames(rolesResponse.data);\n\n      // Load user permissions (returns string[])\n      const permissionsResponse = await adminAPI.getUserPermissions(selectedUser.id);\n      setUserPermissionNames(permissionsResponse.data);\n\n      // Set available roles (roles not assigned to user)\n      const assignedRoleNames = rolesResponse.data;\n      setAvailableRoles(roles.filter(role => !assignedRoleNames.includes(role.name) && role.is_active));\n\n      // Load all permissions for available permissions list\n      // Note: This would need to be implemented in the backend to get all permissions\n      // For now, we'll use an empty array\n      setAvailablePermissions([]);\n\n    } catch (err: any) {\n      onError(`Failed to load user details: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssignRole = async (roleName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.assignRole(selectedUser.id, roleName);\n      onMessage('Role assigned successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to assign role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleRemoveRole = async (roleName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.removeRole(selectedUser.id, roleName);\n      onMessage('Role removed successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to remove role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleAssignPermission = async (permissionName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      // Note: This would need a different API endpoint for direct user permission assignment\n      // For now, we'll show an error message\n      onError('Direct permission assignment not yet implemented');\n    } catch (err: any) {\n      onError(`Failed to assign permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleRemovePermission = async (permissionName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      // Note: This would need a different API endpoint for direct user permission removal\n      // For now, we'll show an error message\n      onError('Direct permission removal not yet implemented');\n    } catch (err: any) {\n      onError(`Failed to remove permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  return (\n    <div>\n      <h2 className=\"text-xl font-semibold mb-6\">User Role & Permission Assignments</h2>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* User Selection */}\n        <div className=\"bg-white p-4 rounded-lg border\">\n          <h3 className=\"text-lg font-medium mb-4\">Select User</h3>\n          <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n            {users.map((user) => (\n              <button\n                key={user.id}\n                onClick={() => setSelectedUser(user)}\n                className={`w-full text-left p-3 rounded border ${selectedUser?.id === user.id\n                  ? 'bg-blue-100 border-blue-500'\n                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'\n                  }`}\n              >\n                <div className=\"font-medium\">{user.username}</div>\n                <div className=\"text-sm text-gray-600\">{user.email}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Assignment Type Toggle */}\n        {selectedUser && (\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white p-4 rounded-lg border\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium\">\n                  Managing: {selectedUser.username}\n                </h3>\n                <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                  <button\n                    onClick={() => setAssignmentType('roles')}\n                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'roles'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                      }`}\n                  >\n                    Roles\n                  </button>\n                  <button\n                    onClick={() => setAssignmentType('permissions')}\n                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'permissions'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                      }`}\n                  >\n                    Permissions\n                  </button>\n                </div>\n              </div>\n\n              {loading ? (\n                <div className=\"text-center py-8\">Loading...</div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {/* Assigned Items */}\n                  <div>\n                    <h4 className=\"font-medium mb-3\">\n                      Assigned {assignmentType === 'roles' ? 'Roles' : 'Permissions'}\n                    </h4>\n                    <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                      {assignmentType === 'roles' ? (\n                        userRoleNames.length > 0 ? (\n                          userRoleNames.map((roleName) => (\n                            <div key={roleName} className=\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\">\n                              <div>\n                                <div className=\"font-medium text-green-800\">{roleName}</div>\n                              </div>\n                              <button\n                                onClick={() => handleRemoveRole(roleName)}\n                                className=\"text-red-600 hover:text-red-800 text-sm\"\n                              >\n                                Remove\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No roles assigned</div>\n                        )\n                      ) : (\n                        userPermissionNames.length > 0 ? (\n                          userPermissionNames.map((permissionName) => (\n                            <div key={permissionName} className=\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\">\n                              <div>\n                                <div className=\"font-medium text-green-800\">{permissionName}</div>\n                              </div>\n                              <button\n                                onClick={() => handleRemovePermission(permissionName)}\n                                className=\"text-red-600 hover:text-red-800 text-sm\"\n                              >\n                                Remove\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No permissions assigned</div>\n                        )\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Available Items */}\n                  <div>\n                    <h4 className=\"font-medium mb-3\">\n                      Available {assignmentType === 'roles' ? 'Roles' : 'Permissions'}\n                    </h4>\n                    <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                      {assignmentType === 'roles' ? (\n                        availableRoles.length > 0 ? (\n                          availableRoles.map((role) => (\n                            <div key={role.id} className=\"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded\">\n                              <div>\n                                <div className=\"font-medium\">{role.name}</div>\n                                {role.description && (\n                                  <div className=\"text-sm text-gray-600\">{role.description}</div>\n                                )}\n                              </div>\n                              <button\n                                onClick={() => handleAssignRole(role.name)}\n                                className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                              >\n                                Assign\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No available roles</div>\n                        )\n                      ) : (\n                        <div className=\"text-gray-500 text-center py-4\">\n                          Permission assignment feature coming soon\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {!selectedUser && (\n        <div className=\"text-center py-12 text-gray-500\">\n          <p>Select a user to manage their roles and permissions</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AssignmentsTab;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAElD,OAASC,QAAQ,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAS9C,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAA0C,IAAzC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,SAAS,CAAEC,OAAQ,CAAC,CAAAJ,IAAA,CACzF,KAAM,CAACK,YAAY,CAAEC,eAAe,CAAC,CAAGd,QAAQ,CAAc,IAAI,CAAC,CACnE,KAAM,CAACe,aAAa,CAAEC,gBAAgB,CAAC,CAAGhB,QAAQ,CAAW,EAAE,CAAC,CAChE,KAAM,CAACiB,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGlB,QAAQ,CAAW,EAAE,CAAC,CAC5E,KAAM,CAACmB,cAAc,CAAEC,iBAAiB,CAAC,CAAGpB,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACqB,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtB,QAAQ,CAAe,EAAE,CAAC,CAClF,KAAM,CAACuB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyB,cAAc,CAAEC,iBAAiB,CAAC,CAAG1B,QAAQ,CAA0B,OAAO,CAAC,CAEtF;AACAC,SAAS,CAAC,IAAM,CACd,GAAIY,YAAY,CAAE,CAChBc,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACd,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAc,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAACd,YAAY,CAAE,OAEnBW,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAAI,aAAa,CAAG,KAAM,CAAA1B,QAAQ,CAAC2B,YAAY,CAAChB,YAAY,CAACiB,EAAE,CAAC,CAClEd,gBAAgB,CAACY,aAAa,CAACG,IAAI,CAAC,CAEpC;AACA,KAAM,CAAAC,mBAAmB,CAAG,KAAM,CAAA9B,QAAQ,CAAC+B,kBAAkB,CAACpB,YAAY,CAACiB,EAAE,CAAC,CAC9EZ,sBAAsB,CAACc,mBAAmB,CAACD,IAAI,CAAC,CAEhD;AACA,KAAM,CAAAG,iBAAiB,CAAGN,aAAa,CAACG,IAAI,CAC5CX,iBAAiB,CAACV,KAAK,CAACyB,MAAM,CAACC,IAAI,EAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,EAAIF,IAAI,CAACG,SAAS,CAAC,CAAC,CAEjG;AACA;AACA;AACAjB,uBAAuB,CAAC,EAAE,CAAC,CAE7B,CAAE,MAAOkB,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjB9B,OAAO,iCAAA+B,MAAA,CAAiC,EAAAF,aAAA,CAAAD,GAAG,CAACI,QAAQ,UAAAH,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcV,IAAI,UAAAW,kBAAA,iBAAlBA,kBAAA,CAAoBG,KAAK,GAAIL,GAAG,CAACM,OAAO,CAAE,CAAC,CACrF,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAuB,gBAAgB,CAAG,KAAO,CAAAC,QAAgB,EAAK,CACnD,GAAI,CAACnC,YAAY,CAAE,OAEnB,GAAI,CACF,KAAM,CAAAX,QAAQ,CAAC+C,UAAU,CAACpC,YAAY,CAACiB,EAAE,CAAEkB,QAAQ,CAAC,CACpDrC,SAAS,CAAC,4BAA4B,CAAC,CACvCgB,eAAe,CAAC,CAAC,CAAE;AACrB,CAAE,MAAOa,GAAQ,CAAE,KAAAU,cAAA,CAAAC,mBAAA,CACjBvC,OAAO,2BAAA+B,MAAA,CAA2B,EAAAO,cAAA,CAAAV,GAAG,CAACI,QAAQ,UAAAM,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcnB,IAAI,UAAAoB,mBAAA,iBAAlBA,mBAAA,CAAoBN,KAAK,GAAIL,GAAG,CAACM,OAAO,CAAE,CAAC,CAC/E,CACF,CAAC,CAED,KAAM,CAAAM,gBAAgB,CAAG,KAAO,CAAAJ,QAAgB,EAAK,CACnD,GAAI,CAACnC,YAAY,CAAE,OAEnB,GAAI,CACF,KAAM,CAAAX,QAAQ,CAACmD,UAAU,CAACxC,YAAY,CAACiB,EAAE,CAAEkB,QAAQ,CAAC,CACpDrC,SAAS,CAAC,2BAA2B,CAAC,CACtCgB,eAAe,CAAC,CAAC,CAAE;AACrB,CAAE,MAAOa,GAAQ,CAAE,KAAAc,cAAA,CAAAC,mBAAA,CACjB3C,OAAO,2BAAA+B,MAAA,CAA2B,EAAAW,cAAA,CAAAd,GAAG,CAACI,QAAQ,UAAAU,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcvB,IAAI,UAAAwB,mBAAA,iBAAlBA,mBAAA,CAAoBV,KAAK,GAAIL,GAAG,CAACM,OAAO,CAAE,CAAC,CAC/E,CACF,CAAC,CAED,KAAM,CAAAU,sBAAsB,CAAG,KAAO,CAAAC,cAAsB,EAAK,CAC/D,GAAI,CAAC5C,YAAY,CAAE,OAEnB,GAAI,CACF;AACA;AACAD,OAAO,CAAC,kDAAkD,CAAC,CAC7D,CAAE,MAAO4B,GAAQ,CAAE,KAAAkB,cAAA,CAAAC,mBAAA,CACjB/C,OAAO,iCAAA+B,MAAA,CAAiC,EAAAe,cAAA,CAAAlB,GAAG,CAACI,QAAQ,UAAAc,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc3B,IAAI,UAAA4B,mBAAA,iBAAlBA,mBAAA,CAAoBd,KAAK,GAAIL,GAAG,CAACM,OAAO,CAAE,CAAC,CACrF,CACF,CAAC,CAED,KAAM,CAAAc,sBAAsB,CAAG,KAAO,CAAAH,cAAsB,EAAK,CAC/D,GAAI,CAAC5C,YAAY,CAAE,OAEnB,GAAI,CACF;AACA;AACAD,OAAO,CAAC,+CAA+C,CAAC,CAC1D,CAAE,MAAO4B,GAAQ,CAAE,KAAAqB,cAAA,CAAAC,mBAAA,CACjBlD,OAAO,iCAAA+B,MAAA,CAAiC,EAAAkB,cAAA,CAAArB,GAAG,CAACI,QAAQ,UAAAiB,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc9B,IAAI,UAAA+B,mBAAA,iBAAlBA,mBAAA,CAAoBjB,KAAK,GAAIL,GAAG,CAACM,OAAO,CAAE,CAAC,CACrF,CACF,CAAC,CAED,mBACExC,KAAA,QAAAyD,QAAA,eACE3D,IAAA,OAAI4D,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,oCAAkC,CAAI,CAAC,cAElFzD,KAAA,QAAK0D,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDzD,KAAA,QAAK0D,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C3D,IAAA,OAAI4D,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,aAAW,CAAI,CAAC,cACzD3D,IAAA,QAAK4D,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDtD,KAAK,CAACwD,GAAG,CAAEC,IAAI,eACd5D,KAAA,WAEE6D,OAAO,CAAEA,CAAA,GAAMrD,eAAe,CAACoD,IAAI,CAAE,CACrCF,SAAS,wCAAArB,MAAA,CAAyC,CAAA9B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiB,EAAE,IAAKoC,IAAI,CAACpC,EAAE,CAC1E,6BAA6B,CAC7B,8CAA8C,CAC7C,CAAAiC,QAAA,eAEL3D,IAAA,QAAK4D,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEG,IAAI,CAACE,QAAQ,CAAM,CAAC,cAClDhE,IAAA,QAAK4D,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAEG,IAAI,CAACG,KAAK,CAAM,CAAC,GARpDH,IAAI,CAACpC,EASJ,CACT,CAAC,CACC,CAAC,EACH,CAAC,CAGLjB,YAAY,eACXT,IAAA,QAAK4D,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BzD,KAAA,QAAK0D,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7CzD,KAAA,QAAK0D,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDzD,KAAA,OAAI0D,SAAS,CAAC,qBAAqB,CAAAD,QAAA,EAAC,YACxB,CAAClD,YAAY,CAACuD,QAAQ,EAC9B,CAAC,cACL9D,KAAA,QAAK0D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C3D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMzC,iBAAiB,CAAC,OAAO,CAAE,CAC1CsC,SAAS,6CAAArB,MAAA,CAA8ClB,cAAc,GAAK,OAAO,CAC7E,kCAAkC,CAClC,mCAAmC,CAClC,CAAAsC,QAAA,CACN,OAED,CAAQ,CAAC,cACT3D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMzC,iBAAiB,CAAC,aAAa,CAAE,CAChDsC,SAAS,6CAAArB,MAAA,CAA8ClB,cAAc,GAAK,aAAa,CACnF,kCAAkC,CAClC,mCAAmC,CAClC,CAAAsC,QAAA,CACN,aAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAELxC,OAAO,cACNnB,IAAA,QAAK4D,SAAS,CAAC,kBAAkB,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,cAElDzD,KAAA,QAAK0D,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDzD,KAAA,QAAAyD,QAAA,eACEzD,KAAA,OAAI0D,SAAS,CAAC,kBAAkB,CAAAD,QAAA,EAAC,WACtB,CAACtC,cAAc,GAAK,OAAO,CAAG,OAAO,CAAG,aAAa,EAC5D,CAAC,cACLrB,IAAA,QAAK4D,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDtC,cAAc,GAAK,OAAO,CACzBV,aAAa,CAACuD,MAAM,CAAG,CAAC,CACtBvD,aAAa,CAACkD,GAAG,CAAEjB,QAAQ,eACzB1C,KAAA,QAAoB0D,SAAS,CAAC,mFAAmF,CAAAD,QAAA,eAC/G3D,IAAA,QAAA2D,QAAA,cACE3D,IAAA,QAAK4D,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAEf,QAAQ,CAAM,CAAC,CACzD,CAAC,cACN5C,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMf,gBAAgB,CAACJ,QAAQ,CAAE,CAC1CgB,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CACpD,QAED,CAAQ,CAAC,GATDf,QAUL,CACN,CAAC,cAEF5C,IAAA,QAAK4D,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CACvE,CAED9C,mBAAmB,CAACqD,MAAM,CAAG,CAAC,CAC5BrD,mBAAmB,CAACgD,GAAG,CAAER,cAAc,eACrCnD,KAAA,QAA0B0D,SAAS,CAAC,mFAAmF,CAAAD,QAAA,eACrH3D,IAAA,QAAA2D,QAAA,cACE3D,IAAA,QAAK4D,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAEN,cAAc,CAAM,CAAC,CAC/D,CAAC,cACNrD,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMP,sBAAsB,CAACH,cAAc,CAAE,CACtDO,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CACpD,QAED,CAAQ,CAAC,GATDN,cAUL,CACN,CAAC,cAEFrD,IAAA,QAAK4D,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,yBAAuB,CAAK,CAE/E,CACE,CAAC,EACH,CAAC,cAGNzD,KAAA,QAAAyD,QAAA,eACEzD,KAAA,OAAI0D,SAAS,CAAC,kBAAkB,CAAAD,QAAA,EAAC,YACrB,CAACtC,cAAc,GAAK,OAAO,CAAG,OAAO,CAAG,aAAa,EAC7D,CAAC,cACLrB,IAAA,QAAK4D,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDtC,cAAc,GAAK,OAAO,CACzBN,cAAc,CAACmD,MAAM,CAAG,CAAC,CACvBnD,cAAc,CAAC8C,GAAG,CAAE7B,IAAI,eACtB9B,KAAA,QAAmB0D,SAAS,CAAC,iFAAiF,CAAAD,QAAA,eAC5GzD,KAAA,QAAAyD,QAAA,eACE3D,IAAA,QAAK4D,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAE3B,IAAI,CAACE,IAAI,CAAM,CAAC,CAC7CF,IAAI,CAACmC,WAAW,eACfnE,IAAA,QAAK4D,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAE3B,IAAI,CAACmC,WAAW,CAAM,CAC/D,EACE,CAAC,cACNnE,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMpB,gBAAgB,CAACX,IAAI,CAACE,IAAI,CAAE,CAC3C0B,SAAS,CAAC,2CAA2C,CAAAD,QAAA,CACtD,QAED,CAAQ,CAAC,GAZD3B,IAAI,CAACN,EAaV,CACN,CAAC,cAEF1B,IAAA,QAAK4D,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CACxE,cAED3D,IAAA,QAAK4D,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,2CAEhD,CAAK,CACN,CACE,CAAC,EACH,CAAC,EACH,CACN,EACE,CAAC,CACH,CACN,EACE,CAAC,CAEL,CAAClD,YAAY,eACZT,IAAA,QAAK4D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9C3D,IAAA,MAAA2D,QAAA,CAAG,qDAAmD,CAAG,CAAC,CACvD,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}