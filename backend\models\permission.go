package models

import "time"

type Permission struct {
	BaseModel
	Name        string `json:"name" gorm:"unique;not null" binding:"required,min=2,max=100"`
	Description string `json:"description" gorm:"type:text"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// Associations
	Roles []Role `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
	Users []User `json:"users,omitempty" gorm:"many2many:user_permissions;"`
}

type RolePermission struct {
	ID           uint `json:"id" gorm:"primaryKey"`
	RoleID       uint `json:"role_id"`
	PermissionID uint `json:"permission_id"`
}

type UserPermission struct {
	ID           uint `json:"id" gorm:"primaryKey"`
	UserID       uint `json:"user_id"`
	PermissionID uint `json:"permission_id"`
}

// Permission CRUD DTOs
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100"`
	Description string `json:"description"`
	IsActive    *bool  `json:"is_active"`
}

type UpdatePermissionRequest struct {
	Name        string `json:"name" binding:"omitempty,min=2,max=100"`
	Description string `json:"description"`
	IsActive    *bool  `json:"is_active"`
}

type PermissionResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
