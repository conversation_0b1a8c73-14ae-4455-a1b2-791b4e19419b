package models

type Permission struct {
	ID   uint   `json:"id" gorm:"primaryKey"`
	Name string `json:"name" gorm:"unique;not null"`
}

type RolePermission struct {
	ID           uint `json:"id" gorm:"primaryKey"`
	RoleID       uint `json:"role_id"`
	PermissionID uint `json:"permission_id"`
}

type UserPermission struct {
	ID           uint `json:"id" gorm:"primaryKey"`
	UserID       uint `json:"user_id"`
	PermissionID uint `json:"permission_id"`
} 