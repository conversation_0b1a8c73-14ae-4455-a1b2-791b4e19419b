{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\RoleManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { roleService, permissionService } from '../../services';\nimport RolePermissionModal from './RolePermissionModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoleManagement = () => {\n  _s();\n  // State management\n  const [roles, setRoles] = useState([]);\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Pagination\n  const [pagination, setPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0,\n    has_next: false,\n    has_prev: false\n  });\n\n  // Search and filters\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  // Modal states\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [selectedRole, setSelectedRole] = useState(null);\n\n  // Form data\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    is_active: true\n  });\n\n  // Load data on component mount\n  useEffect(() => {\n    loadRoles();\n    loadPermissions();\n  }, [pagination.page, pagination.page_size, searchTerm, statusFilter]);\n\n  // Load roles with pagination and search\n  const loadRoles = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const filters = {\n        page: pagination.page,\n        page_size: pagination.page_size\n      };\n      if (searchTerm.trim()) {\n        filters.search_term = searchTerm.trim();\n        filters.search_columns = ['name', 'description'];\n      }\n      if (statusFilter !== 'all') {\n        filters.is_active = statusFilter === 'active';\n      }\n      const response = await roleService.list(filters);\n      setRoles(response.data.data);\n      const paginationData = response.data.pagination;\n      setPagination({\n        ...paginationData,\n        has_next: paginationData.page < paginationData.total_pages,\n        has_prev: paginationData.page > 1\n      });\n    } catch (err) {\n      setError(err.message || 'Failed to load roles');\n      console.error('Error loading roles:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load all permissions for assignment\n  const loadPermissions = async () => {\n    try {\n      const response = await permissionService.list({\n        page: 1,\n        page_size: 100\n      });\n      setPermissions(response.data.data);\n    } catch (err) {\n      console.error('Error loading permissions:', err);\n    }\n  };\n\n  // Handle create role\n  const handleCreate = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      await roleService.create(formData);\n      setSuccess('Role created successfully');\n      setShowCreateModal(false);\n      resetForm();\n      loadRoles();\n    } catch (err) {\n      setError(err.message || 'Failed to create role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle update role\n  const handleUpdate = async e => {\n    e.preventDefault();\n    if (!selectedRole) return;\n    try {\n      setLoading(true);\n      setError(null);\n      await roleService.update(selectedRole.id, formData);\n      setSuccess('Role updated successfully');\n      setShowEditModal(false);\n      resetForm();\n      loadRoles();\n    } catch (err) {\n      setError(err.message || 'Failed to update role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle delete role\n  const handleDelete = async role => {\n    if (!window.confirm(`Are you sure you want to delete role \"${role.name}\"?`)) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      await roleService.delete(role.id);\n      setSuccess('Role deleted successfully');\n      loadRoles();\n    } catch (err) {\n      setError(err.message || 'Failed to delete role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle search\n  const handleSearch = e => {\n    e.preventDefault();\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadRoles();\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      is_active: true\n    });\n    setSelectedRole(null);\n  };\n\n  // Open edit modal\n  const openEditModal = role => {\n    setSelectedRole(role);\n    setFormData({\n      name: role.name,\n      description: role.description || '',\n      is_active: role.is_active\n    });\n    setShowEditModal(true);\n  };\n\n  // Open permission modal\n  const openPermissionModal = role => {\n    setSelectedRole(role);\n    setShowPermissionModal(true);\n  };\n\n  // Clear messages\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Role Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowCreateModal(true),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-plus me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), \"Create Role\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-danger alert-dismissible fade show\",\n          role: \"alert\",\n          children: [error, /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn-close\",\n            onClick: clearMessages\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-success alert-dismissible fade show\",\n          role: \"alert\",\n          children: [success, /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn-close\",\n            onClick: clearMessages\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSearch,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row g-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"search\",\n                    className: \"form-label\",\n                    children: \"Search Roles\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    id: \"search\",\n                    placeholder: \"Search by name or description...\",\n                    value: searchTerm,\n                    onChange: e => setSearchTerm(e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"status\",\n                    className: \"form-label\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"form-select\",\n                    id: \"status\",\n                    value: statusFilter,\n                    onChange: e => setStatusFilter(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"active\",\n                      children: \"Active\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"inactive\",\n                      children: \"Inactive\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3 d-flex align-items-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"submit\",\n                    className: \"btn btn-outline-primary me-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-search me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 23\n                    }, this), \"Search\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-outline-secondary\",\n                    onClick: () => {\n                      setSearchTerm('');\n                      setStatusFilter('all');\n                      setPagination(prev => ({\n                        ...prev,\n                        page: 1\n                      }));\n                    },\n                    children: \"Clear\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner-border\",\n                role: \"status\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"visually-hidden\",\n                  children: \"Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-responsive\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-hover\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    className: \"table-light\",\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Description\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 293,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Created\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: roles.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: /*#__PURE__*/_jsxDEV(\"td\", {\n                        colSpan: 6,\n                        className: \"text-center py-4\",\n                        children: \"No roles found\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 301,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 27\n                    }, this) : roles.map(role => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: role.id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: role.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 310,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: role.description || '-'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 312,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `badge ${role.is_active ? 'bg-success' : 'bg-secondary'}`,\n                          children: role.is_active ? 'Active' : 'Inactive'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 314,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 313,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: role.created_at ? new Date(role.created_at).toLocaleDateString() : '-'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"btn-group btn-group-sm\",\n                          role: \"group\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"btn btn-outline-primary\",\n                            onClick: () => openEditModal(role),\n                            title: \"Edit Role\",\n                            children: /*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-edit\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 328,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 323,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"btn btn-outline-info\",\n                            onClick: () => openPermissionModal(role),\n                            title: \"Manage Permissions\",\n                            children: /*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-key\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 335,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 330,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"btn btn-outline-danger\",\n                            onClick: () => handleDelete(role),\n                            title: \"Delete Role\",\n                            children: /*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-trash\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 342,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 337,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 322,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 31\n                      }, this)]\n                    }, role.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), pagination.total_pages > 1 && /*#__PURE__*/_jsxDEV(\"nav\", {\n                \"aria-label\": \"Roles pagination\",\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"pagination justify-content-center mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: `page-item ${!pagination.has_prev ? 'disabled' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"page-link\",\n                      onClick: () => setPagination(prev => ({\n                        ...prev,\n                        page: prev.page - 1\n                      })),\n                      disabled: !pagination.has_prev,\n                      children: \"Previous\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 25\n                  }, this), Array.from({\n                    length: pagination.total_pages\n                  }, (_, i) => i + 1).map(page => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: `page-item ${pagination.page === page ? 'active' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"page-link\",\n                      onClick: () => setPagination(prev => ({\n                        ...prev,\n                        page\n                      })),\n                      children: page\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 29\n                    }, this)\n                  }, page, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 27\n                  }, this)), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: `page-item ${!pagination.has_next ? 'disabled' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"page-link\",\n                      onClick: () => setPagination(prev => ({\n                        ...prev,\n                        page: prev.page + 1\n                      })),\n                      disabled: !pagination.has_next,\n                      children: \"Next\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), showCreateModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal fade show\",\n      style: {\n        display: 'block'\n      },\n      tabIndex: -1,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-dialog\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"modal-title\",\n              children: \"Create New Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-close\",\n              onClick: () => {\n                setShowCreateModal(false);\n                resetForm();\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreate,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"createName\",\n                  className: \"form-label\",\n                  children: \"Role Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-control\",\n                  id: \"createName\",\n                  value: formData.name,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    name: e.target.value\n                  })),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"createDescription\",\n                  className: \"form-label\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-control\",\n                  id: \"createDescription\",\n                  rows: 3,\n                  value: formData.description,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    description: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  className: \"form-check-input\",\n                  id: \"createActive\",\n                  checked: formData.is_active,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    is_active: e.target.checked\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"createActive\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-secondary\",\n                onClick: () => {\n                  setShowCreateModal(false);\n                  resetForm();\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn btn-primary\",\n                disabled: loading,\n                children: loading ? 'Creating...' : 'Create Role'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this), showEditModal && selectedRole && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal fade show\",\n      style: {\n        display: 'block'\n      },\n      tabIndex: -1,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-dialog\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"modal-title\",\n              children: [\"Edit Role: \", selectedRole.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-close\",\n              onClick: () => {\n                setShowEditModal(false);\n                resetForm();\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleUpdate,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"editName\",\n                  className: \"form-label\",\n                  children: \"Role Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-control\",\n                  id: \"editName\",\n                  value: formData.name,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    name: e.target.value\n                  })),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"editDescription\",\n                  className: \"form-label\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-control\",\n                  id: \"editDescription\",\n                  rows: 3,\n                  value: formData.description,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    description: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  className: \"form-check-input\",\n                  id: \"editActive\",\n                  checked: formData.is_active,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    is_active: e.target.checked\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"editActive\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-secondary\",\n                onClick: () => {\n                  setShowEditModal(false);\n                  resetForm();\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn btn-primary\",\n                disabled: loading,\n                children: loading ? 'Updating...' : 'Update Role'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 9\n    }, this), selectedRole && /*#__PURE__*/_jsxDEV(RolePermissionModal, {\n      role: selectedRole,\n      isOpen: showPermissionModal,\n      onClose: () => {\n        setShowPermissionModal(false);\n        setSelectedRole(null);\n      },\n      onSuccess: message => {\n        setSuccess(message);\n        setTimeout(() => setSuccess(null), 5000);\n      },\n      onError: message => {\n        setError(message);\n        setTimeout(() => setError(null), 5000);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleManagement, \"rvj7gWBokM63Ta+/RooAS2gihi4=\");\n_c = RoleManagement;\nexport default RoleManagement;\nvar _c;\n$RefreshReg$(_c, \"RoleManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "roleService", "permissionService", "RolePermissionModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoleManagement", "_s", "roles", "setRoles", "permissions", "setPermissions", "loading", "setLoading", "error", "setError", "success", "setSuccess", "pagination", "setPagination", "page", "page_size", "total_items", "total_pages", "has_next", "has_prev", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showPermissionModal", "setShowPermissionModal", "selectedR<PERSON>", "setSelectedRole", "formData", "setFormData", "name", "description", "is_active", "loadRoles", "loadPermissions", "filters", "trim", "search_term", "search_columns", "response", "list", "data", "paginationData", "err", "message", "console", "handleCreate", "e", "preventDefault", "create", "resetForm", "handleUpdate", "update", "id", "handleDelete", "role", "window", "confirm", "delete", "handleSearch", "prev", "openEditModal", "openPermissionModal", "clearMessages", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "onSubmit", "htmlFor", "placeholder", "value", "onChange", "target", "length", "colSpan", "map", "created_at", "Date", "toLocaleDateString", "title", "disabled", "Array", "from", "_", "i", "style", "display", "tabIndex", "required", "rows", "checked", "isOpen", "onClose", "onSuccess", "setTimeout", "onError", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/RoleManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { roleService, permissionService } from '../../services';\nimport { Role, Permission, PaginationInfo } from '../../types';\nimport RolePermissionModal from './RolePermissionModal';\n\ninterface RoleManagementProps { }\n\nconst RoleManagement: React.FC<RoleManagementProps> = () => {\n  // State management\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Pagination\n  const [pagination, setPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0,\n    has_next: false,\n    has_prev: false\n  });\n\n  // Search and filters\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');\n\n  // Modal states\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\n\n  // Form data\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    is_active: true\n  });\n\n  // Load data on component mount\n  useEffect(() => {\n    loadRoles();\n    loadPermissions();\n  }, [pagination.page, pagination.page_size, searchTerm, statusFilter]);\n\n  // Load roles with pagination and search\n  const loadRoles = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const filters: any = {\n        page: pagination.page,\n        page_size: pagination.page_size\n      };\n\n      if (searchTerm.trim()) {\n        filters.search_term = searchTerm.trim();\n        filters.search_columns = ['name', 'description'];\n      }\n\n      if (statusFilter !== 'all') {\n        filters.is_active = statusFilter === 'active';\n      }\n\n      const response = await roleService.list(filters);\n      setRoles(response.data.data);\n      const paginationData = response.data.pagination;\n      setPagination({\n        ...paginationData,\n        has_next: paginationData.page < paginationData.total_pages,\n        has_prev: paginationData.page > 1\n      });\n    } catch (err: any) {\n      setError(err.message || 'Failed to load roles');\n      console.error('Error loading roles:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load all permissions for assignment\n  const loadPermissions = async () => {\n    try {\n      const response = await permissionService.list({ page: 1, page_size: 100 });\n      setPermissions(response.data.data);\n    } catch (err: any) {\n      console.error('Error loading permissions:', err);\n    }\n  };\n\n  // Handle create role\n  const handleCreate = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      await roleService.create(formData);\n      setSuccess('Role created successfully');\n      setShowCreateModal(false);\n      resetForm();\n      loadRoles();\n    } catch (err: any) {\n      setError(err.message || 'Failed to create role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle update role\n  const handleUpdate = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!selectedRole) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      await roleService.update(selectedRole.id, formData);\n      setSuccess('Role updated successfully');\n      setShowEditModal(false);\n      resetForm();\n      loadRoles();\n    } catch (err: any) {\n      setError(err.message || 'Failed to update role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle delete role\n  const handleDelete = async (role: Role) => {\n    if (!window.confirm(`Are you sure you want to delete role \"${role.name}\"?`)) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      await roleService.delete(role.id);\n      setSuccess('Role deleted successfully');\n      loadRoles();\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle search\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setPagination(prev => ({ ...prev, page: 1 }));\n    loadRoles();\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      is_active: true\n    });\n    setSelectedRole(null);\n  };\n\n  // Open edit modal\n  const openEditModal = (role: Role) => {\n    setSelectedRole(role);\n    setFormData({\n      name: role.name,\n      description: role.description || '',\n      is_active: role.is_active\n    });\n    setShowEditModal(true);\n  };\n\n  // Open permission modal\n  const openPermissionModal = (role: Role) => {\n    setSelectedRole(role);\n    setShowPermissionModal(true);\n  };\n\n  // Clear messages\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  return (\n    <div className=\"container mt-4\">\n      <div className=\"row\">\n        <div className=\"col-12\">\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\n            <h2>Role Management</h2>\n            <button\n              className=\"btn btn-primary\"\n              onClick={() => setShowCreateModal(true)}\n            >\n              <i className=\"fas fa-plus me-2\"></i>\n              Create Role\n            </button>\n          </div>\n\n          {/* Messages */}\n          {error && (\n            <div className=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n              {error}\n              <button type=\"button\" className=\"btn-close\" onClick={clearMessages}></button>\n            </div>\n          )}\n          {success && (\n            <div className=\"alert alert-success alert-dismissible fade show\" role=\"alert\">\n              {success}\n              <button type=\"button\" className=\"btn-close\" onClick={clearMessages}></button>\n            </div>\n          )}\n\n          {/* Search and Filters */}\n          <div className=\"card mb-4\">\n            <div className=\"card-body\">\n              <form onSubmit={handleSearch}>\n                <div className=\"row g-3\">\n                  <div className=\"col-md-6\">\n                    <label htmlFor=\"search\" className=\"form-label\">Search Roles</label>\n                    <input\n                      type=\"text\"\n                      className=\"form-control\"\n                      id=\"search\"\n                      placeholder=\"Search by name or description...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                  </div>\n                  <div className=\"col-md-3\">\n                    <label htmlFor=\"status\" className=\"form-label\">Status</label>\n                    <select\n                      className=\"form-select\"\n                      id=\"status\"\n                      value={statusFilter}\n                      onChange={(e) => setStatusFilter(e.target.value as any)}\n                    >\n                      <option value=\"all\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                    </select>\n                  </div>\n                  <div className=\"col-md-3 d-flex align-items-end\">\n                    <button type=\"submit\" className=\"btn btn-outline-primary me-2\">\n                      <i className=\"fas fa-search me-1\"></i>\n                      Search\n                    </button>\n                    <button\n                      type=\"button\"\n                      className=\"btn btn-outline-secondary\"\n                      onClick={() => {\n                        setSearchTerm('');\n                        setStatusFilter('all');\n                        setPagination(prev => ({ ...prev, page: 1 }));\n                      }}\n                    >\n                      Clear\n                    </button>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n\n          {/* Roles Table */}\n          <div className=\"card\">\n            <div className=\"card-body\">\n              {loading ? (\n                <div className=\"text-center py-4\">\n                  <div className=\"spinner-border\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading...</span>\n                  </div>\n                </div>\n              ) : (\n                <>\n                  <div className=\"table-responsive\">\n                    <table className=\"table table-hover\">\n                      <thead className=\"table-light\">\n                        <tr>\n                          <th>ID</th>\n                          <th>Name</th>\n                          <th>Description</th>\n                          <th>Status</th>\n                          <th>Created</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {roles.length === 0 ? (\n                          <tr>\n                            <td colSpan={6} className=\"text-center py-4\">\n                              No roles found\n                            </td>\n                          </tr>\n                        ) : (\n                          roles.map((role) => (\n                            <tr key={role.id}>\n                              <td>{role.id}</td>\n                              <td>\n                                <strong>{role.name}</strong>\n                              </td>\n                              <td>{role.description || '-'}</td>\n                              <td>\n                                <span className={`badge ${role.is_active ? 'bg-success' : 'bg-secondary'}`}>\n                                  {role.is_active ? 'Active' : 'Inactive'}\n                                </span>\n                              </td>\n                              <td>\n                                {role.created_at ? new Date(role.created_at).toLocaleDateString() : '-'}\n                              </td>\n                              <td>\n                                <div className=\"btn-group btn-group-sm\" role=\"group\">\n                                  <button\n                                    className=\"btn btn-outline-primary\"\n                                    onClick={() => openEditModal(role)}\n                                    title=\"Edit Role\"\n                                  >\n                                    <i className=\"fas fa-edit\"></i>\n                                  </button>\n                                  <button\n                                    className=\"btn btn-outline-info\"\n                                    onClick={() => openPermissionModal(role)}\n                                    title=\"Manage Permissions\"\n                                  >\n                                    <i className=\"fas fa-key\"></i>\n                                  </button>\n                                  <button\n                                    className=\"btn btn-outline-danger\"\n                                    onClick={() => handleDelete(role)}\n                                    title=\"Delete Role\"\n                                  >\n                                    <i className=\"fas fa-trash\"></i>\n                                  </button>\n                                </div>\n                              </td>\n                            </tr>\n                          ))\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n\n                  {/* Pagination */}\n                  {pagination.total_pages > 1 && (\n                    <nav aria-label=\"Roles pagination\">\n                      <ul className=\"pagination justify-content-center mt-3\">\n                        <li className={`page-item ${!pagination.has_prev ? 'disabled' : ''}`}>\n                          <button\n                            className=\"page-link\"\n                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}\n                            disabled={!pagination.has_prev}\n                          >\n                            Previous\n                          </button>\n                        </li>\n\n                        {Array.from({ length: pagination.total_pages }, (_, i) => i + 1).map((page) => (\n                          <li key={page} className={`page-item ${pagination.page === page ? 'active' : ''}`}>\n                            <button\n                              className=\"page-link\"\n                              onClick={() => setPagination(prev => ({ ...prev, page }))}\n                            >\n                              {page}\n                            </button>\n                          </li>\n                        ))}\n\n                        <li className={`page-item ${!pagination.has_next ? 'disabled' : ''}`}>\n                          <button\n                            className=\"page-link\"\n                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                            disabled={!pagination.has_next}\n                          >\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  )}\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Create Role Modal */}\n      {showCreateModal && (\n        <div className=\"modal fade show\" style={{ display: 'block' }} tabIndex={-1}>\n          <div className=\"modal-dialog\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h5 className=\"modal-title\">Create New Role</h5>\n                <button\n                  type=\"button\"\n                  className=\"btn-close\"\n                  onClick={() => {\n                    setShowCreateModal(false);\n                    resetForm();\n                  }}\n                ></button>\n              </div>\n              <form onSubmit={handleCreate}>\n                <div className=\"modal-body\">\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"createName\" className=\"form-label\">Role Name *</label>\n                    <input\n                      type=\"text\"\n                      className=\"form-control\"\n                      id=\"createName\"\n                      value={formData.name}\n                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                      required\n                    />\n                  </div>\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"createDescription\" className=\"form-label\">Description</label>\n                    <textarea\n                      className=\"form-control\"\n                      id=\"createDescription\"\n                      rows={3}\n                      value={formData.description}\n                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                    ></textarea>\n                  </div>\n                  <div className=\"mb-3 form-check\">\n                    <input\n                      type=\"checkbox\"\n                      className=\"form-check-input\"\n                      id=\"createActive\"\n                      checked={formData.is_active}\n                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"createActive\">\n                      Active\n                    </label>\n                  </div>\n                </div>\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-secondary\"\n                    onClick={() => {\n                      setShowCreateModal(false);\n                      resetForm();\n                    }}\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"btn btn-primary\" disabled={loading}>\n                    {loading ? 'Creating...' : 'Create Role'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Role Modal */}\n      {showEditModal && selectedRole && (\n        <div className=\"modal fade show\" style={{ display: 'block' }} tabIndex={-1}>\n          <div className=\"modal-dialog\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h5 className=\"modal-title\">Edit Role: {selectedRole.name}</h5>\n                <button\n                  type=\"button\"\n                  className=\"btn-close\"\n                  onClick={() => {\n                    setShowEditModal(false);\n                    resetForm();\n                  }}\n                ></button>\n              </div>\n              <form onSubmit={handleUpdate}>\n                <div className=\"modal-body\">\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"editName\" className=\"form-label\">Role Name *</label>\n                    <input\n                      type=\"text\"\n                      className=\"form-control\"\n                      id=\"editName\"\n                      value={formData.name}\n                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                      required\n                    />\n                  </div>\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"editDescription\" className=\"form-label\">Description</label>\n                    <textarea\n                      className=\"form-control\"\n                      id=\"editDescription\"\n                      rows={3}\n                      value={formData.description}\n                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                    ></textarea>\n                  </div>\n                  <div className=\"mb-3 form-check\">\n                    <input\n                      type=\"checkbox\"\n                      className=\"form-check-input\"\n                      id=\"editActive\"\n                      checked={formData.is_active}\n                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"editActive\">\n                      Active\n                    </label>\n                  </div>\n                </div>\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-secondary\"\n                    onClick={() => {\n                      setShowEditModal(false);\n                      resetForm();\n                    }}\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"btn btn-primary\" disabled={loading}>\n                    {loading ? 'Updating...' : 'Update Role'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Permission Management Modal */}\n      {selectedRole && (\n        <RolePermissionModal\n          role={selectedRole}\n          isOpen={showPermissionModal}\n          onClose={() => {\n            setShowPermissionModal(false);\n            setSelectedRole(null);\n          }}\n          onSuccess={(message) => {\n            setSuccess(message);\n            setTimeout(() => setSuccess(null), 5000);\n          }}\n          onError={(message) => {\n            setError(message);\n            setTimeout(() => setError(null), 5000);\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default RoleManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,gBAAgB;AAE/D,OAAOC,mBAAmB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIxD,MAAMC,cAA6C,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1D;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAiB;IAC3DuB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAgC,KAAK,CAAC;;EAEtF;EACA,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAc,IAAI,CAAC;;EAEnE;EACA,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC;IACvC2C,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,SAAS,CAAC,CAAC;IACXC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC1B,UAAU,CAACE,IAAI,EAAEF,UAAU,CAACG,SAAS,EAAEK,UAAU,EAAEE,YAAY,CAAC,CAAC;;EAErE;EACA,MAAMe,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8B,OAAY,GAAG;QACnBzB,IAAI,EAAEF,UAAU,CAACE,IAAI;QACrBC,SAAS,EAAEH,UAAU,CAACG;MACxB,CAAC;MAED,IAAIK,UAAU,CAACoB,IAAI,CAAC,CAAC,EAAE;QACrBD,OAAO,CAACE,WAAW,GAAGrB,UAAU,CAACoB,IAAI,CAAC,CAAC;QACvCD,OAAO,CAACG,cAAc,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;MAClD;MAEA,IAAIpB,YAAY,KAAK,KAAK,EAAE;QAC1BiB,OAAO,CAACH,SAAS,GAAGd,YAAY,KAAK,QAAQ;MAC/C;MAEA,MAAMqB,QAAQ,GAAG,MAAMlD,WAAW,CAACmD,IAAI,CAACL,OAAO,CAAC;MAChDpC,QAAQ,CAACwC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAC5B,MAAMC,cAAc,GAAGH,QAAQ,CAACE,IAAI,CAACjC,UAAU;MAC/CC,aAAa,CAAC;QACZ,GAAGiC,cAAc;QACjB5B,QAAQ,EAAE4B,cAAc,CAAChC,IAAI,GAAGgC,cAAc,CAAC7B,WAAW;QAC1DE,QAAQ,EAAE2B,cAAc,CAAChC,IAAI,GAAG;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOiC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACC,OAAO,IAAI,sBAAsB,CAAC;MAC/CC,OAAO,CAACzC,KAAK,CAAC,sBAAsB,EAAEuC,GAAG,CAAC;IAC5C,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMjD,iBAAiB,CAACkD,IAAI,CAAC;QAAE9B,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAI,CAAC,CAAC;MAC1EV,cAAc,CAACsC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;IACpC,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBE,OAAO,CAACzC,KAAK,CAAC,4BAA4B,EAAEuC,GAAG,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMG,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMhB,WAAW,CAAC4D,MAAM,CAACrB,QAAQ,CAAC;MAClCrB,UAAU,CAAC,2BAA2B,CAAC;MACvCc,kBAAkB,CAAC,KAAK,CAAC;MACzB6B,SAAS,CAAC,CAAC;MACXjB,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACC,OAAO,IAAI,uBAAuB,CAAC;IAClD,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgD,YAAY,GAAG,MAAOJ,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACtB,YAAY,EAAE;IAEnB,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMhB,WAAW,CAAC+D,MAAM,CAAC1B,YAAY,CAAC2B,EAAE,EAAEzB,QAAQ,CAAC;MACnDrB,UAAU,CAAC,2BAA2B,CAAC;MACvCgB,gBAAgB,CAAC,KAAK,CAAC;MACvB2B,SAAS,CAAC,CAAC;MACXjB,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACC,OAAO,IAAI,uBAAuB,CAAC;IAClD,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmD,YAAY,GAAG,MAAOC,IAAU,IAAK;IACzC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,yCAAyCF,IAAI,CAACzB,IAAI,IAAI,CAAC,EAAE;MAC3E;IACF;IAEA,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMhB,WAAW,CAACqE,MAAM,CAACH,IAAI,CAACF,EAAE,CAAC;MACjC9C,UAAU,CAAC,2BAA2B,CAAC;MACvC0B,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACC,OAAO,IAAI,uBAAuB,CAAC;IAClD,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwD,YAAY,GAAIZ,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBvC,aAAa,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElD,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAC7CuB,SAAS,CAAC,CAAC;EACb,CAAC;;EAED;EACA,MAAMiB,SAAS,GAAGA,CAAA,KAAM;IACtBrB,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;IACb,CAAC,CAAC;IACFL,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAIN,IAAU,IAAK;IACpC5B,eAAe,CAAC4B,IAAI,CAAC;IACrB1B,WAAW,CAAC;MACVC,IAAI,EAAEyB,IAAI,CAACzB,IAAI;MACfC,WAAW,EAAEwB,IAAI,CAACxB,WAAW,IAAI,EAAE;MACnCC,SAAS,EAAEuB,IAAI,CAACvB;IAClB,CAAC,CAAC;IACFT,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAIP,IAAU,IAAK;IAC1C5B,eAAe,CAAC4B,IAAI,CAAC;IACrB9B,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMsC,aAAa,GAAGA,CAAA,KAAM;IAC1B1D,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,oBACEd,OAAA;IAAKuE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BxE,OAAA;MAAKuE,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBxE,OAAA;QAAKuE,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBxE,OAAA;UAAKuE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,gBACrExE,OAAA;YAAAwE,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB5E,OAAA;YACEuE,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAMjD,kBAAkB,CAAC,IAAI,CAAE;YAAA4C,QAAA,gBAExCxE,OAAA;cAAGuE,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLjE,KAAK,iBACJX,OAAA;UAAKuE,SAAS,EAAC,gDAAgD;UAACT,IAAI,EAAC,OAAO;UAAAU,QAAA,GACzE7D,KAAK,eACNX,OAAA;YAAQ8E,IAAI,EAAC,QAAQ;YAACP,SAAS,EAAC,WAAW;YAACM,OAAO,EAAEP;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CACN,EACA/D,OAAO,iBACNb,OAAA;UAAKuE,SAAS,EAAC,iDAAiD;UAACT,IAAI,EAAC,OAAO;UAAAU,QAAA,GAC1E3D,OAAO,eACRb,OAAA;YAAQ8E,IAAI,EAAC,QAAQ;YAACP,SAAS,EAAC,WAAW;YAACM,OAAO,EAAEP;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CACN,eAGD5E,OAAA;UAAKuE,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBxE,OAAA;YAAKuE,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxE,OAAA;cAAM+E,QAAQ,EAAEb,YAAa;cAAAM,QAAA,eAC3BxE,OAAA;gBAAKuE,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBxE,OAAA;kBAAKuE,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBxE,OAAA;oBAAOgF,OAAO,EAAC,QAAQ;oBAACT,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnE5E,OAAA;oBACE8E,IAAI,EAAC,MAAM;oBACXP,SAAS,EAAC,cAAc;oBACxBX,EAAE,EAAC,QAAQ;oBACXqB,WAAW,EAAC,kCAAkC;oBAC9CC,KAAK,EAAE3D,UAAW;oBAClB4D,QAAQ,EAAG7B,CAAC,IAAK9B,aAAa,CAAC8B,CAAC,CAAC8B,MAAM,CAACF,KAAK;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5E,OAAA;kBAAKuE,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBxE,OAAA;oBAAOgF,OAAO,EAAC,QAAQ;oBAACT,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7D5E,OAAA;oBACEuE,SAAS,EAAC,aAAa;oBACvBX,EAAE,EAAC,QAAQ;oBACXsB,KAAK,EAAEzD,YAAa;oBACpB0D,QAAQ,EAAG7B,CAAC,IAAK5B,eAAe,CAAC4B,CAAC,CAAC8B,MAAM,CAACF,KAAY,CAAE;oBAAAV,QAAA,gBAExDxE,OAAA;sBAAQkF,KAAK,EAAC,KAAK;sBAAAV,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC5E,OAAA;sBAAQkF,KAAK,EAAC,QAAQ;sBAAAV,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC5E,OAAA;sBAAQkF,KAAK,EAAC,UAAU;sBAAAV,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN5E,OAAA;kBAAKuE,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC9CxE,OAAA;oBAAQ8E,IAAI,EAAC,QAAQ;oBAACP,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC5DxE,OAAA;sBAAGuE,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,UAExC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5E,OAAA;oBACE8E,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,2BAA2B;oBACrCM,OAAO,EAAEA,CAAA,KAAM;sBACbrD,aAAa,CAAC,EAAE,CAAC;sBACjBE,eAAe,CAAC,KAAK,CAAC;sBACtBV,aAAa,CAACmD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAElD,IAAI,EAAE;sBAAE,CAAC,CAAC,CAAC;oBAC/C,CAAE;oBAAAuD,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5E,OAAA;UAAKuE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBxE,OAAA;YAAKuE,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB/D,OAAO,gBACNT,OAAA;cAAKuE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BxE,OAAA;gBAAKuE,SAAS,EAAC,gBAAgB;gBAACT,IAAI,EAAC,QAAQ;gBAAAU,QAAA,eAC3CxE,OAAA;kBAAMuE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN5E,OAAA,CAAAE,SAAA;cAAAsE,QAAA,gBACExE,OAAA;gBAAKuE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BxE,OAAA;kBAAOuE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAClCxE,OAAA;oBAAOuE,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC5BxE,OAAA;sBAAAwE,QAAA,gBACExE,OAAA;wBAAAwE,QAAA,EAAI;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACX5E,OAAA;wBAAAwE,QAAA,EAAI;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACb5E,OAAA;wBAAAwE,QAAA,EAAI;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpB5E,OAAA;wBAAAwE,QAAA,EAAI;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACf5E,OAAA;wBAAAwE,QAAA,EAAI;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChB5E,OAAA;wBAAAwE,QAAA,EAAI;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACR5E,OAAA;oBAAAwE,QAAA,EACGnE,KAAK,CAACgF,MAAM,KAAK,CAAC,gBACjBrF,OAAA;sBAAAwE,QAAA,eACExE,OAAA;wBAAIsF,OAAO,EAAE,CAAE;wBAACf,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,EAAC;sBAE7C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GAELvE,KAAK,CAACkF,GAAG,CAAEzB,IAAI,iBACb9D,OAAA;sBAAAwE,QAAA,gBACExE,OAAA;wBAAAwE,QAAA,EAAKV,IAAI,CAACF;sBAAE;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClB5E,OAAA;wBAAAwE,QAAA,eACExE,OAAA;0BAAAwE,QAAA,EAASV,IAAI,CAACzB;wBAAI;0BAAAoC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACL5E,OAAA;wBAAAwE,QAAA,EAAKV,IAAI,CAACxB,WAAW,IAAI;sBAAG;wBAAAmC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClC5E,OAAA;wBAAAwE,QAAA,eACExE,OAAA;0BAAMuE,SAAS,EAAE,SAAST,IAAI,CAACvB,SAAS,GAAG,YAAY,GAAG,cAAc,EAAG;0BAAAiC,QAAA,EACxEV,IAAI,CAACvB,SAAS,GAAG,QAAQ,GAAG;wBAAU;0BAAAkC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACL5E,OAAA;wBAAAwE,QAAA,EACGV,IAAI,CAAC0B,UAAU,GAAG,IAAIC,IAAI,CAAC3B,IAAI,CAAC0B,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;sBAAG;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC,eACL5E,OAAA;wBAAAwE,QAAA,eACExE,OAAA;0BAAKuE,SAAS,EAAC,wBAAwB;0BAACT,IAAI,EAAC,OAAO;0BAAAU,QAAA,gBAClDxE,OAAA;4BACEuE,SAAS,EAAC,yBAAyB;4BACnCM,OAAO,EAAEA,CAAA,KAAMT,aAAa,CAACN,IAAI,CAAE;4BACnC6B,KAAK,EAAC,WAAW;4BAAAnB,QAAA,eAEjBxE,OAAA;8BAAGuE,SAAS,EAAC;4BAAa;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC,eACT5E,OAAA;4BACEuE,SAAS,EAAC,sBAAsB;4BAChCM,OAAO,EAAEA,CAAA,KAAMR,mBAAmB,CAACP,IAAI,CAAE;4BACzC6B,KAAK,EAAC,oBAAoB;4BAAAnB,QAAA,eAE1BxE,OAAA;8BAAGuE,SAAS,EAAC;4BAAY;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACT5E,OAAA;4BACEuE,SAAS,EAAC,wBAAwB;4BAClCM,OAAO,EAAEA,CAAA,KAAMhB,YAAY,CAACC,IAAI,CAAE;4BAClC6B,KAAK,EAAC,aAAa;4BAAAnB,QAAA,eAEnBxE,OAAA;8BAAGuE,SAAS,EAAC;4BAAc;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA,GAtCEd,IAAI,CAACF,EAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuCZ,CACL;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAGL7D,UAAU,CAACK,WAAW,GAAG,CAAC,iBACzBpB,OAAA;gBAAK,cAAW,kBAAkB;gBAAAwE,QAAA,eAChCxE,OAAA;kBAAIuE,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACpDxE,OAAA;oBAAIuE,SAAS,EAAE,aAAa,CAACxD,UAAU,CAACO,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;oBAAAkD,QAAA,eACnExE,OAAA;sBACEuE,SAAS,EAAC,WAAW;sBACrBM,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAACmD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAElD,IAAI,EAAEkD,IAAI,CAAClD,IAAI,GAAG;sBAAE,CAAC,CAAC,CAAE;sBACzE2E,QAAQ,EAAE,CAAC7E,UAAU,CAACO,QAAS;sBAAAkD,QAAA,EAChC;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,EAEJiB,KAAK,CAACC,IAAI,CAAC;oBAAET,MAAM,EAAEtE,UAAU,CAACK;kBAAY,CAAC,EAAE,CAAC2E,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAACT,GAAG,CAAEtE,IAAI,iBACxEjB,OAAA;oBAAeuE,SAAS,EAAE,aAAaxD,UAAU,CAACE,IAAI,KAAKA,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;oBAAAuD,QAAA,eAChFxE,OAAA;sBACEuE,SAAS,EAAC,WAAW;sBACrBM,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAACmD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAElD;sBAAK,CAAC,CAAC,CAAE;sBAAAuD,QAAA,EAEzDvD;oBAAI;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC,GANF3D,IAAI;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOT,CACL,CAAC,eAEF5E,OAAA;oBAAIuE,SAAS,EAAE,aAAa,CAACxD,UAAU,CAACM,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;oBAAAmD,QAAA,eACnExE,OAAA;sBACEuE,SAAS,EAAC,WAAW;sBACrBM,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAACmD,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAElD,IAAI,EAAEkD,IAAI,CAAClD,IAAI,GAAG;sBAAE,CAAC,CAAC,CAAE;sBACzE2E,QAAQ,EAAE,CAAC7E,UAAU,CAACM,QAAS;sBAAAmD,QAAA,EAChC;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN;YAAA,eACD;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjD,eAAe,iBACd3B,OAAA;MAAKuE,SAAS,EAAC,iBAAiB;MAAC0B,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAE;MAACC,QAAQ,EAAE,CAAC,CAAE;MAAA3B,QAAA,eACzExE,OAAA;QAAKuE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxE,OAAA;UAAKuE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxE,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAIuE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD5E,OAAA;cACE8E,IAAI,EAAC,QAAQ;cACbP,SAAS,EAAC,WAAW;cACrBM,OAAO,EAAEA,CAAA,KAAM;gBACbjD,kBAAkB,CAAC,KAAK,CAAC;gBACzB6B,SAAS,CAAC,CAAC;cACb;YAAE;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACN5E,OAAA;YAAM+E,QAAQ,EAAE1B,YAAa;YAAAmB,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxE,OAAA;gBAAKuE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBxE,OAAA;kBAAOgF,OAAO,EAAC,YAAY;kBAACT,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtE5E,OAAA;kBACE8E,IAAI,EAAC,MAAM;kBACXP,SAAS,EAAC,cAAc;kBACxBX,EAAE,EAAC,YAAY;kBACfsB,KAAK,EAAE/C,QAAQ,CAACE,IAAK;kBACrB8C,QAAQ,EAAG7B,CAAC,IAAKlB,WAAW,CAAC+B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE9B,IAAI,EAAEiB,CAAC,CAAC8B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC1EkB,QAAQ;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAKuE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBxE,OAAA;kBAAOgF,OAAO,EAAC,mBAAmB;kBAACT,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7E5E,OAAA;kBACEuE,SAAS,EAAC,cAAc;kBACxBX,EAAE,EAAC,mBAAmB;kBACtByC,IAAI,EAAE,CAAE;kBACRnB,KAAK,EAAE/C,QAAQ,CAACG,WAAY;kBAC5B6C,QAAQ,EAAG7B,CAAC,IAAKlB,WAAW,CAAC+B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7B,WAAW,EAAEgB,CAAC,CAAC8B,MAAM,CAACF;kBAAM,CAAC,CAAC;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN5E,OAAA;gBAAKuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxE,OAAA;kBACE8E,IAAI,EAAC,UAAU;kBACfP,SAAS,EAAC,kBAAkB;kBAC5BX,EAAE,EAAC,cAAc;kBACjB0C,OAAO,EAAEnE,QAAQ,CAACI,SAAU;kBAC5B4C,QAAQ,EAAG7B,CAAC,IAAKlB,WAAW,CAAC+B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5B,SAAS,EAAEe,CAAC,CAAC8B,MAAM,CAACkB;kBAAQ,CAAC,CAAC;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACF5E,OAAA;kBAAOuE,SAAS,EAAC,kBAAkB;kBAACS,OAAO,EAAC,cAAc;kBAAAR,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5E,OAAA;cAAKuE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxE,OAAA;gBACE8E,IAAI,EAAC,QAAQ;gBACbP,SAAS,EAAC,mBAAmB;gBAC7BM,OAAO,EAAEA,CAAA,KAAM;kBACbjD,kBAAkB,CAAC,KAAK,CAAC;kBACzB6B,SAAS,CAAC,CAAC;gBACb,CAAE;gBAAAe,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5E,OAAA;gBAAQ8E,IAAI,EAAC,QAAQ;gBAACP,SAAS,EAAC,iBAAiB;gBAACqB,QAAQ,EAAEnF,OAAQ;gBAAA+D,QAAA,EACjE/D,OAAO,GAAG,aAAa,GAAG;cAAa;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA/C,aAAa,IAAII,YAAY,iBAC5BjC,OAAA;MAAKuE,SAAS,EAAC,iBAAiB;MAAC0B,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAE;MAACC,QAAQ,EAAE,CAAC,CAAE;MAAA3B,QAAA,eACzExE,OAAA;QAAKuE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxE,OAAA;UAAKuE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxE,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAIuE,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,aAAW,EAACvC,YAAY,CAACI,IAAI;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/D5E,OAAA;cACE8E,IAAI,EAAC,QAAQ;cACbP,SAAS,EAAC,WAAW;cACrBM,OAAO,EAAEA,CAAA,KAAM;gBACb/C,gBAAgB,CAAC,KAAK,CAAC;gBACvB2B,SAAS,CAAC,CAAC;cACb;YAAE;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACN5E,OAAA;YAAM+E,QAAQ,EAAErB,YAAa;YAAAc,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxE,OAAA;gBAAKuE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBxE,OAAA;kBAAOgF,OAAO,EAAC,UAAU;kBAACT,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpE5E,OAAA;kBACE8E,IAAI,EAAC,MAAM;kBACXP,SAAS,EAAC,cAAc;kBACxBX,EAAE,EAAC,UAAU;kBACbsB,KAAK,EAAE/C,QAAQ,CAACE,IAAK;kBACrB8C,QAAQ,EAAG7B,CAAC,IAAKlB,WAAW,CAAC+B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE9B,IAAI,EAAEiB,CAAC,CAAC8B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC1EkB,QAAQ;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAKuE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBxE,OAAA;kBAAOgF,OAAO,EAAC,iBAAiB;kBAACT,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3E5E,OAAA;kBACEuE,SAAS,EAAC,cAAc;kBACxBX,EAAE,EAAC,iBAAiB;kBACpByC,IAAI,EAAE,CAAE;kBACRnB,KAAK,EAAE/C,QAAQ,CAACG,WAAY;kBAC5B6C,QAAQ,EAAG7B,CAAC,IAAKlB,WAAW,CAAC+B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7B,WAAW,EAAEgB,CAAC,CAAC8B,MAAM,CAACF;kBAAM,CAAC,CAAC;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN5E,OAAA;gBAAKuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxE,OAAA;kBACE8E,IAAI,EAAC,UAAU;kBACfP,SAAS,EAAC,kBAAkB;kBAC5BX,EAAE,EAAC,YAAY;kBACf0C,OAAO,EAAEnE,QAAQ,CAACI,SAAU;kBAC5B4C,QAAQ,EAAG7B,CAAC,IAAKlB,WAAW,CAAC+B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5B,SAAS,EAAEe,CAAC,CAAC8B,MAAM,CAACkB;kBAAQ,CAAC,CAAC;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACF5E,OAAA;kBAAOuE,SAAS,EAAC,kBAAkB;kBAACS,OAAO,EAAC,YAAY;kBAAAR,QAAA,EAAC;gBAEzD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5E,OAAA;cAAKuE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxE,OAAA;gBACE8E,IAAI,EAAC,QAAQ;gBACbP,SAAS,EAAC,mBAAmB;gBAC7BM,OAAO,EAAEA,CAAA,KAAM;kBACb/C,gBAAgB,CAAC,KAAK,CAAC;kBACvB2B,SAAS,CAAC,CAAC;gBACb,CAAE;gBAAAe,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5E,OAAA;gBAAQ8E,IAAI,EAAC,QAAQ;gBAACP,SAAS,EAAC,iBAAiB;gBAACqB,QAAQ,EAAEnF,OAAQ;gBAAA+D,QAAA,EACjE/D,OAAO,GAAG,aAAa,GAAG;cAAa;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA3C,YAAY,iBACXjC,OAAA,CAACF,mBAAmB;MAClBgE,IAAI,EAAE7B,YAAa;MACnBsE,MAAM,EAAExE,mBAAoB;MAC5ByE,OAAO,EAAEA,CAAA,KAAM;QACbxE,sBAAsB,CAAC,KAAK,CAAC;QAC7BE,eAAe,CAAC,IAAI,CAAC;MACvB,CAAE;MACFuE,SAAS,EAAGtD,OAAO,IAAK;QACtBrC,UAAU,CAACqC,OAAO,CAAC;QACnBuD,UAAU,CAAC,MAAM5F,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAE;MACF6F,OAAO,EAAGxD,OAAO,IAAK;QACpBvC,QAAQ,CAACuC,OAAO,CAAC;QACjBuD,UAAU,CAAC,MAAM9F,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;MACxC;IAAE;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxE,EAAA,CA5iBID,cAA6C;AAAAyG,EAAA,GAA7CzG,cAA6C;AA8iBnD,eAAeA,cAAc;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}