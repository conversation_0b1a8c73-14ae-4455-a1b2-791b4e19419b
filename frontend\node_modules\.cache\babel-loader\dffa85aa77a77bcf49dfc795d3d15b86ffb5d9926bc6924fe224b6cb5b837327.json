{"ast": null, "code": "// Export all services\nexport{authService}from'./auth.service';export{userService}from'./user.service';export{roleService}from'./role.service';export{permissionService}from'./permission.service';export{adminService}from'./admin.service';// Export base service and types\nexport{BaseCRUDService,api}from'./base.service';// Export service-specific types\n// Default exports for convenience\nimport authService from'./auth.service';import userService from'./user.service';import roleService from'./role.service';import permissionService from'./permission.service';import adminService from'./admin.service';export default{auth:authService,user:userService,role:roleService,permission:permissionService,admin:adminService};", "map": {"version": 3, "names": ["authService", "userService", "roleService", "permissionService", "adminService", "BaseCRUDService", "api", "auth", "user", "role", "permission", "admin"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/index.ts"], "sourcesContent": ["// Export all services\nexport { authService } from './auth.service';\nexport { userService } from './user.service';\nexport { roleService } from './role.service';\nexport { permissionService } from './permission.service';\nexport { adminService } from './admin.service';\n\n// Export base service and types\nexport { BaseCRUDService, api } from './base.service';\nexport type { \n  PaginationInfo, \n  CRUDListResponse, \n  CRUDResponse, \n  SearchRequest, \n  ErrorResponse \n} from './base.service';\n\n// Export service-specific types\nexport type { \n  LoginRequest, \n  RegisterRequest \n} from './auth.service';\n\nexport type { \n  CreateUserRequest, \n  UpdateUserRequest, \n  UserWithRoles \n} from './user.service';\n\nexport type { \n  CreateRoleRequest, \n  UpdateRoleRequest \n} from './role.service';\n\nexport type { \n  CreatePermissionRequest, \n  UpdatePermissionRequest \n} from './permission.service';\n\nexport type { \n  DashboardStats,\n  AssignRoleRequest,\n  RemoveRoleRequest,\n  AssignPermissionRequest,\n  RemovePermissionRequest,\n  AssignUserPermissionRequest,\n  RemoveUserPermissionRequest\n} from './admin.service';\n\n// Default exports for convenience\nimport authService from './auth.service';\nimport userService from './user.service';\nimport roleService from './role.service';\nimport permissionService from './permission.service';\nimport adminService from './admin.service';\n\nexport default {\n  auth: authService,\n  user: userService,\n  role: roleService,\n  permission: permissionService,\n  admin: adminService\n};\n"], "mappings": "AAAA;AACA,OAASA,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,iBAAiB,KAAQ,sBAAsB,CACxD,OAASC,YAAY,KAAQ,iBAAiB,CAE9C;AACA,OAASC,eAAe,CAAEC,GAAG,KAAQ,gBAAgB,CASrD;AAgCA;AACA,MAAO,CAAAN,WAAW,KAAM,gBAAgB,CACxC,MAAO,CAAAC,WAAW,KAAM,gBAAgB,CACxC,MAAO,CAAAC,WAAW,KAAM,gBAAgB,CACxC,MAAO,CAAAC,iBAAiB,KAAM,sBAAsB,CACpD,MAAO,CAAAC,YAAY,KAAM,iBAAiB,CAE1C,cAAe,CACbG,IAAI,CAAEP,WAAW,CACjBQ,IAAI,CAAEP,WAAW,CACjBQ,IAAI,CAAEP,WAAW,CACjBQ,UAAU,CAAEP,iBAAiB,CAC7BQ,KAAK,CAAEP,YACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}