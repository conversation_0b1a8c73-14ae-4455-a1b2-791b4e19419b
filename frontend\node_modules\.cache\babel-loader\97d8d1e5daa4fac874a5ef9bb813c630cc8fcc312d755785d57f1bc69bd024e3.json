{"ast": null, "code": "import{BaseCRUDService,api}from'./base.service';class UserService extends BaseCRUDService{constructor(){super('/admin/user-management');}// Get users with roles (from old admin API)\ngetUsersWithRoles(){return api.get('/admin/users');}// Get user roles\ngetUserRoles(userId){return api.get(\"/admin/user/\".concat(userId,\"/roles\"));}// Get user permissions\ngetUserPermissions(userId){return api.get(\"/admin/user/\".concat(userId,\"/permissions\"));}// Get user direct permissions\ngetUserDirectPermissions(userId){return api.get(\"/admin/user/\".concat(userId,\"/direct-permissions\"));}// Assign role to user\nassignRole(userId,roleName){return api.post('/admin/assign-role',{user_id:userId,role:roleName});}// Remove role from user\nremoveRole(userId,roleName){return api.post('/admin/remove-role',{user_id:userId,role:roleName});}// Assign permission directly to user\nassignPermission(userId,permissionName){return api.post('/admin/assign-user-permission',{user_id:userId,permission:permissionName});}// Remove permission from user\nremovePermission(userId,permissionName){return api.post('/admin/remove-user-permission',{user_id:userId,permission:permissionName});}// Assign role to user using CRUD API\nassignRoleCRUD(userId,roleId){return api.post(\"\".concat(this.baseUrl,\"/\").concat(userId,\"/roles\"),{role_id:roleId});}// Remove role from user using CRUD API\nremoveRoleCRUD(userId,roleId){return api.delete(\"\".concat(this.baseUrl,\"/\").concat(userId,\"/roles/\").concat(roleId));}// Assign permission to user using CRUD API\nassignPermissionCRUD(userId,permissionId){return api.post(\"\".concat(this.baseUrl,\"/\").concat(userId,\"/permissions\"),{permission_id:permissionId});}// Remove permission from user using CRUD API\nremovePermissionCRUD(userId,permissionId){return api.delete(\"\".concat(this.baseUrl,\"/\").concat(userId,\"/permissions/\").concat(permissionId));}}export const userService=new UserService();export default userService;", "map": {"version": 3, "names": ["BaseCRUDService", "api", "UserService", "constructor", "getUsersWithRoles", "get", "getUserRoles", "userId", "concat", "getUserPermissions", "getUserDirectPermissions", "assignRole", "<PERSON><PERSON><PERSON>", "post", "user_id", "role", "removeRole", "assignPermission", "permissionName", "permission", "removePermission", "assignRoleCRUD", "roleId", "baseUrl", "role_id", "removeRoleCRUD", "delete", "assignPermissionCRUD", "permissionId", "permission_id", "removePermissionCRUD", "userService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/user.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { User } from '../types';\nimport { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';\n\nexport interface CreateUserRequest {\n  username: string;\n  email: string;\n  password: string;\n  is_active?: boolean;\n}\n\nexport interface UpdateUserRequest {\n  username?: string;\n  email?: string;\n  password?: string;\n  is_active?: boolean;\n}\n\nexport interface UserWithRoles {\n  id: number;\n  username: string;\n  email: string;\n  roles: string[];\n  is_active: boolean;\n  created_at: string;\n  updated_at?: string;\n}\n\nclass UserService extends BaseCRUDService<User, CreateUserRequest, UpdateUserRequest> {\n  constructor() {\n    super('/admin/user-management');\n  }\n\n  // Get users with roles (from old admin API)\n  getUsersWithRoles(): Promise<AxiosResponse<UserWithRoles[]>> {\n    return api.get('/admin/users');\n  }\n\n  // Get user roles\n  getUserRoles(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/user/${userId}/roles`);\n  }\n\n  // Get user permissions\n  getUserPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/user/${userId}/permissions`);\n  }\n\n  // Get user direct permissions\n  getUserDirectPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/user/${userId}/direct-permissions`);\n  }\n\n  // Assign role to user\n  assignRole(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/assign-role', { user_id: userId, role: roleName });\n  }\n\n  // Remove role from user\n  removeRole(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/remove-role', { user_id: userId, role: roleName });\n  }\n\n  // Assign permission directly to user\n  assignPermission(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/assign-user-permission', { user_id: userId, permission: permissionName });\n  }\n\n  // Remove permission from user\n  removePermission(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/remove-user-permission', { user_id: userId, permission: permissionName });\n  }\n\n  // Assign role to user using CRUD API\n  assignRoleCRUD(userId: number, roleId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/${userId}/roles`, { role_id: roleId });\n  }\n\n  // Remove role from user using CRUD API\n  removeRoleCRUD(userId: number, roleId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${userId}/roles/${roleId}`);\n  }\n\n  // Assign permission to user using CRUD API\n  assignPermissionCRUD(userId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/${userId}/permissions`, { permission_id: permissionId });\n  }\n\n  // Remove permission from user using CRUD API\n  removePermissionCRUD(userId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${userId}/permissions/${permissionId}`);\n  }\n}\n\nexport const userService = new UserService();\nexport default userService;\n"], "mappings": "AAEA,OAASA,eAAe,CAAkCC,GAAG,KAAQ,gBAAgB,CA0BrF,KAAM,CAAAC,WAAW,QAAS,CAAAF,eAA4D,CACpFG,WAAWA,CAAA,CAAG,CACZ,KAAK,CAAC,wBAAwB,CAAC,CACjC,CAEA;AACAC,iBAAiBA,CAAA,CAA4C,CAC3D,MAAO,CAAAH,GAAG,CAACI,GAAG,CAAC,cAAc,CAAC,CAChC,CAEA;AACAC,YAAYA,CAACC,MAAc,CAAoC,CAC7D,MAAO,CAAAN,GAAG,CAACI,GAAG,gBAAAG,MAAA,CAAgBD,MAAM,UAAQ,CAAC,CAC/C,CAEA;AACAE,kBAAkBA,CAACF,MAAc,CAAoC,CACnE,MAAO,CAAAN,GAAG,CAACI,GAAG,gBAAAG,MAAA,CAAgBD,MAAM,gBAAc,CAAC,CACrD,CAEA;AACAG,wBAAwBA,CAACH,MAAc,CAAoC,CACzE,MAAO,CAAAN,GAAG,CAACI,GAAG,gBAAAG,MAAA,CAAgBD,MAAM,uBAAqB,CAAC,CAC5D,CAEA;AACAI,UAAUA,CAACJ,MAAc,CAAEK,QAAgB,CAA+C,CACxF,MAAO,CAAAX,GAAG,CAACY,IAAI,CAAC,oBAAoB,CAAE,CAAEC,OAAO,CAAEP,MAAM,CAAEQ,IAAI,CAAEH,QAAS,CAAC,CAAC,CAC5E,CAEA;AACAI,UAAUA,CAACT,MAAc,CAAEK,QAAgB,CAA+C,CACxF,MAAO,CAAAX,GAAG,CAACY,IAAI,CAAC,oBAAoB,CAAE,CAAEC,OAAO,CAAEP,MAAM,CAAEQ,IAAI,CAAEH,QAAS,CAAC,CAAC,CAC5E,CAEA;AACAK,gBAAgBA,CAACV,MAAc,CAAEW,cAAsB,CAA+C,CACpG,MAAO,CAAAjB,GAAG,CAACY,IAAI,CAAC,+BAA+B,CAAE,CAAEC,OAAO,CAAEP,MAAM,CAAEY,UAAU,CAAED,cAAe,CAAC,CAAC,CACnG,CAEA;AACAE,gBAAgBA,CAACb,MAAc,CAAEW,cAAsB,CAA+C,CACpG,MAAO,CAAAjB,GAAG,CAACY,IAAI,CAAC,+BAA+B,CAAE,CAAEC,OAAO,CAAEP,MAAM,CAAEY,UAAU,CAAED,cAAe,CAAC,CAAC,CACnG,CAEA;AACAG,cAAcA,CAACd,MAAc,CAAEe,MAAc,CAA+C,CAC1F,MAAO,CAAArB,GAAG,CAACY,IAAI,IAAAL,MAAA,CAAI,IAAI,CAACe,OAAO,MAAAf,MAAA,CAAID,MAAM,WAAU,CAAEiB,OAAO,CAAEF,MAAO,CAAC,CAAC,CACzE,CAEA;AACAG,cAAcA,CAAClB,MAAc,CAAEe,MAAc,CAA+C,CAC1F,MAAO,CAAArB,GAAG,CAACyB,MAAM,IAAAlB,MAAA,CAAI,IAAI,CAACe,OAAO,MAAAf,MAAA,CAAID,MAAM,YAAAC,MAAA,CAAUc,MAAM,CAAE,CAAC,CAChE,CAEA;AACAK,oBAAoBA,CAACpB,MAAc,CAAEqB,YAAoB,CAA+C,CACtG,MAAO,CAAA3B,GAAG,CAACY,IAAI,IAAAL,MAAA,CAAI,IAAI,CAACe,OAAO,MAAAf,MAAA,CAAID,MAAM,iBAAgB,CAAEsB,aAAa,CAAED,YAAa,CAAC,CAAC,CAC3F,CAEA;AACAE,oBAAoBA,CAACvB,MAAc,CAAEqB,YAAoB,CAA+C,CACtG,MAAO,CAAA3B,GAAG,CAACyB,MAAM,IAAAlB,MAAA,CAAI,IAAI,CAACe,OAAO,MAAAf,MAAA,CAAID,MAAM,kBAAAC,MAAA,CAAgBoB,YAAY,CAAE,CAAC,CAC5E,CACF,CAEA,MAAO,MAAM,CAAAG,WAAW,CAAG,GAAI,CAAA7B,WAAW,CAAC,CAAC,CAC5C,cAAe,CAAA6B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}