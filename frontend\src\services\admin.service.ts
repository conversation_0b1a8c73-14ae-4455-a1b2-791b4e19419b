import { AxiosResponse } from 'axios';
import { User, Role, Permission } from '../types';
import { api } from './base.service';

export interface DashboardStats {
  total_users: number;
  total_roles: number;
  total_permissions: number;
  active_users: number;
  active_roles: number;
  active_permissions: number;
}

export interface AssignRoleRequest {
  user_id: number;
  role: string;
}

export interface RemoveRoleRequest {
  user_id: number;
  role: string;
}

export interface AssignPermissionRequest {
  role_id: number;
  permission: string;
}

export interface RemovePermissionRequest {
  role_id: number;
  permission: string;
}

export interface AssignUserPermissionRequest {
  user_id: number;
  permission: string;
}

export interface RemoveUserPermissionRequest {
  user_id: number;
  permission: string;
}

class AdminService {
  private baseUrl = '/admin';

  // Dashboard
  getDashboard(): Promise<AxiosResponse<DashboardStats>> {
    return api.get(`${this.baseUrl}/dashboard`);
  }

  // Users
  getUsers(): Promise<AxiosResponse<User[]>> {
    return api.get(`${this.baseUrl}/users`);
  }

  getUserRoles(userId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`${this.baseUrl}/user/${userId}/roles`);
  }

  getUserPermissions(userId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`${this.baseUrl}/user/${userId}/permissions`);
  }

  getUserDirectPermissions(userId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`${this.baseUrl}/user/${userId}/direct-permissions`);
  }

  // Roles
  getRoles(): Promise<AxiosResponse<Role[]>> {
    return api.get(`${this.baseUrl}/roles`);
  }

  getRolePermissions(roleId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`${this.baseUrl}/role/${roleId}/permissions`);
  }

  // Permissions
  getPermissions(): Promise<AxiosResponse<Permission[]>> {
    return api.get(`${this.baseUrl}/permissions`);
  }

  // Role assignments
  assignRole(request: AssignRoleRequest): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/assign-role`, request);
  }

  removeRole(request: RemoveRoleRequest): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/remove-role`, request);
  }

  // Permission assignments to roles
  assignPermission(request: AssignPermissionRequest): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/assign-permission`, request);
  }

  removePermission(request: RemovePermissionRequest): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/remove-permission`, request);
  }

  // Direct permission assignments to users
  assignUserPermission(request: AssignUserPermissionRequest): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/assign-user-permission`, request);
  }

  removeUserPermission(request: RemoveUserPermissionRequest): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/remove-user-permission`, request);
  }

  // Convenience methods
  assignRoleToUser(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {
    return this.assignRole({ user_id: userId, role: roleName });
  }

  removeRoleFromUser(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {
    return this.removeRole({ user_id: userId, role: roleName });
  }

  assignPermissionToRole(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return this.assignPermission({ role_id: roleId, permission: permissionName });
  }

  removePermissionFromRole(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return this.removePermission({ role_id: roleId, permission: permissionName });
  }

  assignPermissionToUser(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return this.assignUserPermission({ user_id: userId, permission: permissionName });
  }

  removePermissionFromUser(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return this.removeUserPermission({ user_id: userId, permission: permissionName });
  }
}

export const adminService = new AdminService();
export default adminService;
