package v1

import (
	"net/http"
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AssignRoleRequest struct {
	UserID uint   `json:"user_id" binding:"required"`
	Role   string `json:"role" binding:"required"`
}

// @Summary Gán role cho user
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param data body AssignRoleRequest true "Thông tin gán role"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/admin/assign-role [post]
func AssignRole(c *gin.Context) {
	logger := utils.GetLogger()
	
	var req AssignRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid assign role request", zap.Error(err))
		c.<PERSON><PERSON>(http.StatusBadRequest, gin.H{"error": err.<PERSON>rror()})
		return
	}

	logger.Info("Role assignment attempt", 
		zap.Uint("user_id", req.UserID),
		zap.String("role", req.Role),
	)

	// Tìm role
	var role models.Role
	if err := database.DB.Where("name = ?", req.Role).First(&role).Error; err != nil {
		logger.Warn("Role assignment failed - role not found", zap.String("role", req.Role))
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}

	// Kiểm tra user đã có role này chưa
	var count int64
	database.DB.Model(&models.UserRole{}).Where("user_id = ? AND role_id = ?", req.UserID, role.ID).Count(&count)
	if count > 0 {
		logger.Warn("Role assignment failed - user already has role", 
			zap.Uint("user_id", req.UserID),
			zap.String("role", req.Role),
		)
		c.JSON(http.StatusConflict, gin.H{"error": "User already has this role"})
		return
	}

	// Gán role cho user
	if err := database.DB.Create(&models.UserRole{UserID: req.UserID, RoleID: role.ID}).Error; err != nil {
		logger.Error("Failed to assign role in database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign role"})
		return
	}

	logger.Info("Role assigned successfully", 
		zap.Uint("user_id", req.UserID),
		zap.String("role", req.Role),
		zap.Uint("role_id", role.ID),
	)

	c.JSON(http.StatusOK, gin.H{"message": "Role assigned successfully"})
}

type UserWithRoles struct {
	ID       uint     `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []string `json:"roles"`
}

// @Summary Lấy danh sách user
// @Tags Admin
// @Security BearerAuth
// @Produce json
// @Success 200 {array} UserWithRoles
// @Router /api/v1/admin/users [get]
func ListUsers(c *gin.Context) {
	logger := utils.GetLogger()
	
	var users []models.User
	database.DB.Find(&users)
	var result []UserWithRoles
	for _, u := range users {
		var roles []string
		database.DB.
			Table("roles").
			Select("roles.name").
			Joins("join user_roles on user_roles.role_id = roles.id").
			Where("user_roles.user_id = ?", u.ID).
			Scan(&roles)
		result = append(result, UserWithRoles{
			ID:       u.ID,
			Username: u.Username,
			Email:    u.Email,
			Roles:    roles,
		})
	}
	
	logger.Info("Users listed", zap.Int("count", len(result)))
	c.JSON(http.StatusOK, result)
}

// @Summary Lấy danh sách role
// @Tags Admin
// @Security BearerAuth
// @Produce json
// @Success 200 {array} models.Role
// @Router /api/v1/admin/roles [get]
func ListRoles(c *gin.Context) {
	logger := utils.GetLogger()
	
	var roles []models.Role
	database.DB.Find(&roles)
	
	logger.Info("Roles listed", zap.Int("count", len(roles)))
	c.JSON(http.StatusOK, roles)
}

// @Summary Lấy danh sách permission
// @Tags Admin
// @Security BearerAuth
// @Produce json
// @Success 200 {array} models.Permission
// @Router /api/v1/admin/permissions [get]
func ListPermissions(c *gin.Context) {
	logger := utils.GetLogger()
	
	var perms []models.Permission
	database.DB.Find(&perms)
	
	logger.Info("Permissions listed", zap.Int("count", len(perms)))
	c.JSON(http.StatusOK, perms)
}

// @Summary Lấy role của user
// @Tags Admin
// @Security BearerAuth
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {array} string
// @Router /api/v1/admin/user/{id}/roles [get]
func GetUserRoles(c *gin.Context) {
	logger := utils.GetLogger()
	
	userID := c.Param("id")
	var roles []string
	database.DB.
		Table("roles").
		Select("roles.name").
		Joins("join user_roles on user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", userID).
		Scan(&roles)
	
	logger.Info("User roles retrieved", 
		zap.String("user_id", userID),
		zap.Int("role_count", len(roles)),
	)
	c.JSON(http.StatusOK, roles)
}

// @Summary Lấy permission của user
// @Tags Admin
// @Security BearerAuth
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {array} string
// @Router /api/v1/admin/user/{id}/permissions [get]
func GetUserPermissions(c *gin.Context) {
	logger := utils.GetLogger()
	
	userID := c.Param("id")
	var perms []string
	database.DB.
		Table("permissions").
		Select("permissions.name").
		Joins("join role_permissions on role_permissions.permission_id = permissions.id").
		Joins("join user_roles on user_roles.role_id = role_permissions.role_id").
		Where("user_roles.user_id = ?", userID).
		Scan(&perms)
	
	logger.Info("User permissions retrieved", 
		zap.String("user_id", userID),
		zap.Int("permission_count", len(perms)),
	)
	c.JSON(http.StatusOK, perms)
}

type RemoveRoleRequest struct {
	UserID uint   `json:"user_id" binding:"required"`
	Role   string `json:"role" binding:"required"`
}

// @Summary Xóa role khỏi user
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param data body RemoveRoleRequest true "Thông tin xóa role"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/admin/remove-role [post]
func RemoveRole(c *gin.Context) {
	logger := utils.GetLogger()
	
	var req RemoveRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid remove role request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("Role removal attempt", 
		zap.Uint("user_id", req.UserID),
		zap.String("role", req.Role),
	)

	var role models.Role
	if err := database.DB.Where("name = ?", req.Role).First(&role).Error; err != nil {
		logger.Warn("Role removal failed - role not found", zap.String("role", req.Role))
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}
	if err := database.DB.Where("user_id = ? AND role_id = ?", req.UserID, role.ID).Delete(&models.UserRole{}).Error; err != nil {
		logger.Error("Failed to remove role from database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove role"})
		return
	}

	logger.Info("Role removed successfully", 
		zap.Uint("user_id", req.UserID),
		zap.String("role", req.Role),
		zap.Uint("role_id", role.ID),
	)

	c.JSON(http.StatusOK, gin.H{"message": "Role removed successfully"})
}

type AssignPermissionRequest struct {
	RoleID      uint   `json:"role_id" binding:"required"`
	Permission  string `json:"permission" binding:"required"`
}

type RemovePermissionRequest struct {
	RoleID      uint   `json:"role_id" binding:"required"`
	Permission  string `json:"permission" binding:"required"`
}

// @Summary Lấy permission của role
// @Tags Admin
// @Security BearerAuth
// @Produce json
// @Param id path int true "Role ID"
// @Success 200 {array} string
// @Router /api/v1/admin/role/{id}/permissions [get]
func GetRolePermissions(c *gin.Context) {
	logger := utils.GetLogger()
	
	roleID := c.Param("id")
	var perms []string
	database.DB.
		Table("permissions").
		Select("permissions.name").
		Joins("join role_permissions on role_permissions.permission_id = permissions.id").
		Where("role_permissions.role_id = ?", roleID).
		Scan(&perms)
	
	logger.Info("Role permissions retrieved", 
		zap.String("role_id", roleID),
		zap.Int("permission_count", len(perms)),
	)
	c.JSON(http.StatusOK, perms)
}

// @Summary Gán permission cho role
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param data body AssignPermissionRequest true "Thông tin gán permission"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/admin/assign-permission [post]
func AssignPermission(c *gin.Context) {
	logger := utils.GetLogger()
	
	var req AssignPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid assign permission request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("Permission assignment attempt", 
		zap.Uint("role_id", req.RoleID),
		zap.String("permission", req.Permission),
	)

	var perm models.Permission
	if err := database.DB.Where("name = ?", req.Permission).First(&perm).Error; err != nil {
		logger.Warn("Permission assignment failed - permission not found", zap.String("permission", req.Permission))
		c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
		return
	}
	var count int64
	database.DB.Model(&models.RolePermission{}).Where("role_id = ? AND permission_id = ?", req.RoleID, perm.ID).Count(&count)
	if count > 0 {
		logger.Warn("Permission assignment failed - role already has permission", 
			zap.Uint("role_id", req.RoleID),
			zap.String("permission", req.Permission),
		)
		c.JSON(http.StatusConflict, gin.H{"error": "Role already has this permission"})
		return
	}
	if err := database.DB.Create(&models.RolePermission{RoleID: req.RoleID, PermissionID: perm.ID}).Error; err != nil {
		logger.Error("Failed to assign permission in database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign permission"})
		return
	}

	logger.Info("Permission assigned successfully", 
		zap.Uint("role_id", req.RoleID),
		zap.String("permission", req.Permission),
		zap.Uint("permission_id", perm.ID),
	)

	c.JSON(http.StatusOK, gin.H{"message": "Permission assigned successfully"})
}

// @Summary Xóa permission khỏi role
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param data body RemovePermissionRequest true "Thông tin xóa permission"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/admin/remove-permission [post]
func RemovePermission(c *gin.Context) {
	logger := utils.GetLogger()
	
	var req RemovePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid remove permission request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("Permission removal attempt", 
		zap.Uint("role_id", req.RoleID),
		zap.String("permission", req.Permission),
	)

	var perm models.Permission
	if err := database.DB.Where("name = ?", req.Permission).First(&perm).Error; err != nil {
		logger.Warn("Permission removal failed - permission not found", zap.String("permission", req.Permission))
		c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
		return
	}
	if err := database.DB.Where("role_id = ? AND permission_id = ?", req.RoleID, perm.ID).Delete(&models.RolePermission{}).Error; err != nil {
		logger.Error("Failed to remove permission from database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove permission"})
		return
	}

	logger.Info("Permission removed successfully", 
		zap.Uint("role_id", req.RoleID),
		zap.String("permission", req.Permission),
		zap.Uint("permission_id", perm.ID),
	)

	c.JSON(http.StatusOK, gin.H{"message": "Permission removed successfully"})
}

type AssignUserPermissionRequest struct {
	UserID     uint   `json:"user_id" binding:"required"`
	Permission string `json:"permission" binding:"required"`
}

type RemoveUserPermissionRequest struct {
	UserID     uint   `json:"user_id" binding:"required"`
	Permission string `json:"permission" binding:"required"`
}

// @Summary Lấy direct permission của user
// @Tags Admin
// @Security BearerAuth
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {array} string
// @Router /api/v1/admin/user/{id}/direct-permissions [get]
func GetUserDirectPermissions(c *gin.Context) {
	logger := utils.GetLogger()
	
	userID := c.Param("id")
	var perms []string
	database.DB.
		Table("permissions").
		Select("permissions.name").
		Joins("join user_permissions on user_permissions.permission_id = permissions.id").
		Where("user_permissions.user_id = ?", userID).
		Scan(&perms)
	
	logger.Info("User direct permissions retrieved", 
		zap.String("user_id", userID),
		zap.Int("permission_count", len(perms)),
	)
	c.JSON(http.StatusOK, perms)
}

// @Summary Gán permission trực tiếp cho user
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param data body AssignUserPermissionRequest true "Thông tin gán permission"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/admin/assign-user-permission [post]
func AssignUserPermission(c *gin.Context) {
	logger := utils.GetLogger()
	
	var req AssignUserPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid assign user permission request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("User permission assignment attempt", 
		zap.Uint("user_id", req.UserID),
		zap.String("permission", req.Permission),
	)

	var perm models.Permission
	if err := database.DB.Where("name = ?", req.Permission).First(&perm).Error; err != nil {
		logger.Warn("User permission assignment failed - permission not found", zap.String("permission", req.Permission))
		c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
		return
	}

	var count int64
	database.DB.Model(&models.UserPermission{}).Where("user_id = ? AND permission_id = ?", req.UserID, perm.ID).Count(&count)
	if count > 0 {
		logger.Warn("User permission assignment failed - user already has permission", 
			zap.Uint("user_id", req.UserID),
			zap.String("permission", req.Permission),
		)
		c.JSON(http.StatusConflict, gin.H{"error": "User already has this permission"})
		return
	}

	if err := database.DB.Create(&models.UserPermission{UserID: req.UserID, PermissionID: perm.ID}).Error; err != nil {
		logger.Error("Failed to assign user permission in database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign permission"})
		return
	}

	logger.Info("User permission assigned successfully", 
		zap.Uint("user_id", req.UserID),
		zap.String("permission", req.Permission),
		zap.Uint("permission_id", perm.ID),
	)

	c.JSON(http.StatusOK, gin.H{"message": "Permission assigned successfully"})
}

// @Summary Xóa permission trực tiếp khỏi user
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param data body RemoveUserPermissionRequest true "Thông tin xóa permission"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/admin/remove-user-permission [post]
func RemoveUserPermission(c *gin.Context) {
	logger := utils.GetLogger()
	
	var req RemoveUserPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid remove user permission request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("User permission removal attempt", 
		zap.Uint("user_id", req.UserID),
		zap.String("permission", req.Permission),
	)

	var perm models.Permission
	if err := database.DB.Where("name = ?", req.Permission).First(&perm).Error; err != nil {
		logger.Warn("User permission removal failed - permission not found", zap.String("permission", req.Permission))
		c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
		return
	}

	if err := database.DB.Where("user_id = ? AND permission_id = ?", req.UserID, perm.ID).Delete(&models.UserPermission{}).Error; err != nil {
		logger.Error("Failed to remove user permission from database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove permission"})
		return
	}

	logger.Info("User permission removed successfully", 
		zap.Uint("user_id", req.UserID),
		zap.String("permission", req.Permission),
		zap.Uint("permission_id", perm.ID),
	)

	c.JSON(http.StatusOK, gin.H{"message": "Permission removed successfully"})
} 