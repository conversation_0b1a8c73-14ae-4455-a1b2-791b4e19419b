{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\AssignmentsTab.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AssignmentsTab = ({\n  users,\n  roles,\n  onMessage,\n  onError\n}) => {\n  _s();\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [userRoleNames, setUserRoleNames] = useState([]);\n  const [userPermissionNames, setUserPermissionNames] = useState([]);\n  const [availableRoles, setAvailableRoles] = useState([]);\n  const [availablePermissions, setAvailablePermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [assignmentType, setAssignmentType] = useState('roles');\n\n  // Load user details when selected\n  useEffect(() => {\n    if (selectedUser) {\n      loadUserDetails();\n    }\n  }, [selectedUser]);\n  const loadUserDetails = async () => {\n    if (!selectedUser) return;\n    setLoading(true);\n    try {\n      // Load user roles (returns string[])\n      const rolesResponse = await adminAPI.getUserRoles(selectedUser.id);\n      setUserRoleNames(rolesResponse.data);\n\n      // Load user permissions (returns string[])\n      const permissionsResponse = await adminAPI.getUserPermissions(selectedUser.id);\n      setUserPermissionNames(permissionsResponse.data);\n\n      // Set available roles (roles not assigned to user)\n      const assignedRoleNames = rolesResponse.data;\n      setAvailableRoles(roles.filter(role => !assignedRoleNames.includes(role.name) && role.is_active));\n\n      // Load all permissions for available permissions list\n      // Note: This would need to be implemented in the backend to get all permissions\n      // For now, we'll use an empty array\n      setAvailablePermissions([]);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      onError(`Failed to load user details: ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAssignRole = async roleName => {\n    if (!selectedUser) return;\n    try {\n      await adminAPI.assignRole(selectedUser.id, roleName);\n      onMessage('Role assigned successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      onError(`Failed to assign role: ${((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n    }\n  };\n  const handleRemoveRole = async roleName => {\n    if (!selectedUser) return;\n    try {\n      await adminAPI.removeRole(selectedUser.id, roleName);\n      onMessage('Role removed successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      onError(`Failed to remove role: ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message}`);\n    }\n  };\n  const handleAssignPermission = async permissionName => {\n    if (!selectedUser) return;\n    try {\n      // Note: This would need a different API endpoint for direct user permission assignment\n      // For now, we'll show an error message\n      onError('Direct permission assignment not yet implemented');\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      onError(`Failed to assign permission: ${((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n    }\n  };\n  const handleRemovePermission = async permissionName => {\n    if (!selectedUser) return;\n    try {\n      // Note: This would need a different API endpoint for direct user permission removal\n      // For now, we'll show an error message\n      onError('Direct permission removal not yet implemented');\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      onError(`Failed to remove permission: ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || err.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold mb-6\",\n      children: \"User Role & Permission Assignments\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium mb-4\",\n          children: \"Select User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 max-h-96 overflow-y-auto\",\n          children: users.map(user => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedUser(user),\n            className: `w-full text-left p-3 rounded border ${(selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.id) === user.id ? 'bg-blue-100 border-blue-500' : 'bg-gray-50 border-gray-200 hover:bg-gray-100'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium\",\n              children: user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)]\n          }, user.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), selectedUser && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 rounded-lg border\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium\",\n              children: [\"Managing: \", selectedUser.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex bg-gray-100 rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setAssignmentType('roles'),\n                className: `px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'roles' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                children: \"Roles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setAssignmentType('permissions'),\n                className: `px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'permissions' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                children: \"Permissions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium mb-3\",\n                children: [\"Assigned \", assignmentType === 'roles' ? 'Roles' : 'Permissions']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                children: assignmentType === 'roles' ? userRoleNames.length > 0 ? userRoleNames.map(roleName => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-green-800\",\n                      children: roleName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRemoveRole(roleName),\n                    className: \"text-red-600 hover:text-red-800 text-sm\",\n                    children: \"Remove\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 31\n                  }, this)]\n                }, roleName, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 29\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"No roles assigned\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 27\n                }, this) : userPermissionNames.length > 0 ? userPermissionNames.map(permissionName => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-green-800\",\n                      children: permissionName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRemovePermission(permissionName),\n                    className: \"text-red-600 hover:text-red-800 text-sm\",\n                    children: \"Remove\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 31\n                  }, this)]\n                }, permissionName, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 29\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"No permissions assigned\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium mb-3\",\n                children: [\"Available \", assignmentType === 'roles' ? 'Roles' : 'Permissions']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                children: assignmentType === 'roles' ? availableRoles.length > 0 ? availableRoles.map(role => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium\",\n                      children: role.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 33\n                    }, this), role.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: role.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleAssignRole(role.name),\n                    className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                    children: \"Assign\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 31\n                  }, this)]\n                }, role.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 29\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"No available roles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"Permission assignment feature coming soon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), !selectedUser && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Select a user to manage their roles and permissions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(AssignmentsTab, \"GAwTYIYVR6urtXGWOIu0LOgEN3c=\");\n_c = AssignmentsTab;\nexport default AssignmentsTab;\nvar _c;\n$RefreshReg$(_c, \"AssignmentsTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AssignmentsTab", "users", "roles", "onMessage", "onError", "_s", "selected<PERSON>ser", "setSelectedUser", "userRoleNames", "setUserRoleNames", "userPermissionNames", "setUserPermissionNames", "availableRoles", "setAvailableRoles", "availablePermissions", "setAvailablePermissions", "loading", "setLoading", "assignmentType", "setAssignmentType", "loadUserDetails", "rolesResponse", "adminAPI", "getUserRoles", "id", "data", "permissionsResponse", "getUserPermissions", "assignedRoleNames", "filter", "role", "includes", "name", "is_active", "err", "_err$response", "_err$response$data", "response", "error", "message", "handleAssignRole", "<PERSON><PERSON><PERSON>", "assignRole", "_err$response2", "_err$response2$data", "handleRemoveRole", "removeRole", "_err$response3", "_err$response3$data", "handleAssignPermission", "permissionName", "_err$response4", "_err$response4$data", "handleRemovePermission", "_err$response5", "_err$response5$data", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "user", "onClick", "username", "email", "length", "description", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/AssignmentsTab.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport { adminService } from '../../services';\n\ninterface AssignmentsTabProps {\n  users: User[];\n  roles: Role[];\n  onMessage: (message: string) => void;\n  onError: (error: string) => void;\n}\n\nconst AssignmentsTab: React.FC<AssignmentsTabProps> = ({ users, roles, onMessage, onError }) => {\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [userRoleNames, setUserRoleNames] = useState<string[]>([]);\n  const [userPermissionNames, setUserPermissionNames] = useState<string[]>([]);\n  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);\n  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [assignmentType, setAssignmentType] = useState<'roles' | 'permissions'>('roles');\n\n  // Load user details when selected\n  useEffect(() => {\n    if (selectedUser) {\n      loadUserDetails();\n    }\n  }, [selectedUser]);\n\n  const loadUserDetails = async () => {\n    if (!selectedUser) return;\n\n    setLoading(true);\n    try {\n      // Load user roles (returns string[])\n      const rolesResponse = await adminAPI.getUserRoles(selectedUser.id);\n      setUserRoleNames(rolesResponse.data);\n\n      // Load user permissions (returns string[])\n      const permissionsResponse = await adminAPI.getUserPermissions(selectedUser.id);\n      setUserPermissionNames(permissionsResponse.data);\n\n      // Set available roles (roles not assigned to user)\n      const assignedRoleNames = rolesResponse.data;\n      setAvailableRoles(roles.filter(role => !assignedRoleNames.includes(role.name) && role.is_active));\n\n      // Load all permissions for available permissions list\n      // Note: This would need to be implemented in the backend to get all permissions\n      // For now, we'll use an empty array\n      setAvailablePermissions([]);\n\n    } catch (err: any) {\n      onError(`Failed to load user details: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssignRole = async (roleName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.assignRole(selectedUser.id, roleName);\n      onMessage('Role assigned successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to assign role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleRemoveRole = async (roleName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.removeRole(selectedUser.id, roleName);\n      onMessage('Role removed successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to remove role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleAssignPermission = async (permissionName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      // Note: This would need a different API endpoint for direct user permission assignment\n      // For now, we'll show an error message\n      onError('Direct permission assignment not yet implemented');\n    } catch (err: any) {\n      onError(`Failed to assign permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleRemovePermission = async (permissionName: string) => {\n    if (!selectedUser) return;\n\n    try {\n      // Note: This would need a different API endpoint for direct user permission removal\n      // For now, we'll show an error message\n      onError('Direct permission removal not yet implemented');\n    } catch (err: any) {\n      onError(`Failed to remove permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  return (\n    <div>\n      <h2 className=\"text-xl font-semibold mb-6\">User Role & Permission Assignments</h2>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* User Selection */}\n        <div className=\"bg-white p-4 rounded-lg border\">\n          <h3 className=\"text-lg font-medium mb-4\">Select User</h3>\n          <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n            {users.map((user) => (\n              <button\n                key={user.id}\n                onClick={() => setSelectedUser(user)}\n                className={`w-full text-left p-3 rounded border ${selectedUser?.id === user.id\n                  ? 'bg-blue-100 border-blue-500'\n                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'\n                  }`}\n              >\n                <div className=\"font-medium\">{user.username}</div>\n                <div className=\"text-sm text-gray-600\">{user.email}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Assignment Type Toggle */}\n        {selectedUser && (\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white p-4 rounded-lg border\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium\">\n                  Managing: {selectedUser.username}\n                </h3>\n                <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                  <button\n                    onClick={() => setAssignmentType('roles')}\n                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'roles'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                      }`}\n                  >\n                    Roles\n                  </button>\n                  <button\n                    onClick={() => setAssignmentType('permissions')}\n                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'permissions'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                      }`}\n                  >\n                    Permissions\n                  </button>\n                </div>\n              </div>\n\n              {loading ? (\n                <div className=\"text-center py-8\">Loading...</div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {/* Assigned Items */}\n                  <div>\n                    <h4 className=\"font-medium mb-3\">\n                      Assigned {assignmentType === 'roles' ? 'Roles' : 'Permissions'}\n                    </h4>\n                    <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                      {assignmentType === 'roles' ? (\n                        userRoleNames.length > 0 ? (\n                          userRoleNames.map((roleName) => (\n                            <div key={roleName} className=\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\">\n                              <div>\n                                <div className=\"font-medium text-green-800\">{roleName}</div>\n                              </div>\n                              <button\n                                onClick={() => handleRemoveRole(roleName)}\n                                className=\"text-red-600 hover:text-red-800 text-sm\"\n                              >\n                                Remove\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No roles assigned</div>\n                        )\n                      ) : (\n                        userPermissionNames.length > 0 ? (\n                          userPermissionNames.map((permissionName) => (\n                            <div key={permissionName} className=\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\">\n                              <div>\n                                <div className=\"font-medium text-green-800\">{permissionName}</div>\n                              </div>\n                              <button\n                                onClick={() => handleRemovePermission(permissionName)}\n                                className=\"text-red-600 hover:text-red-800 text-sm\"\n                              >\n                                Remove\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No permissions assigned</div>\n                        )\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Available Items */}\n                  <div>\n                    <h4 className=\"font-medium mb-3\">\n                      Available {assignmentType === 'roles' ? 'Roles' : 'Permissions'}\n                    </h4>\n                    <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                      {assignmentType === 'roles' ? (\n                        availableRoles.length > 0 ? (\n                          availableRoles.map((role) => (\n                            <div key={role.id} className=\"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded\">\n                              <div>\n                                <div className=\"font-medium\">{role.name}</div>\n                                {role.description && (\n                                  <div className=\"text-sm text-gray-600\">{role.description}</div>\n                                )}\n                              </div>\n                              <button\n                                onClick={() => handleAssignRole(role.name)}\n                                className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                              >\n                                Assign\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No available roles</div>\n                        )\n                      ) : (\n                        <div className=\"text-gray-500 text-center py-4\">\n                          Permission assignment feature coming soon\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {!selectedUser && (\n        <div className=\"text-center py-12 text-gray-500\">\n          <p>Select a user to manage their roles and permissions</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AssignmentsTab;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWnD,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC9F,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAACc,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGf,QAAQ,CAAW,EAAE,CAAC;EAC5E,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACkB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnB,QAAQ,CAAe,EAAE,CAAC;EAClF,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAA0B,OAAO,CAAC;;EAEtF;EACAC,SAAS,CAAC,MAAM;IACd,IAAIS,YAAY,EAAE;MAChBc,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACd,YAAY,CAAC,CAAC;EAElB,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACd,YAAY,EAAE;IAEnBW,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMI,aAAa,GAAG,MAAMC,QAAQ,CAACC,YAAY,CAACjB,YAAY,CAACkB,EAAE,CAAC;MAClEf,gBAAgB,CAACY,aAAa,CAACI,IAAI,CAAC;;MAEpC;MACA,MAAMC,mBAAmB,GAAG,MAAMJ,QAAQ,CAACK,kBAAkB,CAACrB,YAAY,CAACkB,EAAE,CAAC;MAC9Eb,sBAAsB,CAACe,mBAAmB,CAACD,IAAI,CAAC;;MAEhD;MACA,MAAMG,iBAAiB,GAAGP,aAAa,CAACI,IAAI;MAC5CZ,iBAAiB,CAACX,KAAK,CAAC2B,MAAM,CAACC,IAAI,IAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,IAAIF,IAAI,CAACG,SAAS,CAAC,CAAC;;MAEjG;MACA;MACA;MACAlB,uBAAuB,CAAC,EAAE,CAAC;IAE7B,CAAC,CAAC,OAAOmB,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBhC,OAAO,CAAC,gCAAgC,EAAA+B,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcV,IAAI,cAAAW,kBAAA,uBAAlBA,kBAAA,CAAoBE,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IACrF,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,gBAAgB,GAAG,MAAOC,QAAgB,IAAK;IACnD,IAAI,CAACnC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMgB,QAAQ,CAACoB,UAAU,CAACpC,YAAY,CAACkB,EAAE,EAAEiB,QAAQ,CAAC;MACpDtC,SAAS,CAAC,4BAA4B,CAAC;MACvCiB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOc,GAAQ,EAAE;MAAA,IAAAS,cAAA,EAAAC,mBAAA;MACjBxC,OAAO,CAAC,0BAA0B,EAAAuC,cAAA,GAAAT,GAAG,CAACG,QAAQ,cAAAM,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAclB,IAAI,cAAAmB,mBAAA,uBAAlBA,mBAAA,CAAoBN,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IAC/E;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAG,MAAOJ,QAAgB,IAAK;IACnD,IAAI,CAACnC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMgB,QAAQ,CAACwB,UAAU,CAACxC,YAAY,CAACkB,EAAE,EAAEiB,QAAQ,CAAC;MACpDtC,SAAS,CAAC,2BAA2B,CAAC;MACtCiB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOc,GAAQ,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACjB5C,OAAO,CAAC,0BAA0B,EAAA2C,cAAA,GAAAb,GAAG,CAACG,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAActB,IAAI,cAAAuB,mBAAA,uBAAlBA,mBAAA,CAAoBV,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IAC/E;EACF,CAAC;EAED,MAAMU,sBAAsB,GAAG,MAAOC,cAAsB,IAAK;IAC/D,IAAI,CAAC5C,YAAY,EAAE;IAEnB,IAAI;MACF;MACA;MACAF,OAAO,CAAC,kDAAkD,CAAC;IAC7D,CAAC,CAAC,OAAO8B,GAAQ,EAAE;MAAA,IAAAiB,cAAA,EAAAC,mBAAA;MACjBhD,OAAO,CAAC,gCAAgC,EAAA+C,cAAA,GAAAjB,GAAG,CAACG,QAAQ,cAAAc,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc1B,IAAI,cAAA2B,mBAAA,uBAAlBA,mBAAA,CAAoBd,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IACrF;EACF,CAAC;EAED,MAAMc,sBAAsB,GAAG,MAAOH,cAAsB,IAAK;IAC/D,IAAI,CAAC5C,YAAY,EAAE;IAEnB,IAAI;MACF;MACA;MACAF,OAAO,CAAC,+CAA+C,CAAC;IAC1D,CAAC,CAAC,OAAO8B,GAAQ,EAAE;MAAA,IAAAoB,cAAA,EAAAC,mBAAA;MACjBnD,OAAO,CAAC,gCAAgC,EAAAkD,cAAA,GAAApB,GAAG,CAACG,QAAQ,cAAAiB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7B,IAAI,cAAA8B,mBAAA,uBAAlBA,mBAAA,CAAoBjB,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IACrF;EACF,CAAC;EAED,oBACExC,OAAA;IAAAyD,QAAA,gBACEzD,OAAA;MAAI0D,SAAS,EAAC,4BAA4B;MAAAD,QAAA,EAAC;IAAkC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAElF9D,OAAA;MAAK0D,SAAS,EAAC,uCAAuC;MAAAD,QAAA,gBAEpDzD,OAAA;QAAK0D,SAAS,EAAC,gCAAgC;QAAAD,QAAA,gBAC7CzD,OAAA;UAAI0D,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzD9D,OAAA;UAAK0D,SAAS,EAAC,oCAAoC;UAAAD,QAAA,EAChDvD,KAAK,CAAC6D,GAAG,CAAEC,IAAI,iBACdhE,OAAA;YAEEiE,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACwD,IAAI,CAAE;YACrCN,SAAS,EAAE,uCAAuC,CAAAnD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,EAAE,MAAKuC,IAAI,CAACvC,EAAE,GAC1E,6BAA6B,GAC7B,8CAA8C,EAC7C;YAAAgC,QAAA,gBAELzD,OAAA;cAAK0D,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAEO,IAAI,CAACE;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD9D,OAAA;cAAK0D,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAEO,IAAI,CAACG;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GARpDE,IAAI,CAACvC,EAAE;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASN,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLvD,YAAY,iBACXP,OAAA;QAAK0D,SAAS,EAAC,eAAe;QAAAD,QAAA,eAC5BzD,OAAA;UAAK0D,SAAS,EAAC,gCAAgC;UAAAD,QAAA,gBAC7CzD,OAAA;YAAK0D,SAAS,EAAC,wCAAwC;YAAAD,QAAA,gBACrDzD,OAAA;cAAI0D,SAAS,EAAC,qBAAqB;cAAAD,QAAA,GAAC,YACxB,EAAClD,YAAY,CAAC2D,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACL9D,OAAA;cAAK0D,SAAS,EAAC,iCAAiC;cAAAD,QAAA,gBAC9CzD,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAM7C,iBAAiB,CAAC,OAAO,CAAE;gBAC1CsC,SAAS,EAAE,4CAA4CvC,cAAc,KAAK,OAAO,GAC7E,kCAAkC,GAClC,mCAAmC,EAClC;gBAAAsC,QAAA,EACN;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9D,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAM7C,iBAAiB,CAAC,aAAa,CAAE;gBAChDsC,SAAS,EAAE,4CAA4CvC,cAAc,KAAK,aAAa,GACnF,kCAAkC,GAClC,mCAAmC,EAClC;gBAAAsC,QAAA,EACN;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL7C,OAAO,gBACNjB,OAAA;YAAK0D,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAElD9D,OAAA;YAAK0D,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBAEpDzD,OAAA;cAAAyD,QAAA,gBACEzD,OAAA;gBAAI0D,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,GAAC,WACtB,EAACtC,cAAc,KAAK,OAAO,GAAG,OAAO,GAAG,aAAa;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACL9D,OAAA;gBAAK0D,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,EAChDtC,cAAc,KAAK,OAAO,GACzBV,aAAa,CAAC2D,MAAM,GAAG,CAAC,GACtB3D,aAAa,CAACsD,GAAG,CAAErB,QAAQ,iBACzB1C,OAAA;kBAAoB0D,SAAS,EAAC,mFAAmF;kBAAAD,QAAA,gBAC/GzD,OAAA;oBAAAyD,QAAA,eACEzD,OAAA;sBAAK0D,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EAAEf;oBAAQ;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN9D,OAAA;oBACEiE,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAACJ,QAAQ,CAAE;oBAC1CgB,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,EACpD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GATDpB,QAAQ;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUb,CACN,CAAC,gBAEF9D,OAAA;kBAAK0D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACvE,GAEDnD,mBAAmB,CAACyD,MAAM,GAAG,CAAC,GAC5BzD,mBAAmB,CAACoD,GAAG,CAAEZ,cAAc,iBACrCnD,OAAA;kBAA0B0D,SAAS,EAAC,mFAAmF;kBAAAD,QAAA,gBACrHzD,OAAA;oBAAAyD,QAAA,eACEzD,OAAA;sBAAK0D,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EAAEN;oBAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACN9D,OAAA;oBACEiE,OAAO,EAAEA,CAAA,KAAMX,sBAAsB,CAACH,cAAc,CAAE;oBACtDO,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,EACpD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GATDX,cAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUnB,CACN,CAAC,gBAEF9D,OAAA;kBAAK0D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAE/E;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9D,OAAA;cAAAyD,QAAA,gBACEzD,OAAA;gBAAI0D,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,GAAC,YACrB,EAACtC,cAAc,KAAK,OAAO,GAAG,OAAO,GAAG,aAAa;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACL9D,OAAA;gBAAK0D,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,EAChDtC,cAAc,KAAK,OAAO,GACzBN,cAAc,CAACuD,MAAM,GAAG,CAAC,GACvBvD,cAAc,CAACkD,GAAG,CAAEhC,IAAI,iBACtB/B,OAAA;kBAAmB0D,SAAS,EAAC,iFAAiF;kBAAAD,QAAA,gBAC5GzD,OAAA;oBAAAyD,QAAA,gBACEzD,OAAA;sBAAK0D,SAAS,EAAC,aAAa;sBAAAD,QAAA,EAAE1B,IAAI,CAACE;oBAAI;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC7C/B,IAAI,CAACsC,WAAW,iBACfrE,OAAA;sBAAK0D,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAE1B,IAAI,CAACsC;oBAAW;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC/D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN9D,OAAA;oBACEiE,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAACV,IAAI,CAACE,IAAI,CAAE;oBAC3CyB,SAAS,EAAC,2CAA2C;oBAAAD,QAAA,EACtD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GAZD/B,IAAI,CAACN,EAAE;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAaZ,CACN,CAAC,gBAEF9D,OAAA;kBAAK0D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACxE,gBAED9D,OAAA;kBAAK0D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAEhD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACvD,YAAY,iBACZP,OAAA;MAAK0D,SAAS,EAAC,iCAAiC;MAAAD,QAAA,eAC9CzD,OAAA;QAAAyD,QAAA,EAAG;MAAmD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxD,EAAA,CAtPIL,cAA6C;AAAAqE,EAAA,GAA7CrE,cAA6C;AAwPnD,eAAeA,cAAc;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}