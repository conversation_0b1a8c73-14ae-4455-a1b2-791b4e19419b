package middleware

import (
	"net/http"
	"strings"

	"jwt-auth-backend/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := utils.GetLogger()
		
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			logger.Warn("Authentication failed - missing authorization header",
				zap.String("ip", c.ClientIP()),
				zap.String("path", c.Request.URL.Path),
			)
			c.<PERSON><PERSON>(http.StatusUnauthorized, gin.H{"error": "Authorization header is required"})
			c.Abort()
			return
		}

		// Check if the header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			logger.Warn("Authentication failed - invalid authorization header format",
				zap.String("ip", c.ClientIP()),
				zap.String("path", c.Request.URL.Path),
			)
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		// Extract the token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		// Kiểm tra token có bị blacklist không
		if utils.IsAccessTokenBlacklisted(tokenString) {
			logger.Warn("Access token is blacklisted",
				zap.String("ip", c.ClientIP()),
				zap.String("path", c.Request.URL.Path),
			)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token revoked"})
			c.Abort()
			return
		}

		// Validate the token
		userID, username, err := utils.ExtractUserFromToken(tokenString)
		if err != nil {
			logger.Warn("Authentication failed - invalid or expired token",
				zap.String("ip", c.ClientIP()),
				zap.String("path", c.Request.URL.Path),
				zap.Error(err),
			)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", userID)
		c.Set("username", username)

		logger.Debug("User authenticated successfully",
			zap.Uint("user_id", userID),
			zap.String("username", username),
			zap.String("ip", c.ClientIP()),
			zap.String("path", c.Request.URL.Path),
		)

		c.Next()
	}
} 