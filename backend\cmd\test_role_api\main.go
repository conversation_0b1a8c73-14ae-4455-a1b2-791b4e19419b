package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/routes"
	"jwt-auth-backend/utils"
	"log"
	"net/http"
	"net/http/httptest"
	"strings"

	"github.com/gin-gonic/gin"
)

func main() {
	fmt.Println("🧪 Testing Role CRUD API")
	fmt.Println(strings.Repeat("=", 50))

	// Initialize logger first
	utils.InitLogger()

	// Initialize database
	database.InitDB()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Setup routes
	r := routes.SetupRoutes()

	// Get admin token for authentication
	adminToken := getAdminToken(r)
	if adminToken == "" {
		log.Fatal("Failed to get admin token")
	}

	fmt.Printf("✅ Admin token obtained: %s...\n", adminToken[:20])

	// Run tests
	testCreateRole(r, adminToken)
	testListRoles(r, adminToken)
	testGetRole(r, adminToken)
	testUpdateRole(r, adminToken)
	testSearchRoles(r, adminToken)
	testPaginationRoles(r, adminToken)
	testRolePermissions(r, adminToken)
	testDeleteRole(r, adminToken)

	fmt.Println("\n✅ All Role API tests completed successfully!")
}

func getAdminToken(r *gin.Engine) string {
	// Create admin user if not exists
	adminUser := models.User{
		Username: "admin_test",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	hashedPassword, _ := utils.HashPassword(adminUser.Password)
	adminUser.Password = hashedPassword

	// Try to create admin user (ignore if exists)
	database.DB.FirstOrCreate(&adminUser, models.User{Email: adminUser.Email})

	// Assign admin role
	var adminRole models.Role
	database.DB.Where("name = ?", "admin").First(&adminRole)
	database.DB.FirstOrCreate(&models.UserRole{}, models.UserRole{
		UserID: adminUser.ID,
		RoleID: adminRole.ID,
	})

	// Login to get token
	loginReq := models.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	reqBody, _ := json.Marshal(loginReq)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Printf("Login failed with status %d: %s", w.Code, w.Body.String())
		return ""
	}

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	return response["token"].(string)
}

func testCreateRole(r *gin.Engine, token string) {
	fmt.Println("\n📝 Testing Create Role...")

	createReq := models.RoleCreateRequest{
		Name:        "test_role",
		Description: "Test role for API testing",
		IsActive:    true,
	}

	reqBody, _ := json.Marshal(createReq)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/admin/roles", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 201 {
		log.Fatalf("Expected status 201, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	fmt.Printf("✅ Role created: %v\n", response.Data)
}

func testListRoles(r *gin.Engine, token string) {
	fmt.Println("\n📋 Testing List Roles...")

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/admin/roles?page=1&page_size=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDListResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	fmt.Printf("✅ Roles listed. Total: %d, Page: %d\n",
		response.Pagination.Total, response.Pagination.Page)
}

func testGetRole(r *gin.Engine, token string) {
	fmt.Println("\n🔍 Testing Get Role...")

	// Get role ID from database
	var role models.Role
	database.DB.Where("name = ?", "test_role").First(&role)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/admin/roles/%d", role.ID), nil)
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	fmt.Printf("✅ Role retrieved: %v\n", response.Data)
}

func testUpdateRole(r *gin.Engine, token string) {
	fmt.Println("\n✏️ Testing Update Role...")

	// Get role ID from database
	var role models.Role
	database.DB.Where("name = ?", "test_role").First(&role)

	isActive := false
	updateReq := models.RoleUpdateRequest{
		Name:        "updated_test_role",
		Description: "Updated test role description",
		IsActive:    &isActive,
	}

	reqBody, _ := json.Marshal(updateReq)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/admin/roles/%d", role.ID), bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	fmt.Printf("✅ Role updated: %v\n", response.Data)
}

func testSearchRoles(r *gin.Engine, token string) {
	fmt.Println("\n🔍 Testing Search Roles...")

	// Test search by name
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/admin/roles?search_filters[0][field]=name&search_filters[0][operator]=like&search_filters[0][value]=updated", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDListResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	fmt.Printf("✅ Search results: %d roles found\n", response.Pagination.Total)
}

func testPaginationRoles(r *gin.Engine, token string) {
	fmt.Println("\n📄 Testing Pagination...")

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/admin/roles?page=1&page_size=2", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDListResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	fmt.Printf("✅ Pagination test: Page %d of %d, showing %d items\n",
		response.Pagination.Page, response.Pagination.TotalPages, len(response.Data.([]interface{})))
}

func testRolePermissions(r *gin.Engine, token string) {
	fmt.Println("\n🔐 Testing Role Permissions...")

	// Get role and permission IDs
	var role models.Role
	var permission models.Permission
	database.DB.Where("name = ?", "updated_test_role").First(&role)
	database.DB.Where("name = ?", "assign_role").First(&permission)

	// Test assign permission
	assignReq := map[string]uint{"permission_id": permission.ID}
	reqBody, _ := json.Marshal(assignReq)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/admin/roles/%d/permissions", role.ID), bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}
	fmt.Printf("✅ Permission assigned to role\n")

	// Test remove permission
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("DELETE", fmt.Sprintf("/api/v1/admin/roles/%d/permissions/%d", role.ID, permission.ID), nil)
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}
	fmt.Printf("✅ Permission removed from role\n")
}

func testDeleteRole(r *gin.Engine, token string) {
	fmt.Println("\n🗑️ Testing Delete Role...")

	// Get role ID from database
	var role models.Role
	database.DB.Where("name = ?", "updated_test_role").First(&role)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/admin/roles/%d", role.ID), nil)
	req.Header.Set("Authorization", "Bearer "+token)
	r.ServeHTTP(w, req)

	if w.Code != 200 {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	fmt.Printf("✅ Role deleted: %s\n", response.Message)
}
