// Default imports for convenience
import authService from './auth.service';
import userService from './user.service';
import roleService from './role.service';
import permissionService from './permission.service';
import adminService from './admin.service';

// Export all services
export { authService } from './auth.service';
export { userService } from './user.service';
export { roleService } from './role.service';
export { permissionService } from './permission.service';
export { adminService } from './admin.service';

// Export base service and types
export { BaseCRUDService, api } from './base.service';
export type {
  PaginationInfo,
  CRUDListResponse,
  CRUDResponse,
  SearchRequest,
  ErrorResponse
} from './base.service';

// Export service-specific types
export type {
  LoginRequest,
  RegisterRequest
} from './auth.service';

export type {
  CreateUserRequest,
  UpdateUserRequest,
  UserWithRoles
} from './user.service';

export type {
  CreateRoleRequest,
  UpdateRoleRequest
} from './role.service';

export type {
  CreatePermissionRequest,
  UpdatePermissionRequest
} from './permission.service';

export type {
  DashboardStats,
  AssignRoleRequest,
  RemoveRoleRequest,
  AssignPermissionRequest,
  RemovePermissionRequest,
  AssignUserPermissionRequest,
  RemoveUserPermissionRequest
} from './admin.service';

// Default export object
export default {
  auth: authService,
  user: userService,
  role: roleService,
  permission: permissionService,
  admin: adminService
};
