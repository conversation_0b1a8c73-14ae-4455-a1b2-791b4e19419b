{"ast": null, "code": "import{BaseCRUDService,api}from'./base.service';class RoleService extends BaseCRUDService{constructor(){super('/admin/role-management');}// Get all roles (from old admin API)\ngetAllRoles(){return api.get('/admin/roles');}// Get active roles only\ngetActiveRoles(){return this.list({active_only:true});}// Get role permissions\ngetRolePermissions(roleId){return api.get(\"/admin/role/\".concat(roleId,\"/permissions\"));}// Assign permission to role\nassignPermission(roleId,permissionName){return api.post('/admin/assign-permission',{role_id:roleId,permission:permissionName});}// Remove permission from role\nremovePermission(roleId,permissionName){return api.post('/admin/remove-permission',{role_id:roleId,permission:permissionName});}// Assign permission to role using CRUD API\nassignPermissionCRUD(roleId,permissionId){return api.post(\"\".concat(this.baseUrl,\"/\").concat(roleId,\"/permissions\"),{permission_id:permissionId});}// Remove permission from role using CRUD API\nremovePermissionCRUD(roleId,permissionId){return api.delete(\"\".concat(this.baseUrl,\"/\").concat(roleId,\"/permissions/\").concat(permissionId));}// Search roles with custom filters\nsearchRoles(searchTerm){let page=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;let pageSize=arguments.length>2&&arguments[2]!==undefined?arguments[2]:10;return this.search({page,page_size:pageSize,search_term:searchTerm,filters:{is_active:true}});}// Get roles by name pattern\ngetRolesByName(namePattern){return this.list({page:1,page_size:100,name_like:namePattern});}// Toggle role active status\ntoggleActiveStatus(roleId,isActive){return this.update(roleId,{is_active:isActive});}// Bulk operations\nbulkDelete(roleIds){return api.post(\"\".concat(this.baseUrl,\"/bulk-delete\"),{ids:roleIds});}bulkToggleStatus(roleIds,isActive){return api.post(\"\".concat(this.baseUrl,\"/bulk-toggle-status\"),{ids:roleIds,is_active:isActive});}}export const roleService=new RoleService();export default roleService;", "map": {"version": 3, "names": ["BaseCRUDService", "api", "RoleService", "constructor", "getAllRoles", "get", "getActiveRoles", "list", "active_only", "getRolePermissions", "roleId", "concat", "assignPermission", "permissionName", "post", "role_id", "permission", "removePermission", "assignPermissionCRUD", "permissionId", "baseUrl", "permission_id", "removePermissionCRUD", "delete", "searchRoles", "searchTerm", "page", "arguments", "length", "undefined", "pageSize", "search", "page_size", "search_term", "filters", "is_active", "getRolesByName", "namePattern", "name_like", "toggleActiveStatus", "isActive", "update", "bulkDelete", "roleIds", "ids", "bulkToggleStatus", "roleService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/role.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { Role } from '../types';\nimport { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';\n\nexport interface CreateRoleRequest {\n  name: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nexport interface UpdateRoleRequest {\n  name?: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nclass RoleService extends BaseCRUDService<Role, CreateRoleRequest, UpdateRoleRequest> {\n  constructor() {\n    super('/admin/role-management');\n  }\n\n  // Get all roles (from old admin API)\n  getAllRoles(): Promise<AxiosResponse<Role[]>> {\n    return api.get('/admin/roles');\n  }\n\n  // Get active roles only\n  getActiveRoles(): Promise<AxiosResponse<CRUDListResponse<Role>>> {\n    return this.list({ active_only: true });\n  }\n\n  // Get role permissions\n  getRolePermissions(roleId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/role/${roleId}/permissions`);\n  }\n\n  // Assign permission to role\n  assignPermission(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/assign-permission', { role_id: roleId, permission: permissionName });\n  }\n\n  // Remove permission from role\n  removePermission(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/remove-permission', { role_id: roleId, permission: permissionName });\n  }\n\n  // Assign permission to role using CRUD API\n  assignPermissionCRUD(roleId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/${roleId}/permissions`, { permission_id: permissionId });\n  }\n\n  // Remove permission from role using CRUD API\n  removePermissionCRUD(roleId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${roleId}/permissions/${permissionId}`);\n  }\n\n  // Search roles with custom filters\n  searchRoles(searchTerm: string, page: number = 1, pageSize: number = 10): Promise<AxiosResponse<CRUDListResponse<Role>>> {\n    return this.search({\n      page,\n      page_size: pageSize,\n      search_term: searchTerm,\n      filters: {\n        is_active: true\n      }\n    });\n  }\n\n  // Get roles by name pattern\n  getRolesByName(namePattern: string): Promise<AxiosResponse<CRUDListResponse<Role>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      name_like: namePattern\n    });\n  }\n\n  // Toggle role active status\n  toggleActiveStatus(roleId: number, isActive: boolean): Promise<AxiosResponse<CRUDResponse<Role>>> {\n    return this.update(roleId, { is_active: isActive });\n  }\n\n  // Bulk operations\n  bulkDelete(roleIds: number[]): Promise<AxiosResponse<{ message: string; deleted_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-delete`, { ids: roleIds });\n  }\n\n  bulkToggleStatus(roleIds: number[], isActive: boolean): Promise<AxiosResponse<{ message: string; updated_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-toggle-status`, { ids: roleIds, is_active: isActive });\n  }\n}\n\nexport const roleService = new RoleService();\nexport default roleService;\n"], "mappings": "AAEA,OAASA,eAAe,CAAkCC,GAAG,KAAQ,gBAAgB,CAcrF,KAAM,CAAAC,WAAW,QAAS,CAAAF,eAA4D,CACpFG,WAAWA,CAAA,CAAG,CACZ,KAAK,CAAC,wBAAwB,CAAC,CACjC,CAEA;AACAC,WAAWA,CAAA,CAAmC,CAC5C,MAAO,CAAAH,GAAG,CAACI,GAAG,CAAC,cAAc,CAAC,CAChC,CAEA;AACAC,cAAcA,CAAA,CAAmD,CAC/D,MAAO,KAAI,CAACC,IAAI,CAAC,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CACzC,CAEA;AACAC,kBAAkBA,CAACC,MAAc,CAAoC,CACnE,MAAO,CAAAT,GAAG,CAACI,GAAG,gBAAAM,MAAA,CAAgBD,MAAM,gBAAc,CAAC,CACrD,CAEA;AACAE,gBAAgBA,CAACF,MAAc,CAAEG,cAAsB,CAA+C,CACpG,MAAO,CAAAZ,GAAG,CAACa,IAAI,CAAC,0BAA0B,CAAE,CAAEC,OAAO,CAAEL,MAAM,CAAEM,UAAU,CAAEH,cAAe,CAAC,CAAC,CAC9F,CAEA;AACAI,gBAAgBA,CAACP,MAAc,CAAEG,cAAsB,CAA+C,CACpG,MAAO,CAAAZ,GAAG,CAACa,IAAI,CAAC,0BAA0B,CAAE,CAAEC,OAAO,CAAEL,MAAM,CAAEM,UAAU,CAAEH,cAAe,CAAC,CAAC,CAC9F,CAEA;AACAK,oBAAoBA,CAACR,MAAc,CAAES,YAAoB,CAA+C,CACtG,MAAO,CAAAlB,GAAG,CAACa,IAAI,IAAAH,MAAA,CAAI,IAAI,CAACS,OAAO,MAAAT,MAAA,CAAID,MAAM,iBAAgB,CAAEW,aAAa,CAAEF,YAAa,CAAC,CAAC,CAC3F,CAEA;AACAG,oBAAoBA,CAACZ,MAAc,CAAES,YAAoB,CAA+C,CACtG,MAAO,CAAAlB,GAAG,CAACsB,MAAM,IAAAZ,MAAA,CAAI,IAAI,CAACS,OAAO,MAAAT,MAAA,CAAID,MAAM,kBAAAC,MAAA,CAAgBQ,YAAY,CAAE,CAAC,CAC5E,CAEA;AACAK,WAAWA,CAACC,UAAkB,CAA2F,IAAzF,CAAAC,IAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,QAAgB,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrE,MAAO,KAAI,CAACI,MAAM,CAAC,CACjBL,IAAI,CACJM,SAAS,CAAEF,QAAQ,CACnBG,WAAW,CAAER,UAAU,CACvBS,OAAO,CAAE,CACPC,SAAS,CAAE,IACb,CACF,CAAC,CAAC,CACJ,CAEA;AACAC,cAAcA,CAACC,WAAmB,CAAkD,CAClF,MAAO,KAAI,CAAC9B,IAAI,CAAC,CACfmB,IAAI,CAAE,CAAC,CACPM,SAAS,CAAE,GAAG,CACdM,SAAS,CAAED,WACb,CAAC,CAAC,CACJ,CAEA;AACAE,kBAAkBA,CAAC7B,MAAc,CAAE8B,QAAiB,CAA8C,CAChG,MAAO,KAAI,CAACC,MAAM,CAAC/B,MAAM,CAAE,CAAEyB,SAAS,CAAEK,QAAS,CAAC,CAAC,CACrD,CAEA;AACAE,UAAUA,CAACC,OAAiB,CAAsE,CAChG,MAAO,CAAA1C,GAAG,CAACa,IAAI,IAAAH,MAAA,CAAI,IAAI,CAACS,OAAO,iBAAgB,CAAEwB,GAAG,CAAED,OAAQ,CAAC,CAAC,CAClE,CAEAE,gBAAgBA,CAACF,OAAiB,CAAEH,QAAiB,CAAsE,CACzH,MAAO,CAAAvC,GAAG,CAACa,IAAI,IAAAH,MAAA,CAAI,IAAI,CAACS,OAAO,wBAAuB,CAAEwB,GAAG,CAAED,OAAO,CAAER,SAAS,CAAEK,QAAS,CAAC,CAAC,CAC9F,CACF,CAEA,MAAO,MAAM,CAAAM,WAAW,CAAG,GAAI,CAAA5C,WAAW,CAAC,CAAC,CAC5C,cAAe,CAAA4C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}