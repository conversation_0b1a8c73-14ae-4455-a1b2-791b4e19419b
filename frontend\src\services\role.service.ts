import { AxiosResponse } from 'axios';
import { Role } from '../types';
import { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';

export interface CreateRoleRequest {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
}

class RoleService extends BaseCRUDService<Role, CreateRoleRequest, UpdateRoleRequest> {
  constructor() {
    super('/admin/role-management');
  }

  // Get all roles (from old admin API)
  getAllRoles(): Promise<AxiosResponse<Role[]>> {
    return api.get('/admin/roles');
  }

  // Get active roles only
  getActiveRoles(): Promise<AxiosResponse<CRUDListResponse<Role>>> {
    return this.list({ active_only: true });
  }

  // Get role permissions
  getRolePermissions(roleId: number): Promise<AxiosResponse<string[]>> {
    return api.get(`/admin/role/${roleId}/permissions`);
  }

  // Assign permission to role
  assignPermission(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return api.post('/admin/assign-permission', { role_id: roleId, permission: permissionName });
  }

  // Remove permission from role
  removePermission(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {
    return api.post('/admin/remove-permission', { role_id: roleId, permission: permissionName });
  }

  // Assign permission to role using CRUD API
  assignPermissionCRUD(roleId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {
    return api.post(`${this.baseUrl}/${roleId}/permissions`, { permission_id: permissionId });
  }

  // Remove permission from role using CRUD API
  removePermissionCRUD(roleId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {
    return api.delete(`${this.baseUrl}/${roleId}/permissions/${permissionId}`);
  }

  // Search roles with custom filters
  searchRoles(searchTerm: string, page: number = 1, pageSize: number = 10): Promise<AxiosResponse<CRUDListResponse<Role>>> {
    return this.search({
      page,
      page_size: pageSize,
      search_term: searchTerm,
      filters: {
        is_active: true
      }
    });
  }

  // Get roles by name pattern
  getRolesByName(namePattern: string): Promise<AxiosResponse<CRUDListResponse<Role>>> {
    return this.list({
      page: 1,
      page_size: 100,
      name_like: namePattern
    });
  }

  // Toggle role active status
  toggleActiveStatus(roleId: number, isActive: boolean): Promise<AxiosResponse<CRUDResponse<Role>>> {
    return this.update(roleId, { is_active: isActive });
  }

  // Bulk operations
  bulkDelete(roleIds: number[]): Promise<AxiosResponse<{ message: string; deleted_count: number }>> {
    return api.post(`${this.baseUrl}/bulk-delete`, { ids: roleIds });
  }

  bulkToggleStatus(roleIds: number[], isActive: boolean): Promise<AxiosResponse<{ message: string; updated_count: number }>> {
    return api.post(`${this.baseUrl}/bulk-toggle-status`, { ids: roleIds, is_active: isActive });
  }
}

export const roleService = new RoleService();
export default roleService;
