# Role Management API Guide

## Overview

The Role Management API provides comprehensive CRUD operations for managing user roles in the system. It's built on top of the Base CRUD layer, providing advanced features like pagination, multi-column search, and permission management.

## Base URL
```
/api/v1/admin/roles
```

## Authentication
All endpoints require admin authentication:
```
Authorization: Bearer <admin_jwt_token>
```

## Endpoints

### 1. Create Role
**POST** `/api/v1/admin/roles`

Creates a new role in the system.

**Request Body:**
```json
{
  "name": "manager",
  "description": "Manager role with elevated permissions",
  "is_active": true
}
```

**Response (201 Created):**
```json
{
  "data": {
    "id": 3,
    "name": "manager",
    "description": "Manager role with elevated permissions",
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "message": "Role created successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request data
- `409 Conflict`: Role name already exists

### 2. List Roles
**GET** `/api/v1/admin/roles`

Retrieves roles with pagination and search capabilities.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `page_size` (int): Items per page (default: 10, max: 100)
- `order_by` (string): Field to order by (default: "id")
- `order_direction` (string): "asc" or "desc" (default: "asc")
- `active_only` (bool): Return only active roles
- `search_filters[i][field]` (string): Field to search
- `search_filters[i][operator]` (string): Search operator
- `search_filters[i][value]` (string): Search value

**Available Search Fields:**
- `name`: Role name
- `description`: Role description
- `is_active`: Active status (true/false)
- `created_at`: Creation date
- `updated_at`: Last update date

**Available Search Operators:**
- `eq`: Equal
- `ne`: Not equal
- `like`: Contains (case-sensitive)
- `ilike`: Contains (case-insensitive)
- `gt`: Greater than
- `gte`: Greater than or equal
- `lt`: Less than
- `lte`: Less than or equal
- `in`: In list
- `not_in`: Not in list
- `is_null`: Is null
- `not_null`: Is not null

**Example Requests:**

Basic pagination:
```
GET /api/v1/admin/roles?page=1&page_size=5
```

Search by name:
```
GET /api/v1/admin/roles?search_filters[0][field]=name&search_filters[0][operator]=like&search_filters[0][value]=admin
```

Multiple search filters:
```
GET /api/v1/admin/roles?search_filters[0][field]=is_active&search_filters[0][operator]=eq&search_filters[0][value]=true&search_filters[1][field]=name&search_filters[1][operator]=like&search_filters[1][value]=manager
```

Active roles only:
```
GET /api/v1/admin/roles?active_only=true
```

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": 1,
      "name": "admin",
      "description": "Administrator role",
      "is_active": true,
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:00:00Z"
    },
    {
      "id": 2,
      "name": "user",
      "description": "Regular user role",
      "is_active": true,
      "created_at": "2024-01-15T10:15:00Z",
      "updated_at": "2024-01-15T10:15:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 2,
    "total_pages": 1,
    "has_next": false,
    "has_prev": false
  }
}
```

### 3. Get Role
**GET** `/api/v1/admin/roles/:id`

Retrieves a specific role by ID.

**Query Parameters:**
- `include` (string): Set to "associations" to include users and permissions

**Response (200 OK):**
```json
{
  "data": {
    "id": 1,
    "name": "admin",
    "description": "Administrator role",
    "is_active": true,
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:00:00Z"
  },
  "message": "Role retrieved successfully"
}
```

**With associations:**
```json
{
  "data": {
    "id": 1,
    "name": "admin",
    "description": "Administrator role",
    "is_active": true,
    "users": [
      {
        "id": 1,
        "username": "admin_user",
        "email": "<EMAIL>"
      }
    ],
    "permissions": [
      {
        "id": 1,
        "name": "assign_role"
      }
    ],
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:00:00Z"
  },
  "message": "Role retrieved successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid role ID
- `404 Not Found`: Role not found

### 4. Update Role
**PUT** `/api/v1/admin/roles/:id`

Updates an existing role.

**Request Body:**
```json
{
  "name": "senior_manager",
  "description": "Senior manager with additional permissions",
  "is_active": false
}
```

**Response (200 OK):**
```json
{
  "data": {
    "id": 3,
    "name": "senior_manager",
    "description": "Senior manager with additional permissions",
    "is_active": false,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T11:00:00Z"
  },
  "message": "Role updated successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Role not found
- `409 Conflict`: Role name already exists

### 5. Delete Role
**DELETE** `/api/v1/admin/roles/:id`

Soft deletes a role (sets deleted_at timestamp).

**Response (200 OK):**
```json
{
  "message": "Role deleted successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid role ID
- `404 Not Found`: Role not found
- `409 Conflict`: Role is assigned to users (cannot delete)

### 6. Assign Permission to Role
**POST** `/api/v1/admin/roles/:id/permissions`

Assigns a permission to a role.

**Request Body:**
```json
{
  "permission_id": 2
}
```

**Response (200 OK):**
```json
{
  "message": "Permission assigned to role successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Role or permission not found
- `409 Conflict`: Permission already assigned to role

### 7. Remove Permission from Role
**DELETE** `/api/v1/admin/roles/:id/permissions/:permission_id`

Removes a permission from a role.

**Response (200 OK):**
```json
{
  "message": "Permission removed from role successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid role or permission ID
- `404 Not Found`: Permission assignment not found

## Error Response Format

All error responses follow this format:
```json
{
  "error": "Error message",
  "details": "Detailed error information",
  "code": "ERROR_CODE"
}
```

**Common Error Codes:**
- `INVALID_REQUEST`: Invalid request data
- `INVALID_ID`: Invalid ID parameter
- `ROLE_NOT_FOUND`: Role not found
- `ROLE_EXISTS`: Role name already exists
- `ROLE_IN_USE`: Role cannot be deleted (assigned to users)
- `RESOURCE_NOT_FOUND`: Role or permission not found
- `PERMISSION_EXISTS`: Permission already assigned
- `ASSIGNMENT_NOT_FOUND`: Permission assignment not found

## Usage Examples

### JavaScript/Fetch
```javascript
// Create role
const createRole = async () => {
  const response = await fetch('/api/v1/admin/roles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminToken}`
    },
    body: JSON.stringify({
      name: 'editor',
      description: 'Content editor role',
      is_active: true
    })
  });
  return response.json();
};

// List roles with search
const searchRoles = async () => {
  const params = new URLSearchParams({
    'search_filters[0][field]': 'name',
    'search_filters[0][operator]': 'like',
    'search_filters[0][value]': 'admin',
    'page': '1',
    'page_size': '10'
  });
  
  const response = await fetch(`/api/v1/admin/roles?${params}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  return response.json();
};
```

### cURL
```bash
# Create role
curl -X POST "http://localhost:8080/api/v1/admin/roles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "name": "moderator",
    "description": "Content moderator role",
    "is_active": true
  }'

# List roles with pagination
curl -X GET "http://localhost:8080/api/v1/admin/roles?page=1&page_size=5" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Search roles
curl -X GET "http://localhost:8080/api/v1/admin/roles?search_filters[0][field]=name&search_filters[0][operator]=like&search_filters[0][value]=admin" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Integration with Base CRUD

This Role API is built on the Base CRUD system, providing:

- ✅ **Automatic pagination** with configurable page sizes
- ✅ **Multi-column search** with 12 different operators
- ✅ **Flexible ordering** by any field
- ✅ **GORM integration** with optimized queries
- ✅ **Soft delete** support
- ✅ **Comprehensive validation** and error handling
- ✅ **Structured logging** for debugging and monitoring
- ✅ **Type-safe operations** with Go generics

The system automatically handles field name conversion (CamelCase to snake_case) and provides database-agnostic search capabilities.
