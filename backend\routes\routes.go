package routes

import (
	"jwt-auth-backend/handlers"
	v1 "jwt-auth-backend/handlers/v1"
	v2 "jwt-auth-backend/handlers/v2"
	"jwt-auth-backend/middleware"

	"github.com/gin-gonic/gin"
)

func SetupRoutes() *gin.Engine {
	r := gin.Default()

	// CORS middleware
	r.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c<PERSON><PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// API v1 routes
	v1Group := r.Group("/api/v1")
	{
		// Auth routes
		auth := v1Group.Group("/auth")
		{
			auth.POST("/register", v1.Register)
			auth.POST("/login", v1.Login)
			auth.POST("/logout", middleware.AuthMiddleware(), v1.Logout)
		}

		// User routes (protected)
		user := v1Group.Group("/user")
		user.Use(middleware.AuthMiddleware())
		{
			user.GET("/profile", v1.GetProfile)
		}

		// Admin routes (protected)
		admin := v1Group.Group("/admin")
		admin.Use(middleware.AuthMiddleware(), middleware.RequireRoles("admin"))
		{
			admin.GET("/dashboard", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Welcome admin!"})
			})
			admin.POST("/assign-role", middleware.RequirePermissions("assign_role"), v1.AssignRole)
			admin.GET("/users", v1.ListUsers)
			admin.GET("/roles", v1.ListRoles)
			admin.GET("/permissions", v1.ListPermissions)
			admin.GET("/user/:id/roles", v1.GetUserRoles)
			admin.GET("/user/:id/permissions", v1.GetUserPermissions)
			admin.POST("/remove-role", middleware.RequirePermissions("assign_role"), v1.RemoveRole)
			admin.GET("/role/:id/permissions", v1.GetRolePermissions)
			admin.POST("/assign-permission", middleware.RequireRoles("admin"), v1.AssignPermission)
			admin.POST("/remove-permission", middleware.RequireRoles("admin"), v1.RemovePermission)
			admin.GET("/user/:id/direct-permissions", v1.GetUserDirectPermissions)
			admin.POST("/assign-user-permission", v1.AssignUserPermission)
			admin.POST("/remove-user-permission", v1.RemoveUserPermission)

			// Setup CRUD APIs using base CRUD system
			roleHandler := handlers.NewRoleHandler()
			roleHandler.SetupRoleRoutes(admin)

			userHandler := handlers.NewUserHandler()
			userHandler.SetupUserRoutes(admin)

			permissionHandler := handlers.NewPermissionHandler()
			permissionHandler.SetupPermissionRoutes(admin)
		}

		// Example: Route chỉ cho user có permission 'edit_user'
		v1Group.GET("/edit-user-demo", middleware.AuthMiddleware(), middleware.RequirePermissions("edit_user"), func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "You have edit_user permission!"})
		})
	}

	// API v2 routes (demo auth)
	v2Group := r.Group("/api/v2")
	{
		auth := v2Group.Group("/auth")
		{
			auth.POST("/register", v2.Register)
			auth.POST("/login", v2.Login)
			auth.POST("/logout", middleware.AuthMiddleware(), v2.Logout)
		}
		// ... các group khác cho v2 nếu muốn mở rộng ...
	}

	return r
}
