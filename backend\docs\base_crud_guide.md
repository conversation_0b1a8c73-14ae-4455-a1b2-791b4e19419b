# Base CRUD System Documentation

## Overview

The Base CRUD system provides a generic, reusable foundation for implementing Create, Read, Update, Delete operations with advanced features like pagination, multi-column search, and batch operations. It follows the existing project architecture and patterns.

## Features

- **Standard CRUD Operations**: Create, Read, Update, Delete
- **Pagination**: Configurable page size and page number
- **Multi-column Search**: Advanced filtering with multiple operators
- **Batch Operations**: Batch delete functionality
- **Generic Implementation**: Works with any GORM model
- **Consistent Error Handling**: Standardized error responses
- **Structured Logging**: Comprehensive logging with Zap
- **Field Introspection**: Dynamic field name discovery

## Architecture

The system consists of three main layers:

1. **Models Layer** (`models/base_crud.go`): Defines request/response types and validation
2. **Service Layer** (`services/base_crud.go`): Implements business logic and database operations
3. **Handler Layer** (`handlers/base_crud.go`): Provides HTTP endpoints and request handling

## Components

### 1. Models (`models/base_crud.go`)

#### Core Types

- `BaseModel`: Common fields for all models (ID, CreatedAt, UpdatedAt, DeletedAt)
- `PaginationRequest`: Page number and page size parameters
- `PaginationResponse`: Pagination metadata in responses
- `SearchFilter`: Individual search filter with field, operator, and value
- `SearchRequest`: Collection of search filters with ordering
- `CRUDRequest`: Combined pagination and search parameters
- `CRUDResponse`: Standard response format
- `ErrorResponse`: Standardized error format

#### Search Operators

- `eq`: Exact match
- `ne`: Not equal
- `like`: Partial match (contains)
- `ilike`: Case-insensitive partial match
- `gt`: Greater than
- `gte`: Greater than or equal
- `lt`: Less than
- `lte`: Less than or equal
- `in`: In array
- `not_in`: Not in array
- `is_null`: Is null
- `not_null`: Is not null

### 2. Service Layer (`services/base_crud.go`)

#### BaseCRUDService Methods

- `Create(model interface{}) error`: Create a new record
- `GetByID(model interface{}, id uint) error`: Retrieve record by ID
- `Update(model interface{}, id uint, updates interface{}) error`: Update existing record
- `Delete(model interface{}, id uint) error`: Soft delete record
- `List(model interface{}, req CRUDRequest) (interface{}, PaginationResponse, error)`: List with pagination and search
- `GetCount(model interface{}, searchReq SearchRequest) (int64, error)`: Count records
- `Exists(model interface{}, conditions map[string]interface{}) (bool, error)`: Check existence
- `BatchCreate(models interface{}) error`: Create multiple records
- `BatchDelete(model interface{}, ids []uint) error`: Delete multiple records
- `GetFieldNames(model interface{}) []string`: Get available field names

### 3. Handler Layer (`handlers/base_crud.go`)

#### BaseCRUDHandler Methods

- `Create(c *gin.Context)`: POST endpoint for creating records
- `GetByID(c *gin.Context)`: GET endpoint for retrieving by ID
- `Update(c *gin.Context)`: PUT endpoint for updating records
- `Delete(c *gin.Context)`: DELETE endpoint for deleting records
- `List(c *gin.Context)`: GET endpoint for listing with pagination
- `Search(c *gin.Context)`: POST endpoint for advanced search
- `GetFieldNames(c *gin.Context)`: GET endpoint for field names
- `BatchDelete(c *gin.Context)`: DELETE endpoint for batch operations
- `SetupCRUDRoutes(group *gin.RouterGroup, basePath string)`: Setup all routes

## Usage Examples

### 1. Basic Model Setup

First, ensure your model embeds the BaseModel or has compatible fields:

```go
type Product struct {
    models.BaseModel
    Name        string  `json:"name" gorm:"not null"`
    Description string  `json:"description"`
    Price       float64 `json:"price" gorm:"not null"`
    CategoryID  uint    `json:"category_id"`
}
```

### 2. Setting Up CRUD Routes

```go
// In your routes setup
func SetupProductRoutes(r *gin.Engine) {
    // Create handler for Product model
    productHandler := handlers.NewBaseCRUDHandler(&models.Product{})
    
    // Setup routes under /api/v1/products
    v1 := r.Group("/api/v1")
    productHandler.SetupCRUDRoutes(v1, "/products")
}
```

This automatically creates the following endpoints:

- `POST /api/v1/products` - Create product
- `GET /api/v1/products/:id` - Get product by ID
- `PUT /api/v1/products/:id` - Update product
- `DELETE /api/v1/products/:id` - Delete product
- `GET /api/v1/products` - List products with pagination
- `POST /api/v1/products/search` - Advanced search
- `GET /api/v1/products/fields` - Get available fields
- `DELETE /api/v1/products/batch` - Batch delete

### 3. API Usage Examples

#### Create a Product
```bash
POST /api/v1/products
Content-Type: application/json

{
    "name": "Laptop",
    "description": "High-performance laptop",
    "price": 999.99,
    "category_id": 1
}
```

#### List Products with Pagination
```bash
GET /api/v1/products?page=1&page_size=10&order_by=name&order_dir=asc
```

#### Advanced Search
```bash
POST /api/v1/products/search
Content-Type: application/json

{
    "page": 1,
    "page_size": 10,
    "filters": [
        {
            "field": "price",
            "operator": "gte",
            "value": 100
        },
        {
            "field": "name",
            "operator": "ilike",
            "value": "laptop"
        }
    ],
    "order_by": "price",
    "order_dir": "desc"
}
```

#### Batch Delete
```bash
DELETE /api/v1/products/batch
Content-Type: application/json

{
    "ids": [1, 2, 3, 4, 5]
}
```

### 4. Custom Extensions

You can extend the base functionality by creating custom handlers:

```go
type ProductHandler struct {
    *handlers.BaseCRUDHandler
}

func NewProductHandler() *ProductHandler {
    return &ProductHandler{
        BaseCRUDHandler: handlers.NewBaseCRUDHandler(&models.Product{}),
    }
}

// Add custom methods
func (h *ProductHandler) GetByCategory(c *gin.Context) {
    categoryID := c.Param("categoryId")
    // Custom logic here
}

// Setup routes with custom endpoints
func (h *ProductHandler) SetupRoutes(group *gin.RouterGroup) {
    // Setup standard CRUD routes
    h.SetupCRUDRoutes(group, "/products")
    
    // Add custom routes
    group.GET("/products/category/:categoryId", h.GetByCategory)
}
```

## Response Formats

### Success Response (Single Record)
```json
{
    "data": {
        "id": 1,
        "name": "Laptop",
        "price": 999.99,
        "created_at": "2023-01-01T00:00:00Z"
    },
    "message": "Product created successfully"
}
```

### Success Response (List)
```json
{
    "data": [
        {
            "id": 1,
            "name": "Laptop",
            "price": 999.99
        }
    ],
    "pagination": {
        "page": 1,
        "page_size": 10,
        "total": 25,
        "total_pages": 3,
        "has_next": true,
        "has_prev": false
    }
}
```

### Error Response
```json
{
    "error": "Invalid request data",
    "details": "Field 'name' is required",
    "code": "VALIDATION_ERROR"
}
```

## Best Practices

1. **Model Design**: Always include BaseModel fields or compatible ID, timestamps
2. **Validation**: Use GORM tags and Gin binding for validation
3. **Security**: Add authentication/authorization middleware before CRUD routes
4. **Performance**: Use appropriate indexes on searchable fields
5. **Logging**: The system provides comprehensive logging automatically
6. **Error Handling**: Errors are handled consistently across all operations

## Integration with Existing Code

The base CRUD system is designed to work seamlessly with the existing project structure:

- Uses the same database connection (`database.DB`)
- Follows the same logging patterns (Zap logger)
- Uses the same error handling approach
- Compatible with existing middleware
- Follows the same response format conventions
