import axios, { AxiosResponse } from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for automatic token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
          refresh_token: refreshToken,
        });

        const { token: newAccessToken, refresh_token: newRefreshToken } = response.data;

        localStorage.setItem('access_token', newAccessToken);
        localStorage.setItem('refresh_token', newRefreshToken);

        // Retry the original request with new token
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Common interfaces
export interface PaginationInfo {
  page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
}

export interface CRUDListResponse<T> {
  data: T[];
  pagination: PaginationInfo;
  message?: string;
}

export interface CRUDResponse<T> {
  data: T;
  message?: string;
}

export interface SearchRequest {
  page: number;
  page_size: number;
  search_term?: string;
  filters?: Record<string, any>;
}

export interface ErrorResponse {
  error: string;
  details?: string;
  code?: string;
}

// Base CRUD service class
export abstract class BaseCRUDService<T, CreateT = Partial<T>, UpdateT = Partial<T>> {
  protected baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  // List items with pagination
  list(params?: Record<string, any>): Promise<AxiosResponse<CRUDListResponse<T>>> {
    return api.get(this.baseUrl, { params });
  }

  // Search items
  search(searchRequest: SearchRequest): Promise<AxiosResponse<CRUDListResponse<T>>> {
    return api.post(`${this.baseUrl}/search`, searchRequest);
  }

  // Get item by ID
  getById(id: number): Promise<AxiosResponse<CRUDResponse<T>>> {
    return api.get(`${this.baseUrl}/${id}`);
  }

  // Create new item
  create(data: CreateT): Promise<AxiosResponse<CRUDResponse<T>>> {
    return api.post(this.baseUrl, data);
  }

  // Update item
  update(id: number, data: UpdateT): Promise<AxiosResponse<CRUDResponse<T>>> {
    return api.put(`${this.baseUrl}/${id}`, data);
  }

  // Delete item
  delete(id: number): Promise<AxiosResponse<{ message: string }>> {
    return api.delete(`${this.baseUrl}/${id}`);
  }
}

export { api };
export default api;
