{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\TokenManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TokenManager = () => {\n  _s();\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [message, setMessage] = useState('');\n  useEffect(() => {\n    loadTokenInfo();\n  }, []);\n  const loadTokenInfo = () => {\n    const token = localStorage.getItem('token');\n    const refreshToken = localStorage.getItem('refresh_token');\n    const user = localStorage.getItem('user');\n    if (token && refreshToken && user) {\n      try {\n        // Decode JWT to get expiry (simple decode, not verification)\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const expiry = new Date(payload.exp * 1000);\n        setTokenInfo({\n          token: token.substring(0, 20) + '...',\n          refreshToken: refreshToken.substring(0, 20) + '...',\n          user: JSON.parse(user),\n          tokenExpiry: expiry\n        });\n      } catch (error) {\n        console.error('Error parsing token:', error);\n      }\n    }\n  };\n  const handleRefreshToken = async () => {\n    var _tokenInfo$user;\n    if (!(tokenInfo !== null && tokenInfo !== void 0 && (_tokenInfo$user = tokenInfo.user) !== null && _tokenInfo$user !== void 0 && _tokenInfo$user.id)) return;\n    setIsRefreshing(true);\n    setMessage('');\n    try {\n      const refreshToken = localStorage.getItem('refresh_token');\n      if (!refreshToken) {\n        throw new Error('No refresh token found');\n      }\n      const response = await authAPI.refresh(tokenInfo.user.id, refreshToken);\n      setMessage('✅ Token refreshed successfully!');\n      loadTokenInfo(); // Reload token info\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setMessage(`❌ Failed to refresh token: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message}`);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n  const handleRevokeAllTokens = async () => {\n    try {\n      await authAPI.revokeAllTokens();\n      setMessage('✅ All refresh tokens revoked successfully!');\n      // Clear local tokens after revoking\n      localStorage.removeItem('token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user');\n      setTokenInfo(null);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      setMessage(`❌ Failed to revoke tokens: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message}`);\n    }\n  };\n  const isTokenExpired = () => {\n    if (!(tokenInfo !== null && tokenInfo !== void 0 && tokenInfo.tokenExpiry)) return false;\n    return new Date() > tokenInfo.tokenExpiry;\n  };\n  const getTimeUntilExpiry = () => {\n    if (!(tokenInfo !== null && tokenInfo !== void 0 && tokenInfo.tokenExpiry)) return '';\n    const now = new Date();\n    const expiry = tokenInfo.tokenExpiry;\n    const diff = expiry.getTime() - now.getTime();\n    if (diff <= 0) return 'Expired';\n    const minutes = Math.floor(diff / (1000 * 60));\n    const hours = Math.floor(minutes / 60);\n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m`;\n    }\n    return `${minutes}m`;\n  };\n  if (!tokenInfo) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-4\",\n        children: \"Token Manager\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"No active session found. Please login first.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-6 rounded-lg shadow-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-semibold mb-4\",\n      children: \"Token Manager\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"User:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-900\",\n          children: [tokenInfo.user.username, \" (\", tokenInfo.user.email, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Access Token:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-900 font-mono\",\n          children: tokenInfo.token\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isTokenExpired() ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`,\n            children: isTokenExpired() ? 'Expired' : 'Valid'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-xs text-gray-500\",\n            children: isTokenExpired() ? 'Token has expired' : `Expires in ${getTimeUntilExpiry()}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Refresh Token:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-900 font-mono\",\n          children: tokenInfo.refreshToken\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRefreshToken,\n          disabled: isRefreshing,\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n          children: isRefreshing ? 'Refreshing...' : 'Refresh Token'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRevokeAllTokens,\n          className: \"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Revoke All Tokens\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: loadTokenInfo,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Reload Info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-3 rounded ${message.startsWith('✅') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`,\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(TokenManager, \"84JZawnsiAt5KP0/hM/CQ6Qg0TU=\");\n_c = TokenManager;\nexport default TokenManager;\nvar _c;\n$RefreshReg$(_c, \"TokenManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "TokenManager", "_s", "tokenInfo", "setTokenInfo", "isRefreshing", "setIsRefreshing", "message", "setMessage", "loadTokenInfo", "token", "localStorage", "getItem", "refreshToken", "user", "payload", "JSON", "parse", "atob", "split", "expiry", "Date", "exp", "substring", "tokenExpiry", "error", "console", "handleRefreshToken", "_tokenInfo$user", "id", "Error", "response", "refresh", "_error$response", "_error$response$data", "data", "handleRevokeAllTokens", "revokeAllTokens", "removeItem", "_error$response2", "_error$response2$data", "isTokenExpired", "getTimeUntilExpiry", "now", "diff", "getTime", "minutes", "Math", "floor", "hours", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "username", "email", "onClick", "disabled", "startsWith", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/TokenManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\ninterface TokenInfo {\n  token: string;\n  refreshToken: string;\n  user: any;\n  tokenExpiry?: Date;\n}\n\nconst TokenManager: React.FC = () => {\n  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [message, setMessage] = useState('');\n\n  useEffect(() => {\n    loadTokenInfo();\n  }, []);\n\n  const loadTokenInfo = () => {\n    const token = localStorage.getItem('token');\n    const refreshToken = localStorage.getItem('refresh_token');\n    const user = localStorage.getItem('user');\n\n    if (token && refreshToken && user) {\n      try {\n        // Decode JWT to get expiry (simple decode, not verification)\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const expiry = new Date(payload.exp * 1000);\n        \n        setTokenInfo({\n          token: token.substring(0, 20) + '...',\n          refreshToken: refreshToken.substring(0, 20) + '...',\n          user: JSON.parse(user),\n          tokenExpiry: expiry\n        });\n      } catch (error) {\n        console.error('Error parsing token:', error);\n      }\n    }\n  };\n\n  const handleRefreshToken = async () => {\n    if (!tokenInfo?.user?.id) return;\n    \n    setIsRefreshing(true);\n    setMessage('');\n    \n    try {\n      const refreshToken = localStorage.getItem('refresh_token');\n      if (!refreshToken) {\n        throw new Error('No refresh token found');\n      }\n      \n      const response = await authAPI.refresh(tokenInfo.user.id, refreshToken);\n      setMessage('✅ Token refreshed successfully!');\n      loadTokenInfo(); // Reload token info\n    } catch (error: any) {\n      setMessage(`❌ Failed to refresh token: ${error.response?.data?.error || error.message}`);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  const handleRevokeAllTokens = async () => {\n    try {\n      await authAPI.revokeAllTokens();\n      setMessage('✅ All refresh tokens revoked successfully!');\n      // Clear local tokens after revoking\n      localStorage.removeItem('token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user');\n      setTokenInfo(null);\n    } catch (error: any) {\n      setMessage(`❌ Failed to revoke tokens: ${error.response?.data?.error || error.message}`);\n    }\n  };\n\n  const isTokenExpired = () => {\n    if (!tokenInfo?.tokenExpiry) return false;\n    return new Date() > tokenInfo.tokenExpiry;\n  };\n\n  const getTimeUntilExpiry = () => {\n    if (!tokenInfo?.tokenExpiry) return '';\n    const now = new Date();\n    const expiry = tokenInfo.tokenExpiry;\n    const diff = expiry.getTime() - now.getTime();\n    \n    if (diff <= 0) return 'Expired';\n    \n    const minutes = Math.floor(diff / (1000 * 60));\n    const hours = Math.floor(minutes / 60);\n    \n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m`;\n    }\n    return `${minutes}m`;\n  };\n\n  if (!tokenInfo) {\n    return (\n      <div className=\"bg-white p-6 rounded-lg shadow-md\">\n        <h3 className=\"text-lg font-semibold mb-4\">Token Manager</h3>\n        <p className=\"text-gray-600\">No active session found. Please login first.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-md\">\n      <h3 className=\"text-lg font-semibold mb-4\">Token Manager</h3>\n      \n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700\">User:</label>\n          <p className=\"text-sm text-gray-900\">{tokenInfo.user.username} ({tokenInfo.user.email})</p>\n        </div>\n        \n        <div>\n          <label className=\"block text-sm font-medium text-gray-700\">Access Token:</label>\n          <p className=\"text-sm text-gray-900 font-mono\">{tokenInfo.token}</p>\n          <div className=\"flex items-center mt-1\">\n            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n              isTokenExpired() ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'\n            }`}>\n              {isTokenExpired() ? 'Expired' : 'Valid'}\n            </span>\n            <span className=\"ml-2 text-xs text-gray-500\">\n              {isTokenExpired() ? 'Token has expired' : `Expires in ${getTimeUntilExpiry()}`}\n            </span>\n          </div>\n        </div>\n        \n        <div>\n          <label className=\"block text-sm font-medium text-gray-700\">Refresh Token:</label>\n          <p className=\"text-sm text-gray-900 font-mono\">{tokenInfo.refreshToken}</p>\n        </div>\n        \n        <div className=\"flex space-x-3\">\n          <button\n            onClick={handleRefreshToken}\n            disabled={isRefreshing}\n            className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\"\n          >\n            {isRefreshing ? 'Refreshing...' : 'Refresh Token'}\n          </button>\n          \n          <button\n            onClick={handleRevokeAllTokens}\n            className=\"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded\"\n          >\n            Revoke All Tokens\n          </button>\n          \n          <button\n            onClick={loadTokenInfo}\n            className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n          >\n            Reload Info\n          </button>\n        </div>\n        \n        {message && (\n          <div className={`p-3 rounded ${\n            message.startsWith('✅') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'\n          }`}>\n            {message}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TokenManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS1C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAmB,IAAI,CAAC;EAClE,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdY,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC1D,MAAME,IAAI,GAAGH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAEzC,IAAIF,KAAK,IAAIG,YAAY,IAAIC,IAAI,EAAE;MACjC,IAAI;QACF;QACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACR,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,MAAMC,MAAM,GAAG,IAAIC,IAAI,CAACN,OAAO,CAACO,GAAG,GAAG,IAAI,CAAC;QAE3ClB,YAAY,CAAC;UACXM,KAAK,EAAEA,KAAK,CAACa,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;UACrCV,YAAY,EAAEA,YAAY,CAACU,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;UACnDT,IAAI,EAAEE,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;UACtBU,WAAW,EAAEJ;QACf,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,eAAA;IACrC,IAAI,EAACzB,SAAS,aAATA,SAAS,gBAAAyB,eAAA,GAATzB,SAAS,CAAEW,IAAI,cAAAc,eAAA,eAAfA,eAAA,CAAiBC,EAAE,GAAE;IAE1BvB,eAAe,CAAC,IAAI,CAAC;IACrBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMK,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAC1D,IAAI,CAACC,YAAY,EAAE;QACjB,MAAM,IAAIiB,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEA,MAAMC,QAAQ,GAAG,MAAMjC,OAAO,CAACkC,OAAO,CAAC7B,SAAS,CAACW,IAAI,CAACe,EAAE,EAAEhB,YAAY,CAAC;MACvEL,UAAU,CAAC,iCAAiC,CAAC;MAC7CC,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOgB,KAAU,EAAE;MAAA,IAAAQ,eAAA,EAAAC,oBAAA;MACnB1B,UAAU,CAAC,8BAA8B,EAAAyB,eAAA,GAAAR,KAAK,CAACM,QAAQ,cAAAE,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBT,KAAK,KAAIA,KAAK,CAAClB,OAAO,EAAE,CAAC;IAC1F,CAAC,SAAS;MACRD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM8B,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMtC,OAAO,CAACuC,eAAe,CAAC,CAAC;MAC/B7B,UAAU,CAAC,4CAA4C,CAAC;MACxD;MACAG,YAAY,CAAC2B,UAAU,CAAC,OAAO,CAAC;MAChC3B,YAAY,CAAC2B,UAAU,CAAC,eAAe,CAAC;MACxC3B,YAAY,CAAC2B,UAAU,CAAC,MAAM,CAAC;MAC/BlC,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOqB,KAAU,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACnBhC,UAAU,CAAC,8BAA8B,EAAA+B,gBAAA,GAAAd,KAAK,CAACM,QAAQ,cAAAQ,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBf,KAAK,KAAIA,KAAK,CAAClB,OAAO,EAAE,CAAC;IAC1F;EACF,CAAC;EAED,MAAMkC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,EAACtC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEqB,WAAW,GAAE,OAAO,KAAK;IACzC,OAAO,IAAIH,IAAI,CAAC,CAAC,GAAGlB,SAAS,CAACqB,WAAW;EAC3C,CAAC;EAED,MAAMkB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAACvC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEqB,WAAW,GAAE,OAAO,EAAE;IACtC,MAAMmB,GAAG,GAAG,IAAItB,IAAI,CAAC,CAAC;IACtB,MAAMD,MAAM,GAAGjB,SAAS,CAACqB,WAAW;IACpC,MAAMoB,IAAI,GAAGxB,MAAM,CAACyB,OAAO,CAAC,CAAC,GAAGF,GAAG,CAACE,OAAO,CAAC,CAAC;IAE7C,IAAID,IAAI,IAAI,CAAC,EAAE,OAAO,SAAS;IAE/B,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAC9C,MAAMK,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IAEtC,IAAIG,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,KAAKH,OAAO,GAAG,EAAE,GAAG;IACrC;IACA,OAAO,GAAGA,OAAO,GAAG;EACtB,CAAC;EAED,IAAI,CAAC3C,SAAS,EAAE;IACd,oBACEH,OAAA;MAAKkD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnD,OAAA;QAAIkD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DvD,OAAA;QAAGkD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA4C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CAAC;EAEV;EAEA,oBACEvD,OAAA;IAAKkD,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDnD,OAAA;MAAIkD,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE7DvD,OAAA;MAAKkD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBnD,OAAA;QAAAmD,QAAA,gBACEnD,OAAA;UAAOkD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxEvD,OAAA;UAAGkD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAEhD,SAAS,CAACW,IAAI,CAAC0C,QAAQ,EAAC,IAAE,EAACrD,SAAS,CAACW,IAAI,CAAC2C,KAAK,EAAC,GAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC,eAENvD,OAAA;QAAAmD,QAAA,gBACEnD,OAAA;UAAOkD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChFvD,OAAA;UAAGkD,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAEhD,SAAS,CAACO;QAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEvD,OAAA;UAAKkD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCnD,OAAA;YAAMkD,SAAS,EAAE,2EACfT,cAAc,CAAC,CAAC,GAAG,yBAAyB,GAAG,6BAA6B,EAC3E;YAAAU,QAAA,EACAV,cAAc,CAAC,CAAC,GAAG,SAAS,GAAG;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACPvD,OAAA;YAAMkD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACzCV,cAAc,CAAC,CAAC,GAAG,mBAAmB,GAAG,cAAcC,kBAAkB,CAAC,CAAC;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvD,OAAA;QAAAmD,QAAA,gBACEnD,OAAA;UAAOkD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjFvD,OAAA;UAAGkD,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAEhD,SAAS,CAACU;QAAY;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAENvD,OAAA;QAAKkD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnD,OAAA;UACE0D,OAAO,EAAE/B,kBAAmB;UAC5BgC,QAAQ,EAAEtD,YAAa;UACvB6C,SAAS,EAAC,0FAA0F;UAAAC,QAAA,EAEnG9C,YAAY,GAAG,eAAe,GAAG;QAAe;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAETvD,OAAA;UACE0D,OAAO,EAAEtB,qBAAsB;UAC/Bc,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAC/E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvD,OAAA;UACE0D,OAAO,EAAEjD,aAAc;UACvByC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELhD,OAAO,iBACNP,OAAA;QAAKkD,SAAS,EAAE,eACd3C,OAAO,CAACqD,UAAU,CAAC,GAAG,CAAC,GAAG,6BAA6B,GAAG,yBAAyB,EAClF;QAAAT,QAAA,EACA5C;MAAO;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAnKID,YAAsB;AAAA4D,EAAA,GAAtB5D,YAAsB;AAqK5B,eAAeA,YAAY;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}