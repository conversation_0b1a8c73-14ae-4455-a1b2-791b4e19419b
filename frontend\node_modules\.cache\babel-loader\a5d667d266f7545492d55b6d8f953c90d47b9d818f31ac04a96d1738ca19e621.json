{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\AssignmentsTab.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { adminAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AssignmentsTab = ({\n  users,\n  roles,\n  onMessage,\n  onError\n}) => {\n  _s();\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [userRoleNames, setUserRoleNames] = useState([]);\n  const [userPermissionNames, setUserPermissionNames] = useState([]);\n  const [availableRoles, setAvailableRoles] = useState([]);\n  const [availablePermissions, setAvailablePermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [assignmentType, setAssignmentType] = useState('roles');\n\n  // Load user details when selected\n  useEffect(() => {\n    if (selectedUser) {\n      loadUserDetails();\n    }\n  }, [selectedUser]);\n  const loadUserDetails = async () => {\n    if (!selectedUser) return;\n    setLoading(true);\n    try {\n      // Load user roles\n      const rolesResponse = await adminAPI.getUserRoles(selectedUser.id);\n      setUserRoles(rolesResponse.data);\n\n      // Load user permissions\n      const permissionsResponse = await adminAPI.getUserPermissions(selectedUser.id);\n      setUserPermissions(permissionsResponse.data);\n\n      // Set available roles (roles not assigned to user)\n      const assignedRoleIds = rolesResponse.data.map(role => role.id);\n      setAvailableRoles(roles.filter(role => !assignedRoleIds.includes(role.id) && role.is_active));\n\n      // Load all permissions for available permissions list\n      // Note: This would need to be implemented in the backend to get all permissions\n      // For now, we'll use an empty array\n      setAvailablePermissions([]);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      onError(`Failed to load user details: ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAssignRole = async roleId => {\n    if (!selectedUser) return;\n    try {\n      await adminAPI.assignRole(selectedUser.id, roleId);\n      onMessage('Role assigned successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      onError(`Failed to assign role: ${((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n    }\n  };\n  const handleRemoveRole = async roleId => {\n    if (!selectedUser) return;\n    try {\n      await adminAPI.removeRole(selectedUser.id, roleId);\n      onMessage('Role removed successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      onError(`Failed to remove role: ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message}`);\n    }\n  };\n  const handleAssignPermission = async permissionId => {\n    if (!selectedUser) return;\n    try {\n      await adminAPI.assignPermission(selectedUser.id, permissionId);\n      onMessage('Permission assigned successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      onError(`Failed to assign permission: ${((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n    }\n  };\n  const handleRemovePermission = async permissionId => {\n    if (!selectedUser) return;\n    try {\n      await adminAPI.removePermission(selectedUser.id, permissionId);\n      onMessage('Permission removed successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      onError(`Failed to remove permission: ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || err.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold mb-6\",\n      children: \"User Role & Permission Assignments\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium mb-4\",\n          children: \"Select User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 max-h-96 overflow-y-auto\",\n          children: users.map(user => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedUser(user),\n            className: `w-full text-left p-3 rounded border ${(selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.id) === user.id ? 'bg-blue-100 border-blue-500' : 'bg-gray-50 border-gray-200 hover:bg-gray-100'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium\",\n              children: user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)]\n          }, user.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), selectedUser && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 rounded-lg border\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium\",\n              children: [\"Managing: \", selectedUser.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex bg-gray-100 rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setAssignmentType('roles'),\n                className: `px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'roles' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                children: \"Roles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setAssignmentType('permissions'),\n                className: `px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'permissions' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                children: \"Permissions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium mb-3\",\n                children: [\"Assigned \", assignmentType === 'roles' ? 'Roles' : 'Permissions']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                children: assignmentType === 'roles' ? userRoles.length > 0 ? userRoles.map(role => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-green-800\",\n                      children: role.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 33\n                    }, this), role.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-green-600\",\n                      children: role.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRemoveRole(role.id),\n                    className: \"text-red-600 hover:text-red-800 text-sm\",\n                    children: \"Remove\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 31\n                  }, this)]\n                }, role.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 29\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"No roles assigned\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 27\n                }, this) : userPermissions.length > 0 ? userPermissions.map(permission => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-green-800\",\n                      children: permission.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 33\n                    }, this), permission.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-green-600\",\n                      children: permission.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRemovePermission(permission.id),\n                    className: \"text-red-600 hover:text-red-800 text-sm\",\n                    children: \"Remove\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 31\n                  }, this)]\n                }, permission.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 29\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"No permissions assigned\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium mb-3\",\n                children: [\"Available \", assignmentType === 'roles' ? 'Roles' : 'Permissions']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                children: assignmentType === 'roles' ? availableRoles.length > 0 ? availableRoles.map(role => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium\",\n                      children: role.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 33\n                    }, this), role.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: role.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleAssignRole(role.id),\n                    className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                    children: \"Assign\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 31\n                  }, this)]\n                }, role.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 29\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"No available roles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500 text-center py-4\",\n                  children: \"Permission assignment feature coming soon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), !selectedUser && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Select a user to manage their roles and permissions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(AssignmentsTab, \"GAwTYIYVR6urtXGWOIu0LOgEN3c=\");\n_c = AssignmentsTab;\nexport default AssignmentsTab;\nvar _c;\n$RefreshReg$(_c, \"AssignmentsTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "adminAPI", "jsxDEV", "_jsxDEV", "AssignmentsTab", "users", "roles", "onMessage", "onError", "_s", "selected<PERSON>ser", "setSelectedUser", "userRoleNames", "setUserRoleNames", "userPermissionNames", "setUserPermissionNames", "availableRoles", "setAvailableRoles", "availablePermissions", "setAvailablePermissions", "loading", "setLoading", "assignmentType", "setAssignmentType", "loadUserDetails", "rolesResponse", "getUserRoles", "id", "setUserRoles", "data", "permissionsResponse", "getUserPermissions", "setUserPermissions", "assignedRoleIds", "map", "role", "filter", "includes", "is_active", "err", "_err$response", "_err$response$data", "response", "error", "message", "handleAssignRole", "roleId", "assignRole", "_err$response2", "_err$response2$data", "handleRemoveRole", "removeRole", "_err$response3", "_err$response3$data", "handleAssignPermission", "permissionId", "assignPermission", "_err$response4", "_err$response4$data", "handleRemovePermission", "removePermission", "_err$response5", "_err$response5$data", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "user", "onClick", "username", "email", "userRoles", "length", "name", "description", "userPermissions", "permission", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/AssignmentsTab.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport { adminAPI } from '../../services/api';\n\ninterface AssignmentsTabProps {\n  users: User[];\n  roles: Role[];\n  onMessage: (message: string) => void;\n  onError: (error: string) => void;\n}\n\nconst AssignmentsTab: React.FC<AssignmentsTabProps> = ({ users, roles, onMessage, onError }) => {\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [userRoleNames, setUserRoleNames] = useState<string[]>([]);\n  const [userPermissionNames, setUserPermissionNames] = useState<string[]>([]);\n  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);\n  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [assignmentType, setAssignmentType] = useState<'roles' | 'permissions'>('roles');\n\n  // Load user details when selected\n  useEffect(() => {\n    if (selectedUser) {\n      loadUserDetails();\n    }\n  }, [selectedUser]);\n\n  const loadUserDetails = async () => {\n    if (!selectedUser) return;\n\n    setLoading(true);\n    try {\n      // Load user roles\n      const rolesResponse = await adminAPI.getUserRoles(selectedUser.id);\n      setUserRoles(rolesResponse.data);\n\n      // Load user permissions\n      const permissionsResponse = await adminAPI.getUserPermissions(selectedUser.id);\n      setUserPermissions(permissionsResponse.data);\n\n      // Set available roles (roles not assigned to user)\n      const assignedRoleIds = rolesResponse.data.map((role: Role) => role.id);\n      setAvailableRoles(roles.filter(role => !assignedRoleIds.includes(role.id) && role.is_active));\n\n      // Load all permissions for available permissions list\n      // Note: This would need to be implemented in the backend to get all permissions\n      // For now, we'll use an empty array\n      setAvailablePermissions([]);\n\n    } catch (err: any) {\n      onError(`Failed to load user details: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssignRole = async (roleId: number) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.assignRole(selectedUser.id, roleId);\n      onMessage('Role assigned successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to assign role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleRemoveRole = async (roleId: number) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.removeRole(selectedUser.id, roleId);\n      onMessage('Role removed successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to remove role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleAssignPermission = async (permissionId: number) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.assignPermission(selectedUser.id, permissionId);\n      onMessage('Permission assigned successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to assign permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleRemovePermission = async (permissionId: number) => {\n    if (!selectedUser) return;\n\n    try {\n      await adminAPI.removePermission(selectedUser.id, permissionId);\n      onMessage('Permission removed successfully');\n      loadUserDetails(); // Reload to update the lists\n    } catch (err: any) {\n      onError(`Failed to remove permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  return (\n    <div>\n      <h2 className=\"text-xl font-semibold mb-6\">User Role & Permission Assignments</h2>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* User Selection */}\n        <div className=\"bg-white p-4 rounded-lg border\">\n          <h3 className=\"text-lg font-medium mb-4\">Select User</h3>\n          <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n            {users.map((user) => (\n              <button\n                key={user.id}\n                onClick={() => setSelectedUser(user)}\n                className={`w-full text-left p-3 rounded border ${selectedUser?.id === user.id\n                    ? 'bg-blue-100 border-blue-500'\n                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'\n                  }`}\n              >\n                <div className=\"font-medium\">{user.username}</div>\n                <div className=\"text-sm text-gray-600\">{user.email}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Assignment Type Toggle */}\n        {selectedUser && (\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white p-4 rounded-lg border\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium\">\n                  Managing: {selectedUser.username}\n                </h3>\n                <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                  <button\n                    onClick={() => setAssignmentType('roles')}\n                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'roles'\n                        ? 'bg-white text-blue-600 shadow-sm'\n                        : 'text-gray-600 hover:text-gray-900'\n                      }`}\n                  >\n                    Roles\n                  </button>\n                  <button\n                    onClick={() => setAssignmentType('permissions')}\n                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'permissions'\n                        ? 'bg-white text-blue-600 shadow-sm'\n                        : 'text-gray-600 hover:text-gray-900'\n                      }`}\n                  >\n                    Permissions\n                  </button>\n                </div>\n              </div>\n\n              {loading ? (\n                <div className=\"text-center py-8\">Loading...</div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {/* Assigned Items */}\n                  <div>\n                    <h4 className=\"font-medium mb-3\">\n                      Assigned {assignmentType === 'roles' ? 'Roles' : 'Permissions'}\n                    </h4>\n                    <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                      {assignmentType === 'roles' ? (\n                        userRoles.length > 0 ? (\n                          userRoles.map((role) => (\n                            <div key={role.id} className=\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\">\n                              <div>\n                                <div className=\"font-medium text-green-800\">{role.name}</div>\n                                {role.description && (\n                                  <div className=\"text-sm text-green-600\">{role.description}</div>\n                                )}\n                              </div>\n                              <button\n                                onClick={() => handleRemoveRole(role.id)}\n                                className=\"text-red-600 hover:text-red-800 text-sm\"\n                              >\n                                Remove\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No roles assigned</div>\n                        )\n                      ) : (\n                        userPermissions.length > 0 ? (\n                          userPermissions.map((permission) => (\n                            <div key={permission.id} className=\"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded\">\n                              <div>\n                                <div className=\"font-medium text-green-800\">{permission.name}</div>\n                                {permission.description && (\n                                  <div className=\"text-sm text-green-600\">{permission.description}</div>\n                                )}\n                              </div>\n                              <button\n                                onClick={() => handleRemovePermission(permission.id)}\n                                className=\"text-red-600 hover:text-red-800 text-sm\"\n                              >\n                                Remove\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No permissions assigned</div>\n                        )\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Available Items */}\n                  <div>\n                    <h4 className=\"font-medium mb-3\">\n                      Available {assignmentType === 'roles' ? 'Roles' : 'Permissions'}\n                    </h4>\n                    <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                      {assignmentType === 'roles' ? (\n                        availableRoles.length > 0 ? (\n                          availableRoles.map((role) => (\n                            <div key={role.id} className=\"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded\">\n                              <div>\n                                <div className=\"font-medium\">{role.name}</div>\n                                {role.description && (\n                                  <div className=\"text-sm text-gray-600\">{role.description}</div>\n                                )}\n                              </div>\n                              <button\n                                onClick={() => handleAssignRole(role.id)}\n                                className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                              >\n                                Assign\n                              </button>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"text-gray-500 text-center py-4\">No available roles</div>\n                        )\n                      ) : (\n                        <div className=\"text-gray-500 text-center py-4\">\n                          Permission assignment feature coming soon\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {!selectedUser && (\n        <div className=\"text-center py-12 text-gray-500\">\n          <p>Select a user to manage their roles and permissions</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AssignmentsTab;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,QAAQ,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS9C,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC9F,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAACe,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhB,QAAQ,CAAW,EAAE,CAAC;EAC5E,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACmB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpB,QAAQ,CAAe,EAAE,CAAC;EAClF,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAA0B,OAAO,CAAC;;EAEtF;EACAC,SAAS,CAAC,MAAM;IACd,IAAIU,YAAY,EAAE;MAChBc,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACd,YAAY,CAAC,CAAC;EAElB,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACd,YAAY,EAAE;IAEnBW,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMI,aAAa,GAAG,MAAMxB,QAAQ,CAACyB,YAAY,CAAChB,YAAY,CAACiB,EAAE,CAAC;MAClEC,YAAY,CAACH,aAAa,CAACI,IAAI,CAAC;;MAEhC;MACA,MAAMC,mBAAmB,GAAG,MAAM7B,QAAQ,CAAC8B,kBAAkB,CAACrB,YAAY,CAACiB,EAAE,CAAC;MAC9EK,kBAAkB,CAACF,mBAAmB,CAACD,IAAI,CAAC;;MAE5C;MACA,MAAMI,eAAe,GAAGR,aAAa,CAACI,IAAI,CAACK,GAAG,CAAEC,IAAU,IAAKA,IAAI,CAACR,EAAE,CAAC;MACvEV,iBAAiB,CAACX,KAAK,CAAC8B,MAAM,CAACD,IAAI,IAAI,CAACF,eAAe,CAACI,QAAQ,CAACF,IAAI,CAACR,EAAE,CAAC,IAAIQ,IAAI,CAACG,SAAS,CAAC,CAAC;;MAE7F;MACA;MACA;MACAnB,uBAAuB,CAAC,EAAE,CAAC;IAE7B,CAAC,CAAC,OAAOoB,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBjC,OAAO,CAAC,gCAAgC,EAAAgC,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcX,IAAI,cAAAY,kBAAA,uBAAlBA,kBAAA,CAAoBE,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IACrF,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,gBAAgB,GAAG,MAAOC,MAAc,IAAK;IACjD,IAAI,CAACpC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMT,QAAQ,CAAC8C,UAAU,CAACrC,YAAY,CAACiB,EAAE,EAAEmB,MAAM,CAAC;MAClDvC,SAAS,CAAC,4BAA4B,CAAC;MACvCiB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOe,GAAQ,EAAE;MAAA,IAAAS,cAAA,EAAAC,mBAAA;MACjBzC,OAAO,CAAC,0BAA0B,EAAAwC,cAAA,GAAAT,GAAG,CAACG,QAAQ,cAAAM,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnB,IAAI,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoBN,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IAC/E;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAG,MAAOJ,MAAc,IAAK;IACjD,IAAI,CAACpC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMT,QAAQ,CAACkD,UAAU,CAACzC,YAAY,CAACiB,EAAE,EAAEmB,MAAM,CAAC;MAClDvC,SAAS,CAAC,2BAA2B,CAAC;MACtCiB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOe,GAAQ,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACjB7C,OAAO,CAAC,0BAA0B,EAAA4C,cAAA,GAAAb,GAAG,CAACG,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcvB,IAAI,cAAAwB,mBAAA,uBAAlBA,mBAAA,CAAoBV,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IAC/E;EACF,CAAC;EAED,MAAMU,sBAAsB,GAAG,MAAOC,YAAoB,IAAK;IAC7D,IAAI,CAAC7C,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMT,QAAQ,CAACuD,gBAAgB,CAAC9C,YAAY,CAACiB,EAAE,EAAE4B,YAAY,CAAC;MAC9DhD,SAAS,CAAC,kCAAkC,CAAC;MAC7CiB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOe,GAAQ,EAAE;MAAA,IAAAkB,cAAA,EAAAC,mBAAA;MACjBlD,OAAO,CAAC,gCAAgC,EAAAiD,cAAA,GAAAlB,GAAG,CAACG,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoBf,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IACrF;EACF,CAAC;EAED,MAAMe,sBAAsB,GAAG,MAAOJ,YAAoB,IAAK;IAC7D,IAAI,CAAC7C,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMT,QAAQ,CAAC2D,gBAAgB,CAAClD,YAAY,CAACiB,EAAE,EAAE4B,YAAY,CAAC;MAC9DhD,SAAS,CAAC,iCAAiC,CAAC;MAC5CiB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOe,GAAQ,EAAE;MAAA,IAAAsB,cAAA,EAAAC,mBAAA;MACjBtD,OAAO,CAAC,gCAAgC,EAAAqD,cAAA,GAAAtB,GAAG,CAACG,QAAQ,cAAAmB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchC,IAAI,cAAAiC,mBAAA,uBAAlBA,mBAAA,CAAoBnB,KAAK,KAAIJ,GAAG,CAACK,OAAO,EAAE,CAAC;IACrF;EACF,CAAC;EAED,oBACEzC,OAAA;IAAA4D,QAAA,gBACE5D,OAAA;MAAI6D,SAAS,EAAC,4BAA4B;MAAAD,QAAA,EAAC;IAAkC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAElFjE,OAAA;MAAK6D,SAAS,EAAC,uCAAuC;MAAAD,QAAA,gBAEpD5D,OAAA;QAAK6D,SAAS,EAAC,gCAAgC;QAAAD,QAAA,gBAC7C5D,OAAA;UAAI6D,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDjE,OAAA;UAAK6D,SAAS,EAAC,oCAAoC;UAAAD,QAAA,EAChD1D,KAAK,CAAC6B,GAAG,CAAEmC,IAAI,iBACdlE,OAAA;YAEEmE,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAAC0D,IAAI,CAAE;YACrCL,SAAS,EAAE,uCAAuC,CAAAtD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiB,EAAE,MAAK0C,IAAI,CAAC1C,EAAE,GACxE,6BAA6B,GAC7B,8CAA8C,EAC/C;YAAAoC,QAAA,gBAEL5D,OAAA;cAAK6D,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAEM,IAAI,CAACE;YAAQ;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDjE,OAAA;cAAK6D,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAEM,IAAI,CAACG;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GARpDC,IAAI,CAAC1C,EAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASN,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL1D,YAAY,iBACXP,OAAA;QAAK6D,SAAS,EAAC,eAAe;QAAAD,QAAA,eAC5B5D,OAAA;UAAK6D,SAAS,EAAC,gCAAgC;UAAAD,QAAA,gBAC7C5D,OAAA;YAAK6D,SAAS,EAAC,wCAAwC;YAAAD,QAAA,gBACrD5D,OAAA;cAAI6D,SAAS,EAAC,qBAAqB;cAAAD,QAAA,GAAC,YACxB,EAACrD,YAAY,CAAC6D,QAAQ;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACLjE,OAAA;cAAK6D,SAAS,EAAC,iCAAiC;cAAAD,QAAA,gBAC9C5D,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAAC,OAAO,CAAE;gBAC1CyC,SAAS,EAAE,4CAA4C1C,cAAc,KAAK,OAAO,GAC3E,kCAAkC,GAClC,mCAAmC,EACpC;gBAAAyC,QAAA,EACN;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAAC,aAAa,CAAE;gBAChDyC,SAAS,EAAE,4CAA4C1C,cAAc,KAAK,aAAa,GACjF,kCAAkC,GAClC,mCAAmC,EACpC;gBAAAyC,QAAA,EACN;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELhD,OAAO,gBACNjB,OAAA;YAAK6D,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAElDjE,OAAA;YAAK6D,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBAEpD5D,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAI6D,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,GAAC,WACtB,EAACzC,cAAc,KAAK,OAAO,GAAG,OAAO,GAAG,aAAa;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACLjE,OAAA;gBAAK6D,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,EAChDzC,cAAc,KAAK,OAAO,GACzBmD,SAAS,CAACC,MAAM,GAAG,CAAC,GAClBD,SAAS,CAACvC,GAAG,CAAEC,IAAI,iBACjBhC,OAAA;kBAAmB6D,SAAS,EAAC,mFAAmF;kBAAAD,QAAA,gBAC9G5D,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAK6D,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EAAE5B,IAAI,CAACwC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC5DjC,IAAI,CAACyC,WAAW,iBACfzE,OAAA;sBAAK6D,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,EAAE5B,IAAI,CAACyC;oBAAW;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAChE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNjE,OAAA;oBACEmE,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACf,IAAI,CAACR,EAAE,CAAE;oBACzCqC,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,EACpD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GAZDjC,IAAI,CAACR,EAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAaZ,CACN,CAAC,gBAEFjE,OAAA;kBAAK6D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACvE,GAEDS,eAAe,CAACH,MAAM,GAAG,CAAC,GACxBG,eAAe,CAAC3C,GAAG,CAAE4C,UAAU,iBAC7B3E,OAAA;kBAAyB6D,SAAS,EAAC,mFAAmF;kBAAAD,QAAA,gBACpH5D,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAK6D,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EAAEe,UAAU,CAACH;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAClEU,UAAU,CAACF,WAAW,iBACrBzE,OAAA;sBAAK6D,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,EAAEe,UAAU,CAACF;oBAAW;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACtE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNjE,OAAA;oBACEmE,OAAO,EAAEA,CAAA,KAAMX,sBAAsB,CAACmB,UAAU,CAACnD,EAAE,CAAE;oBACrDqC,SAAS,EAAC,yCAAyC;oBAAAD,QAAA,EACpD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GAZDU,UAAU,CAACnD,EAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAalB,CACN,CAAC,gBAEFjE,OAAA;kBAAK6D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAE/E;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjE,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAI6D,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,GAAC,YACrB,EAACzC,cAAc,KAAK,OAAO,GAAG,OAAO,GAAG,aAAa;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACLjE,OAAA;gBAAK6D,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,EAChDzC,cAAc,KAAK,OAAO,GACzBN,cAAc,CAAC0D,MAAM,GAAG,CAAC,GACvB1D,cAAc,CAACkB,GAAG,CAAEC,IAAI,iBACtBhC,OAAA;kBAAmB6D,SAAS,EAAC,iFAAiF;kBAAAD,QAAA,gBAC5G5D,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAK6D,SAAS,EAAC,aAAa;sBAAAD,QAAA,EAAE5B,IAAI,CAACwC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC7CjC,IAAI,CAACyC,WAAW,iBACfzE,OAAA;sBAAK6D,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAE5B,IAAI,CAACyC;oBAAW;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC/D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNjE,OAAA;oBACEmE,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACV,IAAI,CAACR,EAAE,CAAE;oBACzCqC,SAAS,EAAC,2CAA2C;oBAAAD,QAAA,EACtD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GAZDjC,IAAI,CAACR,EAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAaZ,CACN,CAAC,gBAEFjE,OAAA;kBAAK6D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACxE,gBAEDjE,OAAA;kBAAK6D,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,EAAC;gBAEhD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAAC1D,YAAY,iBACZP,OAAA;MAAK6D,SAAS,EAAC,iCAAiC;MAAAD,QAAA,eAC9C5D,OAAA;QAAA4D,QAAA,EAAG;MAAmD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA5PIL,cAA6C;AAAA2E,EAAA,GAA7C3E,cAA6C;AA8PnD,eAAeA,cAAc;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}