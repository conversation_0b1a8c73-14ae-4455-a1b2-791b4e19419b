package v1

import (
	"net/http"
	"time"

	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// @Summary Đăng ký tài khoản
// @Description Đăng ký user mới
// @Tags Auth
// @Accept json
// @Produce json
// @Param data body models.RegisterRequest true "Thông tin đăng ký"
// @Success 201 {object} models.AuthResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/auth/register [post]
func Register(c *gin.Context) {
	logger := utils.GetLogger()

	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid registration request", zap.Error(err))
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("User registration attempt",
		zap.String("email", req.Email),
		zap.String("username", req.Username),
	)

	// Check if user already exists
	var existingUser models.User
	if err := database.DB.Where("email = ? OR username = ?", req.Email, req.Username).First(&existingUser).Error; err == nil {
		logger.Warn("Registration failed - user already exists",
			zap.String("email", req.Email),
			zap.String("username", req.Username),
		)
		c.JSON(http.StatusConflict, gin.H{"error": "User already exists with this email or username"})
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		logger.Error("Failed to hash password", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// Create user
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
	}

	if err := database.DB.Create(&user).Error; err != nil {
		logger.Error("Failed to create user in database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// Gán role mặc định cho user
	var role models.Role
	if err := database.DB.Where("name = ?", "user").First(&role).Error; err == nil {
		database.DB.Create(&models.UserRole{UserID: user.ID, RoleID: role.ID})
		logger.Debug("Assigned default role to user",
			zap.Uint("user_id", user.ID),
			zap.String("role", "user"),
		)
	}

	// Generate JWT token
	token, err := utils.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate JWT token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Lấy roles của user
	var roles []string
	database.DB.
		Table("roles").
		Select("roles.name").
		Joins("join user_roles on user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", user.ID).
		Scan(&roles)

	// Generate refresh token for registration
	refreshToken, err := utils.GenerateRefreshToken()
	if err != nil {
		logger.Error("Failed to generate refresh token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token"})
		return
	}

	// Save refresh token to Redis (7 days expiry)
	refreshExpires := 7 * 24 * time.Hour
	if err := utils.SaveRefreshTokenToRedis(user.ID, refreshToken, refreshExpires); err != nil {
		logger.Error("Failed to save refresh token to Redis", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save refresh token"})
		return
	}

	logger.Info("User registered successfully",
		zap.Uint("user_id", user.ID),
		zap.String("email", user.Email),
		zap.String("username", user.Username),
	)

	response := models.AuthResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
		Roles:        roles,
	}
	c.JSON(http.StatusCreated, response)
}

// @Summary Đăng nhập
// @Description Đăng nhập và nhận JWT
// @Tags Auth
// @Accept json
// @Produce json
// @Param data body models.LoginRequest true "Thông tin đăng nhập"
// @Success 200 {object} models.AuthResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/auth/login [post]
func Login(c *gin.Context) {
	logger := utils.GetLogger()
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid login request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	logger.Info("User login attempt", zap.String("email", req.Email))
	var user models.User
	if err := database.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		logger.Warn("Login failed - user not found", zap.String("email", req.Email))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}
	if !utils.CheckPassword(req.Password, user.Password) {
		logger.Warn("Login failed - invalid password", zap.String("email", req.Email), zap.Uint("user_id", user.ID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}
	token, err := utils.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate JWT token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}
	// Sinh refresh token và lưu vào Redis
	refreshToken, err := utils.GenerateRefreshToken()
	if err != nil {
		logger.Error("Failed to generate refresh token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token"})
		return
	}
	refreshExpires := 7 * 24 * time.Hour
	if err := utils.SaveRefreshTokenToRedis(user.ID, refreshToken, refreshExpires); err != nil {
		logger.Error("Failed to save refresh token to Redis", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save refresh token"})
		return
	}

	// Lấy roles của user
	var roles []string
	database.DB.
		Table("roles").
		Select("roles.name").
		Joins("join user_roles on user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", user.ID).
		Scan(&roles)

	logger.Info("User logged in successfully", zap.Uint("user_id", user.ID), zap.String("email", user.Email), zap.String("username", user.Username))

	response := models.AuthResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
		Roles:        roles,
	}
	c.JSON(http.StatusOK, response)
}

// @Summary Refresh access token
// @Description Lấy access token mới từ refresh token
// @Tags Auth
// @Accept json
// @Produce json
// @Param data body models.RefreshTokenRequest true "Refresh token request"
// @Success 200 {object} models.RefreshTokenResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Router /api/v1/auth/refresh [post]
func RefreshToken(c *gin.Context) {
	logger := utils.GetLogger()
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid refresh token request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("Refresh token attempt", zap.Uint("user_id", req.UserID))

	// Validate refresh token
	if !utils.ValidateRefreshToken(req.UserID, req.RefreshToken) {
		logger.Warn("Invalid or expired refresh token", zap.Uint("user_id", req.UserID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired refresh token"})
		return
	}

	// Get user from database
	var user models.User
	if err := database.DB.First(&user, req.UserID).Error; err != nil {
		logger.Error("User not found for refresh", zap.Uint("user_id", req.UserID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Check if user is active
	if !user.IsActive {
		logger.Warn("Inactive user attempted to refresh token", zap.Uint("user_id", req.UserID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User account is inactive"})
		return
	}

	// Generate new access token
	token, err := utils.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate new access token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	logger.Info("Access token refreshed successfully", zap.Uint("user_id", req.UserID))

	response := models.RefreshTokenResponse{
		Token: token,
	}
	c.JSON(http.StatusOK, response)
}

// @Summary Đăng xuất
// @Description Đăng xuất user, xóa refresh token và blacklist access token
// @Tags Auth
// @Accept json
// @Produce json
// @Param data body models.LogoutRequest true "Logout request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/auth/logout [post]
func Logout(c *gin.Context) {
	logger := utils.GetLogger()
	var req models.LogoutRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid logout request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("User logout attempt", zap.Uint("user_id", req.UserID))

	// Delete refresh token from Redis
	if err := utils.DeleteRefreshToken(req.UserID, req.RefreshToken); err != nil {
		logger.Warn("Failed to delete refresh token", zap.Error(err))
	}

	// Blacklist access token (24 hours - same as token expiry)
	expires := 24 * time.Hour
	if err := utils.BlacklistAccessToken(req.AccessToken, expires); err != nil {
		logger.Warn("Failed to blacklist access token", zap.Error(err))
	}

	logger.Info("User logged out successfully", zap.Uint("user_id", req.UserID))
	c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
}

// @Summary Revoke all refresh tokens
// @Description Revoke tất cả refresh tokens của user (logout khỏi tất cả devices)
// @Tags Auth
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Security BearerAuth
// @Router /api/v1/auth/revoke-all [post]
func RevokeAllTokens(c *gin.Context) {
	logger := utils.GetLogger()

	// Get user ID from JWT token (set by AuthMiddleware)
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDUint := userID.(uint)
	logger.Info("Revoking all refresh tokens", zap.Uint("user_id", userIDUint))

	// Revoke all refresh tokens for this user
	if err := utils.RevokeAllRefreshTokens(userIDUint); err != nil {
		logger.Error("Failed to revoke all refresh tokens", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to revoke tokens"})
		return
	}

	logger.Info("All refresh tokens revoked successfully", zap.Uint("user_id", userIDUint))
	c.JSON(http.StatusOK, gin.H{"message": "All refresh tokens revoked successfully"})
}
