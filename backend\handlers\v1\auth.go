package v1

import (
	"net/http"
	"time"

	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// @Summary Đăng ký tài khoản
// @Description Đăng ký user mới
// @Tags Auth
// @Accept json
// @Produce json
// @Param data body models.RegisterRequest true "Thông tin đăng ký"
// @Success 201 {object} models.AuthResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/auth/register [post]
func Register(c *gin.Context) {
	logger := utils.GetLogger()
	
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid registration request", zap.Error(err))
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	logger.Info("User registration attempt", 
		zap.String("email", req.Email),
		zap.String("username", req.Username),
	)

	// Check if user already exists
	var existingUser models.User
	if err := database.DB.Where("email = ? OR username = ?", req.Email, req.Username).First(&existingUser).Error; err == nil {
		logger.Warn("Registration failed - user already exists", 
			zap.String("email", req.Email),
			zap.String("username", req.Username),
		)
		c.JSON(http.StatusConflict, gin.H{"error": "User already exists with this email or username"})
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		logger.Error("Failed to hash password", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// Create user
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
	}

	if err := database.DB.Create(&user).Error; err != nil {
		logger.Error("Failed to create user in database", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// Gán role mặc định cho user
	var role models.Role
	if err := database.DB.Where("name = ?", "user").First(&role).Error; err == nil {
		database.DB.Create(&models.UserRole{UserID: user.ID, RoleID: role.ID})
		logger.Debug("Assigned default role to user", 
			zap.Uint("user_id", user.ID),
			zap.String("role", "user"),
		)
	}

	// Generate JWT token
	token, err := utils.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate JWT token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Lấy roles của user
	var roles []string
	database.DB.
		Table("roles").
		Select("roles.name").
		Joins("join user_roles on user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", user.ID).
		Scan(&roles)

	logger.Info("User registered successfully", 
		zap.Uint("user_id", user.ID),
		zap.String("email", user.Email),
		zap.String("username", user.Username),
	)

	c.JSON(http.StatusCreated, gin.H{
		"token": token,
		"user": user,
		"roles": roles,
	})
}

// @Summary Đăng nhập
// @Description Đăng nhập và nhận JWT
// @Tags Auth
// @Accept json
// @Produce json
// @Param data body models.LoginRequest true "Thông tin đăng nhập"
// @Success 200 {object} models.AuthResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/auth/login [post]
func Login(c *gin.Context) {
	logger := utils.GetLogger()
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid login request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	logger.Info("User login attempt", zap.String("email", req.Email))
	var user models.User
	if err := database.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		logger.Warn("Login failed - user not found", zap.String("email", req.Email))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}
	if !utils.CheckPassword(req.Password, user.Password) {
		logger.Warn("Login failed - invalid password", zap.String("email", req.Email), zap.Uint("user_id", user.ID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}
	token, err := utils.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate JWT token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}
	// Sinh refresh token và lưu vào Redis
	refreshToken, err := utils.GenerateRefreshToken()
	if err != nil {
		logger.Error("Failed to generate refresh token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token"})
		return
	}
	refreshExpires := 7 * 24 * time.Hour
	if err := utils.SaveRefreshTokenToRedis(user.ID, refreshToken, refreshExpires); err != nil {
		logger.Error("Failed to save refresh token to Redis", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save refresh token"})
		return
	}

	// Lấy roles của user
	var roles []string
	database.DB.
		Table("roles").
		Select("roles.name").
		Joins("join user_roles on user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", user.ID).
		Scan(&roles)

	logger.Info("User logged in successfully", zap.Uint("user_id", user.ID), zap.String("email", user.Email), zap.String("username", user.Username))
	c.JSON(http.StatusOK, gin.H{
		"token": token,
		"refresh_token": refreshToken,
		"user": user,
		"roles": roles,
	})
}

// @Summary Refresh access token
// @Description Lấy access token mới từ refresh token
// @Tags Auth
// @Accept json
// @Produce json
// @Param data body map[string]string true "Refresh token"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/auth/refresh [post]
func RefreshToken(c *gin.Context) {
	logger := utils.GetLogger()
	var req struct {
		UserID       uint   `json:"user_id"`
		RefreshToken string `json:"refresh_token"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid refresh token request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if !utils.ValidateRefreshToken(req.UserID, req.RefreshToken) {
		logger.Warn("Invalid or expired refresh token", zap.Uint("user_id", req.UserID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired refresh token"})
		return
	}
	// Lấy user từ DB
	var user models.User
	if err := database.DB.First(&user, req.UserID).Error; err != nil {
		logger.Error("User not found for refresh", zap.Uint("user_id", req.UserID))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}
	token, err := utils.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate new access token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"token": token})
}

// @Summary Đăng xuất
// @Description Đăng xuất user, xóa refresh token và blacklist access token
// @Tags Auth
// @Produce json
// @Param data body map[string]string true "Refresh token và access token"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/logout [post]
func Logout(c *gin.Context) {
	logger := utils.GetLogger()
	var req struct {
		UserID       uint   `json:"user_id"`
		RefreshToken string `json:"refresh_token"`
		AccessToken  string `json:"access_token"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid logout request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	// Xóa refresh token khỏi Redis
	_ = utils.DeleteRefreshToken(req.UserID, req.RefreshToken)
	// Blacklist access token
	expires := 15 * time.Minute // hoặc lấy từ claims của token
	_ = utils.BlacklistAccessToken(req.AccessToken, expires)
	logger.Info("User logged out and token blacklisted", zap.Uint("user_id", req.UserID))
	c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
} 