package services

import (
	"errors"
	"fmt"
	"jwt-auth-backend/database"
	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PermissionService struct {
	*BaseCRUDService
}

func NewPermissionService() *PermissionService {
	baseCRUDService := NewBaseCRUDService(database.DB)
	return &PermissionService{
		BaseCRUDService: baseCRUDService,
	}
}

// CreatePermission creates a new permission with validation
func (s *PermissionService) CreatePermission(req *models.CreatePermissionRequest) (*models.Permission, error) {
	// Create permission model
	permission := &models.Permission{
		Name:        req.Name,
		Description: req.Description,
		IsActive:    true,
	}

	if req.IsActive != nil {
		permission.IsActive = *req.IsActive
	}

	// Validate unique name
	var existingPermission models.Permission
	if err := s.DB.Where("name = ?", permission.Name).First(&existingPermission).Error; err == nil {
		return nil, errors.New("permission name already exists")
	}

	// Create permission using base service
	if err := s.BaseCRUDService.Create(permission); err != nil {
		return nil, err
	}

	utils.Logger.Info("Permission created successfully",
		zap.Uint("id", permission.ID),
		zap.String("name", permission.Name))

	return permission, nil
}

// GetByID gets a permission by ID
func (s *PermissionService) GetByID(id uint) (*models.Permission, error) {
	var permission models.Permission
	if err := s.BaseCRUDService.GetByID(&permission, id); err != nil {
		return nil, err
	}
	return &permission, nil
}

// List lists permissions with pagination and search
func (s *PermissionService) List(req *models.CRUDRequest) (*models.CRUDResponse, error) {
	var permission models.Permission
	results, pagination, err := s.BaseCRUDService.List(&permission, *req)
	if err != nil {
		return nil, err
	}

	return &models.CRUDResponse{
		Data:       results,
		Pagination: &pagination,
	}, nil
}

// Delete deletes a permission
func (s *PermissionService) Delete(id uint) error {
	var permission models.Permission
	return s.BaseCRUDService.Delete(&permission, id)
}

// UpdatePermission updates permission with validation
func (s *PermissionService) UpdatePermission(id uint, req *models.UpdatePermissionRequest) (*models.Permission, error) {
	// Get existing permission
	var existingPermission models.Permission
	if err := s.BaseCRUDService.GetByID(&existingPermission, id); err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != "" {
		// Check name uniqueness
		var permissionWithSameName models.Permission
		if err := s.DB.Where("name = ? AND id != ?", req.Name, id).First(&permissionWithSameName).Error; err == nil {
			return nil, errors.New("permission name already exists")
		}
		existingPermission.Name = req.Name
	}

	if req.Description != "" {
		existingPermission.Description = req.Description
	}

	if req.IsActive != nil {
		existingPermission.IsActive = *req.IsActive
	}

	// Update using base service
	if err := s.BaseCRUDService.Update(&existingPermission, id, &existingPermission); err != nil {
		return nil, err
	}

	utils.Logger.Info("Permission updated successfully",
		zap.Uint("id", existingPermission.ID),
		zap.String("name", existingPermission.Name))

	return &existingPermission, nil
}

// GetPermissionWithRolesAndUsers gets permission with preloaded roles and users
func (s *PermissionService) GetPermissionWithRolesAndUsers(id uint) (*models.Permission, error) {
	var permission models.Permission
	if err := s.DB.Preload("Roles").Preload("Users").First(&permission, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}

	return &permission, nil
}

// DeletePermission soft deletes a permission after checking dependencies
func (s *PermissionService) DeletePermission(id uint) error {
	// Check if permission exists
	var permission models.Permission
	if err := s.BaseCRUDService.GetByID(&permission, id); err != nil {
		return err
	}

	// Check if permission is assigned to any roles
	var rolePermissionCount int64
	if err := s.DB.Model(&models.RolePermission{}).Where("permission_id = ?", id).Count(&rolePermissionCount).Error; err != nil {
		return fmt.Errorf("failed to check role assignments: %w", err)
	}

	if rolePermissionCount > 0 {
		return fmt.Errorf("cannot delete permission '%s' as it is assigned to %d role(s)", permission.Name, rolePermissionCount)
	}

	// Check if permission is assigned to any users
	var userPermissionCount int64
	if err := s.DB.Model(&models.UserPermission{}).Where("permission_id = ?", id).Count(&userPermissionCount).Error; err != nil {
		return fmt.Errorf("failed to check user assignments: %w", err)
	}

	if userPermissionCount > 0 {
		return fmt.Errorf("cannot delete permission '%s' as it is assigned to %d user(s)", permission.Name, userPermissionCount)
	}

	// Delete using base service
	if err := s.BaseCRUDService.Delete(&permission, id); err != nil {
		return err
	}

	utils.Logger.Info("Permission deleted successfully",
		zap.Uint("id", id),
		zap.String("name", permission.Name))

	return nil
}

// GetPermissionsForRole gets all permissions assigned to a role
func (s *PermissionService) GetPermissionsForRole(roleID uint) ([]models.Permission, error) {
	var permissions []models.Permission

	if err := s.DB.Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ? AND permissions.deleted_at IS NULL", roleID).
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions for role: %w", err)
	}

	return permissions, nil
}

// GetPermissionsForUser gets all permissions assigned to a user (both direct and through roles)
func (s *PermissionService) GetPermissionsForUser(userID uint) ([]models.Permission, error) {
	var permissions []models.Permission

	// Get direct permissions
	if err := s.DB.Joins("JOIN user_permissions ON permissions.id = user_permissions.permission_id").
		Where("user_permissions.user_id = ? AND permissions.deleted_at IS NULL", userID).
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get direct permissions for user: %w", err)
	}

	// Get permissions through roles
	var rolePermissions []models.Permission
	if err := s.DB.Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND permissions.deleted_at IS NULL", userID).
		Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions for user: %w", err)
	}

	// Merge and deduplicate permissions
	permissionMap := make(map[uint]models.Permission)

	// Add direct permissions
	for _, perm := range permissions {
		permissionMap[perm.ID] = perm
	}

	// Add role permissions
	for _, perm := range rolePermissions {
		permissionMap[perm.ID] = perm
	}

	// Convert map back to slice
	var allPermissions []models.Permission
	for _, perm := range permissionMap {
		allPermissions = append(allPermissions, perm)
	}

	return allPermissions, nil
}

// CheckUserPermission checks if a user has a specific permission (direct or through roles)
func (s *PermissionService) CheckUserPermission(userID uint, permissionName string) (bool, error) {
	// Check direct permission
	var directCount int64
	if err := s.DB.Model(&models.UserPermission{}).
		Joins("JOIN permissions ON user_permissions.permission_id = permissions.id").
		Where("user_permissions.user_id = ? AND permissions.name = ? AND permissions.deleted_at IS NULL", userID, permissionName).
		Count(&directCount).Error; err != nil {
		return false, fmt.Errorf("failed to check direct permission: %w", err)
	}

	if directCount > 0 {
		return true, nil
	}

	// Check permission through roles
	var roleCount int64
	if err := s.DB.Model(&models.RolePermission{}).
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND permissions.name = ? AND permissions.deleted_at IS NULL", userID, permissionName).
		Count(&roleCount).Error; err != nil {
		return false, fmt.Errorf("failed to check role permission: %w", err)
	}

	return roleCount > 0, nil
}
