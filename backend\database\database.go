package database

import (
	"fmt"
	"os"

	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"go.uber.org/zap"
)

var DB *gorm.DB

func InitDB() {
	logger := utils.GetLogger()

	// Get database configuration from environment variables
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")
	dbSSLMode := os.Getenv("DB_SSLMODE")

	// Set default values if not provided
	if dbHost == "" {
		dbHost = "localhost"
	}
	if dbPort == "" {
		dbPort = "5432"
	}
	if dbUser == "" {
		dbUser = "jwt_user"
	}
	if dbPassword == "" {
		dbPassword = "jwt_password"
	}
	if dbName == "" {
		dbName = "jwt_auth_db"
	}
	if dbSSLMode == "" {
		dbSSLMode = "disable"
	}

	logger.Info("Connecting to database",
		zap.String("host", dbHost),
		zap.String("port", dbPort),
		zap.String("database", dbName),
		zap.String("user", dbUser),
	)

	// Create PostgreSQL connection string
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=Asia/Ho_Chi_Minh",
		dbHost, dbUser, dbPassword, dbName, dbPort, dbSSLMode)

	var err error
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		logger.Fatal("Failed to connect to database", zap.Error(err))
	}

	logger.Info("Database connected successfully")

	// Auto migrate database
	logger.Info("Starting database migration")
	err = DB.AutoMigrate(&models.User{}, &models.Role{}, &models.UserRole{}, &models.Permission{}, &models.RolePermission{}, &models.UserPermission{})
	if err != nil {
		logger.Fatal("Failed to migrate database", zap.Error(err))
	}
	logger.Info("Database migration completed")

	// Seed roles if not exist
	logger.Info("Seeding roles")
	roles := []string{"admin", "user"}
	for _, r := range roles {
		var count int64
		DB.Model(&models.Role{}).Where("name = ?", r).Count(&count)
		if count == 0 {
			DB.Create(&models.Role{Name: r})
			logger.Info("Created role", zap.String("role", r))
		} else {
			logger.Debug("Role already exists", zap.String("role", r))
		}
	}

	// Seed permissions if not exist
	logger.Info("Seeding permissions")
	permissions := []string{"assign_role", "view_user", "edit_user", "delete_user"}
	for _, p := range permissions {
		var count int64
		DB.Model(&models.Permission{}).Where("name = ?", p).Count(&count)
		if count == 0 {
			DB.Create(&models.Permission{Name: p})
			logger.Info("Created permission", zap.String("permission", p))
		} else {
			logger.Debug("Permission already exists", zap.String("permission", p))
		}
	}

	// Gán permission "assign_role" cho role admin nếu chưa có
	logger.Info("Setting up admin role permissions")
	var adminRole models.Role
	var assignPerm models.Permission
	if err := DB.Where("name = ?", "admin").First(&adminRole).Error; err == nil {
		if err := DB.Where("name = ?", "assign_role").First(&assignPerm).Error; err == nil {
			var count int64
			DB.Model(&models.RolePermission{}).Where("role_id = ? AND permission_id = ?", adminRole.ID, assignPerm.ID).Count(&count)
			if count == 0 {
				DB.Create(&models.RolePermission{RoleID: adminRole.ID, PermissionID: assignPerm.ID})
				logger.Info("Assigned permission to admin role", 
					zap.String("permission", "assign_role"),
					zap.Uint("role_id", adminRole.ID),
				)
			} else {
				logger.Debug("Admin role already has assign_role permission")
			}
		}
	}

	logger.Info("Database initialization completed successfully")
} 