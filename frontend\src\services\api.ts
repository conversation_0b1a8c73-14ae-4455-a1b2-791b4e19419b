import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { User, Role, Permission, AuthResponse } from '../types';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Lưu access token và refresh token vào localStorage
export function setTokens(token: string, refreshToken: string) {
  localStorage.setItem('token', token);
  localStorage.setItem('refresh_token', refreshToken);
}
export function clearTokens() {
  localStorage.removeItem('token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
}
export function getRefreshToken() {
  return localStorage.getItem('refresh_token');
}

// Interceptor thêm access token vào header
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Interceptor tự động refresh token khi gặp 401
let isRefreshing = false;
let failedQueue: any[] = [];

function processQueue(error: any, token: string | null = null) {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
}

api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      const refreshToken = getRefreshToken();
      const user = localStorage.getItem('user');
      if (refreshToken && user) {
        try {
          if (isRefreshing) {
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject });
            })
              .then((token) => {
                const t = token as string;
                originalRequest.headers = originalRequest.headers || {};
                originalRequest.headers['Authorization'] = 'Bearer ' + t;
                return api(originalRequest);
              })
              .catch((err) => Promise.reject(err));
          }
          isRefreshing = true;
          const { id } = JSON.parse(user);
          const res = await api.post<{ token: string }>('/auth/refresh', {
            user_id: id,
            refresh_token: refreshToken,
          });
          setTokens(res.data.token, refreshToken);
          processQueue(null, res.data.token);
          originalRequest.headers = originalRequest.headers || {};
          originalRequest.headers['Authorization'] = 'Bearer ' + res.data.token;
          return api(originalRequest);
        } catch (err) {
          processQueue(err, null);
          clearTokens();
          window.location.href = '/login';
          return Promise.reject(err);
        } finally {
          isRefreshing = false;
        }
      } else {
        clearTokens();
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  register: (userData: Partial<User>) => api.post<AuthResponse>('/auth/register', userData),
  login: async (credentials: { email: string; password: string }) => {
    const res = await api.post<AuthResponse>('/auth/login', credentials);
    setTokens(res.data.token, res.data.refresh_token);
    const userWithRoles = { ...res.data.user, roles: res.data.roles };
    localStorage.setItem('user', JSON.stringify(userWithRoles));
    res.data.user = userWithRoles;
    return res;
  },
  refresh: (userId: number, refreshToken: string) =>
    api.post<{ token: string }>('/auth/refresh', { user_id: userId, refresh_token: refreshToken }),
  logout: () => {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refresh_token');
    const user = localStorage.getItem('user');
    let userId = undefined;
    if (user) {
      try {
        userId = JSON.parse(user).id;
      } catch { }
    }
    clearTokens();
    return api.post('/auth/logout', {
      user_id: userId,
      refresh_token: refreshToken,
      access_token: token,
    });
  },
  revokeAllTokens: () => api.post('/auth/revoke-all'),
};

export const userAPI = {
  getProfile: () => api.get<{ user: User }>('/user/profile'),
};

// Types for API responses
export interface PaginationInfo {
  page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
}

export interface CRUDListResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

export interface CRUDResponse<T> {
  data: T;
  message: string;
}

export interface SearchFilter {
  field: string;
  operator: string;
  value: any;
}

export interface SearchRequest {
  page?: number;
  page_size?: number;
  filters?: SearchFilter[];
  order_by?: string;
  order_dir?: 'asc' | 'desc';
}

// User Management API
export const userManagementAPI = {
  // List users with pagination
  list: (params?: { page?: number; page_size?: number; order_by?: string; order_dir?: string }) =>
    api.get<CRUDListResponse<User>>('/admin/user-management', { params }),

  // Search users
  search: (searchRequest: SearchRequest) =>
    api.post<CRUDListResponse<User>>('/admin/user-management/search', searchRequest),

  // Get user by ID
  getById: (id: number) =>
    api.get<CRUDResponse<User>>(`/admin/user-management/${id}`),

  // Create user
  create: (userData: Partial<User>) =>
    api.post<CRUDResponse<User>>('/admin/user-management', userData),

  // Update user
  update: (id: number, userData: Partial<User>) =>
    api.put<CRUDResponse<User>>(`/admin/user-management/${id}`, userData),

  // Delete user
  delete: (id: number) =>
    api.delete<CRUDResponse<any>>(`/admin/user-management/${id}`),

  // Batch delete users
  batchDelete: (ids: number[]) =>
    api.delete<CRUDResponse<any>>('/admin/user-management/batch', { data: { ids } }),
};

// Role Management API
export const roleManagementAPI = {
  // List roles with pagination
  list: (params?: { page?: number; page_size?: number; order_by?: string; order_dir?: string }) =>
    api.get<CRUDListResponse<Role>>('/admin/role-management', { params }),

  // Search roles
  search: (searchRequest: SearchRequest) =>
    api.post<CRUDListResponse<Role>>('/admin/role-management/search', searchRequest),

  // Get role by ID
  getById: (id: number) =>
    api.get<CRUDResponse<Role>>(`/admin/role-management/${id}`),

  // Create role
  create: (roleData: Partial<Role>) =>
    api.post<CRUDResponse<Role>>('/admin/role-management', roleData),

  // Update role
  update: (id: number, roleData: Partial<Role>) =>
    api.put<CRUDResponse<Role>>(`/admin/role-management/${id}`, roleData),

  // Delete role
  delete: (id: number) =>
    api.delete<CRUDResponse<any>>(`/admin/role-management/${id}`),

  // Batch delete roles
  batchDelete: (ids: number[]) =>
    api.delete<CRUDResponse<any>>('/admin/role-management/batch', { data: { ids } }),
};

// Permission Management API
export const permissionManagementAPI = {
  // List permissions with pagination
  list: (params?: { page?: number; page_size?: number; order_by?: string; order_dir?: string }) =>
    api.get<CRUDListResponse<Permission>>('/admin/permission-management', { params }),

  // Search permissions
  search: (searchRequest: SearchRequest) =>
    api.post<CRUDListResponse<Permission>>('/admin/permission-management/search', searchRequest),

  // Get permission by ID
  getById: (id: number) =>
    api.get<CRUDResponse<Permission>>(`/admin/permission-management/${id}`),

  // Create permission
  create: (permissionData: Partial<Permission>) =>
    api.post<CRUDResponse<Permission>>('/admin/permission-management', permissionData),

  // Update permission
  update: (id: number, permissionData: Partial<Permission>) =>
    api.put<CRUDResponse<Permission>>(`/admin/permission-management/${id}`, permissionData),

  // Delete permission
  delete: (id: number) =>
    api.delete<CRUDResponse<any>>(`/admin/permission-management/${id}`),

  // Batch delete permissions
  batchDelete: (ids: number[]) =>
    api.delete<CRUDResponse<any>>('/admin/permission-management/batch', { data: { ids } }),
};

export const adminAPI = {
  listUsers: () => api.get<User[]>('/admin/users'),
  listRoles: () => api.get<Role[]>('/admin/roles'),
  listPermissions: () => api.get<Permission[]>('/admin/permissions'),
  getUserRoles: (userId: number) => api.get<string[]>(`/admin/user/${userId}/roles`),
  getUserPermissions: (userId: number) => api.get<string[]>(`/admin/user/${userId}/permissions`),
  assignRole: (userId: number, role: string) => api.post('/admin/assign-role', { user_id: userId, role }),
  removeRole: (userId: number, role: string) => api.post('/admin/remove-role', { user_id: userId, role }),
  getRolePermissions: (roleId: number) => api.get<string[]>(`/admin/role/${roleId}/permissions`),
  assignPermission: (roleId: number, permission: string) => api.post('/admin/assign-permission', { role_id: roleId, permission }),
  removePermission: (roleId: number, permission: string) => api.post('/admin/remove-permission', { role_id: roleId, permission }),
  getUserDirectPermissions: (userId: number) => api.get<string[]>(`/admin/user/${userId}/direct-permissions`),
  assignUserPermission: (userId: number, permission: string) => api.post('/admin/assign-user-permission', { user_id: userId, permission }),
  removeUserPermission: (userId: number, permission: string) => api.post('/admin/remove-user-permission', { user_id: userId, permission }),
};

export default api; 