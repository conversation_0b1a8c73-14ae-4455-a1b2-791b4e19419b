import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { User, Role, Permission, AuthResponse } from '../types';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Lưu access token và refresh token vào localStorage
export function setTokens(token: string, refreshToken: string) {
  localStorage.setItem('token', token);
  localStorage.setItem('refresh_token', refreshToken);
}
export function clearTokens() {
  localStorage.removeItem('token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
}
export function getRefreshToken() {
  return localStorage.getItem('refresh_token');
}

// Interceptor thêm access token vào header
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Interceptor tự động refresh token khi gặp 401
let isRefreshing = false;
let failedQueue: any[] = [];

function processQueue(error: any, token: string | null = null) {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
}

api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      const refreshToken = getRefreshToken();
      const user = localStorage.getItem('user');
      if (refreshToken && user) {
        try {
          if (isRefreshing) {
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject });
            })
              .then((token) => {
                const t = token as string;
                originalRequest.headers = originalRequest.headers || {};
                originalRequest.headers['Authorization'] = 'Bearer ' + t;
                return api(originalRequest);
              })
              .catch((err) => Promise.reject(err));
          }
          isRefreshing = true;
          const { id } = JSON.parse(user);
          const res = await api.post<{ token: string }>('/auth/refresh', {
            user_id: id,
            refresh_token: refreshToken,
          });
          setTokens(res.data.token, refreshToken);
          processQueue(null, res.data.token);
          originalRequest.headers = originalRequest.headers || {};
          originalRequest.headers['Authorization'] = 'Bearer ' + res.data.token;
          return api(originalRequest);
        } catch (err) {
          processQueue(err, null);
          clearTokens();
          window.location.href = '/login';
          return Promise.reject(err);
        } finally {
          isRefreshing = false;
        }
      } else {
        clearTokens();
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  register: (userData: Partial<User>) => api.post<AuthResponse>('/auth/register', userData),
  login: async (credentials: { email: string; password: string }) => {
    const res = await api.post<AuthResponse>('/auth/login', credentials);
    setTokens(res.data.token, res.data.refresh_token);
    const userWithRoles = { ...res.data.user, roles: res.data.roles };
    localStorage.setItem('user', JSON.stringify(userWithRoles));
    res.data.user = userWithRoles;
    return res;
  },
  refresh: (userId: number, refreshToken: string) =>
    api.post<{ token: string }>('/auth/refresh', { user_id: userId, refresh_token: refreshToken }),
  logout: () => {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refresh_token');
    const user = localStorage.getItem('user');
    let userId = undefined;
    if (user) {
      try {
        userId = JSON.parse(user).id;
      } catch {}
    }
    clearTokens();
    return api.post('/auth/logout', {
      user_id: userId,
      refresh_token: refreshToken,
      access_token: token,
    });
  },
};

export const userAPI = {
  getProfile: () => api.get<{ user: User }>('/user/profile'),
};

export const adminAPI = {
  listUsers: () => api.get<User[]>('/admin/users'),
  listRoles: () => api.get<Role[]>('/admin/roles'),
  listPermissions: () => api.get<Permission[]>('/admin/permissions'),
  getUserRoles: (userId: number) => api.get<string[]>(`/admin/user/${userId}/roles`),
  getUserPermissions: (userId: number) => api.get<string[]>(`/admin/user/${userId}/permissions`),
  assignRole: (userId: number, role: string) => api.post('/admin/assign-role', { user_id: userId, role }),
  removeRole: (userId: number, role: string) => api.post('/admin/remove-role', { user_id: userId, role }),
  getRolePermissions: (roleId: number) => api.get<string[]>(`/admin/role/${roleId}/permissions`),
  assignPermission: (roleId: number, permission: string) => api.post('/admin/assign-permission', { role_id: roleId, permission }),
  removePermission: (roleId: number, permission: string) => api.post('/admin/remove-permission', { role_id: roleId, permission }),
  getUserDirectPermissions: (userId: number) => api.get<string[]>(`/admin/user/${userId}/direct-permissions`),
  assignUserPermission: (userId: number, permission: string) => api.post('/admin/assign-user-permission', { user_id: userId, permission }),
  removeUserPermission: (userId: number, permission: string) => api.post('/admin/remove-user-permission', { user_id: userId, permission }),
};

export default api; 