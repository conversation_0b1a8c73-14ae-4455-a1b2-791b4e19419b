# Role Management API Test Script
Write-Host "🧪 Testing Role Management API" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

$BASE_URL = "http://localhost:8080"
$ADMIN_EMAIL = "<EMAIL>"
$ADMIN_PASSWORD = "admin123"

# Function to get admin token
function Get-AdminToken {
    Write-Host "🔑 Getting admin token..." -ForegroundColor Yellow
    
    # First register admin user (ignore if exists)
    $registerBody = @{
        username = "admin"
        email    = $ADMIN_EMAIL
        password = $ADMIN_PASSWORD
    } | ConvertTo-<PERSON><PERSON>
    
    try {
        Invoke-RestMethod -Uri "$BASE_URL/api/v1/auth/register" -Method POST -Body $registerBody -ContentType "application/json" -ErrorAction SilentlyContinue | Out-Null
    }
    catch {
        # Ignore registration errors (user might already exist)
    }
    
    # Login to get token
    $loginBody = @{
        email    = $ADMIN_EMAIL
        password = $ADMIN_PASSWORD
    } | ConvertTo-<PERSON><PERSON>
    
    try {
        $response = Invoke-RestMethod -Uri "$BASE_URL/api/v1/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
        Write-Host "✅ Admin token obtained" -ForegroundColor Green
        return $response.token
    }
    catch {
        Write-Host "❌ Failed to get admin token: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to test create role
function Test-CreateRole {
    param($Token)
    
    Write-Host ""
    Write-Host "📝 Testing Create Role..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $Token"
        "Content-Type"  = "application/json"
    }
    
    $body = @{
        name        = "test_manager"
        description = "Test manager role for API testing"
        is_active   = $true
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$BASE_URL/api/v1/admin/role-management" -Method POST -Body $body -Headers $headers
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        Write-Host "✅ Role created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to create role: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Gray
    }
}

# Function to test list roles
function Test-ListRoles {
    param($Token)
    
    Write-Host ""
    Write-Host "📋 Testing List Roles..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $Token"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "$BASE_URL/api/v1/admin/role-management?page=1&page_size=5" -Method GET -Headers $headers
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        Write-Host "✅ Roles listed successfully (Total: $($response.pagination.total))" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to list roles: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to test search roles
function Test-SearchRoles {
    param($Token)
    
    Write-Host ""
    Write-Host "🔍 Testing Search Roles..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $Token"
    }
    
    $searchUrl = "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name`&search_filters[0][operator]=like`&search_filters[0][value]=test"
    
    try {
        $response = Invoke-RestMethod -Uri $searchUrl -Method GET -Headers $headers
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        Write-Host "✅ Role search successful (Found: $($response.pagination.total) roles)" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Role search failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to test get role
function Test-GetRole {
    param($Token)
    
    Write-Host ""
    Write-Host "🔍 Testing Get Role..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $Token"
    }
    
    # First get role ID from list
    $searchUrl = "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name`&search_filters[0][operator]=eq`&search_filters[0][value]=test_manager"
    
    try {
        $listResponse = Invoke-RestMethod -Uri $searchUrl -Method GET -Headers $headers
        
        if ($listResponse.data.Count -eq 0) {
            Write-Host "❌ Could not find test_manager role" -ForegroundColor Red
            return
        }
        
        $roleId = $listResponse.data[0].id
        Write-Host "Found role ID: $roleId" -ForegroundColor Gray
        
        $response = Invoke-RestMethod -Uri "$BASE_URL/api/v1/admin/role-management/$roleId" -Method GET -Headers $headers
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        Write-Host "✅ Role retrieved successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to retrieve role: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to test update role
function Test-UpdateRole {
    param($Token)
    
    Write-Host ""
    Write-Host "✏️ Testing Update Role..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $Token"
        "Content-Type"  = "application/json"
    }
    
    # Get role ID
    $searchUrl = "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name`&search_filters[0][operator]=eq`&search_filters[0][value]=test_manager"
    
    try {
        $listResponse = Invoke-RestMethod -Uri $searchUrl -Method GET -Headers $headers
        
        if ($listResponse.data.Count -eq 0) {
            Write-Host "❌ Could not find test_manager role" -ForegroundColor Red
            return
        }
        
        $roleId = $listResponse.data[0].id
        
        $body = @{
            name        = "updated_test_manager"
            description = "Updated test manager role"
            is_active   = $false
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "$BASE_URL/api/v1/admin/role-management/$roleId" -Method PUT -Body $body -Headers $headers
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        Write-Host "✅ Role updated successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to update role: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to test delete role
function Test-DeleteRole {
    param($Token)
    
    Write-Host ""
    Write-Host "🗑️ Testing Delete Role..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $Token"
    }
    
    # Get role ID
    $searchUrl = "$BASE_URL/api/v1/admin/role-management?search_filters[0][field]=name`&search_filters[0][operator]=eq`&search_filters[0][value]=updated_test_manager"
    
    try {
        $listResponse = Invoke-RestMethod -Uri $searchUrl -Method GET -Headers $headers
        
        if ($listResponse.data.Count -eq 0) {
            Write-Host "❌ Could not find updated_test_manager role" -ForegroundColor Red
            return
        }
        
        $roleId = $listResponse.data[0].id
        
        $response = Invoke-RestMethod -Uri "$BASE_URL/api/v1/admin/role-management/$roleId" -Method DELETE -Headers $headers
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        Write-Host "✅ Role deleted successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to delete role: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
function Main {
    # Check if server is running
    try {
        Invoke-RestMethod -Uri "$BASE_URL/swagger/index.html" -Method GET -ErrorAction Stop | Out-Null
    }
    catch {
        Write-Host "❌ Server is not running at $BASE_URL" -ForegroundColor Red
        Write-Host "Please start the server with: go run main.go" -ForegroundColor Yellow
        return
    }
    
    # Get admin token
    $token = Get-AdminToken
    
    if (-not $token) {
        Write-Host "❌ Cannot proceed without admin token" -ForegroundColor Red
        return
    }
    
    # Run tests
    Test-CreateRole $token
    Test-ListRoles $token
    Test-SearchRoles $token
    Test-GetRole $token
    Test-UpdateRole $token
    Test-DeleteRole $token
    
    Write-Host ""
    Write-Host "✅ All Role Management API tests completed!" -ForegroundColor Green
}

# Run main function
Main
