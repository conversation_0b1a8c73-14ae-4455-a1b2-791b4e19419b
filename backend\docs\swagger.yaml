definitions:
  models.AuthResponse:
    properties:
      token:
        type: string
      user:
        $ref: '#/definitions/models.User'
    type: object
  models.LoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    required:
    - email
    - password
    type: object
  models.Permission:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
  models.RegisterRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    - username
    type: object
  models.Role:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
  models.User:
    properties:
      created_at:
        type: string
      email:
        type: string
      id:
        type: integer
      role:
        type: string
      updated_at:
        type: string
      username:
        type: string
    type: object
  v1.AssignPermissionRequest:
    properties:
      permission:
        type: string
      role_id:
        type: integer
    required:
    - permission
    - role_id
    type: object
  v1.AssignRoleRequest:
    properties:
      role:
        type: string
      user_id:
        type: integer
    required:
    - role
    - user_id
    type: object
  v1.AssignUserPermissionRequest:
    properties:
      permission:
        type: string
      user_id:
        type: integer
    required:
    - permission
    - user_id
    type: object
  v1.RemovePermissionRequest:
    properties:
      permission:
        type: string
      role_id:
        type: integer
    required:
    - permission
    - role_id
    type: object
  v1.RemoveRoleRequest:
    properties:
      role:
        type: string
      user_id:
        type: integer
    required:
    - role
    - user_id
    type: object
  v1.RemoveUserPermissionRequest:
    properties:
      permission:
        type: string
      user_id:
        type: integer
    required:
    - permission
    - user_id
    type: object
  v1.UserWithRoles:
    properties:
      email:
        type: string
      id:
        type: integer
      roles:
        items:
          type: string
        type: array
      username:
        type: string
    type: object
info:
  contact: {}
  description: API with JWT authentication and RBAC
  title: JWT Auth API
  version: "1.0"
paths:
  /api/v1/admin/assign-permission:
    post:
      consumes:
      - application/json
      parameters:
      - description: Thông tin gán permission
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/v1.AssignPermissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Gán permission cho role
      tags:
      - Admin
  /api/v1/admin/assign-role:
    post:
      consumes:
      - application/json
      parameters:
      - description: Thông tin gán role
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/v1.AssignRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Gán role cho user
      tags:
      - Admin
  /api/v1/admin/assign-user-permission:
    post:
      consumes:
      - application/json
      parameters:
      - description: Thông tin gán permission
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/v1.AssignUserPermissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Gán permission trực tiếp cho user
      tags:
      - Admin
  /api/v1/admin/permissions:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Permission'
            type: array
      security:
      - BearerAuth: []
      summary: Lấy danh sách permission
      tags:
      - Admin
  /api/v1/admin/remove-permission:
    post:
      consumes:
      - application/json
      parameters:
      - description: Thông tin xóa permission
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/v1.RemovePermissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Xóa permission khỏi role
      tags:
      - Admin
  /api/v1/admin/remove-role:
    post:
      consumes:
      - application/json
      parameters:
      - description: Thông tin xóa role
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/v1.RemoveRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Xóa role khỏi user
      tags:
      - Admin
  /api/v1/admin/remove-user-permission:
    post:
      consumes:
      - application/json
      parameters:
      - description: Thông tin xóa permission
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/v1.RemoveUserPermissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Xóa permission trực tiếp khỏi user
      tags:
      - Admin
  /api/v1/admin/role/{id}/permissions:
    get:
      parameters:
      - description: Role ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      security:
      - BearerAuth: []
      summary: Lấy permission của role
      tags:
      - Admin
  /api/v1/admin/roles:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Role'
            type: array
      security:
      - BearerAuth: []
      summary: Lấy danh sách role
      tags:
      - Admin
  /api/v1/admin/user/{id}/direct-permissions:
    get:
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      security:
      - BearerAuth: []
      summary: Lấy direct permission của user
      tags:
      - Admin
  /api/v1/admin/user/{id}/permissions:
    get:
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      security:
      - BearerAuth: []
      summary: Lấy permission của user
      tags:
      - Admin
  /api/v1/admin/user/{id}/roles:
    get:
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      security:
      - BearerAuth: []
      summary: Lấy role của user
      tags:
      - Admin
  /api/v1/admin/users:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/v1.UserWithRoles'
            type: array
      security:
      - BearerAuth: []
      summary: Lấy danh sách user
      tags:
      - Admin
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: Đăng nhập và nhận JWT
      parameters:
      - description: Thông tin đăng nhập
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/models.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AuthResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      summary: Đăng nhập
      tags:
      - Auth
  /api/v1/auth/logout:
    post:
      description: Đăng xuất user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
      summary: Đăng xuất
      tags:
      - Auth
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: Đăng ký user mới
      parameters:
      - description: Thông tin đăng ký
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/models.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.AuthResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      summary: Đăng ký tài khoản
      tags:
      - Auth
  /api/v1/user/profile:
    get:
      consumes:
      - application/json
      description: Lấy thông tin user từ JWT token
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Lấy thông tin user
      tags:
      - User
  /api/v2/auth/login:
    post:
      consumes:
      - application/json
      description: Đăng nhập và nhận JWT cho API v2
      parameters:
      - description: Thông tin đăng nhập
        in: body
        name: data
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      summary: Đăng nhập (v2)
      tags:
      - Auth v2
  /api/v2/auth/logout:
    post:
      description: Đăng xuất user cho API v2
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
      summary: Đăng xuất (v2)
      tags:
      - Auth v2
  /api/v2/auth/register:
    post:
      consumes:
      - application/json
      description: Đăng ký user mới cho API v2
      parameters:
      - description: Thông tin đăng ký
        in: body
        name: data
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      summary: Đăng ký tài khoản (v2)
      tags:
      - Auth v2
securityDefinitions:
  BearerAuth:
    description: 'Nhập token dạng: Bearer <token>'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
