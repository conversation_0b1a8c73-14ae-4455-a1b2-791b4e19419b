@echo off
echo Checking PostgreSQL connection...
echo.

echo 1. Checking if PostgreSQL container is running...
docker ps | findstr jwt-auth-postgres
if %errorlevel% neq 0 (
    echo ERROR: PostgreSQL container is not running!
    echo Please run: start-postgres.bat
    pause
    exit /b 1
)

echo.
echo 2. Checking PostgreSQL logs...
docker-compose logs --tail=10 postgres

echo.
echo 3. Testing database connection...
docker exec jwt-auth-postgres pg_isready -U jwt_user -d jwt_auth_db
if %errorlevel% equ 0 (
    echo SUCCESS: Database is ready!
) else (
    echo ERROR: Database is not ready yet.
    echo Please wait a few more seconds and try again.
)

echo.
echo Database Configuration:
echo - Host: localhost
echo - Port: 5432
echo - Database: jwt_auth_db
echo - User: jwt_user
echo - Password: jwt_password

pause 