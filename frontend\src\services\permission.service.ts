import { AxiosResponse } from 'axios';
import { Permission } from '../types';
import { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';

export interface CreatePermissionRequest {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdatePermissionRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
}

class PermissionService extends BaseCRUDService<Permission, CreatePermissionRequest, UpdatePermissionRequest> {
  constructor() {
    super('/admin/permission-management');
  }

  // Get all permissions (from old admin API)
  getAllPermissions(): Promise<AxiosResponse<Permission[]>> {
    return api.get('/admin/permissions');
  }

  // Get active permissions only
  getActivePermissions(): Promise<AxiosResponse<CRUDListResponse<Permission>>> {
    return this.list({ active_only: true });
  }

  // Search permissions with custom filters
  searchPermissions(searchTerm: string, page: number = 1, pageSize: number = 10): Promise<AxiosResponse<CRUDListResponse<Permission>>> {
    return this.search({
      page,
      page_size: pageSize,
      search_term: searchTerm,
      filters: {
        is_active: true
      }
    });
  }

  // Get permissions by name pattern
  getPermissionsByName(namePattern: string): Promise<AxiosResponse<CRUDListResponse<Permission>>> {
    return this.list({
      page: 1,
      page_size: 100,
      name_like: namePattern
    });
  }

  // Toggle permission active status
  toggleActiveStatus(permissionId: number, isActive: boolean): Promise<AxiosResponse<CRUDResponse<Permission>>> {
    return this.update(permissionId, { is_active: isActive });
  }

  // Get permissions by category (if implemented in backend)
  getPermissionsByCategory(category: string): Promise<AxiosResponse<CRUDListResponse<Permission>>> {
    return this.list({
      page: 1,
      page_size: 100,
      category: category
    });
  }

  // Bulk operations
  bulkDelete(permissionIds: number[]): Promise<AxiosResponse<{ message: string; deleted_count: number }>> {
    return api.post(`${this.baseUrl}/bulk-delete`, { ids: permissionIds });
  }

  bulkToggleStatus(permissionIds: number[], isActive: boolean): Promise<AxiosResponse<{ message: string; updated_count: number }>> {
    return api.post(`${this.baseUrl}/bulk-toggle-status`, { ids: permissionIds, is_active: isActive });
  }

  // Get permissions that are not assigned to a specific role
  getUnassignedPermissions(roleId: number): Promise<AxiosResponse<CRUDListResponse<Permission>>> {
    return this.list({
      page: 1,
      page_size: 100,
      exclude_role: roleId,
      is_active: true
    });
  }

  // Get permissions that are not directly assigned to a specific user
  getUnassignedUserPermissions(userId: number): Promise<AxiosResponse<CRUDListResponse<Permission>>> {
    return this.list({
      page: 1,
      page_size: 100,
      exclude_user: userId,
      is_active: true
    });
  }

  // Advanced search with multiple filters
  advancedSearch(filters: {
    search_term?: string;
    is_active?: boolean;
    category?: string;
    created_after?: string;
    created_before?: string;
    page?: number;
    page_size?: number;
  }): Promise<AxiosResponse<CRUDListResponse<Permission>>> {
    return this.search({
      page: filters.page || 1,
      page_size: filters.page_size || 10,
      search_term: filters.search_term,
      filters: {
        is_active: filters.is_active,
        category: filters.category,
        created_after: filters.created_after,
        created_before: filters.created_before
      }
    });
  }
}

export const permissionService = new PermissionService();
export default permissionService;
