{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('access_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for automatic token refresh\napi.interceptors.response.use(response => response, async error => {\n  var _error$response;\n  const originalRequest = error.config;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    try {\n      const refreshToken = localStorage.getItem('refresh_token');\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n      const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n        refresh_token: refreshToken\n      });\n      const {\n        token: newAccessToken,\n        refresh_token: newRefreshToken\n      } = response.data;\n      localStorage.setItem('access_token', newAccessToken);\n      localStorage.setItem('refresh_token', newRefreshToken);\n\n      // Retry the original request with new token\n      originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;\n      return api(originalRequest);\n    } catch (refreshError) {\n      // Refresh failed, redirect to login\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n      return Promise.reject(refreshError);\n    }\n  }\n  return Promise.reject(error);\n});\nexport const authService = {\n  // Login user\n  login: credentials => {\n    return api.post('/auth/login', credentials);\n  },\n  // Register user\n  register: userData => {\n    return api.post('/auth/register', userData);\n  },\n  // Refresh token\n  refreshToken: refreshToken => {\n    return api.post('/auth/refresh', {\n      refresh_token: refreshToken\n    });\n  },\n  // Logout user\n  logout: () => {\n    return api.post('/auth/logout');\n  },\n  // Revoke all tokens\n  revokeAllTokens: () => {\n    return api.post('/auth/revoke-all');\n  },\n  // Get user profile\n  getProfile: () => {\n    return api.get('/user/profile');\n  },\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    const token = localStorage.getItem('access_token');\n    return !!token;\n  },\n  // Get current user from localStorage\n  getCurrentUser: () => {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  },\n  // Check if user has specific role\n  hasRole: role => {\n    var _user$roles;\n    const user = authService.getCurrentUser();\n    return (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes(role)) || false;\n  },\n  // Check if user is admin\n  isAdmin: () => {\n    return authService.hasRole('admin');\n  },\n  // Clear authentication data\n  clearAuth: () => {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n    localStorage.removeItem('user');\n  },\n  // Store authentication data\n  storeAuth: authData => {\n    localStorage.setItem('access_token', authData.token);\n    localStorage.setItem('refresh_token', authData.refresh_token);\n    localStorage.setItem('user', JSON.stringify(authData.user));\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "Error", "post", "refresh_token", "newAccessToken", "newRefreshToken", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "authService", "login", "credentials", "register", "userData", "logout", "revokeAllTokens", "getProfile", "get", "isAuthenticated", "getCurrentUser", "userStr", "JSON", "parse", "hasRole", "role", "_user$roles", "user", "roles", "includes", "isAdmin", "clearAuth", "storeAuth", "authData", "stringify"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/auth.service.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\nimport { User, AuthResponse } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for automatic token refresh\napi.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refresh_token');\n        if (!refreshToken) {\n          throw new Error('No refresh token available');\n        }\n\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refresh_token: refreshToken,\n        });\n\n        const { token: newAccessToken, refresh_token: newRefreshToken } = response.data;\n        \n        localStorage.setItem('access_token', newAccessToken);\n        localStorage.setItem('refresh_token', newRefreshToken);\n\n        // Retry the original request with new token\n        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;\n        return api(originalRequest);\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n}\n\nexport const authService = {\n  // Login user\n  login: (credentials: LoginRequest): Promise<AxiosResponse<AuthResponse>> => {\n    return api.post('/auth/login', credentials);\n  },\n\n  // Register user\n  register: (userData: RegisterRequest): Promise<AxiosResponse<AuthResponse>> => {\n    return api.post('/auth/register', userData);\n  },\n\n  // Refresh token\n  refreshToken: (refreshToken: string): Promise<AxiosResponse<AuthResponse>> => {\n    return api.post('/auth/refresh', { refresh_token: refreshToken });\n  },\n\n  // Logout user\n  logout: (): Promise<AxiosResponse<{ message: string }>> => {\n    return api.post('/auth/logout');\n  },\n\n  // Revoke all tokens\n  revokeAllTokens: (): Promise<AxiosResponse<{ message: string }>> => {\n    return api.post('/auth/revoke-all');\n  },\n\n  // Get user profile\n  getProfile: (): Promise<AxiosResponse<{ user: User; roles: string[] }>> => {\n    return api.get('/user/profile');\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: (): boolean => {\n    const token = localStorage.getItem('access_token');\n    return !!token;\n  },\n\n  // Get current user from localStorage\n  getCurrentUser: (): User | null => {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  },\n\n  // Check if user has specific role\n  hasRole: (role: string): boolean => {\n    const user = authService.getCurrentUser();\n    return user?.roles?.includes(role) || false;\n  },\n\n  // Check if user is admin\n  isAdmin: (): boolean => {\n    return authService.hasRole('admin');\n  },\n\n  // Clear authentication data\n  clearAuth: (): void => {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n    localStorage.removeItem('user');\n  },\n\n  // Store authentication data\n  storeAuth: (authData: AuthResponse): void => {\n    localStorage.setItem('access_token', authData.token);\n    localStorage.setItem('refresh_token', authData.refresh_token);\n    localStorage.setItem('user', JSON.stringify(authData.user));\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;AAG5C,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;;AAEpF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACf,MAAMC,eAAe,GAAGL,KAAK,CAACL,MAAM;EAEpC,IAAI,EAAAS,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,IAAI;MACF,MAAMC,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAC1D,IAAI,CAACU,YAAY,EAAE;QACjB,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;MAC/C;MAEA,MAAMN,QAAQ,GAAG,MAAMpB,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,YAAY,eAAe,EAAE;QAChE2B,aAAa,EAAEH;MACjB,CAAC,CAAC;MAEF,MAAM;QAAEZ,KAAK,EAAEgB,cAAc;QAAED,aAAa,EAAEE;MAAgB,CAAC,GAAGV,QAAQ,CAACW,IAAI;MAE/EjB,YAAY,CAACkB,OAAO,CAAC,cAAc,EAAEH,cAAc,CAAC;MACpDf,YAAY,CAACkB,OAAO,CAAC,eAAe,EAAEF,eAAe,CAAC;;MAEtD;MACAR,eAAe,CAACd,OAAO,CAACQ,aAAa,GAAG,UAAUa,cAAc,EAAE;MAClE,OAAOxB,GAAG,CAACiB,eAAe,CAAC;IAC7B,CAAC,CAAC,OAAOW,YAAY,EAAE;MACrB;MACAnB,YAAY,CAACoB,UAAU,CAAC,cAAc,CAAC;MACvCpB,YAAY,CAACoB,UAAU,CAAC,eAAe,CAAC;MACxCpB,YAAY,CAACoB,UAAU,CAAC,MAAM,CAAC;MAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAOnB,OAAO,CAACC,MAAM,CAACc,YAAY,CAAC;IACrC;EACF;EAEA,OAAOf,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAaD,OAAO,MAAMqB,WAAW,GAAG;EACzB;EACAC,KAAK,EAAGC,WAAyB,IAA2C;IAC1E,OAAOnC,GAAG,CAACsB,IAAI,CAAC,aAAa,EAAEa,WAAW,CAAC;EAC7C,CAAC;EAED;EACAC,QAAQ,EAAGC,QAAyB,IAA2C;IAC7E,OAAOrC,GAAG,CAACsB,IAAI,CAAC,gBAAgB,EAAEe,QAAQ,CAAC;EAC7C,CAAC;EAED;EACAjB,YAAY,EAAGA,YAAoB,IAA2C;IAC5E,OAAOpB,GAAG,CAACsB,IAAI,CAAC,eAAe,EAAE;MAAEC,aAAa,EAAEH;IAAa,CAAC,CAAC;EACnE,CAAC;EAED;EACAkB,MAAM,EAAEA,CAAA,KAAmD;IACzD,OAAOtC,GAAG,CAACsB,IAAI,CAAC,cAAc,CAAC;EACjC,CAAC;EAED;EACAiB,eAAe,EAAEA,CAAA,KAAmD;IAClE,OAAOvC,GAAG,CAACsB,IAAI,CAAC,kBAAkB,CAAC;EACrC,CAAC;EAED;EACAkB,UAAU,EAAEA,CAAA,KAA+D;IACzE,OAAOxC,GAAG,CAACyC,GAAG,CAAC,eAAe,CAAC;EACjC,CAAC;EAED;EACAC,eAAe,EAAEA,CAAA,KAAe;IAC9B,MAAMlC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,OAAO,CAAC,CAACF,KAAK;EAChB,CAAC;EAED;EACAmC,cAAc,EAAEA,CAAA,KAAmB;IACjC,MAAMC,OAAO,GAAGnC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC5C,OAAOkC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC,GAAG,IAAI;EAC7C,CAAC;EAED;EACAG,OAAO,EAAGC,IAAY,IAAc;IAAA,IAAAC,WAAA;IAClC,MAAMC,IAAI,GAAGjB,WAAW,CAACU,cAAc,CAAC,CAAC;IACzC,OAAO,CAAAO,IAAI,aAAJA,IAAI,wBAAAD,WAAA,GAAJC,IAAI,CAAEC,KAAK,cAAAF,WAAA,uBAAXA,WAAA,CAAaG,QAAQ,CAACJ,IAAI,CAAC,KAAI,KAAK;EAC7C,CAAC;EAED;EACAK,OAAO,EAAEA,CAAA,KAAe;IACtB,OAAOpB,WAAW,CAACc,OAAO,CAAC,OAAO,CAAC;EACrC,CAAC;EAED;EACAO,SAAS,EAAEA,CAAA,KAAY;IACrB7C,YAAY,CAACoB,UAAU,CAAC,cAAc,CAAC;IACvCpB,YAAY,CAACoB,UAAU,CAAC,eAAe,CAAC;IACxCpB,YAAY,CAACoB,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED;EACA0B,SAAS,EAAGC,QAAsB,IAAW;IAC3C/C,YAAY,CAACkB,OAAO,CAAC,cAAc,EAAE6B,QAAQ,CAAChD,KAAK,CAAC;IACpDC,YAAY,CAACkB,OAAO,CAAC,eAAe,EAAE6B,QAAQ,CAACjC,aAAa,CAAC;IAC7Dd,YAAY,CAACkB,OAAO,CAAC,MAAM,EAAEkB,IAAI,CAACY,SAAS,CAACD,QAAQ,CAACN,IAAI,CAAC,CAAC;EAC7D;AACF,CAAC;AAED,eAAejB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}