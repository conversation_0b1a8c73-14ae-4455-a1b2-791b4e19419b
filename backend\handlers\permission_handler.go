package handlers

import (
	"net/http"
	"strconv"

	"jwt-auth-backend/models"
	"jwt-auth-backend/services"
	"jwt-auth-backend/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PermissionHandler struct {
	permissionService *services.PermissionService
}

func NewPermissionHandler() *PermissionHandler {
	return &PermissionHandler{
		permissionService: services.NewPermissionService(),
	}
}

// CreatePermission creates a new permission
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req models.CreatePermissionRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		utils.Logger.Warn("Invalid create permission request", zap.Error(err))
		c.JSO<PERSON>(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	permission, err := h.permissionService.CreatePermission(&req)
	if err != nil {
		if err.Error() == "permission name already exists" {
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "Permission already exists",
				Details: err.Error(),
				Code:    "PERMISSION_EXISTS",
			})
			return
		}

		utils.Logger.Error("Failed to create permission", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to create permission",
			Details: err.Error(),
			Code:    "CREATION_FAILED",
		})
		return
	}

	response := models.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		Description: permission.Description,
		IsActive:    permission.IsActive,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}

	c.JSON(http.StatusCreated, models.CRUDResponse{
		Data:    response,
		Message: "Permission created successfully",
	})
}

// ListPermissions lists permissions with pagination and search
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	var req models.CRUDRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Logger.Warn("Invalid list permissions request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	// Set default pagination if not provided
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.permissionService.List(&req)
	if err != nil {
		utils.Logger.Error("Failed to list permissions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to retrieve permissions",
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	var permissionResponses []models.PermissionResponse
	if permissions, ok := result.Data.([]models.Permission); ok {
		for _, permission := range permissions {
			permissionResponses = append(permissionResponses, models.PermissionResponse{
				ID:          permission.ID,
				Name:        permission.Name,
				Description: permission.Description,
				IsActive:    permission.IsActive,
				CreatedAt:   permission.CreatedAt,
				UpdatedAt:   permission.UpdatedAt,
			})
		}
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:       permissionResponses,
		Pagination: result.Pagination,
	})
}

// GetPermission gets a permission by ID
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid permission ID",
			Details: "Permission ID must be a valid number",
		})
		return
	}

	permission, err := h.permissionService.GetByID(uint(id))
	if err != nil {
		utils.Logger.Error("Failed to get permission", zap.Error(err))
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error:   "Permission not found",
			Details: err.Error(),
		})
		return
	}

	response := models.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		Description: permission.Description,
		IsActive:    permission.IsActive,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:    response,
		Message: "Permission retrieved successfully",
	})
}

// UpdatePermission updates a permission
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid permission ID",
			Details: "Permission ID must be a valid number",
		})
		return
	}

	var req models.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Logger.Warn("Invalid update permission request", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request data",
			Details: err.Error(),
		})
		return
	}

	permission, err := h.permissionService.UpdatePermission(uint(id), &req)
	if err != nil {
		if err.Error() == "permission name already exists" {
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "Permission name already exists",
				Details: err.Error(),
				Code:    "PERMISSION_EXISTS",
			})
			return
		}

		utils.Logger.Error("Failed to update permission", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to update permission",
			Details: err.Error(),
			Code:    "UPDATE_FAILED",
		})
		return
	}

	response := models.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		Description: permission.Description,
		IsActive:    permission.IsActive,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Data:    response,
		Message: "Permission updated successfully",
	})
}

// DeletePermission deletes a permission
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid permission ID",
			Details: "Permission ID must be a valid number",
		})
		return
	}

	if err := h.permissionService.DeletePermission(uint(id)); err != nil {
		utils.Logger.Error("Failed to delete permission", zap.Error(err))
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to delete permission",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.CRUDResponse{
		Message: "Permission deleted successfully",
	})
}

// SetupPermissionRoutes sets up all permission-related routes
func (h *PermissionHandler) SetupPermissionRoutes(router *gin.RouterGroup) {
	permissions := router.Group("/permission-management")
	{
		permissions.POST("", h.CreatePermission)       // POST /permission-management
		permissions.GET("", h.ListPermissions)         // GET /permission-management
		permissions.GET("/:id", h.GetPermission)       // GET /permission-management/:id
		permissions.PUT("/:id", h.UpdatePermission)    // PUT /permission-management/:id
		permissions.DELETE("/:id", h.DeletePermission) // DELETE /permission-management/:id
	}
}
