import React, { useEffect, useState } from 'react';
import api, { adminAPI } from '../../services/api';
import { User, Role, Permission } from '../../types';

// ... (các component phụ như RoleBadge, PermissionBadge, UserPermissionModal, PermissionModal giữ nguyên logic, chỉ thêm type cho props)

interface UserWithPermissions extends User {
  permissions: string[];
  directPermissions: string[];
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<UserWithPermissions[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<UserWithPermissions | null>(null);
  const [showPermissions, setShowPermissions] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError('');
    try {
      const res = await adminAPI.listUsers();
      const userList: User[] = res.data;
      // Lấy quyền cho từng user
      const usersWithPerms: UserWithPermissions[] = await Promise.all(
        userList.map(async (user) => {
          const [permissionsRes, directPermsRes] = await Promise.all([
            adminAPI.getUserPermissions(user.id),
            adminAPI.getUserDirectPermissions(user.id),
          ]);
          return {
            ...user,
            permissions: permissionsRes.data,
            directPermissions: directPermsRes.data,
          };
        })
      );
      setUsers(usersWithPerms);
    } catch (err) {
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleShowPermissions = (user: UserWithPermissions) => {
    setSelectedUser(user);
    setShowPermissions(true);
  };

  const handleClosePermissions = () => {
    setSelectedUser(null);
    setShowPermissions(false);
  };

  return (
    <div className="container">
      <h2>User Management</h2>
      {error && <div className="error">{error}</div>}
      {loading ? (
        <p>Loading users...</p>
      ) : (
        <table className="user-table" style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr>
              <th>ID</th>
              <th>Username</th>
              <th>Email</th>
              <th>Role</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.id}>
                <td>{user.id}</td>
                <td>{user.username}</td>
                <td>{user.email}</td>
                <td>{user.roles && user.roles.length > 0 ? user.roles.join(', ') : 'None'}</td>
                <td>
                  <button className="btn" onClick={() => handleShowPermissions(user)}>
                    Xem quyền
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      {/* Modal hiển thị quyền */}
      {showPermissions && selectedUser && (
        <div className="modal-overlay" style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', background: 'rgba(0,0,0,0.3)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <div className="modal" style={{ background: 'white', padding: 30, borderRadius: 10, minWidth: 350 }}>
            <h3>Quyền của user: {selectedUser.username}</h3>
            <div>
              <strong>Quyền tổng hợp (qua role):</strong>
              <ul>
                {!selectedUser.permissions || selectedUser.permissions.length === 0 ? <li>Không có</li> : selectedUser.permissions.map((p) => <li key={p}>{p}</li>)}
              </ul>
            </div>
            <div>
              <strong>Quyền trực tiếp:</strong>
              <ul>
                {!selectedUser.directPermissions || selectedUser.directPermissions.length === 0 ? <li>Không có</li> : selectedUser.directPermissions.map((p) => <li key={p}>{p}</li>)}
              </ul>
            </div>
            <button className="btn btn-secondary" onClick={handleClosePermissions} style={{ marginTop: 20 }}>
              Đóng
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement; 