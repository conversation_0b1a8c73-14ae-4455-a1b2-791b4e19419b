{"ast": null, "code": "import { BaseCRUDService, api } from './base.service';\nclass PermissionService extends BaseCRUDService {\n  constructor() {\n    super('/admin/permission-management');\n  }\n\n  // Get all permissions (from old admin API)\n  getAllPermissions() {\n    return api.get('/admin/permissions');\n  }\n\n  // Get active permissions only\n  getActivePermissions() {\n    return this.list({\n      active_only: true\n    });\n  }\n\n  // Search permissions with custom filters\n  searchPermissions(searchTerm, page = 1, pageSize = 10) {\n    return this.search({\n      page,\n      page_size: pageSize,\n      search_term: searchTerm,\n      filters: {\n        is_active: true\n      }\n    });\n  }\n\n  // Get permissions by name pattern\n  getPermissionsByName(namePattern) {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      name_like: namePattern\n    });\n  }\n\n  // Toggle permission active status\n  toggleActiveStatus(permissionId, isActive) {\n    return this.update(permissionId, {\n      is_active: isActive\n    });\n  }\n\n  // Get permissions by category (if implemented in backend)\n  getPermissionsByCategory(category) {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      category: category\n    });\n  }\n\n  // Bulk operations\n  bulkDelete(permissionIds) {\n    return api.post(`${this.baseUrl}/bulk-delete`, {\n      ids: permissionIds\n    });\n  }\n  bulkToggleStatus(permissionIds, isActive) {\n    return api.post(`${this.baseUrl}/bulk-toggle-status`, {\n      ids: permissionIds,\n      is_active: isActive\n    });\n  }\n\n  // Get permissions that are not assigned to a specific role\n  getUnassignedPermissions(roleId) {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      exclude_role: roleId,\n      is_active: true\n    });\n  }\n\n  // Get permissions that are not directly assigned to a specific user\n  getUnassignedUserPermissions(userId) {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      exclude_user: userId,\n      is_active: true\n    });\n  }\n\n  // Advanced search with multiple filters\n  advancedSearch(filters) {\n    return this.search({\n      page: filters.page || 1,\n      page_size: filters.page_size || 10,\n      search_term: filters.search_term,\n      filters: {\n        is_active: filters.is_active,\n        category: filters.category,\n        created_after: filters.created_after,\n        created_before: filters.created_before\n      }\n    });\n  }\n}\nexport const permissionService = new PermissionService();\nexport default permissionService;", "map": {"version": 3, "names": ["BaseCRUDService", "api", "PermissionService", "constructor", "getAllPermissions", "get", "getActivePermissions", "list", "active_only", "searchPermissions", "searchTerm", "page", "pageSize", "search", "page_size", "search_term", "filters", "is_active", "getPermissionsByName", "namePattern", "name_like", "toggleActiveStatus", "permissionId", "isActive", "update", "getPermissionsByCategory", "category", "bulkDelete", "permissionIds", "post", "baseUrl", "ids", "bulkToggleStatus", "getUnassignedPermissions", "roleId", "exclude_role", "getUnassignedUserPermissions", "userId", "exclude_user", "advancedSearch", "created_after", "created_before", "permissionService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/permission.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { Permission } from '../types';\nimport { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';\n\nexport interface CreatePermissionRequest {\n  name: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nexport interface UpdatePermissionRequest {\n  name?: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nclass PermissionService extends BaseCRUDService<Permission, CreatePermissionRequest, UpdatePermissionRequest> {\n  constructor() {\n    super('/admin/permission-management');\n  }\n\n  // Get all permissions (from old admin API)\n  getAllPermissions(): Promise<AxiosResponse<Permission[]>> {\n    return api.get('/admin/permissions');\n  }\n\n  // Get active permissions only\n  getActivePermissions(): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({ active_only: true });\n  }\n\n  // Search permissions with custom filters\n  searchPermissions(searchTerm: string, page: number = 1, pageSize: number = 10): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.search({\n      page,\n      page_size: pageSize,\n      search_term: searchTerm,\n      filters: {\n        is_active: true\n      }\n    });\n  }\n\n  // Get permissions by name pattern\n  getPermissionsByName(namePattern: string): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      name_like: namePattern\n    });\n  }\n\n  // Toggle permission active status\n  toggleActiveStatus(permissionId: number, isActive: boolean): Promise<AxiosResponse<CRUDResponse<Permission>>> {\n    return this.update(permissionId, { is_active: isActive });\n  }\n\n  // Get permissions by category (if implemented in backend)\n  getPermissionsByCategory(category: string): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      category: category\n    });\n  }\n\n  // Bulk operations\n  bulkDelete(permissionIds: number[]): Promise<AxiosResponse<{ message: string; deleted_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-delete`, { ids: permissionIds });\n  }\n\n  bulkToggleStatus(permissionIds: number[], isActive: boolean): Promise<AxiosResponse<{ message: string; updated_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-toggle-status`, { ids: permissionIds, is_active: isActive });\n  }\n\n  // Get permissions that are not assigned to a specific role\n  getUnassignedPermissions(roleId: number): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      exclude_role: roleId,\n      is_active: true\n    });\n  }\n\n  // Get permissions that are not directly assigned to a specific user\n  getUnassignedUserPermissions(userId: number): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      exclude_user: userId,\n      is_active: true\n    });\n  }\n\n  // Advanced search with multiple filters\n  advancedSearch(filters: {\n    search_term?: string;\n    is_active?: boolean;\n    category?: string;\n    created_after?: string;\n    created_before?: string;\n    page?: number;\n    page_size?: number;\n  }): Promise<AxiosResponse<CRUDListResponse<Permission>>> {\n    return this.search({\n      page: filters.page || 1,\n      page_size: filters.page_size || 10,\n      search_term: filters.search_term,\n      filters: {\n        is_active: filters.is_active,\n        category: filters.category,\n        created_after: filters.created_after,\n        created_before: filters.created_before\n      }\n    });\n  }\n}\n\nexport const permissionService = new PermissionService();\nexport default permissionService;\n"], "mappings": "AAEA,SAASA,eAAe,EAAkCC,GAAG,QAAQ,gBAAgB;AAcrF,MAAMC,iBAAiB,SAASF,eAAe,CAA+D;EAC5GG,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,8BAA8B,CAAC;EACvC;;EAEA;EACAC,iBAAiBA,CAAA,EAAyC;IACxD,OAAOH,GAAG,CAACI,GAAG,CAAC,oBAAoB,CAAC;EACtC;;EAEA;EACAC,oBAAoBA,CAAA,EAAyD;IAC3E,OAAO,IAAI,CAACC,IAAI,CAAC;MAAEC,WAAW,EAAE;IAAK,CAAC,CAAC;EACzC;;EAEA;EACAC,iBAAiBA,CAACC,UAAkB,EAAEC,IAAY,GAAG,CAAC,EAAEC,QAAgB,GAAG,EAAE,EAAwD;IACnI,OAAO,IAAI,CAACC,MAAM,CAAC;MACjBF,IAAI;MACJG,SAAS,EAAEF,QAAQ;MACnBG,WAAW,EAAEL,UAAU;MACvBM,OAAO,EAAE;QACPC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;EACJ;;EAEA;EACAC,oBAAoBA,CAACC,WAAmB,EAAwD;IAC9F,OAAO,IAAI,CAACZ,IAAI,CAAC;MACfI,IAAI,EAAE,CAAC;MACPG,SAAS,EAAE,GAAG;MACdM,SAAS,EAAED;IACb,CAAC,CAAC;EACJ;;EAEA;EACAE,kBAAkBA,CAACC,YAAoB,EAAEC,QAAiB,EAAoD;IAC5G,OAAO,IAAI,CAACC,MAAM,CAACF,YAAY,EAAE;MAAEL,SAAS,EAAEM;IAAS,CAAC,CAAC;EAC3D;;EAEA;EACAE,wBAAwBA,CAACC,QAAgB,EAAwD;IAC/F,OAAO,IAAI,CAACnB,IAAI,CAAC;MACfI,IAAI,EAAE,CAAC;MACPG,SAAS,EAAE,GAAG;MACdY,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ;;EAEA;EACAC,UAAUA,CAACC,aAAuB,EAAsE;IACtG,OAAO3B,GAAG,CAAC4B,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,cAAc,EAAE;MAAEC,GAAG,EAAEH;IAAc,CAAC,CAAC;EACxE;EAEAI,gBAAgBA,CAACJ,aAAuB,EAAEL,QAAiB,EAAsE;IAC/H,OAAOtB,GAAG,CAAC4B,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,qBAAqB,EAAE;MAAEC,GAAG,EAAEH,aAAa;MAAEX,SAAS,EAAEM;IAAS,CAAC,CAAC;EACpG;;EAEA;EACAU,wBAAwBA,CAACC,MAAc,EAAwD;IAC7F,OAAO,IAAI,CAAC3B,IAAI,CAAC;MACfI,IAAI,EAAE,CAAC;MACPG,SAAS,EAAE,GAAG;MACdqB,YAAY,EAAED,MAAM;MACpBjB,SAAS,EAAE;IACb,CAAC,CAAC;EACJ;;EAEA;EACAmB,4BAA4BA,CAACC,MAAc,EAAwD;IACjG,OAAO,IAAI,CAAC9B,IAAI,CAAC;MACfI,IAAI,EAAE,CAAC;MACPG,SAAS,EAAE,GAAG;MACdwB,YAAY,EAAED,MAAM;MACpBpB,SAAS,EAAE;IACb,CAAC,CAAC;EACJ;;EAEA;EACAsB,cAAcA,CAACvB,OAQd,EAAwD;IACvD,OAAO,IAAI,CAACH,MAAM,CAAC;MACjBF,IAAI,EAAEK,OAAO,CAACL,IAAI,IAAI,CAAC;MACvBG,SAAS,EAAEE,OAAO,CAACF,SAAS,IAAI,EAAE;MAClCC,WAAW,EAAEC,OAAO,CAACD,WAAW;MAChCC,OAAO,EAAE;QACPC,SAAS,EAAED,OAAO,CAACC,SAAS;QAC5BS,QAAQ,EAAEV,OAAO,CAACU,QAAQ;QAC1Bc,aAAa,EAAExB,OAAO,CAACwB,aAAa;QACpCC,cAAc,EAAEzB,OAAO,CAACyB;MAC1B;IACF,CAAC,CAAC;EACJ;AACF;AAEA,OAAO,MAAMC,iBAAiB,GAAG,IAAIxC,iBAAiB,CAAC,CAAC;AACxD,eAAewC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}