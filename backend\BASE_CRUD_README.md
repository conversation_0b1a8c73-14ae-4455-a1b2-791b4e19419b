# Base CRUD System

A comprehensive, generic CRUD (Create, Read, Update, Delete) system for the JWT Auth Backend project that provides reusable functionality for any GORM model with advanced features like pagination, multi-column search, and batch operations.

## 🚀 Quick Start

### 1. Basic Setup

```go
// Create a handler for your model
productHandler := handlers.NewBaseCRUDHandler(&models.Product{})

// Setup routes
v1 := r.Group("/api/v1")
productHandler.SetupCRUDRoutes(v1, "/products")
```

This automatically creates 8 endpoints:
- `POST /products` - Create
- `GET /products/:id` - Get by ID
- `PUT /products/:id` - Update
- `DELETE /products/:id` - Delete
- `GET /products` - List with pagination
- `POST /products/search` - Advanced search
- `GET /products/fields` - Get field names
- `DELETE /products/batch` - Batch delete

### 2. Model Requirements

Your model should have an `ID` field and optionally embed `BaseModel`:

```go
type Product struct {
    models.BaseModel  // Includes ID, CreatedAt, UpdatedAt, DeletedAt
    Name        string  `json:"name" gorm:"not null"`
    Price       float64 `json:"price"`
    CategoryID  uint    `json:"category_id"`
}
```

## 📋 Features

### ✅ Standard CRUD Operations
- Create new records
- Read records by ID
- Update existing records
- Delete records (soft delete)

### ✅ Advanced List & Search
- **Pagination**: Configurable page size (1-100 items)
- **Sorting**: Order by any field, ascending or descending
- **Multi-column Search**: Filter by multiple fields simultaneously
- **12 Search Operators**: `eq`, `ne`, `like`, `ilike`, `gt`, `gte`, `lt`, `lte`, `in`, `not_in`, `is_null`, `not_null`

### ✅ Batch Operations
- Batch delete multiple records by IDs
- Batch create multiple records

### ✅ Developer Experience
- **Generic Implementation**: Works with any GORM model
- **Consistent Error Handling**: Standardized JSON error responses
- **Comprehensive Logging**: Structured logging with Zap
- **Field Introspection**: Automatic field name discovery
- **Swagger Documentation**: Auto-generated API docs

## 🔍 Search Examples

### Simple List with Pagination
```bash
GET /api/v1/products?page=1&page_size=10&order_by=name&order_dir=asc
```

### Advanced Search
```bash
POST /api/v1/products/search
Content-Type: application/json

{
    "page": 1,
    "page_size": 10,
    "filters": [
        {
            "field": "price",
            "operator": "gte",
            "value": 100
        },
        {
            "field": "name",
            "operator": "ilike",
            "value": "laptop"
        }
    ],
    "order_by": "price",
    "order_dir": "desc"
}
```

### Search Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `eq` | Exact match | `{"field": "status", "operator": "eq", "value": "active"}` |
| `ne` | Not equal | `{"field": "status", "operator": "ne", "value": "deleted"}` |
| `like` | Contains (case-sensitive) | `{"field": "name", "operator": "like", "value": "Product"}` |
| `ilike` | Contains (case-insensitive) | `{"field": "name", "operator": "ilike", "value": "product"}` |
| `gt` | Greater than | `{"field": "price", "operator": "gt", "value": 100}` |
| `gte` | Greater than or equal | `{"field": "price", "operator": "gte", "value": 100}` |
| `lt` | Less than | `{"field": "price", "operator": "lt", "value": 1000}` |
| `lte` | Less than or equal | `{"field": "price", "operator": "lte", "value": 1000}` |
| `in` | In array | `{"field": "category_id", "operator": "in", "value": [1,2,3]}` |
| `not_in` | Not in array | `{"field": "category_id", "operator": "not_in", "value": [4,5]}` |
| `is_null` | Is null | `{"field": "deleted_at", "operator": "is_null"}` |
| `not_null` | Is not null | `{"field": "updated_at", "operator": "not_null"}` |

## 📁 File Structure

```
backend/
├── models/
│   └── base_crud.go          # Request/response types, validation
├── services/
│   └── base_crud.go          # Business logic, database operations
├── handlers/
│   └── base_crud.go          # HTTP handlers, route setup
├── examples/
│   └── user_crud_example.go  # Usage examples
└── docs/
    └── base_crud_guide.md    # Comprehensive documentation
```

## 🔧 Integration with Existing Code

The base CRUD system integrates seamlessly with the existing project:

- ✅ Uses existing database connection (`database.DB`)
- ✅ Uses existing logging system (Zap)
- ✅ Compatible with existing middleware (auth, RBAC)
- ✅ Follows existing error handling patterns
- ✅ Uses existing response format conventions

## 📖 Usage Examples

### Example 1: User Management (see `examples/user_crud_example.go`)

```go
// Setup User CRUD routes
userHandler := handlers.NewBaseCRUDHandler(&models.User{})
adminGroup.Use(middleware.RequireRoles("admin"))
userHandler.SetupCRUDRoutes(adminGroup, "/users-crud")
```

### Example 2: Custom Extensions

```go
type ProductHandler struct {
    *handlers.BaseCRUDHandler
}

func (h *ProductHandler) GetByCategory(c *gin.Context) {
    // Custom endpoint logic
}

func (h *ProductHandler) SetupRoutes(group *gin.RouterGroup) {
    h.SetupCRUDRoutes(group, "/products")  // Standard CRUD
    group.GET("/products/category/:id", h.GetByCategory)  // Custom
}
```

## 📊 Response Formats

### Success Response (Single Record)
```json
{
    "data": {
        "id": 1,
        "name": "Laptop",
        "price": 999.99,
        "created_at": "2023-01-01T00:00:00Z"
    },
    "message": "Product created successfully"
}
```

### Success Response (List)
```json
{
    "data": [...],
    "pagination": {
        "page": 1,
        "page_size": 10,
        "total": 25,
        "total_pages": 3,
        "has_next": true,
        "has_prev": false
    }
}
```

### Error Response
```json
{
    "error": "Invalid request data",
    "details": "Field 'name' is required"
}
```

## 🛡️ Security

The base CRUD system doesn't include authentication/authorization by default. Add middleware as needed:

```go
// Require authentication
v1.Use(middleware.AuthMiddleware())

// Require admin role
adminGroup.Use(middleware.RequireRoles("admin"))

// Setup CRUD routes with protection
userHandler.SetupCRUDRoutes(adminGroup, "/users")
```

## 🚦 Best Practices

1. **Model Design**: Include `BaseModel` or ensure your model has an `ID` field
2. **Validation**: Use GORM tags and Gin binding for input validation
3. **Indexing**: Add database indexes on frequently searched fields
4. **Security**: Always add appropriate middleware for authentication/authorization
5. **Performance**: Use pagination for large datasets
6. **Error Handling**: The system provides consistent error handling automatically

## 📚 Documentation

- **Comprehensive Guide**: `docs/base_crud_guide.md`
- **Usage Examples**: `examples/user_crud_example.go`
- **API Documentation**: Auto-generated Swagger docs at `/swagger/`

## 🔄 Migration from Existing Endpoints

The base CRUD system can work alongside existing endpoints. You can:

1. **Gradual Migration**: Add CRUD routes with different paths (e.g., `/users-crud`)
2. **Feature Enhancement**: Use CRUD for new features while keeping existing endpoints
3. **Complete Replacement**: Replace existing endpoints once tested

## 🎯 Benefits

- **Reduced Code Duplication**: Write CRUD logic once, use everywhere
- **Consistent API**: All models follow the same patterns
- **Advanced Features**: Pagination and search out of the box
- **Maintainability**: Centralized logic for easy updates
- **Developer Productivity**: Faster development of new features
- **Testing**: Easier to test with consistent patterns
