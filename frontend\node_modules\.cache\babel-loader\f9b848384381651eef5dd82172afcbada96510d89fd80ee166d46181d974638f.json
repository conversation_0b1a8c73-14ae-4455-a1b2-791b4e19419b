{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\components\\\\admin\\\\UserPermissionManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { userService, roleService, permissionService } from '../../services';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport AssignmentsTab from './AssignmentsTab';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserPermissionManagement = () => {\n  _s();\n  // State for active tab\n  const [activeTab, setActiveTab] = useState('users');\n\n  // Users state\n  const [users, setUsers] = useState([]);\n  const [usersPagination, setUsersPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState([]);\n  const [rolesPagination, setRolesPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState([]);\n  const [permissionsPagination, setPermissionsPagination] = useState({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [editingRole, setEditingRole] = useState(null);\n  const [editingPermission, setEditingPermission] = useState(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          search_term: searchTerm,\n          filters: {\n            is_active: true\n          }\n        };\n        response = await userService.search(searchRequest);\n      } else {\n        response = await userService.list({\n          page,\n          page_size: usersPagination.page_size\n        });\n      }\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(`Failed to load users: ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadUsers(1, usersSearchTerm);\n  };\n  const handleUsersPageChange = newPage => {\n    setUsersPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        response = await roleService.searchRoles(searchTerm, page, rolesPagination.page_size);\n      } else {\n        response = await roleService.list({\n          page,\n          page_size: rolesPagination.page_size\n        });\n      }\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(`Failed to load roles: ${((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadRoles(1, rolesSearchTerm);\n  };\n  const handleRolesPageChange = newPage => {\n    setRolesPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        response = await permissionService.searchPermissions(searchTerm, page, permissionsPagination.page_size);\n      } else {\n        response = await permissionService.list({\n          page,\n          page_size: permissionsPagination.page_size\n        });\n      }\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(`Failed to load permissions: ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n  const handlePermissionsPageChange = newPage => {\n    setPermissionsPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async id => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n    try {\n      await userService.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      setError(`Failed to delete user: ${((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n    }\n  };\n  const handleDeleteRole = async id => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n    try {\n      await roleService.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      setError(`Failed to delete role: ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || err.message}`);\n    }\n  };\n  const handleDeletePermission = async id => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n    try {\n      await permissionService.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      setError(`Failed to delete permission: ${((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error) || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination, onPageChange) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(i),\n        className: `px-3 py-1 mx-1 rounded ${i === pagination.page ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n        children: i\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600\",\n        children: [\"Showing \", (pagination.page - 1) * pagination.page_size + 1, \" to\", ' ', Math.min(pagination.page * pagination.page_size, pagination.total_items), \" of\", ' ', pagination.total_items, \" entries\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page - 1),\n          disabled: pagination.page <= 1,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), pages, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onPageChange(pagination.page + 1),\n          disabled: pagination.page >= pagination.total_pages,\n          className: \"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-8\",\n      children: \"User Permission Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b border-gray-200 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"-mb-px flex space-x-8\",\n        children: [{\n          key: 'users',\n          label: 'Users'\n        }, {\n          key: 'roles',\n          label: 'Roles'\n        }, {\n          key: 'permissions',\n          label: 'Permissions'\n        }, {\n          key: 'assignments',\n          label: 'Assignments'\n        }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n          children: tab.label\n        }, tab.key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingUser(null);\n            setShowUserModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search users by username or email...\",\n          value: usersSearchTerm,\n          onChange: e => setUsersSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleUsersSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleUsersSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setUsersSearchTerm('');\n            loadUsers(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this), usersLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading users...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: user.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingUser(user);\n                      setShowUserModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteUser(user.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 25\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 15\n        }, this), renderPagination(usersPagination, handleUsersPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this), activeTab === 'roles' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Role Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingRole(null);\n            setShowRoleModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Role\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search roles by name...\",\n          value: rolesSearchTerm,\n          onChange: e => setRolesSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handleRolesSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRolesSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setRolesSearchTerm('');\n            loadRoles(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 11\n      }, this), rolesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading roles...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: roles.map(role => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: role.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: role.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: role.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: role.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRole(role);\n                      setShowRoleModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteRole(role.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 25\n                }, this)]\n              }, role.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 15\n        }, this), renderPagination(rolesPagination, handleRolesPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 9\n    }, this), activeTab === 'permissions' && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Permission Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setEditingPermission(null);\n            setShowPermissionModal(true);\n          },\n          className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Add Permission\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search permissions by name...\",\n          value: permissionsSearchTerm,\n          onChange: e => setPermissionsSearchTerm(e.target.value),\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          onKeyPress: e => e.key === 'Enter' && handlePermissionsSearch()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePermissionsSearch,\n          className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setPermissionsSearchTerm('');\n            loadPermissions(1, '');\n          },\n          className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this), permissionsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: \"Loading permissions...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full bg-white border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: permissions.map(permission => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: permission.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: permission.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",\n                  children: permission.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: permission.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingPermission(permission);\n                      setShowPermissionModal(true);\n                    },\n                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeletePermission(permission.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 25\n                }, this)]\n              }, permission.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 15\n        }, this), renderPagination(permissionsPagination, handlePermissionsPageChange)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 9\n    }, this), activeTab === 'assignments' && /*#__PURE__*/_jsxDEV(AssignmentsTab, {\n      users: users,\n      roles: roles,\n      onMessage: setMessage,\n      onError: setError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(UserModal, {\n      isOpen: showUserModal,\n      onClose: () => setShowUserModal(false),\n      user: editingUser,\n      onSuccess: () => {\n        setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n        loadUsers(usersPagination.page, usersSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RoleModal, {\n      isOpen: showRoleModal,\n      onClose: () => setShowRoleModal(false),\n      role: editingRole,\n      onSuccess: () => {\n        setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n        loadRoles(rolesPagination.page, rolesSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PermissionModal, {\n      isOpen: showPermissionModal,\n      onClose: () => setShowPermissionModal(false),\n      permission: editingPermission,\n      onSuccess: () => {\n        setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n        loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 280,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPermissionManagement, \"EXXLj32ClIuSM8b9dg4KjmQqFqU=\");\n_c = UserPermissionManagement;\nexport default UserPermissionManagement;\nvar _c;\n$RefreshReg$(_c, \"UserPermissionManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "userService", "roleService", "permissionService", "UserModal", "RoleModal", "PermissionModal", "AssignmentsTab", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserPermissionManagement", "_s", "activeTab", "setActiveTab", "users", "setUsers", "usersPagination", "setUsersPagination", "page", "page_size", "total_items", "total_pages", "usersLoading", "setUsersLoading", "usersSearchTerm", "setUsersSearchTerm", "roles", "setRoles", "rolesPagination", "setRolesPagination", "rolesLoading", "setRolesLoading", "rolesSearchTerm", "setRolesSearchTerm", "permissions", "setPermissions", "permissionsPagination", "setPermissionsPagination", "permissionsLoading", "setPermissionsLoading", "permissionsSearchTerm", "setPermissionsSearchTerm", "message", "setMessage", "error", "setError", "showUserModal", "setShowUserModal", "showRoleModal", "setShowRoleModal", "showPermissionModal", "setShowPermissionModal", "editingUser", "setEditingUser", "editingRole", "setEditingRole", "editingPermission", "setEditingPermission", "loadUsers", "loadRoles", "loadPermissions", "searchTerm", "response", "trim", "searchRequest", "search_term", "filters", "is_active", "search", "list", "data", "pagination", "err", "_err$response", "_err$response$data", "handleUsersSearch", "prev", "handleUsersPageChange", "newPage", "searchRoles", "_err$response2", "_err$response2$data", "handleRolesSearch", "handleRolesPageChange", "searchPermissions", "_err$response3", "_err$response3$data", "handlePermissionsSearch", "handlePermissionsPageChange", "handleDeleteUser", "id", "window", "confirm", "delete", "_err$response4", "_err$response4$data", "handleDeleteRole", "_err$response5", "_err$response5$data", "handleDeletePermission", "_err$response6", "_err$response6$data", "renderPagination", "onPageChange", "pages", "i", "push", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Math", "min", "disabled", "key", "label", "map", "tab", "type", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "user", "username", "email", "created_at", "Date", "toLocaleDateString", "role", "name", "description", "permission", "onMessage", "onError", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/UserPermissionManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport {\n  userService,\n  roleService,\n  permissionService,\n  adminService,\n  PaginationInfo,\n  SearchRequest\n} from '../../services';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport AssignmentsTab from './AssignmentsTab';\n\ninterface UserPermissionManagementProps { }\n\nconst UserPermissionManagement: React.FC<UserPermissionManagementProps> = () => {\n  // State for active tab\n  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'permissions' | 'assignments'>('users');\n\n  // Users state\n  const [users, setUsers] = useState<User[]>([]);\n  const [usersPagination, setUsersPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [rolesPagination, setRolesPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [permissionsPagination, setPermissionsPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          search_term: searchTerm,\n          filters: {\n            is_active: true\n          }\n        };\n        response = await userService.search(searchRequest);\n      } else {\n        response = await userService.list({\n          page,\n          page_size: usersPagination.page_size\n        });\n      }\n\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load users: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({ ...prev, page: 1 }));\n    loadUsers(1, usersSearchTerm);\n  };\n\n  const handleUsersPageChange = (newPage: number) => {\n    setUsersPagination(prev => ({ ...prev, page: newPage }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        response = await roleService.searchRoles(searchTerm, page, rolesPagination.page_size);\n      } else {\n        response = await roleService.list({\n          page,\n          page_size: rolesPagination.page_size\n        });\n      }\n\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load roles: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({ ...prev, page: 1 }));\n    loadRoles(1, rolesSearchTerm);\n  };\n\n  const handleRolesPageChange = (newPage: number) => {\n    setRolesPagination(prev => ({ ...prev, page: newPage }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        response = await permissionService.searchPermissions(searchTerm, page, permissionsPagination.page_size);\n      } else {\n        response = await permissionService.list({\n          page,\n          page_size: permissionsPagination.page_size\n        });\n      }\n\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load permissions: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({ ...prev, page: 1 }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n\n  const handlePermissionsPageChange = (newPage: number) => {\n    setPermissionsPagination(prev => ({ ...prev, page: newPage }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n\n    try {\n      await userService.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete user: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeleteRole = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n\n    try {\n      await roleService.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeletePermission = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n\n    try {\n      await permissionService.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination: PaginationInfo, onPageChange: (page: number) => void) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(\n        <button\n          key={i}\n          onClick={() => onPageChange(i)}\n          className={`px-3 py-1 mx-1 rounded ${i === pagination.page\n            ? 'bg-blue-500 text-white'\n            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n        >\n          {i}\n        </button>\n      );\n    }\n\n    return (\n      <div className=\"flex items-center justify-between mt-4\">\n        <div className=\"text-sm text-gray-600\">\n          Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}\n          {Math.min(pagination.page * pagination.page_size, pagination.total_items)} of{' '}\n          {pagination.total_items} entries\n        </div>\n        <div className=\"flex items-center\">\n          <button\n            onClick={() => onPageChange(pagination.page - 1)}\n            disabled={pagination.page <= 1}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Previous\n          </button>\n          {pages}\n          <button\n            onClick={() => onPageChange(pagination.page + 1)}\n            disabled={pagination.page >= pagination.total_pages}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Next\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">User Permission Management</h1>\n\n      {/* Messages */}\n      {message && (\n        <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n          {message}\n        </div>\n      )}\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {error}\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {[\n            { key: 'users', label: 'Users' },\n            { key: 'roles', label: 'Roles' },\n            { key: 'permissions', label: 'Permissions' },\n            { key: 'assignments', label: 'Assignments' }\n          ].map((tab) => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'users' && (\n        <div>\n          {/* Users Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">User Management</h2>\n            <button\n              onClick={() => {\n                setEditingUser(null);\n                setShowUserModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add User\n            </button>\n          </div>\n\n          {/* Users Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search users by username or email...\"\n              value={usersSearchTerm}\n              onChange={(e) => setUsersSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleUsersSearch()}\n            />\n            <button\n              onClick={handleUsersSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setUsersSearchTerm('');\n                loadUsers(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Users Table */}\n          {usersLoading ? (\n            <div className=\"text-center py-8\">Loading users...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Username\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Email\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <tr key={user.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {user.username}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.email}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingUser(user);\n                              setShowUserModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteUser(user.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Users Pagination */}\n              {renderPagination(usersPagination, handleUsersPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Roles Tab */}\n      {activeTab === 'roles' && (\n        <div>\n          {/* Roles Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Role Management</h2>\n            <button\n              onClick={() => {\n                setEditingRole(null);\n                setShowRoleModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Role\n            </button>\n          </div>\n\n          {/* Roles Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search roles by name...\"\n              value={rolesSearchTerm}\n              onChange={(e) => setRolesSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleRolesSearch()}\n            />\n            <button\n              onClick={handleRolesSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setRolesSearchTerm('');\n                loadRoles(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Roles Table */}\n          {rolesLoading ? (\n            <div className=\"text-center py-8\">Loading roles...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {roles.map((role) => (\n                      <tr key={role.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {role.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {role.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {role.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingRole(role);\n                              setShowRoleModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteRole(role.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Roles Pagination */}\n              {renderPagination(rolesPagination, handleRolesPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Permissions Tab */}\n      {activeTab === 'permissions' && (\n        <div>\n          {/* Permissions Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Permission Management</h2>\n            <button\n              onClick={() => {\n                setEditingPermission(null);\n                setShowPermissionModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Permission\n            </button>\n          </div>\n\n          {/* Permissions Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search permissions by name...\"\n              value={permissionsSearchTerm}\n              onChange={(e) => setPermissionsSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handlePermissionsSearch()}\n            />\n            <button\n              onClick={handlePermissionsSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setPermissionsSearchTerm('');\n                loadPermissions(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Permissions Table */}\n          {permissionsLoading ? (\n            <div className=\"text-center py-8\">Loading permissions...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {permissions.map((permission) => (\n                      <tr key={permission.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {permission.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {permission.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {permission.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingPermission(permission);\n                              setShowPermissionModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeletePermission(permission.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Permissions Pagination */}\n              {renderPagination(permissionsPagination, handlePermissionsPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Assignments Tab */}\n      {activeTab === 'assignments' && (\n        <AssignmentsTab\n          users={users}\n          roles={roles}\n          onMessage={setMessage}\n          onError={setError}\n        />\n      )}\n\n      {/* Modals */}\n      <UserModal\n        isOpen={showUserModal}\n        onClose={() => setShowUserModal(false)}\n        user={editingUser}\n        onSuccess={() => {\n          setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n          loadUsers(usersPagination.page, usersSearchTerm);\n        }}\n      />\n\n      <RoleModal\n        isOpen={showRoleModal}\n        onClose={() => setShowRoleModal(false)}\n        role={editingRole}\n        onSuccess={() => {\n          setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n          loadRoles(rolesPagination.page, rolesSearchTerm);\n        }}\n      />\n\n      <PermissionModal\n        isOpen={showPermissionModal}\n        onClose={() => setShowPermissionModal(false)}\n        permission={editingPermission}\n        onSuccess={() => {\n          setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n          loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n        }}\n      />\n    </div>\n  );\n};\n\nexport default UserPermissionManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SACEC,WAAW,EACXC,WAAW,EACXC,iBAAiB,QAIZ,gBAAgB;AACvB,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI9C,MAAMC,wBAAiE,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9E;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAoD,OAAO,CAAC;;EAEtG;EACA,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAiB;IACrEqB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAiB;IACrEqB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACuC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxC,QAAQ,CAAiB;IACjFqB,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;;EAEtE;EACA,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC2D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAoB,IAAI,CAAC;;EAEnF;EACAC,SAAS,CAAC,MAAM;IACd,QAAQc,SAAS;MACf,KAAK,OAAO;QACV8C,SAAS,CAAC,CAAC;QACX;MACF,KAAK,OAAO;QACVC,SAAS,CAAC,CAAC;QACX;MACF,KAAK,aAAa;QAChBC,eAAe,CAAC,CAAC;QACjB;MACF,KAAK,aAAa;QAChBF,SAAS,CAAC,CAAC,CAAC,CAAC;QACbC,SAAS,CAAC,CAAC,CAAC,CAAC;QACb;IACJ;EACF,CAAC,EAAE,CAAC/C,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM8C,SAAS,GAAG,MAAAA,CAAOxC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrDtC,eAAe,CAAC,IAAI,CAAC;IACrBsB,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,aAA4B,GAAG;UACnC9C,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG,SAAS;UACpC8C,WAAW,EAAEJ,UAAU;UACvBK,OAAO,EAAE;YACPC,SAAS,EAAE;UACb;QACF,CAAC;QACDL,QAAQ,GAAG,MAAM/D,WAAW,CAACqE,MAAM,CAACJ,aAAa,CAAC;MACpD,CAAC,MAAM;QACLF,QAAQ,GAAG,MAAM/D,WAAW,CAACsE,IAAI,CAAC;UAChCnD,IAAI;UACJC,SAAS,EAAEH,eAAe,CAACG;QAC7B,CAAC,CAAC;MACJ;MAEAJ,QAAQ,CAAC+C,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC5BrD,kBAAkB,CAAC6C,QAAQ,CAACQ,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjB7B,QAAQ,CAAC,yBAAyB,EAAA4B,aAAA,GAAAD,GAAG,CAACV,QAAQ,cAAAW,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoB9B,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRnB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMoD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1D,kBAAkB,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDwC,SAAS,CAAC,CAAC,EAAElC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMqD,qBAAqB,GAAIC,OAAe,IAAK;IACjD7D,kBAAkB,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE4D;IAAQ,CAAC,CAAC,CAAC;IACxDpB,SAAS,CAACoB,OAAO,EAAEtD,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAMmC,SAAS,GAAG,MAAAA,CAAOzC,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IACrD9B,eAAe,CAAC,IAAI,CAAC;IACrBc,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrBD,QAAQ,GAAG,MAAM9D,WAAW,CAAC+E,WAAW,CAAClB,UAAU,EAAE3C,IAAI,EAAEU,eAAe,CAACT,SAAS,CAAC;MACvF,CAAC,MAAM;QACL2C,QAAQ,GAAG,MAAM9D,WAAW,CAACqE,IAAI,CAAC;UAChCnD,IAAI;UACJC,SAAS,EAAES,eAAe,CAACT;QAC7B,CAAC,CAAC;MACJ;MAEAQ,QAAQ,CAACmC,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC5BzC,kBAAkB,CAACiC,QAAQ,CAACQ,IAAI,CAACC,UAAU,CAAC;IAC9C,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAQ,cAAA,EAAAC,mBAAA;MACjBpC,QAAQ,CAAC,yBAAyB,EAAAmC,cAAA,GAAAR,GAAG,CAACV,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcV,IAAI,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBrC,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAC/E,CAAC,SAAS;MACRX,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMmD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrD,kBAAkB,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IAClDyC,SAAS,CAAC,CAAC,EAAE3B,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMmD,qBAAqB,GAAIL,OAAe,IAAK;IACjDjD,kBAAkB,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE4D;IAAQ,CAAC,CAAC,CAAC;IACxDnB,SAAS,CAACmB,OAAO,EAAE9C,eAAe,CAAC;EACrC,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAG,MAAAA,CAAO1C,IAAI,GAAG,CAAC,EAAE2C,UAAU,GAAG,EAAE,KAAK;IAC3DtB,qBAAqB,CAAC,IAAI,CAAC;IAC3BM,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIiB,QAAQ;MACZ,IAAID,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;QACrBD,QAAQ,GAAG,MAAM7D,iBAAiB,CAACmF,iBAAiB,CAACvB,UAAU,EAAE3C,IAAI,EAAEkB,qBAAqB,CAACjB,SAAS,CAAC;MACzG,CAAC,MAAM;QACL2C,QAAQ,GAAG,MAAM7D,iBAAiB,CAACoE,IAAI,CAAC;UACtCnD,IAAI;UACJC,SAAS,EAAEiB,qBAAqB,CAACjB;QACnC,CAAC,CAAC;MACJ;MAEAgB,cAAc,CAAC2B,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAClCjC,wBAAwB,CAACyB,QAAQ,CAACQ,IAAI,CAACC,UAAU,CAAC;IACpD,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACjBzC,QAAQ,CAAC,+BAA+B,EAAAwC,cAAA,GAAAb,GAAG,CAACV,QAAQ,cAAAuB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcf,IAAI,cAAAgB,mBAAA,uBAAlBA,mBAAA,CAAoB1C,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IACrF,CAAC,SAAS;MACRH,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMgD,uBAAuB,GAAGA,CAAA,KAAM;IACpClD,wBAAwB,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IACxD0C,eAAe,CAAC,CAAC,EAAEpB,qBAAqB,CAAC;EAC3C,CAAC;EAED,MAAMgD,2BAA2B,GAAIV,OAAe,IAAK;IACvDzC,wBAAwB,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE4D;IAAQ,CAAC,CAAC,CAAC;IAC9DlB,eAAe,CAACkB,OAAO,EAAEtC,qBAAqB,CAAC;EACjD,CAAC;;EAED;EACA,MAAMiD,gBAAgB,GAAG,MAAOC,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAM7F,WAAW,CAAC8F,MAAM,CAACH,EAAE,CAAC;MAC5B/C,UAAU,CAAC,2BAA2B,CAAC;MACvCe,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;IAClD,CAAC,CAAC,OAAOgD,GAAQ,EAAE;MAAA,IAAAsB,cAAA,EAAAC,mBAAA;MACjBlD,QAAQ,CAAC,0BAA0B,EAAAiD,cAAA,GAAAtB,GAAG,CAACV,QAAQ,cAAAgC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxB,IAAI,cAAAyB,mBAAA,uBAAlBA,mBAAA,CAAoBnD,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAMsD,gBAAgB,GAAG,MAAON,EAAU,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAM5F,WAAW,CAAC6F,MAAM,CAACH,EAAE,CAAC;MAC5B/C,UAAU,CAAC,2BAA2B,CAAC;MACvCgB,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;IAClD,CAAC,CAAC,OAAOwC,GAAQ,EAAE;MAAA,IAAAyB,cAAA,EAAAC,mBAAA;MACjBrD,QAAQ,CAAC,0BAA0B,EAAAoD,cAAA,GAAAzB,GAAG,CAACV,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc3B,IAAI,cAAA4B,mBAAA,uBAAlBA,mBAAA,CAAoBtD,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IAChF;EACF,CAAC;EAED,MAAMyD,sBAAsB,GAAG,MAAOT,EAAU,IAAK;IACnD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;IAEzE,IAAI;MACF,MAAM3F,iBAAiB,CAAC4F,MAAM,CAACH,EAAE,CAAC;MAClC/C,UAAU,CAAC,iCAAiC,CAAC;MAC7CiB,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;IACpE,CAAC,CAAC,OAAOgC,GAAQ,EAAE;MAAA,IAAA4B,cAAA,EAAAC,mBAAA;MACjBxD,QAAQ,CAAC,gCAAgC,EAAAuD,cAAA,GAAA5B,GAAG,CAACV,QAAQ,cAAAsC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc9B,IAAI,cAAA+B,mBAAA,uBAAlBA,mBAAA,CAAoBzD,KAAK,KAAI4B,GAAG,CAAC9B,OAAO,EAAE,CAAC;IACtF;EACF,CAAC;;EAED;EACA,MAAM4D,gBAAgB,GAAGA,CAAC/B,UAA0B,EAAEgC,YAAoC,KAAK;IAC7F,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIlC,UAAU,CAAClD,WAAW,EAAEoF,CAAC,EAAE,EAAE;MAChDD,KAAK,CAACE,IAAI,cACRnG,OAAA;QAEEoG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACE,CAAC,CAAE;QAC/BG,SAAS,EAAE,0BAA0BH,CAAC,KAAKlC,UAAU,CAACrD,IAAI,GACtD,wBAAwB,GACxB,6CAA6C,EAC5C;QAAA2F,QAAA,EAEJJ;MAAC,GAPGA,CAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQA,CACV,CAAC;IACH;IAEA,oBACE1G,OAAA;MAAKqG,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDtG,OAAA;QAAKqG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC7B,EAAE,CAACtC,UAAU,CAACrD,IAAI,GAAG,CAAC,IAAIqD,UAAU,CAACpD,SAAS,GAAI,CAAC,EAAC,KAAG,EAAC,GAAG,EAClE+F,IAAI,CAACC,GAAG,CAAC5C,UAAU,CAACrD,IAAI,GAAGqD,UAAU,CAACpD,SAAS,EAAEoD,UAAU,CAACnD,WAAW,CAAC,EAAC,KAAG,EAAC,GAAG,EAChFmD,UAAU,CAACnD,WAAW,EAAC,UAC1B;MAAA;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN1G,OAAA;QAAKqG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtG,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAAChC,UAAU,CAACrD,IAAI,GAAG,CAAC,CAAE;UACjDkG,QAAQ,EAAE7C,UAAU,CAACrD,IAAI,IAAI,CAAE;UAC/B0F,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRT,KAAK,eACNjG,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAAChC,UAAU,CAACrD,IAAI,GAAG,CAAC,CAAE;UACjDkG,QAAQ,EAAE7C,UAAU,CAACrD,IAAI,IAAIqD,UAAU,CAAClD,WAAY;UACpDuF,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACE1G,OAAA;IAAKqG,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1CtG,OAAA;MAAIqG,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAGtEvE,OAAO,iBACNnC,OAAA;MAAKqG,SAAS,EAAC,4EAA4E;MAAAC,QAAA,EACxFnE;IAAO;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACArE,KAAK,iBACJrC,OAAA;MAAKqG,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClFjE;IAAK;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1G,OAAA;MAAKqG,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CtG,OAAA;QAAKqG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACnC,CACC;UAAEQ,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAQ,CAAC,EAChC;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,EAC5C;UAAED,GAAG,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAc,CAAC,CAC7C,CAACC,GAAG,CAAEC,GAAG,iBACRjH,OAAA;UAEEoG,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC2G,GAAG,CAACH,GAAU,CAAE;UAC5CT,SAAS,EAAE,4CAA4ChG,SAAS,KAAK4G,GAAG,CAACH,GAAG,GACxE,+BAA+B,GAC/B,4EAA4E,EAC3E;UAAAR,QAAA,EAEJW,GAAG,CAACF;QAAK,GAPLE,GAAG,CAACH,GAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrG,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAAsG,QAAA,gBAEEtG,OAAA;QAAKqG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDtG,OAAA;UAAIqG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1D1G,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAM;YACbtD,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACF6D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1G,OAAA;QAAKqG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BtG,OAAA;UACEkH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,sCAAsC;UAClDC,KAAK,EAAEnG,eAAgB;UACvBoG,QAAQ,EAAGC,CAAC,IAAKpG,kBAAkB,CAACoG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDf,SAAS,EAAC,wGAAwG;UAClHmB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAK,OAAO,IAAI1C,iBAAiB,CAAC;QAAE;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF1G,OAAA;UACEoG,OAAO,EAAEhC,iBAAkB;UAC3BiC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1G,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAM;YACblF,kBAAkB,CAAC,EAAE,CAAC;YACtBiC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFkD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL3F,YAAY,gBACXf,OAAA;QAAKqG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExD1G,OAAA,CAAAE,SAAA;QAAAoG,QAAA,gBACEtG,OAAA;UAAKqG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BtG,OAAA;YAAOqG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3DtG,OAAA;cAAOqG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3BtG,OAAA;gBAAAsG,QAAA,gBACEtG,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR1G,OAAA;cAAOqG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjD/F,KAAK,CAACyG,GAAG,CAAES,IAAI,iBACdzH,OAAA;gBAAAsG,QAAA,gBACEtG,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DmB,IAAI,CAACtC;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EmB,IAAI,CAACC;gBAAQ;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DmB,IAAI,CAACE;gBAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCtG,OAAA;oBAAMqG,SAAS,EAAE,4DAA4DoB,IAAI,CAAC7D,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAA0C,QAAA,EACFmB,IAAI,CAAC7D,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DmB,IAAI,CAACG,UAAU,GAAG,IAAIC,IAAI,CAACJ,IAAI,CAACG,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7DtG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM;sBACbtD,cAAc,CAAC2E,IAAI,CAAC;sBACpBjF,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACF6D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1G,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAMlB,gBAAgB,CAACuC,IAAI,CAACtC,EAAE,CAAE;oBACzCkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEe,IAAI,CAACtC,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAACtF,eAAe,EAAE6D,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGArG,SAAS,KAAK,OAAO,iBACpBL,OAAA;MAAAsG,QAAA,gBAEEtG,OAAA;QAAKqG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDtG,OAAA;UAAIqG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1D1G,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAM;YACbpD,cAAc,CAAC,IAAI,CAAC;YACpBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACF2D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1G,OAAA;QAAKqG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BtG,OAAA;UACEkH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,yBAAyB;UACrCC,KAAK,EAAE3F,eAAgB;UACvB4F,QAAQ,EAAGC,CAAC,IAAK5F,kBAAkB,CAAC4F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDf,SAAS,EAAC,wGAAwG;UAClHmB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAK,OAAO,IAAInC,iBAAiB,CAAC;QAAE;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF1G,OAAA;UACEoG,OAAO,EAAEzB,iBAAkB;UAC3B0B,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1G,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAM;YACb1E,kBAAkB,CAAC,EAAE,CAAC;YACtB0B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAE;UACFiD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLnF,YAAY,gBACXvB,OAAA;QAAKqG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAExD1G,OAAA,CAAAE,SAAA;QAAAoG,QAAA,gBACEtG,OAAA;UAAKqG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BtG,OAAA;YAAOqG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3DtG,OAAA;cAAOqG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3BtG,OAAA;gBAAAsG,QAAA,gBACEtG,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR1G,OAAA;cAAOqG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDnF,KAAK,CAAC6F,GAAG,CAAEe,IAAI,iBACd/H,OAAA;gBAAAsG,QAAA,gBACEtG,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAAC5C;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EyB,IAAI,CAACC;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAACE,WAAW,IAAI;gBAAgB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCtG,OAAA;oBAAMqG,SAAS,EAAE,4DAA4D0B,IAAI,CAACnE,SAAS,GACvF,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAA0C,QAAA,EACFyB,IAAI,CAACnE,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAACH,UAAU,GAAG,IAAIC,IAAI,CAACE,IAAI,CAACH,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7DtG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM;sBACbpD,cAAc,CAAC+E,IAAI,CAAC;sBACpBrF,gBAAgB,CAAC,IAAI,CAAC;oBACxB,CAAE;oBACF2D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1G,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACsC,IAAI,CAAC5C,EAAE,CAAE;oBACzCkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEqB,IAAI,CAAC5C,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAAC1E,eAAe,EAAEuD,qBAAqB,CAAC;MAAA,eACzD,CACH;IAAA;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGArG,SAAS,KAAK,aAAa,iBAC1BL,OAAA;MAAAsG,QAAA,gBAEEtG,OAAA;QAAKqG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDtG,OAAA;UAAIqG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE1G,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAM;YACblD,oBAAoB,CAAC,IAAI,CAAC;YAC1BN,sBAAsB,CAAC,IAAI,CAAC;UAC9B,CAAE;UACFyD,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1G,OAAA;QAAKqG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BtG,OAAA;UACEkH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,+BAA+B;UAC3CC,KAAK,EAAEnF,qBAAsB;UAC7BoF,QAAQ,EAAGC,CAAC,IAAKpF,wBAAwB,CAACoF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC1Df,SAAS,EAAC,wGAAwG;UAClHmB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAK,OAAO,IAAI9B,uBAAuB,CAAC;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACF1G,OAAA;UACEoG,OAAO,EAAEpB,uBAAwB;UACjCqB,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1G,OAAA;UACEoG,OAAO,EAAEA,CAAA,KAAM;YACblE,wBAAwB,CAAC,EAAE,CAAC;YAC5BmB,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC;UACxB,CAAE;UACFgD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL3E,kBAAkB,gBACjB/B,OAAA;QAAKqG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAE9D1G,OAAA,CAAAE,SAAA;QAAAoG,QAAA,gBACEtG,OAAA;UAAKqG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BtG,OAAA;YAAOqG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3DtG,OAAA;cAAOqG,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3BtG,OAAA;gBAAAsG,QAAA,gBACEtG,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR1G,OAAA;cAAOqG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjD3E,WAAW,CAACqF,GAAG,CAAEkB,UAAU,iBAC1BlI,OAAA;gBAAAsG,QAAA,gBACEtG,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAAC/C;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1E4B,UAAU,CAACF;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAACD,WAAW,IAAI;gBAAgB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCtG,OAAA;oBAAMqG,SAAS,EAAE,4DAA4D6B,UAAU,CAACtE,SAAS,GAC7F,6BAA6B,GAC7B,yBAAyB,EACxB;oBAAA0C,QAAA,EACF4B,UAAU,CAACtE,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D4B,UAAU,CAACN,UAAU,GAAG,IAAIC,IAAI,CAACK,UAAU,CAACN,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACL1G,OAAA;kBAAIqG,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC7DtG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM;sBACblD,oBAAoB,CAACgF,UAAU,CAAC;sBAChCtF,sBAAsB,CAAC,IAAI,CAAC;oBAC9B,CAAE;oBACFyD,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EACnD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1G,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACsC,UAAU,CAAC/C,EAAE,CAAE;oBACrDkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GArCEwB,UAAU,CAAC/C,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsClB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLX,gBAAgB,CAAClE,qBAAqB,EAAEoD,2BAA2B,CAAC;MAAA,eACrE,CACH;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGArG,SAAS,KAAK,aAAa,iBAC1BL,OAAA,CAACF,cAAc;MACbS,KAAK,EAAEA,KAAM;MACbY,KAAK,EAAEA,KAAM;MACbgH,SAAS,EAAE/F,UAAW;MACtBgG,OAAO,EAAE9F;IAAS;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF,eAGD1G,OAAA,CAACL,SAAS;MACR0I,MAAM,EAAE9F,aAAc;MACtB+F,OAAO,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;MACvCiF,IAAI,EAAE5E,WAAY;MAClB0F,SAAS,EAAEA,CAAA,KAAM;QACfnG,UAAU,CAACS,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFM,SAAS,CAAC1C,eAAe,CAACE,IAAI,EAAEM,eAAe,CAAC;MAClD;IAAE;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF1G,OAAA,CAACJ,SAAS;MACRyI,MAAM,EAAE5F,aAAc;MACtB6F,OAAO,EAAEA,CAAA,KAAM5F,gBAAgB,CAAC,KAAK,CAAE;MACvCqF,IAAI,EAAEhF,WAAY;MAClBwF,SAAS,EAAEA,CAAA,KAAM;QACfnG,UAAU,CAACW,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;QACnFK,SAAS,CAAC/B,eAAe,CAACV,IAAI,EAAEc,eAAe,CAAC;MAClD;IAAE;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF1G,OAAA,CAACH,eAAe;MACdwI,MAAM,EAAE1F,mBAAoB;MAC5B2F,OAAO,EAAEA,CAAA,KAAM1F,sBAAsB,CAAC,KAAK,CAAE;MAC7CsF,UAAU,EAAEjF,iBAAkB;MAC9BsF,SAAS,EAAEA,CAAA,KAAM;QACfnG,UAAU,CAACa,iBAAiB,GAAG,iCAAiC,GAAG,iCAAiC,CAAC;QACrGI,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,EAAEsB,qBAAqB,CAAC;MACpE;IAAE;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtG,EAAA,CAhtBID,wBAAiE;AAAAqI,EAAA,GAAjErI,wBAAiE;AAktBvE,eAAeA,wBAAwB;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}