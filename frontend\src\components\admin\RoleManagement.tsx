import React, { useState, useEffect } from 'react';
import { roleService, permissionService } from '../../services';
import { Role, Permission, PaginationInfo } from '../../types';
import RolePermissionModal from './RolePermissionModal';

interface RoleManagementProps { }

const RoleManagement: React.FC<RoleManagementProps> = () => {
  // State management
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Pagination
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    page_size: 10,
    total_items: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  });

  // Search and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true
  });

  // Load data on component mount
  useEffect(() => {
    loadRoles();
    loadPermissions();
  }, [pagination.page, pagination.page_size, searchTerm, statusFilter]);

  // Load roles with pagination and search
  const loadRoles = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: any = {
        page: pagination.page,
        page_size: pagination.page_size
      };

      if (searchTerm.trim()) {
        filters.search_term = searchTerm.trim();
        filters.search_columns = ['name', 'description'];
      }

      if (statusFilter !== 'all') {
        filters.is_active = statusFilter === 'active';
      }

      const response = await roleService.list(filters);
      setRoles(response.data.data);
      const paginationData = response.data.pagination;
      setPagination({
        ...paginationData,
        has_next: paginationData.page < paginationData.total_pages,
        has_prev: paginationData.page > 1
      });
    } catch (err: any) {
      setError(err.message || 'Failed to load roles');
      console.error('Error loading roles:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load all permissions for assignment
  const loadPermissions = async () => {
    try {
      const response = await permissionService.list({ page: 1, page_size: 100 });
      setPermissions(response.data.data);
    } catch (err: any) {
      console.error('Error loading permissions:', err);
    }
  };

  // Handle create role
  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      await roleService.create(formData);
      setSuccess('Role created successfully');
      setShowCreateModal(false);
      resetForm();
      loadRoles();
    } catch (err: any) {
      setError(err.message || 'Failed to create role');
    } finally {
      setLoading(false);
    }
  };

  // Handle update role
  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRole) return;

    try {
      setLoading(true);
      setError(null);

      await roleService.update(selectedRole.id, formData);
      setSuccess('Role updated successfully');
      setShowEditModal(false);
      resetForm();
      loadRoles();
    } catch (err: any) {
      setError(err.message || 'Failed to update role');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete role
  const handleDelete = async (role: Role) => {
    if (!window.confirm(`Are you sure you want to delete role "${role.name}"?`)) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await roleService.delete(role.id);
      setSuccess('Role deleted successfully');
      loadRoles();
    } catch (err: any) {
      setError(err.message || 'Failed to delete role');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    loadRoles();
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      is_active: true
    });
    setSelectedRole(null);
  };

  // Open edit modal
  const openEditModal = (role: Role) => {
    setSelectedRole(role);
    setFormData({
      name: role.name,
      description: role.description || '',
      is_active: role.is_active
    });
    setShowEditModal(true);
  };

  // Open permission modal
  const openPermissionModal = (role: Role) => {
    setSelectedRole(role);
    setShowPermissionModal(true);
  };

  // Clear messages
  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  return (
    <div className="container mt-4">
      <div className="row">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h2>Role Management</h2>
            <button
              className="btn btn-primary"
              onClick={() => setShowCreateModal(true)}
            >
              <i className="fas fa-plus me-2"></i>
              Create Role
            </button>
          </div>

          {/* Messages */}
          {error && (
            <div className="alert alert-danger alert-dismissible fade show" role="alert">
              {error}
              <button type="button" className="btn-close" onClick={clearMessages}></button>
            </div>
          )}
          {success && (
            <div className="alert alert-success alert-dismissible fade show" role="alert">
              {success}
              <button type="button" className="btn-close" onClick={clearMessages}></button>
            </div>
          )}

          {/* Search and Filters */}
          <div className="card mb-4">
            <div className="card-body">
              <form onSubmit={handleSearch}>
                <div className="row g-3">
                  <div className="col-md-6">
                    <label htmlFor="search" className="form-label">Search Roles</label>
                    <input
                      type="text"
                      className="form-control"
                      id="search"
                      placeholder="Search by name or description..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="status" className="form-label">Status</label>
                    <select
                      className="form-select"
                      id="status"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value as any)}
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                  <div className="col-md-3 d-flex align-items-end">
                    <button type="submit" className="btn btn-outline-primary me-2">
                      <i className="fas fa-search me-1"></i>
                      Search
                    </button>
                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={() => {
                        setSearchTerm('');
                        setStatusFilter('all');
                        setPagination(prev => ({ ...prev, page: 1 }));
                      }}
                    >
                      Clear
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          {/* Roles Table */}
          <div className="card">
            <div className="card-body">
              {loading ? (
                <div className="text-center py-4">
                  <div className="spinner-border" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              ) : (
                <>
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead className="table-light">
                        <tr>
                          <th>ID</th>
                          <th>Name</th>
                          <th>Description</th>
                          <th>Status</th>
                          <th>Created</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {roles.length === 0 ? (
                          <tr>
                            <td colSpan={6} className="text-center py-4">
                              No roles found
                            </td>
                          </tr>
                        ) : (
                          roles.map((role) => (
                            <tr key={role.id}>
                              <td>{role.id}</td>
                              <td>
                                <strong>{role.name}</strong>
                              </td>
                              <td>{role.description || '-'}</td>
                              <td>
                                <span className={`badge ${role.is_active ? 'bg-success' : 'bg-secondary'}`}>
                                  {role.is_active ? 'Active' : 'Inactive'}
                                </span>
                              </td>
                              <td>
                                {role.created_at ? new Date(role.created_at).toLocaleDateString() : '-'}
                              </td>
                              <td>
                                <div className="btn-group btn-group-sm" role="group">
                                  <button
                                    className="btn btn-outline-primary"
                                    onClick={() => openEditModal(role)}
                                    title="Edit Role"
                                  >
                                    <i className="fas fa-edit"></i>
                                  </button>
                                  <button
                                    className="btn btn-outline-info"
                                    onClick={() => openPermissionModal(role)}
                                    title="Manage Permissions"
                                  >
                                    <i className="fas fa-key"></i>
                                  </button>
                                  <button
                                    className="btn btn-outline-danger"
                                    onClick={() => handleDelete(role)}
                                    title="Delete Role"
                                  >
                                    <i className="fas fa-trash"></i>
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>

                  {/* Pagination */}
                  {pagination.total_pages > 1 && (
                    <nav aria-label="Roles pagination">
                      <ul className="pagination justify-content-center mt-3">
                        <li className={`page-item ${!pagination.has_prev ? 'disabled' : ''}`}>
                          <button
                            className="page-link"
                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                            disabled={!pagination.has_prev}
                          >
                            Previous
                          </button>
                        </li>

                        {Array.from({ length: pagination.total_pages }, (_, i) => i + 1).map((page) => (
                          <li key={page} className={`page-item ${pagination.page === page ? 'active' : ''}`}>
                            <button
                              className="page-link"
                              onClick={() => setPagination(prev => ({ ...prev, page }))}
                            >
                              {page}
                            </button>
                          </li>
                        ))}

                        <li className={`page-item ${!pagination.has_next ? 'disabled' : ''}`}>
                          <button
                            className="page-link"
                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                            disabled={!pagination.has_next}
                          >
                            Next
                          </button>
                        </li>
                      </ul>
                    </nav>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Create Role Modal */}
      {showCreateModal && (
        <div className="modal fade show" style={{ display: 'block' }} tabIndex={-1}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Create New Role</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowCreateModal(false);
                    resetForm();
                  }}
                ></button>
              </div>
              <form onSubmit={handleCreate}>
                <div className="modal-body">
                  <div className="mb-3">
                    <label htmlFor="createName" className="form-label">Role Name *</label>
                    <input
                      type="text"
                      className="form-control"
                      id="createName"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label htmlFor="createDescription" className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      id="createDescription"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    ></textarea>
                  </div>
                  <div className="mb-3 form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="createActive"
                      checked={formData.is_active}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    />
                    <label className="form-check-label" htmlFor="createActive">
                      Active
                    </label>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowCreateModal(false);
                      resetForm();
                    }}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Role'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Role Modal */}
      {showEditModal && selectedRole && (
        <div className="modal fade show" style={{ display: 'block' }} tabIndex={-1}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Edit Role: {selectedRole.name}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowEditModal(false);
                    resetForm();
                  }}
                ></button>
              </div>
              <form onSubmit={handleUpdate}>
                <div className="modal-body">
                  <div className="mb-3">
                    <label htmlFor="editName" className="form-label">Role Name *</label>
                    <input
                      type="text"
                      className="form-control"
                      id="editName"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label htmlFor="editDescription" className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      id="editDescription"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    ></textarea>
                  </div>
                  <div className="mb-3 form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="editActive"
                      checked={formData.is_active}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    />
                    <label className="form-check-label" htmlFor="editActive">
                      Active
                    </label>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowEditModal(false);
                      resetForm();
                    }}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary" disabled={loading}>
                    {loading ? 'Updating...' : 'Update Role'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Permission Management Modal */}
      <RolePermissionModal
        role={selectedRole!}
        isOpen={showPermissionModal}
        onClose={() => {
          setShowPermissionModal(false);
          setSelectedRole(null);
        }}
        onSuccess={(message) => {
          setSuccess(message);
          setTimeout(() => setSuccess(null), 5000);
        }}
        onError={(message) => {
          setError(message);
          setTimeout(() => setError(null), 5000);
        }}
      />
    </div>
  );
};

export default RoleManagement;
