{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('access_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for automatic token refresh\napi.interceptors.response.use(response => response, async error => {\n  var _error$response;\n  const originalRequest = error.config;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    try {\n      const refreshToken = localStorage.getItem('refresh_token');\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n      const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n        refresh_token: refreshToken\n      });\n      const {\n        token: newAccessToken,\n        refresh_token: newRefreshToken\n      } = response.data;\n      localStorage.setItem('access_token', newAccessToken);\n      localStorage.setItem('refresh_token', newRefreshToken);\n\n      // Retry the original request with new token\n      originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;\n      return api(originalRequest);\n    } catch (refreshError) {\n      // Refresh failed, redirect to login\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n      return Promise.reject(refreshError);\n    }\n  }\n  return Promise.reject(error);\n});\n\n// Common interfaces\n\n// Base CRUD service class\nexport class BaseCRUDService {\n  constructor(baseUrl) {\n    this.baseUrl = void 0;\n    this.baseUrl = baseUrl;\n  }\n\n  // List items with pagination\n  list(params) {\n    return api.get(this.baseUrl, {\n      params\n    });\n  }\n\n  // Search items\n  search(searchRequest) {\n    return api.post(`${this.baseUrl}/search`, searchRequest);\n  }\n\n  // Get item by ID\n  getById(id) {\n    return api.get(`${this.baseUrl}/${id}`);\n  }\n\n  // Create new item\n  create(data) {\n    return api.post(this.baseUrl, data);\n  }\n\n  // Update item\n  update(id, data) {\n    return api.put(`${this.baseUrl}/${id}`, data);\n  }\n\n  // Delete item\n  delete(id) {\n    return api.delete(`${this.baseUrl}/${id}`);\n  }\n}\nexport { api };\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "Error", "post", "refresh_token", "newAccessToken", "newRefreshToken", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "BaseCRUDService", "constructor", "baseUrl", "list", "params", "get", "search", "searchRequest", "getById", "id", "update", "put", "delete"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/base.service.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for automatic token refresh\napi.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refresh_token');\n        if (!refreshToken) {\n          throw new Error('No refresh token available');\n        }\n\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refresh_token: refreshToken,\n        });\n\n        const { token: newAccessToken, refresh_token: newRefreshToken } = response.data;\n\n        localStorage.setItem('access_token', newAccessToken);\n        localStorage.setItem('refresh_token', newRefreshToken);\n\n        // Retry the original request with new token\n        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;\n        return api(originalRequest);\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// Common interfaces\nexport interface PaginationInfo {\n  page: number;\n  page_size: number;\n  total_items: number;\n  total_pages: number;\n}\n\nexport interface CRUDListResponse<T> {\n  data: T[];\n  pagination: PaginationInfo;\n  message?: string;\n}\n\nexport interface CRUDResponse<T> {\n  data: T;\n  message?: string;\n}\n\nexport interface SearchRequest {\n  page: number;\n  page_size: number;\n  search_term?: string;\n  filters?: Record<string, any>;\n}\n\nexport interface ErrorResponse {\n  error: string;\n  details?: string;\n  code?: string;\n}\n\n// Base CRUD service class\nexport abstract class BaseCRUDService<T, CreateT = Partial<T>, UpdateT = Partial<T>> {\n  protected baseUrl: string;\n\n  constructor(baseUrl: string) {\n    this.baseUrl = baseUrl;\n  }\n\n  // List items with pagination\n  list(params?: Record<string, any>): Promise<AxiosResponse<CRUDListResponse<T>>> {\n    return api.get(this.baseUrl, { params });\n  }\n\n  // Search items\n  search(searchRequest: SearchRequest): Promise<AxiosResponse<CRUDListResponse<T>>> {\n    return api.post(`${this.baseUrl}/search`, searchRequest);\n  }\n\n  // Get item by ID\n  getById(id: number): Promise<AxiosResponse<CRUDResponse<T>>> {\n    return api.get(`${this.baseUrl}/${id}`);\n  }\n\n  // Create new item\n  create(data: CreateT): Promise<AxiosResponse<CRUDResponse<T>>> {\n    return api.post(this.baseUrl, data);\n  }\n\n  // Update item\n  update(id: number, data: UpdateT): Promise<AxiosResponse<CRUDResponse<T>>> {\n    return api.put(`${this.baseUrl}/${id}`, data);\n  }\n\n  // Delete item\n  delete(id: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${id}`);\n  }\n}\n\nexport { api };\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;AAE5C,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;;AAEpF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACf,MAAMC,eAAe,GAAGL,KAAK,CAACL,MAAM;EAEpC,IAAI,EAAAS,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,IAAI;MACF,MAAMC,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAC1D,IAAI,CAACU,YAAY,EAAE;QACjB,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;MAC/C;MAEA,MAAMN,QAAQ,GAAG,MAAMpB,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,YAAY,eAAe,EAAE;QAChE2B,aAAa,EAAEH;MACjB,CAAC,CAAC;MAEF,MAAM;QAAEZ,KAAK,EAAEgB,cAAc;QAAED,aAAa,EAAEE;MAAgB,CAAC,GAAGV,QAAQ,CAACW,IAAI;MAE/EjB,YAAY,CAACkB,OAAO,CAAC,cAAc,EAAEH,cAAc,CAAC;MACpDf,YAAY,CAACkB,OAAO,CAAC,eAAe,EAAEF,eAAe,CAAC;;MAEtD;MACAR,eAAe,CAACd,OAAO,CAACQ,aAAa,GAAG,UAAUa,cAAc,EAAE;MAClE,OAAOxB,GAAG,CAACiB,eAAe,CAAC;IAC7B,CAAC,CAAC,OAAOW,YAAY,EAAE;MACrB;MACAnB,YAAY,CAACoB,UAAU,CAAC,cAAc,CAAC;MACvCpB,YAAY,CAACoB,UAAU,CAAC,eAAe,CAAC;MACxCpB,YAAY,CAACoB,UAAU,CAAC,MAAM,CAAC;MAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAOnB,OAAO,CAACC,MAAM,CAACc,YAAY,CAAC;IACrC;EACF;EAEA,OAAOf,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AAgCA;AACA,OAAO,MAAeqB,eAAe,CAAgD;EAGnFC,WAAWA,CAACC,OAAe,EAAE;IAAA,KAFnBA,OAAO;IAGf,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;;EAEA;EACAC,IAAIA,CAACC,MAA4B,EAA+C;IAC9E,OAAOrC,GAAG,CAACsC,GAAG,CAAC,IAAI,CAACH,OAAO,EAAE;MAAEE;IAAO,CAAC,CAAC;EAC1C;;EAEA;EACAE,MAAMA,CAACC,aAA4B,EAA+C;IAChF,OAAOxC,GAAG,CAACsB,IAAI,CAAC,GAAG,IAAI,CAACa,OAAO,SAAS,EAAEK,aAAa,CAAC;EAC1D;;EAEA;EACAC,OAAOA,CAACC,EAAU,EAA2C;IAC3D,OAAO1C,GAAG,CAACsC,GAAG,CAAC,GAAG,IAAI,CAACH,OAAO,IAAIO,EAAE,EAAE,CAAC;EACzC;;EAEA;EACAzC,MAAMA,CAACyB,IAAa,EAA2C;IAC7D,OAAO1B,GAAG,CAACsB,IAAI,CAAC,IAAI,CAACa,OAAO,EAAET,IAAI,CAAC;EACrC;;EAEA;EACAiB,MAAMA,CAACD,EAAU,EAAEhB,IAAa,EAA2C;IACzE,OAAO1B,GAAG,CAAC4C,GAAG,CAAC,GAAG,IAAI,CAACT,OAAO,IAAIO,EAAE,EAAE,EAAEhB,IAAI,CAAC;EAC/C;;EAEA;EACAmB,MAAMA,CAACH,EAAU,EAA+C;IAC9D,OAAO1C,GAAG,CAAC6C,MAAM,CAAC,GAAG,IAAI,CAACV,OAAO,IAAIO,EAAE,EAAE,CAAC;EAC5C;AACF;AAEA,SAAS1C,GAAG;AACZ,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}