### Test JWT Authentication API

### 1. Register a new user
POST http://localhost:8080/api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}

### 2. Login
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 3. Get user profile (replace TOKEN with actual token from login response)
GET http://localhost:8080/api/user/profile
Authorization: Bearer TOKEN

### 4. Logout (replace TOKE<PERSON> with actual token)
POST http://localhost:8080/api/auth/logout
Authorization: Bearer TOKEN 