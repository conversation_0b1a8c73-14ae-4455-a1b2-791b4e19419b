package middleware

import (
	"net/http"

	"jwt-auth-backend/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GlobalExceptionMiddleware bắt mọi panic và trả về JSON error chuẩn
func GlobalExceptionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if r := recover(); r != nil {
				logger := utils.GetLogger()
				logger.Error("Recovered from panic", zap.Any("error", r))
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
					"error":  "Internal Server Error",
					"detail": r,
				})
			}
		}()
		c.Next()
		// Nếu có lỗi từ các handler, trả về JSON error chuẩn
		if len(c.Errors) > 0 {
			logger := utils.GetLogger()
			for _, e := range c.Errors {
				logger.Error("Handler error", zap.Error(e))
			}
			c.<PERSON>(-1, gin.H{
				"error": c.Errors[0].Error(),
			})
		}
	}
} 