{"ast": null, "code": "import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8080/api/v1';// Create axios instance\nconst api=axios.create({baseURL:API_BASE_URL,headers:{'Content-Type':'application/json'}});// Request interceptor to add auth token\napi.interceptors.request.use(config=>{const token=localStorage.getItem('access_token');if(token){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// Response interceptor for automatic token refresh\napi.interceptors.response.use(response=>response,async error=>{var _error$response;const originalRequest=error.config;if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401&&!originalRequest._retry){originalRequest._retry=true;try{const refreshToken=localStorage.getItem('refresh_token');if(!refreshToken){throw new Error('No refresh token available');}const response=await axios.post(\"\".concat(API_BASE_URL,\"/auth/refresh\"),{refresh_token:refreshToken});const{token:newAccessToken,refresh_token:newRefreshToken}=response.data;localStorage.setItem('access_token',newAccessToken);localStorage.setItem('refresh_token',newRefreshToken);// Retry the original request with new token\noriginalRequest.headers.Authorization=\"Bearer \".concat(newAccessToken);return api(originalRequest);}catch(refreshError){// Refresh failed, redirect to login\nlocalStorage.removeItem('access_token');localStorage.removeItem('refresh_token');localStorage.removeItem('user');window.location.href='/login';return Promise.reject(refreshError);}}return Promise.reject(error);});// Common interfaces\n// Base CRUD service class\nexport class BaseCRUDService{constructor(baseUrl){this.baseUrl=void 0;this.baseUrl=baseUrl;}// List items with pagination\nlist(params){return api.get(this.baseUrl,{params});}// Search items\nsearch(searchRequest){return api.post(\"\".concat(this.baseUrl,\"/search\"),searchRequest);}// Get item by ID\ngetById(id){return api.get(\"\".concat(this.baseUrl,\"/\").concat(id));}// Create new item\ncreate(data){return api.post(this.baseUrl,data);}// Update item\nupdate(id,data){return api.put(\"\".concat(this.baseUrl,\"/\").concat(id),data);}// Delete item\ndelete(id){return api.delete(\"\".concat(this.baseUrl,\"/\").concat(id));}}export{api};export default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "Error", "post", "refresh_token", "newAccessToken", "newRefreshToken", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "BaseCRUDService", "constructor", "baseUrl", "list", "params", "get", "search", "searchRequest", "getById", "id", "update", "put", "delete"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/base.service.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for automatic token refresh\napi.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refresh_token');\n        if (!refreshToken) {\n          throw new Error('No refresh token available');\n        }\n\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refresh_token: refreshToken,\n        });\n\n        const { token: newAccessToken, refresh_token: newRefreshToken } = response.data;\n\n        localStorage.setItem('access_token', newAccessToken);\n        localStorage.setItem('refresh_token', newRefreshToken);\n\n        // Retry the original request with new token\n        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;\n        return api(originalRequest);\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// Common interfaces\nexport interface PaginationInfo {\n  page: number;\n  page_size: number;\n  total_items: number;\n  total_pages: number;\n}\n\nexport interface CRUDListResponse<T> {\n  data: T[];\n  pagination: PaginationInfo;\n  message?: string;\n}\n\nexport interface CRUDResponse<T> {\n  data: T;\n  message?: string;\n}\n\nexport interface SearchRequest {\n  page: number;\n  page_size: number;\n  search_term?: string;\n  filters?: Record<string, any>;\n}\n\nexport interface ErrorResponse {\n  error: string;\n  details?: string;\n  code?: string;\n}\n\n// Base CRUD service class\nexport abstract class BaseCRUDService<T, CreateT = Partial<T>, UpdateT = Partial<T>> {\n  protected baseUrl: string;\n\n  constructor(baseUrl: string) {\n    this.baseUrl = baseUrl;\n  }\n\n  // List items with pagination\n  list(params?: Record<string, any>): Promise<AxiosResponse<CRUDListResponse<T>>> {\n    return api.get(this.baseUrl, { params });\n  }\n\n  // Search items\n  search(searchRequest: SearchRequest): Promise<AxiosResponse<CRUDListResponse<T>>> {\n    return api.post(`${this.baseUrl}/search`, searchRequest);\n  }\n\n  // Get item by ID\n  getById(id: number): Promise<AxiosResponse<CRUDResponse<T>>> {\n    return api.get(`${this.baseUrl}/${id}`);\n  }\n\n  // Create new item\n  create(data: CreateT): Promise<AxiosResponse<CRUDResponse<T>>> {\n    return api.post(this.baseUrl, data);\n  }\n\n  // Update item\n  update(id: number, data: UpdateT): Promise<AxiosResponse<CRUDResponse<T>>> {\n    return api.put(`${this.baseUrl}/${id}`, data);\n  }\n\n  // Delete item\n  delete(id: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${id}`);\n  }\n}\n\nexport { api };\nexport default api;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAyB,OAAO,CAE5C,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,8BAA8B,CAEpF;AACA,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAClD,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAM,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAb,GAAG,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,EAAKA,QAAQ,CACtB,KAAO,CAAAH,KAAK,EAAK,KAAAI,eAAA,CACf,KAAM,CAAAC,eAAe,CAAGL,KAAK,CAACN,MAAM,CAEpC,GAAI,EAAAU,eAAA,CAAAJ,KAAK,CAACG,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBE,MAAM,IAAK,GAAG,EAAI,CAACD,eAAe,CAACE,MAAM,CAAE,CAC7DF,eAAe,CAACE,MAAM,CAAG,IAAI,CAE7B,GAAI,CACF,KAAM,CAAAC,YAAY,CAAGZ,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAC1D,GAAI,CAACW,YAAY,CAAE,CACjB,KAAM,IAAI,CAAAC,KAAK,CAAC,4BAA4B,CAAC,CAC/C,CAEA,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAArB,KAAK,CAAC4B,IAAI,IAAAX,MAAA,CAAIhB,YAAY,kBAAiB,CAChE4B,aAAa,CAAEH,YACjB,CAAC,CAAC,CAEF,KAAM,CAAEb,KAAK,CAAEiB,cAAc,CAAED,aAAa,CAAEE,eAAgB,CAAC,CAAGV,QAAQ,CAACW,IAAI,CAE/ElB,YAAY,CAACmB,OAAO,CAAC,cAAc,CAAEH,cAAc,CAAC,CACpDhB,YAAY,CAACmB,OAAO,CAAC,eAAe,CAAEF,eAAe,CAAC,CAEtD;AACAR,eAAe,CAACf,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaa,cAAc,CAAE,CAClE,MAAO,CAAAzB,GAAG,CAACkB,eAAe,CAAC,CAC7B,CAAE,MAAOW,YAAY,CAAE,CACrB;AACApB,YAAY,CAACqB,UAAU,CAAC,cAAc,CAAC,CACvCrB,YAAY,CAACqB,UAAU,CAAC,eAAe,CAAC,CACxCrB,YAAY,CAACqB,UAAU,CAAC,MAAM,CAAC,CAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CAC/B,MAAO,CAAAnB,OAAO,CAACC,MAAM,CAACc,YAAY,CAAC,CACrC,CACF,CAEA,MAAO,CAAAf,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AAgCA;AACA,MAAO,MAAe,CAAAqB,eAA+D,CAGnFC,WAAWA,CAACC,OAAe,CAAE,MAFnBA,OAAO,QAGf,IAAI,CAACA,OAAO,CAAGA,OAAO,CACxB,CAEA;AACAC,IAAIA,CAACC,MAA4B,CAA+C,CAC9E,MAAO,CAAAtC,GAAG,CAACuC,GAAG,CAAC,IAAI,CAACH,OAAO,CAAE,CAAEE,MAAO,CAAC,CAAC,CAC1C,CAEA;AACAE,MAAMA,CAACC,aAA4B,CAA+C,CAChF,MAAO,CAAAzC,GAAG,CAACuB,IAAI,IAAAX,MAAA,CAAI,IAAI,CAACwB,OAAO,YAAWK,aAAa,CAAC,CAC1D,CAEA;AACAC,OAAOA,CAACC,EAAU,CAA2C,CAC3D,MAAO,CAAA3C,GAAG,CAACuC,GAAG,IAAA3B,MAAA,CAAI,IAAI,CAACwB,OAAO,MAAAxB,MAAA,CAAI+B,EAAE,CAAE,CAAC,CACzC,CAEA;AACA1C,MAAMA,CAAC0B,IAAa,CAA2C,CAC7D,MAAO,CAAA3B,GAAG,CAACuB,IAAI,CAAC,IAAI,CAACa,OAAO,CAAET,IAAI,CAAC,CACrC,CAEA;AACAiB,MAAMA,CAACD,EAAU,CAAEhB,IAAa,CAA2C,CACzE,MAAO,CAAA3B,GAAG,CAAC6C,GAAG,IAAAjC,MAAA,CAAI,IAAI,CAACwB,OAAO,MAAAxB,MAAA,CAAI+B,EAAE,EAAIhB,IAAI,CAAC,CAC/C,CAEA;AACAmB,MAAMA,CAACH,EAAU,CAA+C,CAC9D,MAAO,CAAA3C,GAAG,CAAC8C,MAAM,IAAAlC,MAAA,CAAI,IAAI,CAACwB,OAAO,MAAAxB,MAAA,CAAI+B,EAAE,CAAE,CAAC,CAC5C,CACF,CAEA,OAAS3C,GAAG,EACZ,cAAe,CAAAA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}