{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/Build-Project/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{userManagementAPI,roleManagementAPI,permissionManagementAPI}from'../../services/api';import UserModal from'./UserModal';import RoleModal from'./RoleModal';import PermissionModal from'./PermissionModal';import AssignmentsTab from'./AssignmentsTab';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const UserPermissionManagement=()=>{// State for active tab\nconst[activeTab,setActiveTab]=useState('users');// Users state\nconst[users,setUsers]=useState([]);const[usersPagination,setUsersPagination]=useState({page:1,page_size:10,total_items:0,total_pages:0});const[usersLoading,setUsersLoading]=useState(false);const[usersSearchTerm,setUsersSearchTerm]=useState('');// Roles state\nconst[roles,setRoles]=useState([]);const[rolesPagination,setRolesPagination]=useState({page:1,page_size:10,total_items:0,total_pages:0});const[rolesLoading,setRolesLoading]=useState(false);const[rolesSearchTerm,setRolesSearchTerm]=useState('');// Permissions state\nconst[permissions,setPermissions]=useState([]);const[permissionsPagination,setPermissionsPagination]=useState({page:1,page_size:10,total_items:0,total_pages:0});const[permissionsLoading,setPermissionsLoading]=useState(false);const[permissionsSearchTerm,setPermissionsSearchTerm]=useState('');// General state\nconst[message,setMessage]=useState('');const[error,setError]=useState('');// Modal states\nconst[showUserModal,setShowUserModal]=useState(false);const[showRoleModal,setShowRoleModal]=useState(false);const[showPermissionModal,setShowPermissionModal]=useState(false);const[editingUser,setEditingUser]=useState(null);const[editingRole,setEditingRole]=useState(null);const[editingPermission,setEditingPermission]=useState(null);// Load data on component mount and tab change\nuseEffect(()=>{switch(activeTab){case'users':loadUsers();break;case'roles':loadRoles();break;case'permissions':loadPermissions();break;case'assignments':loadUsers();// Load users for assignment interface\nloadRoles();// Load roles for assignment interface\nbreak;}},[activeTab]);// Users functions\nconst loadUsers=async function(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;let searchTerm=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';setUsersLoading(true);setError('');try{let response;if(searchTerm.trim()){const searchRequest={page,page_size:usersPagination.page_size,filters:[{field:'username',operator:'ilike',value:\"%\".concat(searchTerm,\"%\")},{field:'email',operator:'ilike',value:\"%\".concat(searchTerm,\"%\")}],order_by:'username',order_dir:'asc'};response=await userManagementAPI.search(searchRequest);}else{response=await userManagementAPI.list({page,page_size:usersPagination.page_size,order_by:'username',order_dir:'asc'});}setUsers(response.data.data);setUsersPagination(response.data.pagination);}catch(err){var _err$response,_err$response$data;setError(\"Failed to load users: \".concat(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.error)||err.message));}finally{setUsersLoading(false);}};const handleUsersSearch=()=>{setUsersPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:1}));loadUsers(1,usersSearchTerm);};const handleUsersPageChange=newPage=>{setUsersPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:newPage}));loadUsers(newPage,usersSearchTerm);};// Roles functions\nconst loadRoles=async function(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;let searchTerm=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';setRolesLoading(true);setError('');try{let response;if(searchTerm.trim()){const searchRequest={page,page_size:rolesPagination.page_size,filters:[{field:'name',operator:'ilike',value:\"%\".concat(searchTerm,\"%\")}],order_by:'name',order_dir:'asc'};response=await roleManagementAPI.search(searchRequest);}else{response=await roleManagementAPI.list({page,page_size:rolesPagination.page_size,order_by:'name',order_dir:'asc'});}setRoles(response.data.data);setRolesPagination(response.data.pagination);}catch(err){var _err$response2,_err$response2$data;setError(\"Failed to load roles: \".concat(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.error)||err.message));}finally{setRolesLoading(false);}};const handleRolesSearch=()=>{setRolesPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:1}));loadRoles(1,rolesSearchTerm);};const handleRolesPageChange=newPage=>{setRolesPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:newPage}));loadRoles(newPage,rolesSearchTerm);};// Permissions functions\nconst loadPermissions=async function(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;let searchTerm=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';setPermissionsLoading(true);setError('');try{let response;if(searchTerm.trim()){const searchRequest={page,page_size:permissionsPagination.page_size,filters:[{field:'name',operator:'ilike',value:\"%\".concat(searchTerm,\"%\")}],order_by:'name',order_dir:'asc'};response=await permissionManagementAPI.search(searchRequest);}else{response=await permissionManagementAPI.list({page,page_size:permissionsPagination.page_size,order_by:'name',order_dir:'asc'});}setPermissions(response.data.data);setPermissionsPagination(response.data.pagination);}catch(err){var _err$response3,_err$response3$data;setError(\"Failed to load permissions: \".concat(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.error)||err.message));}finally{setPermissionsLoading(false);}};const handlePermissionsSearch=()=>{setPermissionsPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:1}));loadPermissions(1,permissionsSearchTerm);};const handlePermissionsPageChange=newPage=>{setPermissionsPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:newPage}));loadPermissions(newPage,permissionsSearchTerm);};// Delete functions\nconst handleDeleteUser=async id=>{if(!window.confirm('Are you sure you want to delete this user?'))return;try{await userManagementAPI.delete(id);setMessage('User deleted successfully');loadUsers(usersPagination.page,usersSearchTerm);}catch(err){var _err$response4,_err$response4$data;setError(\"Failed to delete user: \".concat(((_err$response4=err.response)===null||_err$response4===void 0?void 0:(_err$response4$data=_err$response4.data)===null||_err$response4$data===void 0?void 0:_err$response4$data.error)||err.message));}};const handleDeleteRole=async id=>{if(!window.confirm('Are you sure you want to delete this role?'))return;try{await roleManagementAPI.delete(id);setMessage('Role deleted successfully');loadRoles(rolesPagination.page,rolesSearchTerm);}catch(err){var _err$response5,_err$response5$data;setError(\"Failed to delete role: \".concat(((_err$response5=err.response)===null||_err$response5===void 0?void 0:(_err$response5$data=_err$response5.data)===null||_err$response5$data===void 0?void 0:_err$response5$data.error)||err.message));}};const handleDeletePermission=async id=>{if(!window.confirm('Are you sure you want to delete this permission?'))return;try{await permissionManagementAPI.delete(id);setMessage('Permission deleted successfully');loadPermissions(permissionsPagination.page,permissionsSearchTerm);}catch(err){var _err$response6,_err$response6$data;setError(\"Failed to delete permission: \".concat(((_err$response6=err.response)===null||_err$response6===void 0?void 0:(_err$response6$data=_err$response6.data)===null||_err$response6$data===void 0?void 0:_err$response6$data.error)||err.message));}};// Render pagination\nconst renderPagination=(pagination,onPageChange)=>{const pages=[];for(let i=1;i<=pagination.total_pages;i++){pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(i),className:\"px-3 py-1 mx-1 rounded \".concat(i===pagination.page?'bg-blue-500 text-white':'bg-gray-200 text-gray-700 hover:bg-gray-300'),children:i},i));}return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mt-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-600\",children:[\"Showing \",(pagination.page-1)*pagination.page_size+1,\" to\",' ',Math.min(pagination.page*pagination.page_size,pagination.total_items),\" of\",' ',pagination.total_items,\" entries\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(pagination.page-1),disabled:pagination.page<=1,className:\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",children:\"Previous\"}),pages,/*#__PURE__*/_jsx(\"button\",{onClick:()=>onPageChange(pagination.page+1),disabled:pagination.page>=pagination.total_pages,className:\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\",children:\"Next\"})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-4 py-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-8\",children:\"User Permission Management\"}),message&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\",children:message}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",children:error}),/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200 mb-6\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"-mb-px flex space-x-8\",children:[{key:'users',label:'Users'},{key:'roles',label:'Roles'},{key:'permissions',label:'Permissions'},{key:'assignments',label:'Assignments'}].map(tab=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab(tab.key),className:\"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab===tab.key?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:tab.label},tab.key))})}),activeTab==='users'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"User Management\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingUser(null);setShowUserModal(true);},className:\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",children:\"Add User\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 flex gap-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search users by username or email...\",value:usersSearchTerm,onChange:e=>setUsersSearchTerm(e.target.value),className:\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",onKeyPress:e=>e.key==='Enter'&&handleUsersSearch()}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUsersSearch,className:\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",children:\"Search\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setUsersSearchTerm('');loadUsers(1,'');},className:\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",children:\"Clear\"})]}),usersLoading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8\",children:\"Loading users...\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full bg-white border border-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Username\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:users.map(user=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:user.id}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",children:user.username}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:user.email}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.is_active?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:user.is_active?'Active':'Inactive'})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:user.created_at?new Date(user.created_at).toLocaleDateString():'N/A'}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingUser(user);setShowUserModal(true);},className:\"text-blue-600 hover:text-blue-900 mr-3\",children:\"Edit\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteUser(user.id),className:\"text-red-600 hover:text-red-900\",children:\"Delete\"})]})]},user.id))})]})}),renderPagination(usersPagination,handleUsersPageChange)]})]}),activeTab==='roles'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"Role Management\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingRole(null);setShowRoleModal(true);},className:\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",children:\"Add Role\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 flex gap-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search roles by name...\",value:rolesSearchTerm,onChange:e=>setRolesSearchTerm(e.target.value),className:\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",onKeyPress:e=>e.key==='Enter'&&handleRolesSearch()}),/*#__PURE__*/_jsx(\"button\",{onClick:handleRolesSearch,className:\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",children:\"Search\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setRolesSearchTerm('');loadRoles(1,'');},className:\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",children:\"Clear\"})]}),rolesLoading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8\",children:\"Loading roles...\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full bg-white border border-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Description\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:roles.map(role=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:role.id}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",children:role.name}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",children:role.description||'No description'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(role.is_active?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:role.is_active?'Active':'Inactive'})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:role.created_at?new Date(role.created_at).toLocaleDateString():'N/A'}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingRole(role);setShowRoleModal(true);},className:\"text-blue-600 hover:text-blue-900 mr-3\",children:\"Edit\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteRole(role.id),className:\"text-red-600 hover:text-red-900\",children:\"Delete\"})]})]},role.id))})]})}),renderPagination(rolesPagination,handleRolesPageChange)]})]}),activeTab==='permissions'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"Permission Management\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingPermission(null);setShowPermissionModal(true);},className:\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",children:\"Add Permission\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 flex gap-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search permissions by name...\",value:permissionsSearchTerm,onChange:e=>setPermissionsSearchTerm(e.target.value),className:\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",onKeyPress:e=>e.key==='Enter'&&handlePermissionsSearch()}),/*#__PURE__*/_jsx(\"button\",{onClick:handlePermissionsSearch,className:\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\",children:\"Search\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setPermissionsSearchTerm('');loadPermissions(1,'');},className:\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\",children:\"Clear\"})]}),permissionsLoading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8\",children:\"Loading permissions...\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full bg-white border border-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Description\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:permissions.map(permission=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:permission.id}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",children:permission.name}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\",children:permission.description||'No description'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(permission.is_active?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:permission.is_active?'Active':'Inactive'})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:permission.created_at?new Date(permission.created_at).toLocaleDateString():'N/A'}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingPermission(permission);setShowPermissionModal(true);},className:\"text-blue-600 hover:text-blue-900 mr-3\",children:\"Edit\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeletePermission(permission.id),className:\"text-red-600 hover:text-red-900\",children:\"Delete\"})]})]},permission.id))})]})}),renderPagination(permissionsPagination,handlePermissionsPageChange)]})]}),activeTab==='assignments'&&/*#__PURE__*/_jsx(AssignmentsTab,{users:users,roles:roles,onMessage:setMessage,onError:setError}),/*#__PURE__*/_jsx(UserModal,{isOpen:showUserModal,onClose:()=>setShowUserModal(false),user:editingUser,onSuccess:()=>{setMessage(editingUser?'User updated successfully':'User created successfully');loadUsers(usersPagination.page,usersSearchTerm);}}),/*#__PURE__*/_jsx(RoleModal,{isOpen:showRoleModal,onClose:()=>setShowRoleModal(false),role:editingRole,onSuccess:()=>{setMessage(editingRole?'Role updated successfully':'Role created successfully');loadRoles(rolesPagination.page,rolesSearchTerm);}}),/*#__PURE__*/_jsx(PermissionModal,{isOpen:showPermissionModal,onClose:()=>setShowPermissionModal(false),permission:editingPermission,onSuccess:()=>{setMessage(editingPermission?'Permission updated successfully':'Permission created successfully');loadPermissions(permissionsPagination.page,permissionsSearchTerm);}})]});};export default UserPermissionManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "userManagementAPI", "roleManagementAPI", "permissionManagementAPI", "UserModal", "RoleModal", "PermissionModal", "AssignmentsTab", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "UserPermissionManagement", "activeTab", "setActiveTab", "users", "setUsers", "usersPagination", "setUsersPagination", "page", "page_size", "total_items", "total_pages", "usersLoading", "setUsersLoading", "usersSearchTerm", "setUsersSearchTerm", "roles", "setRoles", "rolesPagination", "setRolesPagination", "rolesLoading", "setRolesLoading", "rolesSearchTerm", "setRolesSearchTerm", "permissions", "setPermissions", "permissionsPagination", "setPermissionsPagination", "permissionsLoading", "setPermissionsLoading", "permissionsSearchTerm", "setPermissionsSearchTerm", "message", "setMessage", "error", "setError", "showUserModal", "setShowUserModal", "showRoleModal", "setShowRoleModal", "showPermissionModal", "setShowPermissionModal", "editingUser", "setEditingUser", "editingRole", "setEditingRole", "editingPermission", "setEditingPermission", "loadUsers", "loadRoles", "loadPermissions", "arguments", "length", "undefined", "searchTerm", "response", "trim", "searchRequest", "filters", "field", "operator", "value", "concat", "order_by", "order_dir", "search", "list", "data", "pagination", "err", "_err$response", "_err$response$data", "handleUsersSearch", "prev", "_objectSpread", "handleUsersPageChange", "newPage", "_err$response2", "_err$response2$data", "handleRolesSearch", "handleRolesPageChange", "_err$response3", "_err$response3$data", "handlePermissionsSearch", "handlePermissionsPageChange", "handleDeleteUser", "id", "window", "confirm", "delete", "_err$response4", "_err$response4$data", "handleDeleteRole", "_err$response5", "_err$response5$data", "handleDeletePermission", "_err$response6", "_err$response6$data", "renderPagination", "onPageChange", "pages", "i", "push", "onClick", "className", "children", "Math", "min", "disabled", "key", "label", "map", "tab", "type", "placeholder", "onChange", "e", "target", "onKeyPress", "user", "username", "email", "is_active", "created_at", "Date", "toLocaleDateString", "role", "name", "description", "permission", "onMessage", "onError", "isOpen", "onClose", "onSuccess"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/UserPermissionManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User, Role, Permission } from '../../types';\nimport {\n  userManagementAPI,\n  roleManagementAPI,\n  permissionManagementAPI,\n  adminAPI,\n  PaginationInfo,\n  SearchRequest\n} from '../../services/api';\nimport UserModal from './UserModal';\nimport RoleModal from './RoleModal';\nimport PermissionModal from './PermissionModal';\nimport AssignmentsTab from './AssignmentsTab';\n\ninterface UserPermissionManagementProps { }\n\nconst UserPermissionManagement: React.FC<UserPermissionManagementProps> = () => {\n  // State for active tab\n  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'permissions' | 'assignments'>('users');\n\n  // Users state\n  const [users, setUsers] = useState<User[]>([]);\n  const [usersPagination, setUsersPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [usersLoading, setUsersLoading] = useState(false);\n  const [usersSearchTerm, setUsersSearchTerm] = useState('');\n\n  // Roles state\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [rolesPagination, setRolesPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [rolesLoading, setRolesLoading] = useState(false);\n  const [rolesSearchTerm, setRolesSearchTerm] = useState('');\n\n  // Permissions state\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [permissionsPagination, setPermissionsPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0\n  });\n  const [permissionsLoading, setPermissionsLoading] = useState(false);\n  const [permissionsSearchTerm, setPermissionsSearchTerm] = useState('');\n\n  // General state\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // Modal states\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [showRoleModal, setShowRoleModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n\n  // Load data on component mount and tab change\n  useEffect(() => {\n    switch (activeTab) {\n      case 'users':\n        loadUsers();\n        break;\n      case 'roles':\n        loadRoles();\n        break;\n      case 'permissions':\n        loadPermissions();\n        break;\n      case 'assignments':\n        loadUsers(); // Load users for assignment interface\n        loadRoles(); // Load roles for assignment interface\n        break;\n    }\n  }, [activeTab]);\n\n  // Users functions\n  const loadUsers = async (page = 1, searchTerm = '') => {\n    setUsersLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: usersPagination.page_size,\n          filters: [\n            {\n              field: 'username',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            },\n            {\n              field: 'email',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'username',\n          order_dir: 'asc'\n        };\n        response = await userManagementAPI.search(searchRequest);\n      } else {\n        response = await userManagementAPI.list({\n          page,\n          page_size: usersPagination.page_size,\n          order_by: 'username',\n          order_dir: 'asc'\n        });\n      }\n\n      setUsers(response.data.data);\n      setUsersPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load users: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setUsersLoading(false);\n    }\n  };\n\n  const handleUsersSearch = () => {\n    setUsersPagination(prev => ({ ...prev, page: 1 }));\n    loadUsers(1, usersSearchTerm);\n  };\n\n  const handleUsersPageChange = (newPage: number) => {\n    setUsersPagination(prev => ({ ...prev, page: newPage }));\n    loadUsers(newPage, usersSearchTerm);\n  };\n\n  // Roles functions\n  const loadRoles = async (page = 1, searchTerm = '') => {\n    setRolesLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: rolesPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await roleManagementAPI.search(searchRequest);\n      } else {\n        response = await roleManagementAPI.list({\n          page,\n          page_size: rolesPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setRoles(response.data.data);\n      setRolesPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load roles: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setRolesLoading(false);\n    }\n  };\n\n  const handleRolesSearch = () => {\n    setRolesPagination(prev => ({ ...prev, page: 1 }));\n    loadRoles(1, rolesSearchTerm);\n  };\n\n  const handleRolesPageChange = (newPage: number) => {\n    setRolesPagination(prev => ({ ...prev, page: newPage }));\n    loadRoles(newPage, rolesSearchTerm);\n  };\n\n  // Permissions functions\n  const loadPermissions = async (page = 1, searchTerm = '') => {\n    setPermissionsLoading(true);\n    setError('');\n    try {\n      let response;\n      if (searchTerm.trim()) {\n        const searchRequest: SearchRequest = {\n          page,\n          page_size: permissionsPagination.page_size,\n          filters: [\n            {\n              field: 'name',\n              operator: 'ilike',\n              value: `%${searchTerm}%`\n            }\n          ],\n          order_by: 'name',\n          order_dir: 'asc'\n        };\n        response = await permissionManagementAPI.search(searchRequest);\n      } else {\n        response = await permissionManagementAPI.list({\n          page,\n          page_size: permissionsPagination.page_size,\n          order_by: 'name',\n          order_dir: 'asc'\n        });\n      }\n\n      setPermissions(response.data.data);\n      setPermissionsPagination(response.data.pagination);\n    } catch (err: any) {\n      setError(`Failed to load permissions: ${err.response?.data?.error || err.message}`);\n    } finally {\n      setPermissionsLoading(false);\n    }\n  };\n\n  const handlePermissionsSearch = () => {\n    setPermissionsPagination(prev => ({ ...prev, page: 1 }));\n    loadPermissions(1, permissionsSearchTerm);\n  };\n\n  const handlePermissionsPageChange = (newPage: number) => {\n    setPermissionsPagination(prev => ({ ...prev, page: newPage }));\n    loadPermissions(newPage, permissionsSearchTerm);\n  };\n\n  // Delete functions\n  const handleDeleteUser = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this user?')) return;\n\n    try {\n      await userManagementAPI.delete(id);\n      setMessage('User deleted successfully');\n      loadUsers(usersPagination.page, usersSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete user: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeleteRole = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this role?')) return;\n\n    try {\n      await roleManagementAPI.delete(id);\n      setMessage('Role deleted successfully');\n      loadRoles(rolesPagination.page, rolesSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete role: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  const handleDeletePermission = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this permission?')) return;\n\n    try {\n      await permissionManagementAPI.delete(id);\n      setMessage('Permission deleted successfully');\n      loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n    } catch (err: any) {\n      setError(`Failed to delete permission: ${err.response?.data?.error || err.message}`);\n    }\n  };\n\n  // Render pagination\n  const renderPagination = (pagination: PaginationInfo, onPageChange: (page: number) => void) => {\n    const pages = [];\n    for (let i = 1; i <= pagination.total_pages; i++) {\n      pages.push(\n        <button\n          key={i}\n          onClick={() => onPageChange(i)}\n          className={`px-3 py-1 mx-1 rounded ${i === pagination.page\n            ? 'bg-blue-500 text-white'\n            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n        >\n          {i}\n        </button>\n      );\n    }\n\n    return (\n      <div className=\"flex items-center justify-between mt-4\">\n        <div className=\"text-sm text-gray-600\">\n          Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}\n          {Math.min(pagination.page * pagination.page_size, pagination.total_items)} of{' '}\n          {pagination.total_items} entries\n        </div>\n        <div className=\"flex items-center\">\n          <button\n            onClick={() => onPageChange(pagination.page - 1)}\n            disabled={pagination.page <= 1}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Previous\n          </button>\n          {pages}\n          <button\n            onClick={() => onPageChange(pagination.page + 1)}\n            disabled={pagination.page >= pagination.total_pages}\n            className=\"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50\"\n          >\n            Next\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">User Permission Management</h1>\n\n      {/* Messages */}\n      {message && (\n        <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n          {message}\n        </div>\n      )}\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {error}\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {[\n            { key: 'users', label: 'Users' },\n            { key: 'roles', label: 'Roles' },\n            { key: 'permissions', label: 'Permissions' },\n            { key: 'assignments', label: 'Assignments' }\n          ].map((tab) => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'users' && (\n        <div>\n          {/* Users Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">User Management</h2>\n            <button\n              onClick={() => {\n                setEditingUser(null);\n                setShowUserModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add User\n            </button>\n          </div>\n\n          {/* Users Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search users by username or email...\"\n              value={usersSearchTerm}\n              onChange={(e) => setUsersSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleUsersSearch()}\n            />\n            <button\n              onClick={handleUsersSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setUsersSearchTerm('');\n                loadUsers(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Users Table */}\n          {usersLoading ? (\n            <div className=\"text-center py-8\">Loading users...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Username\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Email\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <tr key={user.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {user.username}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.email}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingUser(user);\n                              setShowUserModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteUser(user.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Users Pagination */}\n              {renderPagination(usersPagination, handleUsersPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Roles Tab */}\n      {activeTab === 'roles' && (\n        <div>\n          {/* Roles Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Role Management</h2>\n            <button\n              onClick={() => {\n                setEditingRole(null);\n                setShowRoleModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Role\n            </button>\n          </div>\n\n          {/* Roles Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search roles by name...\"\n              value={rolesSearchTerm}\n              onChange={(e) => setRolesSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleRolesSearch()}\n            />\n            <button\n              onClick={handleRolesSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setRolesSearchTerm('');\n                loadRoles(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Roles Table */}\n          {rolesLoading ? (\n            <div className=\"text-center py-8\">Loading roles...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {roles.map((role) => (\n                      <tr key={role.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {role.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {role.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {role.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingRole(role);\n                              setShowRoleModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeleteRole(role.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Roles Pagination */}\n              {renderPagination(rolesPagination, handleRolesPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Permissions Tab */}\n      {activeTab === 'permissions' && (\n        <div>\n          {/* Permissions Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold\">Permission Management</h2>\n            <button\n              onClick={() => {\n                setEditingPermission(null);\n                setShowPermissionModal(true);\n              }}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Permission\n            </button>\n          </div>\n\n          {/* Permissions Search */}\n          <div className=\"mb-4 flex gap-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search permissions by name...\"\n              value={permissionsSearchTerm}\n              onChange={(e) => setPermissionsSearchTerm(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handlePermissionsSearch()}\n            />\n            <button\n              onClick={handlePermissionsSearch}\n              className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Search\n            </button>\n            <button\n              onClick={() => {\n                setPermissionsSearchTerm('');\n                loadPermissions(1, '');\n              }}\n              className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            >\n              Clear\n            </button>\n          </div>\n\n          {/* Permissions Table */}\n          {permissionsLoading ? (\n            <div className=\"text-center py-8\">Loading permissions...</div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full bg-white border border-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        ID\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Name\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Description\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {permissions.map((permission) => (\n                      <tr key={permission.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.id}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {permission.name}\n                        </td>\n                        <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                          {permission.description || 'No description'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${permission.is_active\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                            }`}>\n                            {permission.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {permission.created_at ? new Date(permission.created_at).toLocaleDateString() : 'N/A'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => {\n                              setEditingPermission(permission);\n                              setShowPermissionModal(true);\n                            }}\n                            className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleDeletePermission(permission.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Permissions Pagination */}\n              {renderPagination(permissionsPagination, handlePermissionsPageChange)}\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Assignments Tab */}\n      {activeTab === 'assignments' && (\n        <AssignmentsTab\n          users={users}\n          roles={roles}\n          onMessage={setMessage}\n          onError={setError}\n        />\n      )}\n\n      {/* Modals */}\n      <UserModal\n        isOpen={showUserModal}\n        onClose={() => setShowUserModal(false)}\n        user={editingUser}\n        onSuccess={() => {\n          setMessage(editingUser ? 'User updated successfully' : 'User created successfully');\n          loadUsers(usersPagination.page, usersSearchTerm);\n        }}\n      />\n\n      <RoleModal\n        isOpen={showRoleModal}\n        onClose={() => setShowRoleModal(false)}\n        role={editingRole}\n        onSuccess={() => {\n          setMessage(editingRole ? 'Role updated successfully' : 'Role created successfully');\n          loadRoles(rolesPagination.page, rolesSearchTerm);\n        }}\n      />\n\n      <PermissionModal\n        isOpen={showPermissionModal}\n        onClose={() => setShowPermissionModal(false)}\n        permission={editingPermission}\n        onSuccess={() => {\n          setMessage(editingPermission ? 'Permission updated successfully' : 'Permission created successfully');\n          loadPermissions(permissionsPagination.page, permissionsSearchTerm);\n        }}\n      />\n    </div>\n  );\n};\n\nexport default UserPermissionManagement;\n"], "mappings": "kIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAElD,OACEC,iBAAiB,CACjBC,iBAAiB,CACjBC,uBAAuB,KAIlB,oBAAoB,CAC3B,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAI9C,KAAM,CAAAC,wBAAiE,CAAGA,CAAA,GAAM,CAC9E;AACA,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAoD,OAAO,CAAC,CAEtG;AACA,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACoB,eAAe,CAAEC,kBAAkB,CAAC,CAAGrB,QAAQ,CAAiB,CACrEsB,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,EAAE,CACbC,WAAW,CAAE,CAAC,CACdC,WAAW,CAAE,CACf,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4B,eAAe,CAAEC,kBAAkB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAE1D;AACA,KAAM,CAAC8B,KAAK,CAAEC,QAAQ,CAAC,CAAG/B,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACgC,eAAe,CAAEC,kBAAkB,CAAC,CAAGjC,QAAQ,CAAiB,CACrEsB,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,EAAE,CACbC,WAAW,CAAE,CAAC,CACdC,WAAW,CAAE,CACf,CAAC,CAAC,CACF,KAAM,CAACS,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACoC,eAAe,CAAEC,kBAAkB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAE1D;AACA,KAAM,CAACsC,WAAW,CAAEC,cAAc,CAAC,CAAGvC,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAACwC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGzC,QAAQ,CAAiB,CACjFsB,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,EAAE,CACbC,WAAW,CAAE,CAAC,CACdC,WAAW,CAAE,CACf,CAAC,CAAC,CACF,KAAM,CAACiB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC4C,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAEtE;AACA,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAEtC;AACA,KAAM,CAACkD,aAAa,CAAEC,gBAAgB,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACoD,aAAa,CAAEC,gBAAgB,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACsD,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAACwD,WAAW,CAAEC,cAAc,CAAC,CAAGzD,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAAC0D,WAAW,CAAEC,cAAc,CAAC,CAAG3D,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAAC4D,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7D,QAAQ,CAAoB,IAAI,CAAC,CAEnF;AACAC,SAAS,CAAC,IAAM,CACd,OAAQe,SAAS,EACf,IAAK,OAAO,CACV8C,SAAS,CAAC,CAAC,CACX,MACF,IAAK,OAAO,CACVC,SAAS,CAAC,CAAC,CACX,MACF,IAAK,aAAa,CAChBC,eAAe,CAAC,CAAC,CACjB,MACF,IAAK,aAAa,CAChBF,SAAS,CAAC,CAAC,CAAE;AACbC,SAAS,CAAC,CAAC,CAAE;AACb,MACJ,CACF,CAAC,CAAE,CAAC/C,SAAS,CAAC,CAAC,CAEf;AACA,KAAM,CAAA8C,SAAS,CAAG,cAAAA,CAAA,CAAqC,IAA9B,CAAAxC,IAAI,CAAA2C,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,UAAU,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAChDtC,eAAe,CAAC,IAAI,CAAC,CACrBsB,QAAQ,CAAC,EAAE,CAAC,CACZ,GAAI,CACF,GAAI,CAAAoB,QAAQ,CACZ,GAAID,UAAU,CAACE,IAAI,CAAC,CAAC,CAAE,CACrB,KAAM,CAAAC,aAA4B,CAAG,CACnCjD,IAAI,CACJC,SAAS,CAAEH,eAAe,CAACG,SAAS,CACpCiD,OAAO,CAAE,CACP,CACEC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,OAAO,CACjBC,KAAK,KAAAC,MAAA,CAAMR,UAAU,KACvB,CAAC,CACD,CACEK,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,OAAO,CACjBC,KAAK,KAAAC,MAAA,CAAMR,UAAU,KACvB,CAAC,CACF,CACDS,QAAQ,CAAE,UAAU,CACpBC,SAAS,CAAE,KACb,CAAC,CACDT,QAAQ,CAAG,KAAM,CAAAnE,iBAAiB,CAAC6E,MAAM,CAACR,aAAa,CAAC,CAC1D,CAAC,IAAM,CACLF,QAAQ,CAAG,KAAM,CAAAnE,iBAAiB,CAAC8E,IAAI,CAAC,CACtC1D,IAAI,CACJC,SAAS,CAAEH,eAAe,CAACG,SAAS,CACpCsD,QAAQ,CAAE,UAAU,CACpBC,SAAS,CAAE,KACb,CAAC,CAAC,CACJ,CAEA3D,QAAQ,CAACkD,QAAQ,CAACY,IAAI,CAACA,IAAI,CAAC,CAC5B5D,kBAAkB,CAACgD,QAAQ,CAACY,IAAI,CAACC,UAAU,CAAC,CAC9C,CAAE,MAAOC,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBpC,QAAQ,0BAAA2B,MAAA,CAA0B,EAAAQ,aAAA,CAAAD,GAAG,CAACd,QAAQ,UAAAe,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcH,IAAI,UAAAI,kBAAA,iBAAlBA,kBAAA,CAAoBrC,KAAK,GAAImC,GAAG,CAACrC,OAAO,CAAE,CAAC,CAC/E,CAAC,OAAS,CACRnB,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAA2D,iBAAiB,CAAGA,CAAA,GAAM,CAC9BjE,kBAAkB,CAACkE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEjE,IAAI,CAAE,CAAC,EAAG,CAAC,CAClDwC,SAAS,CAAC,CAAC,CAAElC,eAAe,CAAC,CAC/B,CAAC,CAED,KAAM,CAAA6D,qBAAqB,CAAIC,OAAe,EAAK,CACjDrE,kBAAkB,CAACkE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEjE,IAAI,CAAEoE,OAAO,EAAG,CAAC,CACxD5B,SAAS,CAAC4B,OAAO,CAAE9D,eAAe,CAAC,CACrC,CAAC,CAED;AACA,KAAM,CAAAmC,SAAS,CAAG,cAAAA,CAAA,CAAqC,IAA9B,CAAAzC,IAAI,CAAA2C,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,UAAU,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAChD9B,eAAe,CAAC,IAAI,CAAC,CACrBc,QAAQ,CAAC,EAAE,CAAC,CACZ,GAAI,CACF,GAAI,CAAAoB,QAAQ,CACZ,GAAID,UAAU,CAACE,IAAI,CAAC,CAAC,CAAE,CACrB,KAAM,CAAAC,aAA4B,CAAG,CACnCjD,IAAI,CACJC,SAAS,CAAES,eAAe,CAACT,SAAS,CACpCiD,OAAO,CAAE,CACP,CACEC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,OAAO,CACjBC,KAAK,KAAAC,MAAA,CAAMR,UAAU,KACvB,CAAC,CACF,CACDS,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,KACb,CAAC,CACDT,QAAQ,CAAG,KAAM,CAAAlE,iBAAiB,CAAC4E,MAAM,CAACR,aAAa,CAAC,CAC1D,CAAC,IAAM,CACLF,QAAQ,CAAG,KAAM,CAAAlE,iBAAiB,CAAC6E,IAAI,CAAC,CACtC1D,IAAI,CACJC,SAAS,CAAES,eAAe,CAACT,SAAS,CACpCsD,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,KACb,CAAC,CAAC,CACJ,CAEA/C,QAAQ,CAACsC,QAAQ,CAACY,IAAI,CAACA,IAAI,CAAC,CAC5BhD,kBAAkB,CAACoC,QAAQ,CAACY,IAAI,CAACC,UAAU,CAAC,CAC9C,CAAE,MAAOC,GAAQ,CAAE,KAAAQ,cAAA,CAAAC,mBAAA,CACjB3C,QAAQ,0BAAA2B,MAAA,CAA0B,EAAAe,cAAA,CAAAR,GAAG,CAACd,QAAQ,UAAAsB,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcV,IAAI,UAAAW,mBAAA,iBAAlBA,mBAAA,CAAoB5C,KAAK,GAAImC,GAAG,CAACrC,OAAO,CAAE,CAAC,CAC/E,CAAC,OAAS,CACRX,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAA0D,iBAAiB,CAAGA,CAAA,GAAM,CAC9B5D,kBAAkB,CAACsD,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEjE,IAAI,CAAE,CAAC,EAAG,CAAC,CAClDyC,SAAS,CAAC,CAAC,CAAE3B,eAAe,CAAC,CAC/B,CAAC,CAED,KAAM,CAAA0D,qBAAqB,CAAIJ,OAAe,EAAK,CACjDzD,kBAAkB,CAACsD,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEjE,IAAI,CAAEoE,OAAO,EAAG,CAAC,CACxD3B,SAAS,CAAC2B,OAAO,CAAEtD,eAAe,CAAC,CACrC,CAAC,CAED;AACA,KAAM,CAAA4B,eAAe,CAAG,cAAAA,CAAA,CAAqC,IAA9B,CAAA1C,IAAI,CAAA2C,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,UAAU,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACtDtB,qBAAqB,CAAC,IAAI,CAAC,CAC3BM,QAAQ,CAAC,EAAE,CAAC,CACZ,GAAI,CACF,GAAI,CAAAoB,QAAQ,CACZ,GAAID,UAAU,CAACE,IAAI,CAAC,CAAC,CAAE,CACrB,KAAM,CAAAC,aAA4B,CAAG,CACnCjD,IAAI,CACJC,SAAS,CAAEiB,qBAAqB,CAACjB,SAAS,CAC1CiD,OAAO,CAAE,CACP,CACEC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,OAAO,CACjBC,KAAK,KAAAC,MAAA,CAAMR,UAAU,KACvB,CAAC,CACF,CACDS,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,KACb,CAAC,CACDT,QAAQ,CAAG,KAAM,CAAAjE,uBAAuB,CAAC2E,MAAM,CAACR,aAAa,CAAC,CAChE,CAAC,IAAM,CACLF,QAAQ,CAAG,KAAM,CAAAjE,uBAAuB,CAAC4E,IAAI,CAAC,CAC5C1D,IAAI,CACJC,SAAS,CAAEiB,qBAAqB,CAACjB,SAAS,CAC1CsD,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,KACb,CAAC,CAAC,CACJ,CAEAvC,cAAc,CAAC8B,QAAQ,CAACY,IAAI,CAACA,IAAI,CAAC,CAClCxC,wBAAwB,CAAC4B,QAAQ,CAACY,IAAI,CAACC,UAAU,CAAC,CACpD,CAAE,MAAOC,GAAQ,CAAE,KAAAY,cAAA,CAAAC,mBAAA,CACjB/C,QAAQ,gCAAA2B,MAAA,CAAgC,EAAAmB,cAAA,CAAAZ,GAAG,CAACd,QAAQ,UAAA0B,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcd,IAAI,UAAAe,mBAAA,iBAAlBA,mBAAA,CAAoBhD,KAAK,GAAImC,GAAG,CAACrC,OAAO,CAAE,CAAC,CACrF,CAAC,OAAS,CACRH,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAsD,uBAAuB,CAAGA,CAAA,GAAM,CACpCxD,wBAAwB,CAAC8C,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEjE,IAAI,CAAE,CAAC,EAAG,CAAC,CACxD0C,eAAe,CAAC,CAAC,CAAEpB,qBAAqB,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAsD,2BAA2B,CAAIR,OAAe,EAAK,CACvDjD,wBAAwB,CAAC8C,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEjE,IAAI,CAAEoE,OAAO,EAAG,CAAC,CAC9D1B,eAAe,CAAC0B,OAAO,CAAE9C,qBAAqB,CAAC,CACjD,CAAC,CAED;AACA,KAAM,CAAAuD,gBAAgB,CAAG,KAAO,CAAAC,EAAU,EAAK,CAC7C,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,CAAE,OAEnE,GAAI,CACF,KAAM,CAAApG,iBAAiB,CAACqG,MAAM,CAACH,EAAE,CAAC,CAClCrD,UAAU,CAAC,2BAA2B,CAAC,CACvCe,SAAS,CAAC1C,eAAe,CAACE,IAAI,CAAEM,eAAe,CAAC,CAClD,CAAE,MAAOuD,GAAQ,CAAE,KAAAqB,cAAA,CAAAC,mBAAA,CACjBxD,QAAQ,2BAAA2B,MAAA,CAA2B,EAAA4B,cAAA,CAAArB,GAAG,CAACd,QAAQ,UAAAmC,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcvB,IAAI,UAAAwB,mBAAA,iBAAlBA,mBAAA,CAAoBzD,KAAK,GAAImC,GAAG,CAACrC,OAAO,CAAE,CAAC,CAChF,CACF,CAAC,CAED,KAAM,CAAA4D,gBAAgB,CAAG,KAAO,CAAAN,EAAU,EAAK,CAC7C,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,CAAE,OAEnE,GAAI,CACF,KAAM,CAAAnG,iBAAiB,CAACoG,MAAM,CAACH,EAAE,CAAC,CAClCrD,UAAU,CAAC,2BAA2B,CAAC,CACvCgB,SAAS,CAAC/B,eAAe,CAACV,IAAI,CAAEc,eAAe,CAAC,CAClD,CAAE,MAAO+C,GAAQ,CAAE,KAAAwB,cAAA,CAAAC,mBAAA,CACjB3D,QAAQ,2BAAA2B,MAAA,CAA2B,EAAA+B,cAAA,CAAAxB,GAAG,CAACd,QAAQ,UAAAsC,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc1B,IAAI,UAAA2B,mBAAA,iBAAlBA,mBAAA,CAAoB5D,KAAK,GAAImC,GAAG,CAACrC,OAAO,CAAE,CAAC,CAChF,CACF,CAAC,CAED,KAAM,CAAA+D,sBAAsB,CAAG,KAAO,CAAAT,EAAU,EAAK,CACnD,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,CAAE,OAEzE,GAAI,CACF,KAAM,CAAAlG,uBAAuB,CAACmG,MAAM,CAACH,EAAE,CAAC,CACxCrD,UAAU,CAAC,iCAAiC,CAAC,CAC7CiB,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,CAAEsB,qBAAqB,CAAC,CACpE,CAAE,MAAOuC,GAAQ,CAAE,KAAA2B,cAAA,CAAAC,mBAAA,CACjB9D,QAAQ,iCAAA2B,MAAA,CAAiC,EAAAkC,cAAA,CAAA3B,GAAG,CAACd,QAAQ,UAAAyC,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc7B,IAAI,UAAA8B,mBAAA,iBAAlBA,mBAAA,CAAoB/D,KAAK,GAAImC,GAAG,CAACrC,OAAO,CAAE,CAAC,CACtF,CACF,CAAC,CAED;AACA,KAAM,CAAAkE,gBAAgB,CAAGA,CAAC9B,UAA0B,CAAE+B,YAAoC,GAAK,CAC7F,KAAM,CAAAC,KAAK,CAAG,EAAE,CAChB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAIjC,UAAU,CAACzD,WAAW,CAAE0F,CAAC,EAAE,CAAE,CAChDD,KAAK,CAACE,IAAI,cACR1G,IAAA,WAEE2G,OAAO,CAAEA,CAAA,GAAMJ,YAAY,CAACE,CAAC,CAAE,CAC/BG,SAAS,2BAAA1C,MAAA,CAA4BuC,CAAC,GAAKjC,UAAU,CAAC5D,IAAI,CACtD,wBAAwB,CACxB,6CAA6C,CAC5C,CAAAiG,QAAA,CAEJJ,CAAC,EAPGA,CAQC,CACV,CAAC,CACH,CAEA,mBACEvG,KAAA,QAAK0G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD3G,KAAA,QAAK0G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,UAC7B,CAAE,CAACrC,UAAU,CAAC5D,IAAI,CAAG,CAAC,EAAI4D,UAAU,CAAC3D,SAAS,CAAI,CAAC,CAAC,KAAG,CAAC,GAAG,CAClEiG,IAAI,CAACC,GAAG,CAACvC,UAAU,CAAC5D,IAAI,CAAG4D,UAAU,CAAC3D,SAAS,CAAE2D,UAAU,CAAC1D,WAAW,CAAC,CAAC,KAAG,CAAC,GAAG,CAChF0D,UAAU,CAAC1D,WAAW,CAAC,UAC1B,EAAK,CAAC,cACNZ,KAAA,QAAK0G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMJ,YAAY,CAAC/B,UAAU,CAAC5D,IAAI,CAAG,CAAC,CAAE,CACjDoG,QAAQ,CAAExC,UAAU,CAAC5D,IAAI,EAAI,CAAE,CAC/BgG,SAAS,CAAC,wFAAwF,CAAAC,QAAA,CACnG,UAED,CAAQ,CAAC,CACRL,KAAK,cACNxG,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMJ,YAAY,CAAC/B,UAAU,CAAC5D,IAAI,CAAG,CAAC,CAAE,CACjDoG,QAAQ,CAAExC,UAAU,CAAC5D,IAAI,EAAI4D,UAAU,CAACzD,WAAY,CACpD6F,SAAS,CAAC,wFAAwF,CAAAC,QAAA,CACnG,MAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,mBACE3G,KAAA,QAAK0G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C7G,IAAA,OAAI4G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,4BAA0B,CAAI,CAAC,CAGtEzE,OAAO,eACNpC,IAAA,QAAK4G,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACxFzE,OAAO,CACL,CACN,CACAE,KAAK,eACJtC,IAAA,QAAK4G,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAClFvE,KAAK,CACH,CACN,cAGDtC,IAAA,QAAK4G,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5C7G,IAAA,QAAK4G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnC,CACC,CAAEI,GAAG,CAAE,OAAO,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAChC,CAAED,GAAG,CAAE,OAAO,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAChC,CAAED,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAE,aAAc,CAAC,CAC5C,CAAED,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAE,aAAc,CAAC,CAC7C,CAACC,GAAG,CAAEC,GAAG,eACRpH,IAAA,WAEE2G,OAAO,CAAEA,CAAA,GAAMpG,YAAY,CAAC6G,GAAG,CAACH,GAAU,CAAE,CAC5CL,SAAS,6CAAA1C,MAAA,CAA8C5D,SAAS,GAAK8G,GAAG,CAACH,GAAG,CACxE,+BAA+B,CAC/B,4EAA4E,CAC3E,CAAAJ,QAAA,CAEJO,GAAG,CAACF,KAAK,EAPLE,GAAG,CAACH,GAQH,CACT,CAAC,CACC,CAAC,CACH,CAAC,CAGL3G,SAAS,GAAK,OAAO,eACpBJ,KAAA,QAAA2G,QAAA,eAEE3G,KAAA,QAAK0G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD7G,IAAA,OAAI4G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC1D7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACb5D,cAAc,CAAC,IAAI,CAAC,CACpBN,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFmE,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CACjF,UAED,CAAQ,CAAC,EACN,CAAC,cAGN3G,KAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B7G,IAAA,UACEqH,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,sCAAsC,CAClDrD,KAAK,CAAE/C,eAAgB,CACvBqG,QAAQ,CAAGC,CAAC,EAAKrG,kBAAkB,CAACqG,CAAC,CAACC,MAAM,CAACxD,KAAK,CAAE,CACpD2C,SAAS,CAAC,wGAAwG,CAClHc,UAAU,CAAGF,CAAC,EAAKA,CAAC,CAACP,GAAG,GAAK,OAAO,EAAIrC,iBAAiB,CAAC,CAAE,CAC7D,CAAC,cACF5E,IAAA,WACE2G,OAAO,CAAE/B,iBAAkB,CAC3BgC,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CACjF,QAED,CAAQ,CAAC,cACT7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACbxF,kBAAkB,CAAC,EAAE,CAAC,CACtBiC,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAClB,CAAE,CACFwD,SAAS,CAAC,yEAAyE,CAAAC,QAAA,CACpF,OAED,CAAQ,CAAC,EACN,CAAC,CAGL7F,YAAY,cACXhB,IAAA,QAAK4G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,cAExD3G,KAAA,CAAAE,SAAA,EAAAyG,QAAA,eACE7G,IAAA,QAAK4G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3G,KAAA,UAAO0G,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAC3D7G,IAAA,UAAO4G,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B3G,KAAA,OAAA2G,QAAA,eACE7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,IAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,UAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR7G,IAAA,UAAO4G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDrG,KAAK,CAAC2G,GAAG,CAAEQ,IAAI,eACdzH,KAAA,OAAA2G,QAAA,eACE7G,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9Dc,IAAI,CAACjC,EAAE,CACN,CAAC,cACL1F,IAAA,OAAI4G,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1Ec,IAAI,CAACC,QAAQ,CACZ,CAAC,cACL5H,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9Dc,IAAI,CAACE,KAAK,CACT,CAAC,cACL7H,IAAA,OAAI4G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC7G,IAAA,SAAM4G,SAAS,6DAAA1C,MAAA,CAA8DyD,IAAI,CAACG,SAAS,CACvF,6BAA6B,CAC7B,yBAAyB,CACxB,CAAAjB,QAAA,CACFc,IAAI,CAACG,SAAS,CAAG,QAAQ,CAAG,UAAU,CACnC,CAAC,CACL,CAAC,cACL9H,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9Dc,IAAI,CAACI,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACL,IAAI,CAACI,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAAG,KAAK,CACvE,CAAC,cACL/H,KAAA,OAAI0G,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC7D7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACb5D,cAAc,CAAC4E,IAAI,CAAC,CACpBlF,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFmE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CACnD,MAED,CAAQ,CAAC,cACT7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMlB,gBAAgB,CAACkC,IAAI,CAACjC,EAAE,CAAE,CACzCkB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC5C,QAED,CAAQ,CAAC,EACP,CAAC,GArCEc,IAAI,CAACjC,EAsCV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAGLY,gBAAgB,CAAC5F,eAAe,CAAEqE,qBAAqB,CAAC,EACzD,CACH,EACE,CACN,CAGAzE,SAAS,GAAK,OAAO,eACpBJ,KAAA,QAAA2G,QAAA,eAEE3G,KAAA,QAAK0G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD7G,IAAA,OAAI4G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC1D7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACb1D,cAAc,CAAC,IAAI,CAAC,CACpBN,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFiE,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CACjF,UAED,CAAQ,CAAC,EACN,CAAC,cAGN3G,KAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B7G,IAAA,UACEqH,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,yBAAyB,CACrCrD,KAAK,CAAEvC,eAAgB,CACvB6F,QAAQ,CAAGC,CAAC,EAAK7F,kBAAkB,CAAC6F,CAAC,CAACC,MAAM,CAACxD,KAAK,CAAE,CACpD2C,SAAS,CAAC,wGAAwG,CAClHc,UAAU,CAAGF,CAAC,EAAKA,CAAC,CAACP,GAAG,GAAK,OAAO,EAAI9B,iBAAiB,CAAC,CAAE,CAC7D,CAAC,cACFnF,IAAA,WACE2G,OAAO,CAAExB,iBAAkB,CAC3ByB,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CACjF,QAED,CAAQ,CAAC,cACT7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACbhF,kBAAkB,CAAC,EAAE,CAAC,CACtB0B,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAClB,CAAE,CACFuD,SAAS,CAAC,yEAAyE,CAAAC,QAAA,CACpF,OAED,CAAQ,CAAC,EACN,CAAC,CAGLrF,YAAY,cACXxB,IAAA,QAAK4G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,cAExD3G,KAAA,CAAAE,SAAA,EAAAyG,QAAA,eACE7G,IAAA,QAAK4G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3G,KAAA,UAAO0G,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAC3D7G,IAAA,UAAO4G,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B3G,KAAA,OAAA2G,QAAA,eACE7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,IAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,aAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR7G,IAAA,UAAO4G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDzF,KAAK,CAAC+F,GAAG,CAAEe,IAAI,eACdhI,KAAA,OAAA2G,QAAA,eACE7G,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DqB,IAAI,CAACxC,EAAE,CACN,CAAC,cACL1F,IAAA,OAAI4G,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1EqB,IAAI,CAACC,IAAI,CACR,CAAC,cACLnI,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DqB,IAAI,CAACE,WAAW,EAAI,gBAAgB,CACnC,CAAC,cACLpI,IAAA,OAAI4G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC7G,IAAA,SAAM4G,SAAS,6DAAA1C,MAAA,CAA8DgE,IAAI,CAACJ,SAAS,CACvF,6BAA6B,CAC7B,yBAAyB,CACxB,CAAAjB,QAAA,CACFqB,IAAI,CAACJ,SAAS,CAAG,QAAQ,CAAG,UAAU,CACnC,CAAC,CACL,CAAC,cACL9H,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DqB,IAAI,CAACH,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACE,IAAI,CAACH,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAAG,KAAK,CACvE,CAAC,cACL/H,KAAA,OAAI0G,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC7D7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACb1D,cAAc,CAACiF,IAAI,CAAC,CACpBvF,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFiE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CACnD,MAED,CAAQ,CAAC,cACT7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMX,gBAAgB,CAACkC,IAAI,CAACxC,EAAE,CAAE,CACzCkB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC5C,QAED,CAAQ,CAAC,EACP,CAAC,GArCEqB,IAAI,CAACxC,EAsCV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAGLY,gBAAgB,CAAChF,eAAe,CAAE8D,qBAAqB,CAAC,EACzD,CACH,EACE,CACN,CAGA9E,SAAS,GAAK,aAAa,eAC1BJ,KAAA,QAAA2G,QAAA,eAEE3G,KAAA,QAAK0G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD7G,IAAA,OAAI4G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAChE7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACbxD,oBAAoB,CAAC,IAAI,CAAC,CAC1BN,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAE,CACF+D,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CACjF,gBAED,CAAQ,CAAC,EACN,CAAC,cAGN3G,KAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B7G,IAAA,UACEqH,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,+BAA+B,CAC3CrD,KAAK,CAAE/B,qBAAsB,CAC7BqF,QAAQ,CAAGC,CAAC,EAAKrF,wBAAwB,CAACqF,CAAC,CAACC,MAAM,CAACxD,KAAK,CAAE,CAC1D2C,SAAS,CAAC,wGAAwG,CAClHc,UAAU,CAAGF,CAAC,EAAKA,CAAC,CAACP,GAAG,GAAK,OAAO,EAAI1B,uBAAuB,CAAC,CAAE,CACnE,CAAC,cACFvF,IAAA,WACE2G,OAAO,CAAEpB,uBAAwB,CACjCqB,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CACjF,QAED,CAAQ,CAAC,cACT7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACbxE,wBAAwB,CAAC,EAAE,CAAC,CAC5BmB,eAAe,CAAC,CAAC,CAAE,EAAE,CAAC,CACxB,CAAE,CACFsD,SAAS,CAAC,yEAAyE,CAAAC,QAAA,CACpF,OAED,CAAQ,CAAC,EACN,CAAC,CAGL7E,kBAAkB,cACjBhC,IAAA,QAAK4G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,wBAAsB,CAAK,CAAC,cAE9D3G,KAAA,CAAAE,SAAA,EAAAyG,QAAA,eACE7G,IAAA,QAAK4G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3G,KAAA,UAAO0G,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAC3D7G,IAAA,UAAO4G,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B3G,KAAA,OAAA2G,QAAA,eACE7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,IAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,aAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,cACL7G,IAAA,OAAI4G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR7G,IAAA,UAAO4G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDjF,WAAW,CAACuF,GAAG,CAAEkB,UAAU,eAC1BnI,KAAA,OAAA2G,QAAA,eACE7G,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DwB,UAAU,CAAC3C,EAAE,CACZ,CAAC,cACL1F,IAAA,OAAI4G,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1EwB,UAAU,CAACF,IAAI,CACd,CAAC,cACLnI,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DwB,UAAU,CAACD,WAAW,EAAI,gBAAgB,CACzC,CAAC,cACLpI,IAAA,OAAI4G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC7G,IAAA,SAAM4G,SAAS,6DAAA1C,MAAA,CAA8DmE,UAAU,CAACP,SAAS,CAC7F,6BAA6B,CAC7B,yBAAyB,CACxB,CAAAjB,QAAA,CACFwB,UAAU,CAACP,SAAS,CAAG,QAAQ,CAAG,UAAU,CACzC,CAAC,CACL,CAAC,cACL9H,IAAA,OAAI4G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DwB,UAAU,CAACN,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACK,UAAU,CAACN,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAAG,KAAK,CACnF,CAAC,cACL/H,KAAA,OAAI0G,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC7D7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACbxD,oBAAoB,CAACkF,UAAU,CAAC,CAChCxF,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAE,CACF+D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CACnD,MAED,CAAQ,CAAC,cACT7G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMR,sBAAsB,CAACkC,UAAU,CAAC3C,EAAE,CAAE,CACrDkB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC5C,QAED,CAAQ,CAAC,EACP,CAAC,GArCEwB,UAAU,CAAC3C,EAsChB,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAGLY,gBAAgB,CAACxE,qBAAqB,CAAE0D,2BAA2B,CAAC,EACrE,CACH,EACE,CACN,CAGAlF,SAAS,GAAK,aAAa,eAC1BN,IAAA,CAACF,cAAc,EACbU,KAAK,CAAEA,KAAM,CACbY,KAAK,CAAEA,KAAM,CACbkH,SAAS,CAAEjG,UAAW,CACtBkG,OAAO,CAAEhG,QAAS,CACnB,CACF,cAGDvC,IAAA,CAACL,SAAS,EACR6I,MAAM,CAAEhG,aAAc,CACtBiG,OAAO,CAAEA,CAAA,GAAMhG,gBAAgB,CAAC,KAAK,CAAE,CACvCkF,IAAI,CAAE7E,WAAY,CAClB4F,SAAS,CAAEA,CAAA,GAAM,CACfrG,UAAU,CAACS,WAAW,CAAG,2BAA2B,CAAG,2BAA2B,CAAC,CACnFM,SAAS,CAAC1C,eAAe,CAACE,IAAI,CAAEM,eAAe,CAAC,CAClD,CAAE,CACH,CAAC,cAEFlB,IAAA,CAACJ,SAAS,EACR4I,MAAM,CAAE9F,aAAc,CACtB+F,OAAO,CAAEA,CAAA,GAAM9F,gBAAgB,CAAC,KAAK,CAAE,CACvCuF,IAAI,CAAElF,WAAY,CAClB0F,SAAS,CAAEA,CAAA,GAAM,CACfrG,UAAU,CAACW,WAAW,CAAG,2BAA2B,CAAG,2BAA2B,CAAC,CACnFK,SAAS,CAAC/B,eAAe,CAACV,IAAI,CAAEc,eAAe,CAAC,CAClD,CAAE,CACH,CAAC,cAEF1B,IAAA,CAACH,eAAe,EACd2I,MAAM,CAAE5F,mBAAoB,CAC5B6F,OAAO,CAAEA,CAAA,GAAM5F,sBAAsB,CAAC,KAAK,CAAE,CAC7CwF,UAAU,CAAEnF,iBAAkB,CAC9BwF,SAAS,CAAEA,CAAA,GAAM,CACfrG,UAAU,CAACa,iBAAiB,CAAG,iCAAiC,CAAG,iCAAiC,CAAC,CACrGI,eAAe,CAACxB,qBAAqB,CAAClB,IAAI,CAAEsB,qBAAqB,CAAC,CACpE,CAAE,CACH,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7B,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}