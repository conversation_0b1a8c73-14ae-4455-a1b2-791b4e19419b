#!/bin/bash

# Base CRUD Integration Test Script
echo "🚀 Base CRUD System Integration Test"
echo "===================================="

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed or not in PATH"
    exit 1
fi

# Check if config.env exists
if [ ! -f "config.env" ]; then
    echo "⚠️  config.env not found. Creating a sample one..."
    cat > config.env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=jwt_auth_db
DB_SSLMODE=disable

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRY=24h

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Server Configuration
PORT=8080
GIN_MODE=debug

# Log Configuration
LOG_LEVEL=debug
EOF
    echo "📝 Sample config.env created. Please update with your actual database credentials."
    echo "   Make sure your PostgreSQL database is running and accessible."
fi

echo ""
echo "📋 Prerequisites Check:"
echo "  ✅ Go installed: $(go version)"

# Check if database is accessible (optional)
if command -v psql &> /dev/null; then
    echo "  ✅ PostgreSQL client available"
else
    echo "  ⚠️  PostgreSQL client not found (optional)"
fi

echo ""
echo "🔧 Building and running integration test..."

# Run the integration test
go run test_crud_integration.go test-crud

# Check the exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Integration test completed successfully!"
    echo ""
    echo "📚 Next Steps:"
    echo "  1. Review the test output above"
    echo "  2. Check the documentation in docs/base_crud_guide.md"
    echo "  3. See usage examples in examples/user_crud_example.go"
    echo "  4. Integrate CRUD routes into your main application"
    echo ""
    echo "🚀 To use the base CRUD system in your application:"
    echo "  // Create handler"
    echo "  userHandler := handlers.NewBaseCRUDHandler(&models.User{})"
    echo "  "
    echo "  // Setup routes"
    echo "  v1 := r.Group(\"/api/v1\")"
    echo "  userHandler.SetupCRUDRoutes(v1, \"/users\")"
else
    echo ""
    echo "❌ Integration test failed!"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "  1. Check that your database is running and accessible"
    echo "  2. Verify config.env has correct database credentials"
    echo "  3. Ensure all Go dependencies are installed: go mod tidy"
    echo "  4. Check the error messages above for specific issues"
fi
