# Simple Role Management API Test
Write-Host "Testing Role Management API" -ForegroundColor Cyan

$BASE_URL = "http://localhost:8080"

# Test 1: Register admin user
Write-Host "1. Registering admin user..." -ForegroundColor Yellow
$registerBody = '{"username":"admin","email":"<EMAIL>","password":"admin123"}'
try {
    $null = Invoke-RestMethod -Uri "$BASE_URL/api/v1/auth/register" -Method POST -Body $registerBody -ContentType "application/json" -ErrorAction SilentlyContinue
    Write-Host "Admin user registered (or already exists)" -ForegroundColor Green
}
catch {
    Write-Host "Registration response: $($_.Exception.Message)" -ForegroundColor Gray
}

# Test 2: Login to get token
Write-Host "2. Getting admin token..." -ForegroundColor Yellow
$loginBody = '{"email":"<EMAIL>","password":"admin123"}'
try {
    $loginResponse = Invoke-RestMethod -Uri "$BASE_URL/api/v1/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.token
    Write-Host "Token obtained successfully" -ForegroundColor Green
}
catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Create role
Write-Host "3. Creating test role..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type"  = "application/json"
}
$createBody = '{"name":"test_manager","description":"Test manager role","is_active":true}'
try {
    $createResponse = Invoke-RestMethod -Uri "$BASE_URL/api/v1/admin/role-management" -Method POST -Body $createBody -Headers $headers
    Write-Host "Role created: $($createResponse.data.name)" -ForegroundColor Green
}
catch {
    Write-Host "Create role failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: List roles
Write-Host "4. Listing roles..." -ForegroundColor Yellow
try {
    $listResponse = Invoke-RestMethod -Uri "$BASE_URL/api/v1/admin/role-management?page=1&page_size=10" -Method GET -Headers @{"Authorization" = "Bearer $token" }
    Write-Host "Found $($listResponse.pagination.total) roles" -ForegroundColor Green
    foreach ($role in $listResponse.data) {
        Write-Host "  - ID: $($role.id), Name: $($role.name), Active: $($role.is_active)" -ForegroundColor Gray
    }
}
catch {
    Write-Host "List roles failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Search roles
Write-Host "5. Searching for test roles..." -ForegroundColor Yellow
$searchUri = [System.Uri]::EscapeUriString("$BASE_URL/api/v1/admin/role-management?page=1&page_size=10&search_filters[0][field]=name&search_filters[0][operator]=like&search_filters[0][value]=test")
try {
    $searchResponse = Invoke-RestMethod -Uri $searchUri -Method GET -Headers @{"Authorization" = "Bearer $token" }
    Write-Host "Search found $($searchResponse.pagination.total) roles" -ForegroundColor Green
}
catch {
    Write-Host "Search failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed!" -ForegroundColor Green
