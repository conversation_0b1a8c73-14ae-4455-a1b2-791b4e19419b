// @title JWT Auth API
// @version 1.0
// @description API with JWT authentication and RBAC
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Nhập token dạng: Bearer <token>
package main

import (
	"context"
	"os"

	"jwt-auth-backend/database"
	"jwt-auth-backend/middleware"
	"jwt-auth-backend/routes"
	"jwt-auth-backend/utils"
	"github.com/joho/godotenv"
	"github.com/swaggo/gin-swagger"
	"github.com/swaggo/files"
	_ "jwt-auth-backend/docs"
	"go.uber.org/zap"
)

func main() {
	// Load environment variables
	if err := godotenv.Load("config.env"); err != nil {
		// Use default logger for this message since Zap isn't initialized yet
		println("No config.env file found, using default values")
	}

	// Initialize Zap logger
	utils.InitLogger()
	logger := utils.GetLogger()
	defer logger.Sync()

	logger.Info("Starting JWT Auth Backend")

	// Initialize JWT
	utils.InitJWT()
	logger.Info("JWT initialized")

	// Initialize database
	database.InitDB()
	logger.Info("Database initialized")

	// Initialize Redis
	utils.InitRedis()
	if err := utils.PingRedis(context.Background()); err != nil {
		logger.Fatal("Failed to connect to Redis", zap.Error(err))
	}
	logger.Info("Redis connected")

	// Setup routes
	r := routes.SetupRoutes()

	// Add global exception middleware
	r.Use(middleware.GlobalExceptionMiddleware())

	// Add logging middleware
	r.Use(middleware.RequestLogger())

	// Swagger UI route
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Get port from environment
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	logger.Info("Server starting", zap.String("port", port))
	logger.Fatal("Server failed to start", zap.Error(r.Run(":" + port)))
} 