{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/Build-Project/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{roleService,permissionService}from'../../services';import RolePermissionModal from'./RolePermissionModal';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const RoleManagement=()=>{// State management\nconst[roles,setRoles]=useState([]);const[permissions,setPermissions]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[success,setSuccess]=useState(null);// Pagination\nconst[pagination,setPagination]=useState({page:1,page_size:10,total_items:0,total_pages:0,has_next:false,has_prev:false});// Search and filters\nconst[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('all');// Modal states\nconst[showCreateModal,setShowCreateModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[showPermissionModal,setShowPermissionModal]=useState(false);const[selectedRole,setSelectedRole]=useState(null);// Form data\nconst[formData,setFormData]=useState({name:'',description:'',is_active:true});// Load data on component mount\nuseEffect(()=>{loadRoles();loadPermissions();},[pagination.page,pagination.page_size,searchTerm,statusFilter]);// Load roles with pagination and search\nconst loadRoles=async()=>{try{setLoading(true);setError(null);const filters={page:pagination.page,page_size:pagination.page_size};if(searchTerm.trim()){filters.search_term=searchTerm.trim();filters.search_columns=['name','description'];}if(statusFilter!=='all'){filters.is_active=statusFilter==='active';}const response=await roleService.list(filters);setRoles(response.data.data);const paginationData=response.data.pagination;setPagination(_objectSpread(_objectSpread({},paginationData),{},{has_next:paginationData.page<paginationData.total_pages,has_prev:paginationData.page>1}));}catch(err){setError(err.message||'Failed to load roles');console.error('Error loading roles:',err);}finally{setLoading(false);}};// Load all permissions for assignment\nconst loadPermissions=async()=>{try{const response=await permissionService.list({page:1,page_size:100});setPermissions(response.data.data);}catch(err){console.error('Error loading permissions:',err);}};// Handle create role\nconst handleCreate=async e=>{e.preventDefault();try{setLoading(true);setError(null);await roleService.create(formData);setSuccess('Role created successfully');setShowCreateModal(false);resetForm();loadRoles();}catch(err){setError(err.message||'Failed to create role');}finally{setLoading(false);}};// Handle update role\nconst handleUpdate=async e=>{e.preventDefault();if(!selectedRole)return;try{setLoading(true);setError(null);await roleService.update(selectedRole.id,formData);setSuccess('Role updated successfully');setShowEditModal(false);resetForm();loadRoles();}catch(err){setError(err.message||'Failed to update role');}finally{setLoading(false);}};// Handle delete role\nconst handleDelete=async role=>{if(!window.confirm(\"Are you sure you want to delete role \\\"\".concat(role.name,\"\\\"?\"))){return;}try{setLoading(true);setError(null);await roleService.delete(role.id);setSuccess('Role deleted successfully');loadRoles();}catch(err){setError(err.message||'Failed to delete role');}finally{setLoading(false);}};// Handle search\nconst handleSearch=e=>{e.preventDefault();setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:1}));loadRoles();};// Reset form\nconst resetForm=()=>{setFormData({name:'',description:'',is_active:true});setSelectedRole(null);};// Open edit modal\nconst openEditModal=role=>{setSelectedRole(role);setFormData({name:role.name,description:role.description||'',is_active:role.is_active});setShowEditModal(true);};// Open permission modal\nconst openPermissionModal=role=>{setSelectedRole(role);setShowPermissionModal(true);};// Clear messages\nconst clearMessages=()=>{setError(null);setSuccess(null);};return/*#__PURE__*/_jsxs(\"div\",{className:\"container mt-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"row\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"col-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-4\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Role Management\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn btn-primary\",onClick:()=>setShowCreateModal(true),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-plus me-2\"}),\"Create Role\"]})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-danger alert-dismissible fade show\",role:\"alert\",children:[error,/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-close\",onClick:clearMessages})]}),success&&/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-success alert-dismissible fade show\",role:\"alert\",children:[success,/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-close\",onClick:clearMessages})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card mb-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"form\",{onSubmit:handleSearch,children:/*#__PURE__*/_jsxs(\"div\",{className:\"row g-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-md-6\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"search\",className:\"form-label\",children:\"Search Roles\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"form-control\",id:\"search\",placeholder:\"Search by name or description...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-md-3\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"status\",className:\"form-label\",children:\"Status\"}),/*#__PURE__*/_jsxs(\"select\",{className:\"form-select\",id:\"status\",value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"active\",children:\"Active\"}),/*#__PURE__*/_jsx(\"option\",{value:\"inactive\",children:\"Inactive\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-md-3 d-flex align-items-end\",children:[/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"btn btn-outline-primary me-2\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-search me-1\"}),\"Search\"]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn btn-outline-secondary\",onClick:()=>{setSearchTerm('');setStatusFilter('all');setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:1}));},children:\"Clear\"})]})]})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"spinner-border\",role:\"status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})})}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-hover\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"table-light\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Description\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Created\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:roles.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:6,className:\"text-center py-4\",children:\"No roles found\"})}):roles.map(role=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:role.id}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:role.name})}),/*#__PURE__*/_jsx(\"td\",{children:role.description||'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"badge \".concat(role.is_active?'bg-success':'bg-secondary'),children:role.is_active?'Active':'Inactive'})}),/*#__PURE__*/_jsx(\"td\",{children:role.created_at?new Date(role.created_at).toLocaleDateString():'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"btn-group btn-group-sm\",role:\"group\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-outline-primary\",onClick:()=>openEditModal(role),title:\"Edit Role\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-edit\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-outline-info\",onClick:()=>openPermissionModal(role),title:\"Manage Permissions\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-key\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-outline-danger\",onClick:()=>handleDelete(role),title:\"Delete Role\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-trash\"})})]})})]},role.id))})]})}),pagination.total_pages>1&&/*#__PURE__*/_jsx(\"nav\",{\"aria-label\":\"Roles pagination\",children:/*#__PURE__*/_jsxs(\"ul\",{className:\"pagination justify-content-center mt-3\",children:[/*#__PURE__*/_jsx(\"li\",{className:\"page-item \".concat(!pagination.has_prev?'disabled':''),children:/*#__PURE__*/_jsx(\"button\",{className:\"page-link\",onClick:()=>setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:prev.page-1})),disabled:!pagination.has_prev,children:\"Previous\"})}),Array.from({length:pagination.total_pages},(_,i)=>i+1).map(page=>/*#__PURE__*/_jsx(\"li\",{className:\"page-item \".concat(pagination.page===page?'active':''),children:/*#__PURE__*/_jsx(\"button\",{className:\"page-link\",onClick:()=>setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page})),children:page})},page)),/*#__PURE__*/_jsx(\"li\",{className:\"page-item \".concat(!pagination.has_next?'disabled':''),children:/*#__PURE__*/_jsx(\"button\",{className:\"page-link\",onClick:()=>setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{page:prev.page+1})),disabled:!pagination.has_next,children:\"Next\"})})]})})]})})})]})}),showCreateModal&&/*#__PURE__*/_jsx(\"div\",{className:\"modal fade show\",style:{display:'block'},tabIndex:-1,children:/*#__PURE__*/_jsx(\"div\",{className:\"modal-dialog\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"modal-title\",children:\"Create New Role\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-close\",onClick:()=>{setShowCreateModal(false);resetForm();}})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleCreate,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"createName\",className:\"form-label\",children:\"Role Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"form-control\",id:\"createName\",value:formData.name,onChange:e=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{name:e.target.value})),required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"createDescription\",className:\"form-label\",children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{className:\"form-control\",id:\"createDescription\",rows:3,value:formData.description,onChange:e=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{description:e.target.value}))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3 form-check\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"form-check-input\",id:\"createActive\",checked:formData.is_active,onChange:e=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{is_active:e.target.checked}))}),/*#__PURE__*/_jsx(\"label\",{className:\"form-check-label\",htmlFor:\"createActive\",children:\"Active\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-footer\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn btn-secondary\",onClick:()=>{setShowCreateModal(false);resetForm();},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading,children:loading?'Creating...':'Create Role'})]})]})]})})}),showEditModal&&selectedRole&&/*#__PURE__*/_jsx(\"div\",{className:\"modal fade show\",style:{display:'block'},tabIndex:-1,children:/*#__PURE__*/_jsx(\"div\",{className:\"modal-dialog\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"modal-title\",children:[\"Edit Role: \",selectedRole.name]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-close\",onClick:()=>{setShowEditModal(false);resetForm();}})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleUpdate,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"editName\",className:\"form-label\",children:\"Role Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"form-control\",id:\"editName\",value:formData.name,onChange:e=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{name:e.target.value})),required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"editDescription\",className:\"form-label\",children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{className:\"form-control\",id:\"editDescription\",rows:3,value:formData.description,onChange:e=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{description:e.target.value}))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3 form-check\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"form-check-input\",id:\"editActive\",checked:formData.is_active,onChange:e=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{is_active:e.target.checked}))}),/*#__PURE__*/_jsx(\"label\",{className:\"form-check-label\",htmlFor:\"editActive\",children:\"Active\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-footer\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn btn-secondary\",onClick:()=>{setShowEditModal(false);resetForm();},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading,children:loading?'Updating...':'Update Role'})]})]})]})})}),selectedRole&&/*#__PURE__*/_jsx(RolePermissionModal,{role:selectedRole,isOpen:showPermissionModal,onClose:()=>{setShowPermissionModal(false);setSelectedRole(null);},onSuccess:message=>{setSuccess(message);setTimeout(()=>setSuccess(null),5000);},onError:message=>{setError(message);setTimeout(()=>setError(null),5000);}})]});};export default RoleManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "roleService", "permissionService", "RolePermissionModal", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "RoleManagement", "roles", "setRoles", "permissions", "setPermissions", "loading", "setLoading", "error", "setError", "success", "setSuccess", "pagination", "setPagination", "page", "page_size", "total_items", "total_pages", "has_next", "has_prev", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showPermissionModal", "setShowPermissionModal", "selectedR<PERSON>", "setSelectedRole", "formData", "setFormData", "name", "description", "is_active", "loadRoles", "loadPermissions", "filters", "trim", "search_term", "search_columns", "response", "list", "data", "paginationData", "_objectSpread", "err", "message", "console", "handleCreate", "e", "preventDefault", "create", "resetForm", "handleUpdate", "update", "id", "handleDelete", "role", "window", "confirm", "concat", "delete", "handleSearch", "prev", "openEditModal", "openPermissionModal", "clearMessages", "className", "children", "onClick", "type", "onSubmit", "htmlFor", "placeholder", "value", "onChange", "target", "length", "colSpan", "map", "created_at", "Date", "toLocaleDateString", "title", "disabled", "Array", "from", "_", "i", "style", "display", "tabIndex", "required", "rows", "checked", "isOpen", "onClose", "onSuccess", "setTimeout", "onError"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/components/admin/RoleManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { roleService, permissionService } from '../../services';\nimport { Role, Permission, PaginationInfo } from '../../types';\nimport RolePermissionModal from './RolePermissionModal';\n\ninterface RoleManagementProps { }\n\nconst RoleManagement: React.FC<RoleManagementProps> = () => {\n  // State management\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Pagination\n  const [pagination, setPagination] = useState<PaginationInfo>({\n    page: 1,\n    page_size: 10,\n    total_items: 0,\n    total_pages: 0,\n    has_next: false,\n    has_prev: false\n  });\n\n  // Search and filters\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');\n\n  // Modal states\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showPermissionModal, setShowPermissionModal] = useState(false);\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\n\n  // Form data\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    is_active: true\n  });\n\n  // Load data on component mount\n  useEffect(() => {\n    loadRoles();\n    loadPermissions();\n  }, [pagination.page, pagination.page_size, searchTerm, statusFilter]);\n\n  // Load roles with pagination and search\n  const loadRoles = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const filters: any = {\n        page: pagination.page,\n        page_size: pagination.page_size\n      };\n\n      if (searchTerm.trim()) {\n        filters.search_term = searchTerm.trim();\n        filters.search_columns = ['name', 'description'];\n      }\n\n      if (statusFilter !== 'all') {\n        filters.is_active = statusFilter === 'active';\n      }\n\n      const response = await roleService.list(filters);\n      setRoles(response.data.data);\n      const paginationData = response.data.pagination;\n      setPagination({\n        ...paginationData,\n        has_next: paginationData.page < paginationData.total_pages,\n        has_prev: paginationData.page > 1\n      });\n    } catch (err: any) {\n      setError(err.message || 'Failed to load roles');\n      console.error('Error loading roles:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load all permissions for assignment\n  const loadPermissions = async () => {\n    try {\n      const response = await permissionService.list({ page: 1, page_size: 100 });\n      setPermissions(response.data.data);\n    } catch (err: any) {\n      console.error('Error loading permissions:', err);\n    }\n  };\n\n  // Handle create role\n  const handleCreate = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      await roleService.create(formData);\n      setSuccess('Role created successfully');\n      setShowCreateModal(false);\n      resetForm();\n      loadRoles();\n    } catch (err: any) {\n      setError(err.message || 'Failed to create role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle update role\n  const handleUpdate = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!selectedRole) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      await roleService.update(selectedRole.id, formData);\n      setSuccess('Role updated successfully');\n      setShowEditModal(false);\n      resetForm();\n      loadRoles();\n    } catch (err: any) {\n      setError(err.message || 'Failed to update role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle delete role\n  const handleDelete = async (role: Role) => {\n    if (!window.confirm(`Are you sure you want to delete role \"${role.name}\"?`)) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      await roleService.delete(role.id);\n      setSuccess('Role deleted successfully');\n      loadRoles();\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete role');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle search\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setPagination(prev => ({ ...prev, page: 1 }));\n    loadRoles();\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      is_active: true\n    });\n    setSelectedRole(null);\n  };\n\n  // Open edit modal\n  const openEditModal = (role: Role) => {\n    setSelectedRole(role);\n    setFormData({\n      name: role.name,\n      description: role.description || '',\n      is_active: role.is_active\n    });\n    setShowEditModal(true);\n  };\n\n  // Open permission modal\n  const openPermissionModal = (role: Role) => {\n    setSelectedRole(role);\n    setShowPermissionModal(true);\n  };\n\n  // Clear messages\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  return (\n    <div className=\"container mt-4\">\n      <div className=\"row\">\n        <div className=\"col-12\">\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\n            <h2>Role Management</h2>\n            <button\n              className=\"btn btn-primary\"\n              onClick={() => setShowCreateModal(true)}\n            >\n              <i className=\"fas fa-plus me-2\"></i>\n              Create Role\n            </button>\n          </div>\n\n          {/* Messages */}\n          {error && (\n            <div className=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n              {error}\n              <button type=\"button\" className=\"btn-close\" onClick={clearMessages}></button>\n            </div>\n          )}\n          {success && (\n            <div className=\"alert alert-success alert-dismissible fade show\" role=\"alert\">\n              {success}\n              <button type=\"button\" className=\"btn-close\" onClick={clearMessages}></button>\n            </div>\n          )}\n\n          {/* Search and Filters */}\n          <div className=\"card mb-4\">\n            <div className=\"card-body\">\n              <form onSubmit={handleSearch}>\n                <div className=\"row g-3\">\n                  <div className=\"col-md-6\">\n                    <label htmlFor=\"search\" className=\"form-label\">Search Roles</label>\n                    <input\n                      type=\"text\"\n                      className=\"form-control\"\n                      id=\"search\"\n                      placeholder=\"Search by name or description...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                  </div>\n                  <div className=\"col-md-3\">\n                    <label htmlFor=\"status\" className=\"form-label\">Status</label>\n                    <select\n                      className=\"form-select\"\n                      id=\"status\"\n                      value={statusFilter}\n                      onChange={(e) => setStatusFilter(e.target.value as any)}\n                    >\n                      <option value=\"all\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                    </select>\n                  </div>\n                  <div className=\"col-md-3 d-flex align-items-end\">\n                    <button type=\"submit\" className=\"btn btn-outline-primary me-2\">\n                      <i className=\"fas fa-search me-1\"></i>\n                      Search\n                    </button>\n                    <button\n                      type=\"button\"\n                      className=\"btn btn-outline-secondary\"\n                      onClick={() => {\n                        setSearchTerm('');\n                        setStatusFilter('all');\n                        setPagination(prev => ({ ...prev, page: 1 }));\n                      }}\n                    >\n                      Clear\n                    </button>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n\n          {/* Roles Table */}\n          <div className=\"card\">\n            <div className=\"card-body\">\n              {loading ? (\n                <div className=\"text-center py-4\">\n                  <div className=\"spinner-border\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading...</span>\n                  </div>\n                </div>\n              ) : (\n                <>\n                  <div className=\"table-responsive\">\n                    <table className=\"table table-hover\">\n                      <thead className=\"table-light\">\n                        <tr>\n                          <th>ID</th>\n                          <th>Name</th>\n                          <th>Description</th>\n                          <th>Status</th>\n                          <th>Created</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {roles.length === 0 ? (\n                          <tr>\n                            <td colSpan={6} className=\"text-center py-4\">\n                              No roles found\n                            </td>\n                          </tr>\n                        ) : (\n                          roles.map((role) => (\n                            <tr key={role.id}>\n                              <td>{role.id}</td>\n                              <td>\n                                <strong>{role.name}</strong>\n                              </td>\n                              <td>{role.description || '-'}</td>\n                              <td>\n                                <span className={`badge ${role.is_active ? 'bg-success' : 'bg-secondary'}`}>\n                                  {role.is_active ? 'Active' : 'Inactive'}\n                                </span>\n                              </td>\n                              <td>\n                                {role.created_at ? new Date(role.created_at).toLocaleDateString() : '-'}\n                              </td>\n                              <td>\n                                <div className=\"btn-group btn-group-sm\" role=\"group\">\n                                  <button\n                                    className=\"btn btn-outline-primary\"\n                                    onClick={() => openEditModal(role)}\n                                    title=\"Edit Role\"\n                                  >\n                                    <i className=\"fas fa-edit\"></i>\n                                  </button>\n                                  <button\n                                    className=\"btn btn-outline-info\"\n                                    onClick={() => openPermissionModal(role)}\n                                    title=\"Manage Permissions\"\n                                  >\n                                    <i className=\"fas fa-key\"></i>\n                                  </button>\n                                  <button\n                                    className=\"btn btn-outline-danger\"\n                                    onClick={() => handleDelete(role)}\n                                    title=\"Delete Role\"\n                                  >\n                                    <i className=\"fas fa-trash\"></i>\n                                  </button>\n                                </div>\n                              </td>\n                            </tr>\n                          ))\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n\n                  {/* Pagination */}\n                  {pagination.total_pages > 1 && (\n                    <nav aria-label=\"Roles pagination\">\n                      <ul className=\"pagination justify-content-center mt-3\">\n                        <li className={`page-item ${!pagination.has_prev ? 'disabled' : ''}`}>\n                          <button\n                            className=\"page-link\"\n                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}\n                            disabled={!pagination.has_prev}\n                          >\n                            Previous\n                          </button>\n                        </li>\n\n                        {Array.from({ length: pagination.total_pages }, (_, i) => i + 1).map((page) => (\n                          <li key={page} className={`page-item ${pagination.page === page ? 'active' : ''}`}>\n                            <button\n                              className=\"page-link\"\n                              onClick={() => setPagination(prev => ({ ...prev, page }))}\n                            >\n                              {page}\n                            </button>\n                          </li>\n                        ))}\n\n                        <li className={`page-item ${!pagination.has_next ? 'disabled' : ''}`}>\n                          <button\n                            className=\"page-link\"\n                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                            disabled={!pagination.has_next}\n                          >\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  )}\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Create Role Modal */}\n      {showCreateModal && (\n        <div className=\"modal fade show\" style={{ display: 'block' }} tabIndex={-1}>\n          <div className=\"modal-dialog\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h5 className=\"modal-title\">Create New Role</h5>\n                <button\n                  type=\"button\"\n                  className=\"btn-close\"\n                  onClick={() => {\n                    setShowCreateModal(false);\n                    resetForm();\n                  }}\n                ></button>\n              </div>\n              <form onSubmit={handleCreate}>\n                <div className=\"modal-body\">\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"createName\" className=\"form-label\">Role Name *</label>\n                    <input\n                      type=\"text\"\n                      className=\"form-control\"\n                      id=\"createName\"\n                      value={formData.name}\n                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                      required\n                    />\n                  </div>\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"createDescription\" className=\"form-label\">Description</label>\n                    <textarea\n                      className=\"form-control\"\n                      id=\"createDescription\"\n                      rows={3}\n                      value={formData.description}\n                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                    ></textarea>\n                  </div>\n                  <div className=\"mb-3 form-check\">\n                    <input\n                      type=\"checkbox\"\n                      className=\"form-check-input\"\n                      id=\"createActive\"\n                      checked={formData.is_active}\n                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"createActive\">\n                      Active\n                    </label>\n                  </div>\n                </div>\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-secondary\"\n                    onClick={() => {\n                      setShowCreateModal(false);\n                      resetForm();\n                    }}\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"btn btn-primary\" disabled={loading}>\n                    {loading ? 'Creating...' : 'Create Role'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Role Modal */}\n      {showEditModal && selectedRole && (\n        <div className=\"modal fade show\" style={{ display: 'block' }} tabIndex={-1}>\n          <div className=\"modal-dialog\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h5 className=\"modal-title\">Edit Role: {selectedRole.name}</h5>\n                <button\n                  type=\"button\"\n                  className=\"btn-close\"\n                  onClick={() => {\n                    setShowEditModal(false);\n                    resetForm();\n                  }}\n                ></button>\n              </div>\n              <form onSubmit={handleUpdate}>\n                <div className=\"modal-body\">\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"editName\" className=\"form-label\">Role Name *</label>\n                    <input\n                      type=\"text\"\n                      className=\"form-control\"\n                      id=\"editName\"\n                      value={formData.name}\n                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                      required\n                    />\n                  </div>\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"editDescription\" className=\"form-label\">Description</label>\n                    <textarea\n                      className=\"form-control\"\n                      id=\"editDescription\"\n                      rows={3}\n                      value={formData.description}\n                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                    ></textarea>\n                  </div>\n                  <div className=\"mb-3 form-check\">\n                    <input\n                      type=\"checkbox\"\n                      className=\"form-check-input\"\n                      id=\"editActive\"\n                      checked={formData.is_active}\n                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"editActive\">\n                      Active\n                    </label>\n                  </div>\n                </div>\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-secondary\"\n                    onClick={() => {\n                      setShowEditModal(false);\n                      resetForm();\n                    }}\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"btn btn-primary\" disabled={loading}>\n                    {loading ? 'Updating...' : 'Update Role'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Permission Management Modal */}\n      {selectedRole && (\n        <RolePermissionModal\n          role={selectedRole}\n          isOpen={showPermissionModal}\n          onClose={() => {\n            setShowPermissionModal(false);\n            setSelectedRole(null);\n          }}\n          onSuccess={(message) => {\n            setSuccess(message);\n            setTimeout(() => setSuccess(null), 5000);\n          }}\n          onError={(message) => {\n            setError(message);\n            setTimeout(() => setError(null), 5000);\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default RoleManagement;\n"], "mappings": "kIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,iBAAiB,KAAQ,gBAAgB,CAE/D,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAIxD,KAAM,CAAAC,cAA6C,CAAGA,CAAA,GAAM,CAC1D;AACA,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGb,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACc,WAAW,CAAEC,cAAc,CAAC,CAAGf,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAgB,IAAI,CAAC,CAE3D;AACA,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAiB,CAC3DwB,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,EAAE,CACbC,WAAW,CAAE,CAAC,CACdC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,KACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACgC,YAAY,CAAEC,eAAe,CAAC,CAAGjC,QAAQ,CAAgC,KAAK,CAAC,CAEtF;AACA,KAAM,CAACkC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACoC,aAAa,CAAEC,gBAAgB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACsC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAACwC,YAAY,CAAEC,eAAe,CAAC,CAAGzC,QAAQ,CAAc,IAAI,CAAC,CAEnE;AACA,KAAM,CAAC0C,QAAQ,CAAEC,WAAW,CAAC,CAAG3C,QAAQ,CAAC,CACvC4C,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,IACb,CAAC,CAAC,CAEF;AACA7C,SAAS,CAAC,IAAM,CACd8C,SAAS,CAAC,CAAC,CACXC,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAAC1B,UAAU,CAACE,IAAI,CAAEF,UAAU,CAACG,SAAS,CAAEK,UAAU,CAAEE,YAAY,CAAC,CAAC,CAErE;AACA,KAAM,CAAAe,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF9B,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAA8B,OAAY,CAAG,CACnBzB,IAAI,CAAEF,UAAU,CAACE,IAAI,CACrBC,SAAS,CAAEH,UAAU,CAACG,SACxB,CAAC,CAED,GAAIK,UAAU,CAACoB,IAAI,CAAC,CAAC,CAAE,CACrBD,OAAO,CAACE,WAAW,CAAGrB,UAAU,CAACoB,IAAI,CAAC,CAAC,CACvCD,OAAO,CAACG,cAAc,CAAG,CAAC,MAAM,CAAE,aAAa,CAAC,CAClD,CAEA,GAAIpB,YAAY,GAAK,KAAK,CAAE,CAC1BiB,OAAO,CAACH,SAAS,CAAGd,YAAY,GAAK,QAAQ,CAC/C,CAEA,KAAM,CAAAqB,QAAQ,CAAG,KAAM,CAAAnD,WAAW,CAACoD,IAAI,CAACL,OAAO,CAAC,CAChDpC,QAAQ,CAACwC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAC5B,KAAM,CAAAC,cAAc,CAAGH,QAAQ,CAACE,IAAI,CAACjC,UAAU,CAC/CC,aAAa,CAAAkC,aAAA,CAAAA,aAAA,IACRD,cAAc,MACjB5B,QAAQ,CAAE4B,cAAc,CAAChC,IAAI,CAAGgC,cAAc,CAAC7B,WAAW,CAC1DE,QAAQ,CAAE2B,cAAc,CAAChC,IAAI,CAAG,CAAC,EAClC,CAAC,CACJ,CAAE,MAAOkC,GAAQ,CAAE,CACjBvC,QAAQ,CAACuC,GAAG,CAACC,OAAO,EAAI,sBAAsB,CAAC,CAC/CC,OAAO,CAAC1C,KAAK,CAAC,sBAAsB,CAAEwC,GAAG,CAAC,CAC5C,CAAC,OAAS,CACRzC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA+B,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAlD,iBAAiB,CAACmD,IAAI,CAAC,CAAE9B,IAAI,CAAE,CAAC,CAAEC,SAAS,CAAE,GAAI,CAAC,CAAC,CAC1EV,cAAc,CAACsC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CACpC,CAAE,MAAOG,GAAQ,CAAE,CACjBE,OAAO,CAAC1C,KAAK,CAAC,4BAA4B,CAAEwC,GAAG,CAAC,CAClD,CACF,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACjDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CACF9C,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAjB,WAAW,CAAC8D,MAAM,CAACtB,QAAQ,CAAC,CAClCrB,UAAU,CAAC,2BAA2B,CAAC,CACvCc,kBAAkB,CAAC,KAAK,CAAC,CACzB8B,SAAS,CAAC,CAAC,CACXlB,SAAS,CAAC,CAAC,CACb,CAAE,MAAOW,GAAQ,CAAE,CACjBvC,QAAQ,CAACuC,GAAG,CAACC,OAAO,EAAI,uBAAuB,CAAC,CAClD,CAAC,OAAS,CACR1C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAiD,YAAY,CAAG,KAAO,CAAAJ,CAAkB,EAAK,CACjDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACvB,YAAY,CAAE,OAEnB,GAAI,CACFvB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAjB,WAAW,CAACiE,MAAM,CAAC3B,YAAY,CAAC4B,EAAE,CAAE1B,QAAQ,CAAC,CACnDrB,UAAU,CAAC,2BAA2B,CAAC,CACvCgB,gBAAgB,CAAC,KAAK,CAAC,CACvB4B,SAAS,CAAC,CAAC,CACXlB,SAAS,CAAC,CAAC,CACb,CAAE,MAAOW,GAAQ,CAAE,CACjBvC,QAAQ,CAACuC,GAAG,CAACC,OAAO,EAAI,uBAAuB,CAAC,CAClD,CAAC,OAAS,CACR1C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAoD,YAAY,CAAG,KAAO,CAAAC,IAAU,EAAK,CACzC,GAAI,CAACC,MAAM,CAACC,OAAO,2CAAAC,MAAA,CAA0CH,IAAI,CAAC1B,IAAI,OAAI,CAAC,CAAE,CAC3E,OACF,CAEA,GAAI,CACF3B,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAjB,WAAW,CAACwE,MAAM,CAACJ,IAAI,CAACF,EAAE,CAAC,CACjC/C,UAAU,CAAC,2BAA2B,CAAC,CACvC0B,SAAS,CAAC,CAAC,CACb,CAAE,MAAOW,GAAQ,CAAE,CACjBvC,QAAQ,CAACuC,GAAG,CAACC,OAAO,EAAI,uBAAuB,CAAC,CAClD,CAAC,OAAS,CACR1C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA0D,YAAY,CAAIb,CAAkB,EAAK,CAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBxC,aAAa,CAACqD,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAEpD,IAAI,CAAE,CAAC,EAAG,CAAC,CAC7CuB,SAAS,CAAC,CAAC,CACb,CAAC,CAED;AACA,KAAM,CAAAkB,SAAS,CAAGA,CAAA,GAAM,CACtBtB,WAAW,CAAC,CACVC,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,IACb,CAAC,CAAC,CACFL,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAoC,aAAa,CAAIP,IAAU,EAAK,CACpC7B,eAAe,CAAC6B,IAAI,CAAC,CACrB3B,WAAW,CAAC,CACVC,IAAI,CAAE0B,IAAI,CAAC1B,IAAI,CACfC,WAAW,CAAEyB,IAAI,CAACzB,WAAW,EAAI,EAAE,CACnCC,SAAS,CAAEwB,IAAI,CAACxB,SAClB,CAAC,CAAC,CACFT,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAyC,mBAAmB,CAAIR,IAAU,EAAK,CAC1C7B,eAAe,CAAC6B,IAAI,CAAC,CACrB/B,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAAwC,aAAa,CAAGA,CAAA,GAAM,CAC1B5D,QAAQ,CAAC,IAAI,CAAC,CACdE,UAAU,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,mBACEb,KAAA,QAAKwE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3E,IAAA,QAAK0E,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBzE,KAAA,QAAKwE,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBzE,KAAA,QAAKwE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrE3E,IAAA,OAAA2E,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBzE,KAAA,WACEwE,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAM/C,kBAAkB,CAAC,IAAI,CAAE,CAAA8C,QAAA,eAExC3E,IAAA,MAAG0E,SAAS,CAAC,kBAAkB,CAAI,CAAC,cAEtC,EAAQ,CAAC,EACN,CAAC,CAGL9D,KAAK,eACJV,KAAA,QAAKwE,SAAS,CAAC,gDAAgD,CAACV,IAAI,CAAC,OAAO,CAAAW,QAAA,EACzE/D,KAAK,cACNZ,IAAA,WAAQ6E,IAAI,CAAC,QAAQ,CAACH,SAAS,CAAC,WAAW,CAACE,OAAO,CAAEH,aAAc,CAAS,CAAC,EAC1E,CACN,CACA3D,OAAO,eACNZ,KAAA,QAAKwE,SAAS,CAAC,iDAAiD,CAACV,IAAI,CAAC,OAAO,CAAAW,QAAA,EAC1E7D,OAAO,cACRd,IAAA,WAAQ6E,IAAI,CAAC,QAAQ,CAACH,SAAS,CAAC,WAAW,CAACE,OAAO,CAAEH,aAAc,CAAS,CAAC,EAC1E,CACN,cAGDzE,IAAA,QAAK0E,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB3E,IAAA,QAAK0E,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB3E,IAAA,SAAM8E,QAAQ,CAAET,YAAa,CAAAM,QAAA,cAC3BzE,KAAA,QAAKwE,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBzE,KAAA,QAAKwE,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB3E,IAAA,UAAO+E,OAAO,CAAC,QAAQ,CAACL,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cACnE3E,IAAA,UACE6E,IAAI,CAAC,MAAM,CACXH,SAAS,CAAC,cAAc,CACxBZ,EAAE,CAAC,QAAQ,CACXkB,WAAW,CAAC,kCAAkC,CAC9CC,KAAK,CAAEzD,UAAW,CAClB0D,QAAQ,CAAG1B,CAAC,EAAK/B,aAAa,CAAC+B,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE,CAChD,CAAC,EACC,CAAC,cACN/E,KAAA,QAAKwE,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB3E,IAAA,UAAO+E,OAAO,CAAC,QAAQ,CAACL,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cAC7DzE,KAAA,WACEwE,SAAS,CAAC,aAAa,CACvBZ,EAAE,CAAC,QAAQ,CACXmB,KAAK,CAAEvD,YAAa,CACpBwD,QAAQ,CAAG1B,CAAC,EAAK7B,eAAe,CAAC6B,CAAC,CAAC2B,MAAM,CAACF,KAAY,CAAE,CAAAN,QAAA,eAExD3E,IAAA,WAAQiF,KAAK,CAAC,KAAK,CAAAN,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvC3E,IAAA,WAAQiF,KAAK,CAAC,QAAQ,CAAAN,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC3E,IAAA,WAAQiF,KAAK,CAAC,UAAU,CAAAN,QAAA,CAAC,UAAQ,CAAQ,CAAC,EACpC,CAAC,EACN,CAAC,cACNzE,KAAA,QAAKwE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CzE,KAAA,WAAQ2E,IAAI,CAAC,QAAQ,CAACH,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC5D3E,IAAA,MAAG0E,SAAS,CAAC,oBAAoB,CAAI,CAAC,SAExC,EAAQ,CAAC,cACT1E,IAAA,WACE6E,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,2BAA2B,CACrCE,OAAO,CAAEA,CAAA,GAAM,CACbnD,aAAa,CAAC,EAAE,CAAC,CACjBE,eAAe,CAAC,KAAK,CAAC,CACtBV,aAAa,CAACqD,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAEpD,IAAI,CAAE,CAAC,EAAG,CAAC,CAC/C,CAAE,CAAAyD,QAAA,CACH,OAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACF,CAAC,CACJ,CAAC,CACH,CAAC,cAGN3E,IAAA,QAAK0E,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB3E,IAAA,QAAK0E,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBjE,OAAO,cACNV,IAAA,QAAK0E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B3E,IAAA,QAAK0E,SAAS,CAAC,gBAAgB,CAACV,IAAI,CAAC,QAAQ,CAAAW,QAAA,cAC3C3E,IAAA,SAAM0E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,CAChD,CAAC,CACH,CAAC,cAENzE,KAAA,CAAAE,SAAA,EAAAuE,QAAA,eACE3E,IAAA,QAAK0E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BzE,KAAA,UAAOwE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClC3E,IAAA,UAAO0E,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC5BzE,KAAA,OAAAyE,QAAA,eACE3E,IAAA,OAAA2E,QAAA,CAAI,IAAE,CAAI,CAAC,cACX3E,IAAA,OAAA2E,QAAA,CAAI,MAAI,CAAI,CAAC,cACb3E,IAAA,OAAA2E,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB3E,IAAA,OAAA2E,QAAA,CAAI,QAAM,CAAI,CAAC,cACf3E,IAAA,OAAA2E,QAAA,CAAI,SAAO,CAAI,CAAC,cAChB3E,IAAA,OAAA2E,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACR3E,IAAA,UAAA2E,QAAA,CACGrE,KAAK,CAAC8E,MAAM,GAAK,CAAC,cACjBpF,IAAA,OAAA2E,QAAA,cACE3E,IAAA,OAAIqF,OAAO,CAAE,CAAE,CAACX,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,gBAE7C,CAAI,CAAC,CACH,CAAC,CAELrE,KAAK,CAACgF,GAAG,CAAEtB,IAAI,eACb9D,KAAA,OAAAyE,QAAA,eACE3E,IAAA,OAAA2E,QAAA,CAAKX,IAAI,CAACF,EAAE,CAAK,CAAC,cAClB9D,IAAA,OAAA2E,QAAA,cACE3E,IAAA,WAAA2E,QAAA,CAASX,IAAI,CAAC1B,IAAI,CAAS,CAAC,CAC1B,CAAC,cACLtC,IAAA,OAAA2E,QAAA,CAAKX,IAAI,CAACzB,WAAW,EAAI,GAAG,CAAK,CAAC,cAClCvC,IAAA,OAAA2E,QAAA,cACE3E,IAAA,SAAM0E,SAAS,UAAAP,MAAA,CAAWH,IAAI,CAACxB,SAAS,CAAG,YAAY,CAAG,cAAc,CAAG,CAAAmC,QAAA,CACxEX,IAAI,CAACxB,SAAS,CAAG,QAAQ,CAAG,UAAU,CACnC,CAAC,CACL,CAAC,cACLxC,IAAA,OAAA2E,QAAA,CACGX,IAAI,CAACuB,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACxB,IAAI,CAACuB,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAAG,GAAG,CACrE,CAAC,cACLzF,IAAA,OAAA2E,QAAA,cACEzE,KAAA,QAAKwE,SAAS,CAAC,wBAAwB,CAACV,IAAI,CAAC,OAAO,CAAAW,QAAA,eAClD3E,IAAA,WACE0E,SAAS,CAAC,yBAAyB,CACnCE,OAAO,CAAEA,CAAA,GAAML,aAAa,CAACP,IAAI,CAAE,CACnC0B,KAAK,CAAC,WAAW,CAAAf,QAAA,cAEjB3E,IAAA,MAAG0E,SAAS,CAAC,aAAa,CAAI,CAAC,CACzB,CAAC,cACT1E,IAAA,WACE0E,SAAS,CAAC,sBAAsB,CAChCE,OAAO,CAAEA,CAAA,GAAMJ,mBAAmB,CAACR,IAAI,CAAE,CACzC0B,KAAK,CAAC,oBAAoB,CAAAf,QAAA,cAE1B3E,IAAA,MAAG0E,SAAS,CAAC,YAAY,CAAI,CAAC,CACxB,CAAC,cACT1E,IAAA,WACE0E,SAAS,CAAC,wBAAwB,CAClCE,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAACC,IAAI,CAAE,CAClC0B,KAAK,CAAC,aAAa,CAAAf,QAAA,cAEnB3E,IAAA,MAAG0E,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,EACN,CAAC,CACJ,CAAC,GAtCEV,IAAI,CAACF,EAuCV,CACL,CACF,CACI,CAAC,EACH,CAAC,CACL,CAAC,CAGL9C,UAAU,CAACK,WAAW,CAAG,CAAC,eACzBrB,IAAA,QAAK,aAAW,kBAAkB,CAAA2E,QAAA,cAChCzE,KAAA,OAAIwE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACpD3E,IAAA,OAAI0E,SAAS,cAAAP,MAAA,CAAe,CAACnD,UAAU,CAACO,QAAQ,CAAG,UAAU,CAAG,EAAE,CAAG,CAAAoD,QAAA,cACnE3E,IAAA,WACE0E,SAAS,CAAC,WAAW,CACrBE,OAAO,CAAEA,CAAA,GAAM3D,aAAa,CAACqD,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAEpD,IAAI,CAAEoD,IAAI,CAACpD,IAAI,CAAG,CAAC,EAAG,CAAE,CACzEyE,QAAQ,CAAE,CAAC3E,UAAU,CAACO,QAAS,CAAAoD,QAAA,CAChC,UAED,CAAQ,CAAC,CACP,CAAC,CAEJiB,KAAK,CAACC,IAAI,CAAC,CAAET,MAAM,CAAEpE,UAAU,CAACK,WAAY,CAAC,CAAE,CAACyE,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAG,CAAC,CAAC,CAACT,GAAG,CAAEpE,IAAI,eACxElB,IAAA,OAAe0E,SAAS,cAAAP,MAAA,CAAenD,UAAU,CAACE,IAAI,GAAKA,IAAI,CAAG,QAAQ,CAAG,EAAE,CAAG,CAAAyD,QAAA,cAChF3E,IAAA,WACE0E,SAAS,CAAC,WAAW,CACrBE,OAAO,CAAEA,CAAA,GAAM3D,aAAa,CAACqD,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAEpD,IAAI,EAAG,CAAE,CAAAyD,QAAA,CAEzDzD,IAAI,CACC,CAAC,EANFA,IAOL,CACL,CAAC,cAEFlB,IAAA,OAAI0E,SAAS,cAAAP,MAAA,CAAe,CAACnD,UAAU,CAACM,QAAQ,CAAG,UAAU,CAAG,EAAE,CAAG,CAAAqD,QAAA,cACnE3E,IAAA,WACE0E,SAAS,CAAC,WAAW,CACrBE,OAAO,CAAEA,CAAA,GAAM3D,aAAa,CAACqD,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAEpD,IAAI,CAAEoD,IAAI,CAACpD,IAAI,CAAG,CAAC,EAAG,CAAE,CACzEyE,QAAQ,CAAE,CAAC3E,UAAU,CAACM,QAAS,CAAAqD,QAAA,CAChC,MAED,CAAQ,CAAC,CACP,CAAC,EACH,CAAC,CACF,CACN,EACD,CACH,CACE,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,CAGL/C,eAAe,eACd5B,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAACsB,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAACC,QAAQ,CAAE,CAAC,CAAE,CAAAvB,QAAA,cACzE3E,IAAA,QAAK0E,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BzE,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzE,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3E,IAAA,OAAI0E,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAChD3E,IAAA,WACE6E,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,WAAW,CACrBE,OAAO,CAAEA,CAAA,GAAM,CACb/C,kBAAkB,CAAC,KAAK,CAAC,CACzB8B,SAAS,CAAC,CAAC,CACb,CAAE,CACK,CAAC,EACP,CAAC,cACNzD,KAAA,SAAM4E,QAAQ,CAAEvB,YAAa,CAAAoB,QAAA,eAC3BzE,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzE,KAAA,QAAKwE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3E,IAAA,UAAO+E,OAAO,CAAC,YAAY,CAACL,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cACtE3E,IAAA,UACE6E,IAAI,CAAC,MAAM,CACXH,SAAS,CAAC,cAAc,CACxBZ,EAAE,CAAC,YAAY,CACfmB,KAAK,CAAE7C,QAAQ,CAACE,IAAK,CACrB4C,QAAQ,CAAG1B,CAAC,EAAKnB,WAAW,CAACiC,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAEhC,IAAI,CAAEkB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAG,CAAE,CAC1EkB,QAAQ,MACT,CAAC,EACC,CAAC,cACNjG,KAAA,QAAKwE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3E,IAAA,UAAO+E,OAAO,CAAC,mBAAmB,CAACL,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cAC7E3E,IAAA,aACE0E,SAAS,CAAC,cAAc,CACxBZ,EAAE,CAAC,mBAAmB,CACtBsC,IAAI,CAAE,CAAE,CACRnB,KAAK,CAAE7C,QAAQ,CAACG,WAAY,CAC5B2C,QAAQ,CAAG1B,CAAC,EAAKnB,WAAW,CAACiC,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAE/B,WAAW,CAAEiB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAG,CAAE,CACxE,CAAC,EACT,CAAC,cACN/E,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3E,IAAA,UACE6E,IAAI,CAAC,UAAU,CACfH,SAAS,CAAC,kBAAkB,CAC5BZ,EAAE,CAAC,cAAc,CACjBuC,OAAO,CAAEjE,QAAQ,CAACI,SAAU,CAC5B0C,QAAQ,CAAG1B,CAAC,EAAKnB,WAAW,CAACiC,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAE9B,SAAS,CAAEgB,CAAC,CAAC2B,MAAM,CAACkB,OAAO,EAAG,CAAE,CAClF,CAAC,cACFrG,IAAA,UAAO0E,SAAS,CAAC,kBAAkB,CAACK,OAAO,CAAC,cAAc,CAAAJ,QAAA,CAAC,QAE3D,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cACNzE,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3E,IAAA,WACE6E,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,mBAAmB,CAC7BE,OAAO,CAAEA,CAAA,GAAM,CACb/C,kBAAkB,CAAC,KAAK,CAAC,CACzB8B,SAAS,CAAC,CAAC,CACb,CAAE,CAAAgB,QAAA,CACH,QAED,CAAQ,CAAC,cACT3E,IAAA,WAAQ6E,IAAI,CAAC,QAAQ,CAACH,SAAS,CAAC,iBAAiB,CAACiB,QAAQ,CAAEjF,OAAQ,CAAAiE,QAAA,CACjEjE,OAAO,CAAG,aAAa,CAAG,aAAa,CAClC,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CAGAoB,aAAa,EAAII,YAAY,eAC5BlC,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAACsB,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAACC,QAAQ,CAAE,CAAC,CAAE,CAAAvB,QAAA,cACzE3E,IAAA,QAAK0E,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BzE,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzE,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BzE,KAAA,OAAIwE,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,aAAW,CAACzC,YAAY,CAACI,IAAI,EAAK,CAAC,cAC/DtC,IAAA,WACE6E,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,WAAW,CACrBE,OAAO,CAAEA,CAAA,GAAM,CACb7C,gBAAgB,CAAC,KAAK,CAAC,CACvB4B,SAAS,CAAC,CAAC,CACb,CAAE,CACK,CAAC,EACP,CAAC,cACNzD,KAAA,SAAM4E,QAAQ,CAAElB,YAAa,CAAAe,QAAA,eAC3BzE,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzE,KAAA,QAAKwE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3E,IAAA,UAAO+E,OAAO,CAAC,UAAU,CAACL,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cACpE3E,IAAA,UACE6E,IAAI,CAAC,MAAM,CACXH,SAAS,CAAC,cAAc,CACxBZ,EAAE,CAAC,UAAU,CACbmB,KAAK,CAAE7C,QAAQ,CAACE,IAAK,CACrB4C,QAAQ,CAAG1B,CAAC,EAAKnB,WAAW,CAACiC,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAEhC,IAAI,CAAEkB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAG,CAAE,CAC1EkB,QAAQ,MACT,CAAC,EACC,CAAC,cACNjG,KAAA,QAAKwE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3E,IAAA,UAAO+E,OAAO,CAAC,iBAAiB,CAACL,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cAC3E3E,IAAA,aACE0E,SAAS,CAAC,cAAc,CACxBZ,EAAE,CAAC,iBAAiB,CACpBsC,IAAI,CAAE,CAAE,CACRnB,KAAK,CAAE7C,QAAQ,CAACG,WAAY,CAC5B2C,QAAQ,CAAG1B,CAAC,EAAKnB,WAAW,CAACiC,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAE/B,WAAW,CAAEiB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAG,CAAE,CACxE,CAAC,EACT,CAAC,cACN/E,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3E,IAAA,UACE6E,IAAI,CAAC,UAAU,CACfH,SAAS,CAAC,kBAAkB,CAC5BZ,EAAE,CAAC,YAAY,CACfuC,OAAO,CAAEjE,QAAQ,CAACI,SAAU,CAC5B0C,QAAQ,CAAG1B,CAAC,EAAKnB,WAAW,CAACiC,IAAI,EAAAnB,aAAA,CAAAA,aAAA,IAAUmB,IAAI,MAAE9B,SAAS,CAAEgB,CAAC,CAAC2B,MAAM,CAACkB,OAAO,EAAG,CAAE,CAClF,CAAC,cACFrG,IAAA,UAAO0E,SAAS,CAAC,kBAAkB,CAACK,OAAO,CAAC,YAAY,CAAAJ,QAAA,CAAC,QAEzD,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cACNzE,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3E,IAAA,WACE6E,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,mBAAmB,CAC7BE,OAAO,CAAEA,CAAA,GAAM,CACb7C,gBAAgB,CAAC,KAAK,CAAC,CACvB4B,SAAS,CAAC,CAAC,CACb,CAAE,CAAAgB,QAAA,CACH,QAED,CAAQ,CAAC,cACT3E,IAAA,WAAQ6E,IAAI,CAAC,QAAQ,CAACH,SAAS,CAAC,iBAAiB,CAACiB,QAAQ,CAAEjF,OAAQ,CAAAiE,QAAA,CACjEjE,OAAO,CAAG,aAAa,CAAG,aAAa,CAClC,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CAGAwB,YAAY,eACXlC,IAAA,CAACF,mBAAmB,EAClBkE,IAAI,CAAE9B,YAAa,CACnBoE,MAAM,CAAEtE,mBAAoB,CAC5BuE,OAAO,CAAEA,CAAA,GAAM,CACbtE,sBAAsB,CAAC,KAAK,CAAC,CAC7BE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CACFqE,SAAS,CAAGnD,OAAO,EAAK,CACtBtC,UAAU,CAACsC,OAAO,CAAC,CACnBoD,UAAU,CAAC,IAAM1F,UAAU,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAE,CACF2F,OAAO,CAAGrD,OAAO,EAAK,CACpBxC,QAAQ,CAACwC,OAAO,CAAC,CACjBoD,UAAU,CAAC,IAAM5F,QAAQ,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,CACxC,CAAE,CACH,CACF,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}