{"ast": null, "code": "import { BaseCRUDService, api } from './base.service';\nclass RoleService extends BaseCRUDService {\n  constructor() {\n    super('/admin/role-management');\n  }\n\n  // Get all roles (from old admin API)\n  getAllRoles() {\n    return api.get('/admin/roles');\n  }\n\n  // Get active roles only\n  getActiveRoles() {\n    return this.list({\n      active_only: true\n    });\n  }\n\n  // Get role permissions\n  getRolePermissions(roleId) {\n    return api.get(`/admin/role/${roleId}/permissions`);\n  }\n\n  // Assign permission to role\n  assignPermission(roleId, permissionName) {\n    return api.post('/admin/assign-permission', {\n      role_id: roleId,\n      permission: permissionName\n    });\n  }\n\n  // Remove permission from role\n  removePermission(roleId, permissionName) {\n    return api.post('/admin/remove-permission', {\n      role_id: roleId,\n      permission: permissionName\n    });\n  }\n\n  // Assign permission to role using CRUD API\n  assignPermissionCRUD(roleId, permissionId) {\n    return api.post(`${this.baseUrl}/${roleId}/permissions`, {\n      permission_id: permissionId\n    });\n  }\n\n  // Remove permission from role using CRUD API\n  removePermissionCRUD(roleId, permissionId) {\n    return api.delete(`${this.baseUrl}/${roleId}/permissions/${permissionId}`);\n  }\n\n  // Search roles with custom filters\n  searchRoles(searchTerm, page = 1, pageSize = 10) {\n    return this.search({\n      page,\n      page_size: pageSize,\n      search_term: searchTerm,\n      filters: {\n        is_active: true\n      }\n    });\n  }\n\n  // Get roles by name pattern\n  getRolesByName(namePattern) {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      name_like: namePattern\n    });\n  }\n\n  // Toggle role active status\n  toggleActiveStatus(roleId, isActive) {\n    return this.update(roleId, {\n      is_active: isActive\n    });\n  }\n\n  // Bulk operations\n  bulkDelete(roleIds) {\n    return api.post(`${this.baseUrl}/bulk-delete`, {\n      ids: roleIds\n    });\n  }\n  bulkToggleStatus(roleIds, isActive) {\n    return api.post(`${this.baseUrl}/bulk-toggle-status`, {\n      ids: roleIds,\n      is_active: isActive\n    });\n  }\n}\nexport const roleService = new RoleService();\nexport default roleService;", "map": {"version": 3, "names": ["BaseCRUDService", "api", "RoleService", "constructor", "getAllRoles", "get", "getActiveRoles", "list", "active_only", "getRolePermissions", "roleId", "assignPermission", "permissionName", "post", "role_id", "permission", "removePermission", "assignPermissionCRUD", "permissionId", "baseUrl", "permission_id", "removePermissionCRUD", "delete", "searchRoles", "searchTerm", "page", "pageSize", "search", "page_size", "search_term", "filters", "is_active", "getRolesByName", "namePattern", "name_like", "toggleActiveStatus", "isActive", "update", "bulkDelete", "roleIds", "ids", "bulkToggleStatus", "roleService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/role.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { Role } from '../types';\nimport { BaseCRUDService, CRUDListResponse, CRUDResponse, api } from './base.service';\n\nexport interface CreateRoleRequest {\n  name: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nexport interface UpdateRoleRequest {\n  name?: string;\n  description?: string;\n  is_active?: boolean;\n}\n\nclass RoleService extends BaseCRUDService<Role, CreateRoleRequest, UpdateRoleRequest> {\n  constructor() {\n    super('/admin/role-management');\n  }\n\n  // Get all roles (from old admin API)\n  getAllRoles(): Promise<AxiosResponse<Role[]>> {\n    return api.get('/admin/roles');\n  }\n\n  // Get active roles only\n  getActiveRoles(): Promise<AxiosResponse<CRUDListResponse<Role>>> {\n    return this.list({ active_only: true });\n  }\n\n  // Get role permissions\n  getRolePermissions(roleId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`/admin/role/${roleId}/permissions`);\n  }\n\n  // Assign permission to role\n  assignPermission(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/assign-permission', { role_id: roleId, permission: permissionName });\n  }\n\n  // Remove permission from role\n  removePermission(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return api.post('/admin/remove-permission', { role_id: roleId, permission: permissionName });\n  }\n\n  // Assign permission to role using CRUD API\n  assignPermissionCRUD(roleId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/${roleId}/permissions`, { permission_id: permissionId });\n  }\n\n  // Remove permission from role using CRUD API\n  removePermissionCRUD(roleId: number, permissionId: number): Promise<AxiosResponse<{ message: string }>> {\n    return api.delete(`${this.baseUrl}/${roleId}/permissions/${permissionId}`);\n  }\n\n  // Search roles with custom filters\n  searchRoles(searchTerm: string, page: number = 1, pageSize: number = 10): Promise<AxiosResponse<CRUDListResponse<Role>>> {\n    return this.search({\n      page,\n      page_size: pageSize,\n      search_term: searchTerm,\n      filters: {\n        is_active: true\n      }\n    });\n  }\n\n  // Get roles by name pattern\n  getRolesByName(namePattern: string): Promise<AxiosResponse<CRUDListResponse<Role>>> {\n    return this.list({\n      page: 1,\n      page_size: 100,\n      name_like: namePattern\n    });\n  }\n\n  // Toggle role active status\n  toggleActiveStatus(roleId: number, isActive: boolean): Promise<AxiosResponse<CRUDResponse<Role>>> {\n    return this.update(roleId, { is_active: isActive });\n  }\n\n  // Bulk operations\n  bulkDelete(roleIds: number[]): Promise<AxiosResponse<{ message: string; deleted_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-delete`, { ids: roleIds });\n  }\n\n  bulkToggleStatus(roleIds: number[], isActive: boolean): Promise<AxiosResponse<{ message: string; updated_count: number }>> {\n    return api.post(`${this.baseUrl}/bulk-toggle-status`, { ids: roleIds, is_active: isActive });\n  }\n}\n\nexport const roleService = new RoleService();\nexport default roleService;\n"], "mappings": "AAEA,SAASA,eAAe,EAAkCC,GAAG,QAAQ,gBAAgB;AAcrF,MAAMC,WAAW,SAASF,eAAe,CAA6C;EACpFG,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,wBAAwB,CAAC;EACjC;;EAEA;EACAC,WAAWA,CAAA,EAAmC;IAC5C,OAAOH,GAAG,CAACI,GAAG,CAAC,cAAc,CAAC;EAChC;;EAEA;EACAC,cAAcA,CAAA,EAAmD;IAC/D,OAAO,IAAI,CAACC,IAAI,CAAC;MAAEC,WAAW,EAAE;IAAK,CAAC,CAAC;EACzC;;EAEA;EACAC,kBAAkBA,CAACC,MAAc,EAAoC;IACnE,OAAOT,GAAG,CAACI,GAAG,CAAC,eAAeK,MAAM,cAAc,CAAC;EACrD;;EAEA;EACAC,gBAAgBA,CAACD,MAAc,EAAEE,cAAsB,EAA+C;IACpG,OAAOX,GAAG,CAACY,IAAI,CAAC,0BAA0B,EAAE;MAAEC,OAAO,EAAEJ,MAAM;MAAEK,UAAU,EAAEH;IAAe,CAAC,CAAC;EAC9F;;EAEA;EACAI,gBAAgBA,CAACN,MAAc,EAAEE,cAAsB,EAA+C;IACpG,OAAOX,GAAG,CAACY,IAAI,CAAC,0BAA0B,EAAE;MAAEC,OAAO,EAAEJ,MAAM;MAAEK,UAAU,EAAEH;IAAe,CAAC,CAAC;EAC9F;;EAEA;EACAK,oBAAoBA,CAACP,MAAc,EAAEQ,YAAoB,EAA+C;IACtG,OAAOjB,GAAG,CAACY,IAAI,CAAC,GAAG,IAAI,CAACM,OAAO,IAAIT,MAAM,cAAc,EAAE;MAAEU,aAAa,EAAEF;IAAa,CAAC,CAAC;EAC3F;;EAEA;EACAG,oBAAoBA,CAACX,MAAc,EAAEQ,YAAoB,EAA+C;IACtG,OAAOjB,GAAG,CAACqB,MAAM,CAAC,GAAG,IAAI,CAACH,OAAO,IAAIT,MAAM,gBAAgBQ,YAAY,EAAE,CAAC;EAC5E;;EAEA;EACAK,WAAWA,CAACC,UAAkB,EAAEC,IAAY,GAAG,CAAC,EAAEC,QAAgB,GAAG,EAAE,EAAkD;IACvH,OAAO,IAAI,CAACC,MAAM,CAAC;MACjBF,IAAI;MACJG,SAAS,EAAEF,QAAQ;MACnBG,WAAW,EAAEL,UAAU;MACvBM,OAAO,EAAE;QACPC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;EACJ;;EAEA;EACAC,cAAcA,CAACC,WAAmB,EAAkD;IAClF,OAAO,IAAI,CAAC1B,IAAI,CAAC;MACfkB,IAAI,EAAE,CAAC;MACPG,SAAS,EAAE,GAAG;MACdM,SAAS,EAAED;IACb,CAAC,CAAC;EACJ;;EAEA;EACAE,kBAAkBA,CAACzB,MAAc,EAAE0B,QAAiB,EAA8C;IAChG,OAAO,IAAI,CAACC,MAAM,CAAC3B,MAAM,EAAE;MAAEqB,SAAS,EAAEK;IAAS,CAAC,CAAC;EACrD;;EAEA;EACAE,UAAUA,CAACC,OAAiB,EAAsE;IAChG,OAAOtC,GAAG,CAACY,IAAI,CAAC,GAAG,IAAI,CAACM,OAAO,cAAc,EAAE;MAAEqB,GAAG,EAAED;IAAQ,CAAC,CAAC;EAClE;EAEAE,gBAAgBA,CAACF,OAAiB,EAAEH,QAAiB,EAAsE;IACzH,OAAOnC,GAAG,CAACY,IAAI,CAAC,GAAG,IAAI,CAACM,OAAO,qBAAqB,EAAE;MAAEqB,GAAG,EAAED,OAAO;MAAER,SAAS,EAAEK;IAAS,CAAC,CAAC;EAC9F;AACF;AAEA,OAAO,MAAMM,WAAW,GAAG,IAAIxC,WAAW,CAAC,CAAC;AAC5C,eAAewC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}