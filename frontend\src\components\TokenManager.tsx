import React, { useState, useEffect } from 'react';
import { authAPI } from '../services/api';

interface TokenInfo {
  token: string;
  refreshToken: string;
  user: any;
  tokenExpiry?: Date;
}

const TokenManager: React.FC = () => {
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    loadTokenInfo();
  }, []);

  const loadTokenInfo = () => {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refresh_token');
    const user = localStorage.getItem('user');

    if (token && refreshToken && user) {
      try {
        // Decode JWT to get expiry (simple decode, not verification)
        const payload = JSON.parse(atob(token.split('.')[1]));
        const expiry = new Date(payload.exp * 1000);
        
        setTokenInfo({
          token: token.substring(0, 20) + '...',
          refreshToken: refreshToken.substring(0, 20) + '...',
          user: JSON.parse(user),
          tokenExpiry: expiry
        });
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    }
  };

  const handleRefreshToken = async () => {
    if (!tokenInfo?.user?.id) return;
    
    setIsRefreshing(true);
    setMessage('');
    
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        throw new Error('No refresh token found');
      }
      
      const response = await authAPI.refresh(tokenInfo.user.id, refreshToken);
      setMessage('✅ Token refreshed successfully!');
      loadTokenInfo(); // Reload token info
    } catch (error: any) {
      setMessage(`❌ Failed to refresh token: ${error.response?.data?.error || error.message}`);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleRevokeAllTokens = async () => {
    try {
      await authAPI.revokeAllTokens();
      setMessage('✅ All refresh tokens revoked successfully!');
      // Clear local tokens after revoking
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
      setTokenInfo(null);
    } catch (error: any) {
      setMessage(`❌ Failed to revoke tokens: ${error.response?.data?.error || error.message}`);
    }
  };

  const isTokenExpired = () => {
    if (!tokenInfo?.tokenExpiry) return false;
    return new Date() > tokenInfo.tokenExpiry;
  };

  const getTimeUntilExpiry = () => {
    if (!tokenInfo?.tokenExpiry) return '';
    const now = new Date();
    const expiry = tokenInfo.tokenExpiry;
    const diff = expiry.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  if (!tokenInfo) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Token Manager</h3>
        <p className="text-gray-600">No active session found. Please login first.</p>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Token Manager</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">User:</label>
          <p className="text-sm text-gray-900">{tokenInfo.user.username} ({tokenInfo.user.email})</p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Access Token:</label>
          <p className="text-sm text-gray-900 font-mono">{tokenInfo.token}</p>
          <div className="flex items-center mt-1">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              isTokenExpired() ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
            }`}>
              {isTokenExpired() ? 'Expired' : 'Valid'}
            </span>
            <span className="ml-2 text-xs text-gray-500">
              {isTokenExpired() ? 'Token has expired' : `Expires in ${getTimeUntilExpiry()}`}
            </span>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Refresh Token:</label>
          <p className="text-sm text-gray-900 font-mono">{tokenInfo.refreshToken}</p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={handleRefreshToken}
            disabled={isRefreshing}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            {isRefreshing ? 'Refreshing...' : 'Refresh Token'}
          </button>
          
          <button
            onClick={handleRevokeAllTokens}
            className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
          >
            Revoke All Tokens
          </button>
          
          <button
            onClick={loadTokenInfo}
            className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
          >
            Reload Info
          </button>
        </div>
        
        {message && (
          <div className={`p-3 rounded ${
            message.startsWith('✅') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
          }`}>
            {message}
          </div>
        )}
      </div>
    </div>
  );
};

export default TokenManager;
