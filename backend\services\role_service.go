package services

import (
	"fmt"
	"jwt-auth-backend/models"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RoleService extends BaseCRUDService with role-specific functionality
type RoleService struct {
	*BaseCRUDService
}

// NewRoleService creates a new instance of RoleService
func NewRoleService(db *gorm.DB, logger *zap.Logger) *RoleService {
	return &RoleService{
		BaseCRUDService: NewBaseCRUDService(db),
	}
}

// CreateRole creates a new role with validation
func (s *RoleService) CreateRole(req models.RoleCreateRequest) (*models.Role, error) {
	s.Logger.Debug("Creating new role", zap.String("name", req.Name))

	// Check if role name already exists
	exists, err := s.Exists(&models.Role{}, map[string]interface{}{
		"name": req.Name,
	})
	if err != nil {
		s.Logger.Error("Failed to check role existence", zap.Error(err))
		return nil, fmt.Errorf("failed to check role existence: %w", err)
	}
	if exists {
		s.Logger.Warn("Role creation failed - name already exists", zap.String("name", req.Name))
		return nil, fmt.Errorf("role with name '%s' already exists", req.Name)
	}

	// Create role
	role := &models.Role{
		Name:        req.Name,
		Description: req.Description,
		IsActive:    req.IsActive,
	}

	if err := s.Create(role); err != nil {
		s.Logger.Error("Failed to create role", zap.Error(err))
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	s.Logger.Info("Role created successfully",
		zap.Uint("id", role.ID),
		zap.String("name", role.Name),
	)

	return role, nil
}

// UpdateRole updates an existing role
func (s *RoleService) UpdateRole(id uint, req models.RoleUpdateRequest) (*models.Role, error) {
	s.Logger.Debug("Updating role", zap.Uint("id", id))

	// Check if role exists
	var role models.Role
	if err := s.GetByID(&role, id); err != nil {
		s.Logger.Warn("Role update failed - role not found", zap.Uint("id", id))
		return nil, fmt.Errorf("role not found")
	}

	// Check if new name already exists (if name is being updated)
	if req.Name != "" && req.Name != role.Name {
		exists, err := s.Exists(&models.Role{}, map[string]interface{}{
			"name": req.Name,
		})
		if err != nil {
			s.Logger.Error("Failed to check role name existence", zap.Error(err))
			return nil, fmt.Errorf("failed to check role name existence: %w", err)
		}
		if exists {
			s.Logger.Warn("Role update failed - name already exists", zap.String("name", req.Name))
			return nil, fmt.Errorf("role with name '%s' already exists", req.Name)
		}
	}

	// Prepare updates
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	// Update role
	if err := s.Update(&role, id, updates); err != nil {
		s.Logger.Error("Failed to update role", zap.Error(err))
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	// Reload updated role
	if err := s.GetByID(&role, id); err != nil {
		s.Logger.Error("Failed to reload updated role", zap.Error(err))
		return nil, fmt.Errorf("failed to reload updated role: %w", err)
	}

	s.Logger.Info("Role updated successfully",
		zap.Uint("id", role.ID),
		zap.String("name", role.Name),
	)

	return &role, nil
}

// GetRoleWithAssociations retrieves a role with its users and permissions
func (s *RoleService) GetRoleWithAssociations(id uint) (*models.Role, error) {
	s.Logger.Debug("Getting role with associations", zap.Uint("id", id))

	var role models.Role
	err := s.DB.Preload("Users").Preload("Permissions").First(&role, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.Logger.Warn("Role not found", zap.Uint("id", id))
			return nil, fmt.Errorf("role not found")
		}
		s.Logger.Error("Failed to get role with associations", zap.Error(err))
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	return &role, nil
}

// ListRolesWithPagination retrieves roles with pagination and search
func (s *RoleService) ListRolesWithPagination(req models.CRUDRequest) (interface{}, models.PaginationResponse, error) {
	s.Logger.Debug("Listing roles with pagination",
		zap.Int("page", req.PaginationRequest.Page),
		zap.Int("page_size", req.PaginationRequest.PageSize),
	)

	return s.List(&models.Role{}, req)
}

// ListActiveRoles retrieves only active roles
func (s *RoleService) ListActiveRoles() ([]models.Role, error) {
	s.Logger.Debug("Listing active roles")

	var roles []models.Role
	err := s.DB.Where("is_active = ?", true).Find(&roles).Error
	if err != nil {
		s.Logger.Error("Failed to list active roles", zap.Error(err))
		return nil, fmt.Errorf("failed to list active roles: %w", err)
	}

	return roles, nil
}

// DeleteRole soft deletes a role
func (s *RoleService) DeleteRole(id uint) error {
	s.Logger.Debug("Deleting role", zap.Uint("id", id))

	// Check if role exists
	var role models.Role
	if err := s.GetByID(&role, id); err != nil {
		s.Logger.Warn("Role deletion failed - role not found", zap.Uint("id", id))
		return fmt.Errorf("role not found")
	}

	// Check if role is being used by users
	var userCount int64
	if err := s.DB.Model(&models.UserRole{}).Where("role_id = ?", id).Count(&userCount).Error; err != nil {
		s.Logger.Error("Failed to check role usage", zap.Error(err))
		return fmt.Errorf("failed to check role usage: %w", err)
	}

	if userCount > 0 {
		s.Logger.Warn("Role deletion failed - role is in use",
			zap.Uint("id", id),
			zap.Int64("user_count", userCount),
		)
		return fmt.Errorf("cannot delete role: it is assigned to %d user(s)", userCount)
	}

	// Delete role
	if err := s.Delete(&role, id); err != nil {
		s.Logger.Error("Failed to delete role", zap.Error(err))
		return fmt.Errorf("failed to delete role: %w", err)
	}

	s.Logger.Info("Role deleted successfully",
		zap.Uint("id", id),
		zap.String("name", role.Name),
	)

	return nil
}

// AssignPermissionToRole assigns a permission to a role
func (s *RoleService) AssignPermissionToRole(roleID, permissionID uint) error {
	s.Logger.Debug("Assigning permission to role",
		zap.Uint("role_id", roleID),
		zap.Uint("permission_id", permissionID),
	)

	// Check if role exists
	var role models.Role
	if err := s.GetByID(&role, roleID); err != nil {
		return fmt.Errorf("role not found")
	}

	// Check if permission exists
	var permission models.Permission
	if err := s.GetByID(&permission, permissionID); err != nil {
		return fmt.Errorf("permission not found")
	}

	// Check if assignment already exists
	var count int64
	err := s.DB.Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ?", roleID, permissionID).
		Count(&count).Error
	if err != nil {
		s.Logger.Error("Failed to check permission assignment", zap.Error(err))
		return fmt.Errorf("failed to check permission assignment: %w", err)
	}

	if count > 0 {
		s.Logger.Warn("Permission already assigned to role",
			zap.Uint("role_id", roleID),
			zap.Uint("permission_id", permissionID),
		)
		return fmt.Errorf("permission already assigned to role")
	}

	// Create assignment
	rolePermission := &models.RolePermission{
		RoleID:       roleID,
		PermissionID: permissionID,
	}

	if err := s.DB.Create(rolePermission).Error; err != nil {
		s.Logger.Error("Failed to assign permission to role", zap.Error(err))
		return fmt.Errorf("failed to assign permission to role: %w", err)
	}

	s.Logger.Info("Permission assigned to role successfully",
		zap.Uint("role_id", roleID),
		zap.Uint("permission_id", permissionID),
	)

	return nil
}

// RemovePermissionFromRole removes a permission from a role
func (s *RoleService) RemovePermissionFromRole(roleID, permissionID uint) error {
	s.Logger.Debug("Removing permission from role",
		zap.Uint("role_id", roleID),
		zap.Uint("permission_id", permissionID),
	)

	result := s.DB.Where("role_id = ? AND permission_id = ?", roleID, permissionID).
		Delete(&models.RolePermission{})

	if result.Error != nil {
		s.Logger.Error("Failed to remove permission from role", zap.Error(result.Error))
		return fmt.Errorf("failed to remove permission from role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		s.Logger.Warn("Permission assignment not found",
			zap.Uint("role_id", roleID),
			zap.Uint("permission_id", permissionID),
		)
		return fmt.Errorf("permission assignment not found")
	}

	s.Logger.Info("Permission removed from role successfully",
		zap.Uint("role_id", roleID),
		zap.Uint("permission_id", permissionID),
	)

	return nil
}
