{"ast": null, "code": "import{api}from'./base.service';class AdminService{constructor(){this.baseUrl='/admin';}// Dashboard\ngetDashboard(){return api.get(\"\".concat(this.baseUrl,\"/dashboard\"));}// Users\ngetUsers(){return api.get(\"\".concat(this.baseUrl,\"/users\"));}getUserRoles(userId){return api.get(\"\".concat(this.baseUrl,\"/user/\").concat(userId,\"/roles\"));}getUserPermissions(userId){return api.get(\"\".concat(this.baseUrl,\"/user/\").concat(userId,\"/permissions\"));}getUserDirectPermissions(userId){return api.get(\"\".concat(this.baseUrl,\"/user/\").concat(userId,\"/direct-permissions\"));}// Roles\ngetRoles(){return api.get(\"\".concat(this.baseUrl,\"/roles\"));}getRolePermissions(roleId){return api.get(\"\".concat(this.baseUrl,\"/role/\").concat(roleId,\"/permissions\"));}// Permissions\ngetPermissions(){return api.get(\"\".concat(this.baseUrl,\"/permissions\"));}// Role assignments\nassignRole(request){return api.post(\"\".concat(this.baseUrl,\"/assign-role\"),request);}removeRole(request){return api.post(\"\".concat(this.baseUrl,\"/remove-role\"),request);}// Permission assignments to roles\nassignPermission(request){return api.post(\"\".concat(this.baseUrl,\"/assign-permission\"),request);}removePermission(request){return api.post(\"\".concat(this.baseUrl,\"/remove-permission\"),request);}// Direct permission assignments to users\nassignUserPermission(request){return api.post(\"\".concat(this.baseUrl,\"/assign-user-permission\"),request);}removeUserPermission(request){return api.post(\"\".concat(this.baseUrl,\"/remove-user-permission\"),request);}// Convenience methods\nassignRoleToUser(userId,roleName){return this.assignRole({user_id:userId,role:roleName});}removeRoleFromUser(userId,roleName){return this.removeRole({user_id:userId,role:roleName});}assignPermissionToRole(roleId,permissionName){return this.assignPermission({role_id:roleId,permission:permissionName});}removePermissionFromRole(roleId,permissionName){return this.removePermission({role_id:roleId,permission:permissionName});}assignPermissionToUser(userId,permissionName){return this.assignUserPermission({user_id:userId,permission:permissionName});}removePermissionFromUser(userId,permissionName){return this.removeUserPermission({user_id:userId,permission:permissionName});}}export const adminService=new AdminService();export default adminService;", "map": {"version": 3, "names": ["api", "AdminService", "constructor", "baseUrl", "getDashboard", "get", "concat", "getUsers", "getUserRoles", "userId", "getUserPermissions", "getUserDirectPermissions", "getRoles", "getRolePermissions", "roleId", "getPermissions", "assignRole", "request", "post", "removeRole", "assignPermission", "removePermission", "assignUserPermission", "removeUserPermission", "assignRoleToUser", "<PERSON><PERSON><PERSON>", "user_id", "role", "removeRoleFromUser", "assignPermissionToRole", "permissionName", "role_id", "permission", "removePermissionFromRole", "assignPermissionToUser", "removePermissionFromUser", "adminService"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/services/admin.service.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { User, Role, Permission } from '../types';\nimport { api } from './base.service';\n\nexport interface DashboardStats {\n  total_users: number;\n  total_roles: number;\n  total_permissions: number;\n  active_users: number;\n  active_roles: number;\n  active_permissions: number;\n}\n\nexport interface AssignRoleRequest {\n  user_id: number;\n  role: string;\n}\n\nexport interface RemoveRoleRequest {\n  user_id: number;\n  role: string;\n}\n\nexport interface AssignPermissionRequest {\n  role_id: number;\n  permission: string;\n}\n\nexport interface RemovePermissionRequest {\n  role_id: number;\n  permission: string;\n}\n\nexport interface AssignUserPermissionRequest {\n  user_id: number;\n  permission: string;\n}\n\nexport interface RemoveUserPermissionRequest {\n  user_id: number;\n  permission: string;\n}\n\nclass AdminService {\n  private baseUrl = '/admin';\n\n  // Dashboard\n  getDashboard(): Promise<AxiosResponse<DashboardStats>> {\n    return api.get(`${this.baseUrl}/dashboard`);\n  }\n\n  // Users\n  getUsers(): Promise<AxiosResponse<User[]>> {\n    return api.get(`${this.baseUrl}/users`);\n  }\n\n  getUserRoles(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/user/${userId}/roles`);\n  }\n\n  getUserPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/user/${userId}/permissions`);\n  }\n\n  getUserDirectPermissions(userId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/user/${userId}/direct-permissions`);\n  }\n\n  // Roles\n  getRoles(): Promise<AxiosResponse<Role[]>> {\n    return api.get(`${this.baseUrl}/roles`);\n  }\n\n  getRolePermissions(roleId: number): Promise<AxiosResponse<string[]>> {\n    return api.get(`${this.baseUrl}/role/${roleId}/permissions`);\n  }\n\n  // Permissions\n  getPermissions(): Promise<AxiosResponse<Permission[]>> {\n    return api.get(`${this.baseUrl}/permissions`);\n  }\n\n  // Role assignments\n  assignRole(request: AssignRoleRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/assign-role`, request);\n  }\n\n  removeRole(request: RemoveRoleRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/remove-role`, request);\n  }\n\n  // Permission assignments to roles\n  assignPermission(request: AssignPermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/assign-permission`, request);\n  }\n\n  removePermission(request: RemovePermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/remove-permission`, request);\n  }\n\n  // Direct permission assignments to users\n  assignUserPermission(request: AssignUserPermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/assign-user-permission`, request);\n  }\n\n  removeUserPermission(request: RemoveUserPermissionRequest): Promise<AxiosResponse<{ message: string }>> {\n    return api.post(`${this.baseUrl}/remove-user-permission`, request);\n  }\n\n  // Convenience methods\n  assignRoleToUser(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.assignRole({ user_id: userId, role: roleName });\n  }\n\n  removeRoleFromUser(userId: number, roleName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.removeRole({ user_id: userId, role: roleName });\n  }\n\n  assignPermissionToRole(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.assignPermission({ role_id: roleId, permission: permissionName });\n  }\n\n  removePermissionFromRole(roleId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.removePermission({ role_id: roleId, permission: permissionName });\n  }\n\n  assignPermissionToUser(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.assignUserPermission({ user_id: userId, permission: permissionName });\n  }\n\n  removePermissionFromUser(userId: number, permissionName: string): Promise<AxiosResponse<{ message: string }>> {\n    return this.removeUserPermission({ user_id: userId, permission: permissionName });\n  }\n}\n\nexport const adminService = new AdminService();\nexport default adminService;\n"], "mappings": "AAEA,OAASA,GAAG,KAAQ,gBAAgB,CAyCpC,KAAM,CAAAC,YAAa,CAAAC,YAAA,OACTC,OAAO,CAAG,QAAQ,EAE1B;AACAC,YAAYA,CAAA,CAA2C,CACrD,MAAO,CAAAJ,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,cAAY,CAAC,CAC7C,CAEA;AACAI,QAAQA,CAAA,CAAmC,CACzC,MAAO,CAAAP,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,UAAQ,CAAC,CACzC,CAEAK,YAAYA,CAACC,MAAc,CAAoC,CAC7D,MAAO,CAAAT,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,WAAAG,MAAA,CAASG,MAAM,UAAQ,CAAC,CACxD,CAEAC,kBAAkBA,CAACD,MAAc,CAAoC,CACnE,MAAO,CAAAT,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,WAAAG,MAAA,CAASG,MAAM,gBAAc,CAAC,CAC9D,CAEAE,wBAAwBA,CAACF,MAAc,CAAoC,CACzE,MAAO,CAAAT,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,WAAAG,MAAA,CAASG,MAAM,uBAAqB,CAAC,CACrE,CAEA;AACAG,QAAQA,CAAA,CAAmC,CACzC,MAAO,CAAAZ,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,UAAQ,CAAC,CACzC,CAEAU,kBAAkBA,CAACC,MAAc,CAAoC,CACnE,MAAO,CAAAd,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,WAAAG,MAAA,CAASQ,MAAM,gBAAc,CAAC,CAC9D,CAEA;AACAC,cAAcA,CAAA,CAAyC,CACrD,MAAO,CAAAf,GAAG,CAACK,GAAG,IAAAC,MAAA,CAAI,IAAI,CAACH,OAAO,gBAAc,CAAC,CAC/C,CAEA;AACAa,UAAUA,CAACC,OAA0B,CAA+C,CAClF,MAAO,CAAAjB,GAAG,CAACkB,IAAI,IAAAZ,MAAA,CAAI,IAAI,CAACH,OAAO,iBAAgBc,OAAO,CAAC,CACzD,CAEAE,UAAUA,CAACF,OAA0B,CAA+C,CAClF,MAAO,CAAAjB,GAAG,CAACkB,IAAI,IAAAZ,MAAA,CAAI,IAAI,CAACH,OAAO,iBAAgBc,OAAO,CAAC,CACzD,CAEA;AACAG,gBAAgBA,CAACH,OAAgC,CAA+C,CAC9F,MAAO,CAAAjB,GAAG,CAACkB,IAAI,IAAAZ,MAAA,CAAI,IAAI,CAACH,OAAO,uBAAsBc,OAAO,CAAC,CAC/D,CAEAI,gBAAgBA,CAACJ,OAAgC,CAA+C,CAC9F,MAAO,CAAAjB,GAAG,CAACkB,IAAI,IAAAZ,MAAA,CAAI,IAAI,CAACH,OAAO,uBAAsBc,OAAO,CAAC,CAC/D,CAEA;AACAK,oBAAoBA,CAACL,OAAoC,CAA+C,CACtG,MAAO,CAAAjB,GAAG,CAACkB,IAAI,IAAAZ,MAAA,CAAI,IAAI,CAACH,OAAO,4BAA2Bc,OAAO,CAAC,CACpE,CAEAM,oBAAoBA,CAACN,OAAoC,CAA+C,CACtG,MAAO,CAAAjB,GAAG,CAACkB,IAAI,IAAAZ,MAAA,CAAI,IAAI,CAACH,OAAO,4BAA2Bc,OAAO,CAAC,CACpE,CAEA;AACAO,gBAAgBA,CAACf,MAAc,CAAEgB,QAAgB,CAA+C,CAC9F,MAAO,KAAI,CAACT,UAAU,CAAC,CAAEU,OAAO,CAAEjB,MAAM,CAAEkB,IAAI,CAAEF,QAAS,CAAC,CAAC,CAC7D,CAEAG,kBAAkBA,CAACnB,MAAc,CAAEgB,QAAgB,CAA+C,CAChG,MAAO,KAAI,CAACN,UAAU,CAAC,CAAEO,OAAO,CAAEjB,MAAM,CAAEkB,IAAI,CAAEF,QAAS,CAAC,CAAC,CAC7D,CAEAI,sBAAsBA,CAACf,MAAc,CAAEgB,cAAsB,CAA+C,CAC1G,MAAO,KAAI,CAACV,gBAAgB,CAAC,CAAEW,OAAO,CAAEjB,MAAM,CAAEkB,UAAU,CAAEF,cAAe,CAAC,CAAC,CAC/E,CAEAG,wBAAwBA,CAACnB,MAAc,CAAEgB,cAAsB,CAA+C,CAC5G,MAAO,KAAI,CAACT,gBAAgB,CAAC,CAAEU,OAAO,CAAEjB,MAAM,CAAEkB,UAAU,CAAEF,cAAe,CAAC,CAAC,CAC/E,CAEAI,sBAAsBA,CAACzB,MAAc,CAAEqB,cAAsB,CAA+C,CAC1G,MAAO,KAAI,CAACR,oBAAoB,CAAC,CAAEI,OAAO,CAAEjB,MAAM,CAAEuB,UAAU,CAAEF,cAAe,CAAC,CAAC,CACnF,CAEAK,wBAAwBA,CAAC1B,MAAc,CAAEqB,cAAsB,CAA+C,CAC5G,MAAO,KAAI,CAACP,oBAAoB,CAAC,CAAEG,OAAO,CAAEjB,MAAM,CAAEuB,UAAU,CAAEF,cAAe,CAAC,CAAC,CACnF,CACF,CAEA,MAAO,MAAM,CAAAM,YAAY,CAAG,GAAI,CAAAnC,YAAY,CAAC,CAAC,CAC9C,cAAe,CAAAmC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}