package models

// Role represents a user role in the system
type Role struct {
	BaseModel
	Name        string `json:"name" gorm:"unique;not null" binding:"required,min=2,max=50"`
	Description string `json:"description" gorm:"type:text"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// Associations
	Users       []User       `json:"users,omitempty" gorm:"many2many:user_roles;"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
}

// RoleCreateRequest defines the request structure for creating a role
type RoleCreateRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active"`
}

// RoleUpdateRequest defines the request structure for updating a role
type RoleUpdateRequest struct {
	Name        string `json:"name,omitempty" binding:"omitempty,min=2,max=50"`
	Description string `json:"description,omitempty"`
	IsActive    *bool  `json:"is_active,omitempty"`
}

type UserRole struct {
	ID     uint `json:"id" gorm:"primaryKey"`
	UserID uint `json:"user_id"`
	RoleID uint `json:"role_id"`
}
