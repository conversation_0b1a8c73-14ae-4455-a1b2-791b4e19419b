package models

import (
	"time"
	"gorm.io/gorm"
)

// BaseModel defines the common fields for all models
type BaseModel struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// PaginationRequest defines pagination parameters
type PaginationRequest struct {
	Page     int `json:"page" form:"page" binding:"min=1"`         // Page number (1-based)
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"` // Items per page
}

// PaginationResponse defines pagination metadata in response
type PaginationResponse struct {
	Page       int   `json:"page"`        // Current page number
	PageSize   int   `json:"page_size"`   // Items per page
	Total      int64 `json:"total"`       // Total number of items
	TotalPages int   `json:"total_pages"` // Total number of pages
	HasNext    bool  `json:"has_next"`    // Whether there's a next page
	HasPrev    bool  `json:"has_prev"`    // Whether there's a previous page
}

// SearchOperator defines the type of search operation
type SearchOperator string

const (
	SearchOperatorEqual        SearchOperator = "eq"       // Exact match
	SearchOperatorNotEqual     SearchOperator = "ne"       // Not equal
	SearchOperatorLike         SearchOperator = "like"     // Partial match (contains)
	SearchOperatorILike        SearchOperator = "ilike"    // Case-insensitive partial match
	SearchOperatorGreaterThan  SearchOperator = "gt"       // Greater than
	SearchOperatorGreaterEqual SearchOperator = "gte"      // Greater than or equal
	SearchOperatorLessThan     SearchOperator = "lt"       // Less than
	SearchOperatorLessEqual    SearchOperator = "lte"      // Less than or equal
	SearchOperatorIn           SearchOperator = "in"       // In array
	SearchOperatorNotIn        SearchOperator = "not_in"   // Not in array
	SearchOperatorIsNull       SearchOperator = "is_null"  // Is null
	SearchOperatorIsNotNull    SearchOperator = "not_null" // Is not null
)

// SearchFilter defines a single search filter
type SearchFilter struct {
	Field    string         `json:"field" form:"field"`       // Database column name
	Operator SearchOperator `json:"operator" form:"operator"` // Search operator
	Value    interface{}    `json:"value" form:"value"`       // Search value
}

// SearchRequest defines multi-column search parameters
type SearchRequest struct {
	Filters []SearchFilter `json:"filters" form:"filters"` // Array of search filters
	OrderBy string         `json:"order_by" form:"order_by"` // Order by field
	OrderDir string        `json:"order_dir" form:"order_dir"` // Order direction (asc/desc)
}

// CRUDRequest combines pagination and search
type CRUDRequest struct {
	PaginationRequest
	SearchRequest
}

// CRUDResponse defines the standard response format for CRUD operations
type CRUDResponse struct {
	Data       interface{}         `json:"data"`
	Pagination *PaginationResponse `json:"pagination,omitempty"`
	Message    string              `json:"message,omitempty"`
}

// CRUDListResponse defines the response format for list operations
type CRUDListResponse struct {
	Data       interface{}        `json:"data"`
	Pagination PaginationResponse `json:"pagination"`
}

// ErrorResponse defines the standard error response format
type ErrorResponse struct {
	Error   string      `json:"error"`
	Details interface{} `json:"details,omitempty"`
	Code    string      `json:"code,omitempty"`
}

// ValidateSearchFilter validates a search filter
func (sf *SearchFilter) Validate() error {
	if sf.Field == "" {
		return &ValidationError{Field: "field", Message: "Field is required"}
	}
	
	// Validate operator
	validOperators := []SearchOperator{
		SearchOperatorEqual, SearchOperatorNotEqual, SearchOperatorLike, SearchOperatorILike,
		SearchOperatorGreaterThan, SearchOperatorGreaterEqual, SearchOperatorLessThan, SearchOperatorLessEqual,
		SearchOperatorIn, SearchOperatorNotIn, SearchOperatorIsNull, SearchOperatorIsNotNull,
	}
	
	isValidOperator := false
	for _, op := range validOperators {
		if sf.Operator == op {
			isValidOperator = true
			break
		}
	}
	
	if !isValidOperator {
		return &ValidationError{Field: "operator", Message: "Invalid search operator"}
	}
	
	// Validate value based on operator
	if sf.Operator != SearchOperatorIsNull && sf.Operator != SearchOperatorIsNotNull && sf.Value == nil {
		return &ValidationError{Field: "value", Message: "Value is required for this operator"}
	}
	
	return nil
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return e.Message
}

// SetDefaultPagination sets default values for pagination
func (pr *PaginationRequest) SetDefaults() {
	if pr.Page <= 0 {
		pr.Page = 1
	}
	if pr.PageSize <= 0 {
		pr.PageSize = 10
	}
	if pr.PageSize > 100 {
		pr.PageSize = 100
	}
}

// CalculatePagination calculates pagination metadata
func CalculatePagination(page, pageSize int, total int64) PaginationResponse {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	if totalPages == 0 {
		totalPages = 1
	}
	
	return PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// SetDefaultSearch sets default values for search
func (sr *SearchRequest) SetDefaults() {
	if sr.OrderDir == "" {
		sr.OrderDir = "asc"
	}
	if sr.OrderDir != "asc" && sr.OrderDir != "desc" {
		sr.OrderDir = "asc"
	}
}
