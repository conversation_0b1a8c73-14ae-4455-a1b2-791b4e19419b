package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"strings"

	"jwt-auth-backend/database"
	"jwt-auth-backend/handlers"
	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

// TestProduct model for testing CRUD operations
type TestProduct struct {
	models.BaseModel
	Name        string  `json:"name" gorm:"not null" binding:"required"`
	Description string  `json:"description"`
	Price       float64 `json:"price" gorm:"not null" binding:"required,min=0"`
	CategoryID  uint    `json:"category_id"`
	IsActive    bool    `json:"is_active" gorm:"default:true"`
}

func main() {
	fmt.Println("🚀 Starting Base CRUD Integration Test")

	// Initialize environment
	if err := godotenv.Load("../../config.env"); err != nil {
		log.Println("No config.env file found, using default values")
	}

	// Initialize logger
	utils.InitLogger()
	logger := utils.GetLogger()
	defer logger.Sync()

	// Initialize database
	database.InitDB()

	// Auto-migrate test model
	if err := database.DB.AutoMigrate(&TestProduct{}); err != nil {
		log.Fatal("Failed to migrate TestProduct:", err)
	}

	// Clean up any existing test data
	database.DB.Where("1 = 1").Delete(&TestProduct{})

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	r := gin.New()

	// Create CRUD handler for TestProduct
	productHandler := handlers.NewBaseCRUDHandler(&TestProduct{})

	// Setup routes
	v1 := r.Group("/api/v1")
	productHandler.SetupCRUDRoutes(v1, "/products")

	fmt.Println("✅ Setup completed, starting tests...")

	// Run tests
	testCreateProduct(r)
	testGetProduct(r)
	testListProducts(r)
	testSearchProducts(r)
	testUpdateProduct(r)
	testBatchDelete(r)
	testGetFieldNames(r)

	// Demonstrate search operators
	fmt.Println("\n" + strings.Repeat("=", 50))
	demonstrateSearchOperators(r)

	fmt.Println("🎉 All tests completed successfully!")
}

func testCreateProduct(r *gin.Engine) {
	fmt.Println("\n📝 Testing Create Product...")

	product := TestProduct{
		Name:        "Test Laptop",
		Description: "High-performance test laptop",
		Price:       999.99,
		CategoryID:  1,
		IsActive:    true,
	}

	jsonData, _ := json.Marshal(product)
	req, _ := http.NewRequest("POST", "/api/v1/products", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	if w.Code != http.StatusCreated {
		log.Fatalf("Expected status 201, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)

	fmt.Printf("✅ Product created successfully: %s\n", response.Message)
}

func testGetProduct(r *gin.Engine) {
	fmt.Println("\n🔍 Testing Get Product by ID...")

	req, _ := http.NewRequest("GET", "/api/v1/products/1", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)

	fmt.Println("✅ Product retrieved successfully")
}

func testListProducts(r *gin.Engine) {
	fmt.Println("\n📋 Testing List Products with Pagination...")

	// Create a few more products for testing
	products := []TestProduct{
		{Name: "Gaming Mouse", Price: 49.99, CategoryID: 2},
		{Name: "Mechanical Keyboard", Price: 129.99, CategoryID: 2},
		{Name: "4K Monitor", Price: 299.99, CategoryID: 3},
	}

	for _, product := range products {
		jsonData, _ := json.Marshal(product)
		req, _ := http.NewRequest("POST", "/api/v1/products", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
	}

	// Test pagination
	req, _ := http.NewRequest("GET", "/api/v1/products?page=1&page_size=2&order_by=name&order_dir=asc", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDListResponse
	json.Unmarshal(w.Body.Bytes(), &response)

	fmt.Printf("✅ Products listed successfully. Total: %d, Page: %d, PageSize: %d\n",
		response.Pagination.Total, response.Pagination.Page, response.Pagination.PageSize)
}

func testSearchProducts(r *gin.Engine) {
	fmt.Println("\n🔎 Testing Advanced Search...")

	searchReq := models.CRUDRequest{
		PaginationRequest: models.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
		SearchRequest: models.SearchRequest{
			Filters: []models.SearchFilter{
				{
					Field:    "price",
					Operator: models.SearchOperatorGreaterEqual,
					Value:    100.0,
				},
				{
					Field:    "name",
					Operator: models.SearchOperatorILike,
					Value:    "laptop",
				},
			},
			OrderBy:  "price",
			OrderDir: "desc",
		},
	}

	jsonData, _ := json.Marshal(searchReq)
	req, _ := http.NewRequest("POST", "/api/v1/products/search", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDListResponse
	json.Unmarshal(w.Body.Bytes(), &response)

	fmt.Printf("✅ Search completed successfully. Found: %d products\n", response.Pagination.Total)
}

func testUpdateProduct(r *gin.Engine) {
	fmt.Println("\n✏️ Testing Update Product...")

	updates := map[string]interface{}{
		"name":        "Updated Test Laptop",
		"description": "Updated high-performance laptop",
		"price":       1199.99,
	}

	jsonData, _ := json.Marshal(updates)
	req, _ := http.NewRequest("PUT", "/api/v1/products/1", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)

	fmt.Printf("✅ Product updated successfully: %s\n", response.Message)
}

func testBatchDelete(r *gin.Engine) {
	fmt.Println("\n🗑️ Testing Batch Delete...")

	batchReq := map[string][]uint{
		"ids": {2, 3},
	}

	jsonData, _ := json.Marshal(batchReq)
	req, _ := http.NewRequest("DELETE", "/api/v1/products/batch", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response models.CRUDResponse
	json.Unmarshal(w.Body.Bytes(), &response)

	fmt.Printf("✅ Batch delete completed: %s\n", response.Message)
}

func testGetFieldNames(r *gin.Engine) {
	fmt.Println("\n📊 Testing Get Field Names...")

	req, _ := http.NewRequest("GET", "/api/v1/products/fields", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		log.Fatalf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)

	fields := response["fields"].([]interface{})
	fmt.Printf("✅ Field names retrieved: %v\n", fields)
}

// Demonstration of different search operators
func demonstrateSearchOperators(r *gin.Engine) {
	fmt.Println("\n🔍 Demonstrating Search Operators...")

	searchExamples := []struct {
		name     string
		operator models.SearchOperator
		field    string
		value    interface{}
	}{
		{"Exact match", models.SearchOperatorEqual, "category_id", 1},
		{"Not equal", models.SearchOperatorNotEqual, "category_id", 1},
		{"Contains (case-sensitive)", models.SearchOperatorLike, "name", "Laptop"},
		{"Contains (case-insensitive)", models.SearchOperatorILike, "name", "laptop"},
		{"Greater than", models.SearchOperatorGreaterThan, "price", 100.0},
		{"Less than or equal", models.SearchOperatorLessEqual, "price", 500.0},
		{"In array", models.SearchOperatorIn, "category_id", []uint{1, 2, 3}},
		{"Not null", models.SearchOperatorIsNotNull, "description", nil},
	}

	for _, example := range searchExamples {
		fmt.Printf("  Testing: %s\n", example.name)

		searchReq := models.CRUDRequest{
			PaginationRequest: models.PaginationRequest{Page: 1, PageSize: 5},
			SearchRequest: models.SearchRequest{
				Filters: []models.SearchFilter{
					{
						Field:    example.field,
						Operator: example.operator,
						Value:    example.value,
					},
				},
			},
		}

		jsonData, _ := json.Marshal(searchReq)
		req, _ := http.NewRequest("POST", "/api/v1/products/search", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			var response models.CRUDListResponse
			json.Unmarshal(w.Body.Bytes(), &response)
			fmt.Printf("    ✅ Found %d results\n", response.Pagination.Total)
		} else {
			fmt.Printf("    ❌ Failed with status %d\n", w.Code)
		}
	}
}
