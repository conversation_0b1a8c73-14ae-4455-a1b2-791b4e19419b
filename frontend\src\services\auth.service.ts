import axios, { AxiosResponse } from 'axios';
import { User, AuthResponse } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for automatic token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
          refresh_token: refreshToken,
        });

        const { token: newAccessToken, refresh_token: newRefreshToken } = response.data;
        
        localStorage.setItem('access_token', newAccessToken);
        localStorage.setItem('refresh_token', newRefreshToken);

        // Retry the original request with new token
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export const authService = {
  // Login user
  login: (credentials: LoginRequest): Promise<AxiosResponse<AuthResponse>> => {
    return api.post('/auth/login', credentials);
  },

  // Register user
  register: (userData: RegisterRequest): Promise<AxiosResponse<AuthResponse>> => {
    return api.post('/auth/register', userData);
  },

  // Refresh token
  refreshToken: (refreshToken: string): Promise<AxiosResponse<AuthResponse>> => {
    return api.post('/auth/refresh', { refresh_token: refreshToken });
  },

  // Logout user
  logout: (): Promise<AxiosResponse<{ message: string }>> => {
    return api.post('/auth/logout');
  },

  // Revoke all tokens
  revokeAllTokens: (): Promise<AxiosResponse<{ message: string }>> => {
    return api.post('/auth/revoke-all');
  },

  // Get user profile
  getProfile: (): Promise<AxiosResponse<{ user: User; roles: string[] }>> => {
    return api.get('/user/profile');
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('access_token');
    return !!token;
  },

  // Get current user from localStorage
  getCurrentUser: (): User | null => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user has specific role
  hasRole: (role: string): boolean => {
    const user = authService.getCurrentUser();
    return user?.roles?.includes(role) || false;
  },

  // Check if user is admin
  isAdmin: (): boolean => {
    return authService.hasRole('admin');
  },

  // Clear authentication data
  clearAuth: (): void => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
  },

  // Store authentication data
  storeAuth: (authData: AuthResponse): void => {
    localStorage.setItem('access_token', authData.token);
    localStorage.setItem('refresh_token', authData.refresh_token);
    localStorage.setItem('user', JSON.stringify(authData.user));
  }
};

export default authService;
