import React from 'react';
import { Link } from 'react-router-dom';
import { User } from '../types';

interface NavbarProps {
  user: User | null;
  onLogout: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ user, onLogout }) => {
  const isAdmin = user && user.roles && user.roles.includes('admin');
  return (
    <nav className="navbar">
      <div className="container">
        <h1>JWT Auth App</h1>
        <div>
          {isAdmin && (
            <Link to="/admin/users" style={{ color: 'white', marginRight: 20, textDecoration: 'underline' }}>
              Admin
            </Link>
          )}
          {user && (
            <>
              <span style={{ marginRight: '15px' }}>
                Welcome, {user.username}!
              </span>
              <button onClick={onLogout}>Logout</button>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar; 