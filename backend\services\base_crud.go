package services

import (
	"fmt"
	"reflect"
	"strings"

	"jwt-auth-backend/models"
	"jwt-auth-backend/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// BaseCRUDService provides generic CRUD operations for any GORM model
type BaseCRUDService struct {
	DB     *gorm.DB
	Logger *zap.Logger
}

// NewBaseCRUDService creates a new instance of BaseCRUDService
func NewBaseCRUDService(db *gorm.DB) *BaseCRUDService {
	return &BaseCRUDService{
		DB:     db,
		Logger: utils.GetLogger(),
	}
}

// Create creates a new record
func (s *BaseCRUDService) Create(model interface{}) error {
	s.Logger.Debug("Creating new record", zap.String("type", reflect.TypeOf(model).String()))

	if err := s.DB.Create(model).Error; err != nil {
		s.Logger.Error("Failed to create record",
			zap.Error(err),
			zap.String("type", reflect.TypeOf(model).String()),
		)
		return err
	}

	s.Logger.Info("Record created successfully", zap.String("type", reflect.TypeOf(model).String()))
	return nil
}

// GetByID retrieves a record by ID
func (s *BaseCRUDService) GetByID(model interface{}, id uint) error {
	s.Logger.Debug("Retrieving record by ID",
		zap.Uint("id", id),
		zap.String("type", reflect.TypeOf(model).String()),
	)

	if err := s.DB.First(model, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			s.Logger.Warn("Record not found",
				zap.Uint("id", id),
				zap.String("type", reflect.TypeOf(model).String()),
			)
		} else {
			s.Logger.Error("Failed to retrieve record",
				zap.Error(err),
				zap.Uint("id", id),
				zap.String("type", reflect.TypeOf(model).String()),
			)
		}
		return err
	}

	s.Logger.Debug("Record retrieved successfully",
		zap.Uint("id", id),
		zap.String("type", reflect.TypeOf(model).String()),
	)
	return nil
}

// Update updates an existing record
func (s *BaseCRUDService) Update(model interface{}, id uint, updates interface{}) error {
	s.Logger.Debug("Updating record",
		zap.Uint("id", id),
		zap.String("type", reflect.TypeOf(model).String()),
	)

	result := s.DB.Model(model).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		s.Logger.Error("Failed to update record",
			zap.Error(result.Error),
			zap.Uint("id", id),
			zap.String("type", reflect.TypeOf(model).String()),
		)
		return result.Error
	}

	if result.RowsAffected == 0 {
		s.Logger.Warn("No record updated - record not found",
			zap.Uint("id", id),
			zap.String("type", reflect.TypeOf(model).String()),
		)
		return gorm.ErrRecordNotFound
	}

	s.Logger.Info("Record updated successfully",
		zap.Uint("id", id),
		zap.Int64("rows_affected", result.RowsAffected),
		zap.String("type", reflect.TypeOf(model).String()),
	)
	return nil
}

// Delete soft deletes a record
func (s *BaseCRUDService) Delete(model interface{}, id uint) error {
	s.Logger.Debug("Deleting record",
		zap.Uint("id", id),
		zap.String("type", reflect.TypeOf(model).String()),
	)

	result := s.DB.Delete(model, id)
	if result.Error != nil {
		s.Logger.Error("Failed to delete record",
			zap.Error(result.Error),
			zap.Uint("id", id),
			zap.String("type", reflect.TypeOf(model).String()),
		)
		return result.Error
	}

	if result.RowsAffected == 0 {
		s.Logger.Warn("No record deleted - record not found",
			zap.Uint("id", id),
			zap.String("type", reflect.TypeOf(model).String()),
		)
		return gorm.ErrRecordNotFound
	}

	s.Logger.Info("Record deleted successfully",
		zap.Uint("id", id),
		zap.Int64("rows_affected", result.RowsAffected),
		zap.String("type", reflect.TypeOf(model).String()),
	)
	return nil
}

// List retrieves records with pagination and search
func (s *BaseCRUDService) List(model interface{}, req models.CRUDRequest) (interface{}, models.PaginationResponse, error) {
	// Set defaults
	req.PaginationRequest.SetDefaults()
	req.SearchRequest.SetDefaults()

	s.Logger.Debug("Listing records",
		zap.String("type", reflect.TypeOf(model).String()),
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize),
		zap.Int("filter_count", len(req.Filters)),
	)

	// Create query
	query := s.DB.Model(model)

	// Apply search filters
	if err := s.applySearchFilters(query, req.SearchRequest); err != nil {
		s.Logger.Error("Failed to apply search filters", zap.Error(err))
		return nil, models.PaginationResponse{}, err
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		s.Logger.Error("Failed to count records", zap.Error(err))
		return nil, models.PaginationResponse{}, err
	}

	// Apply pagination
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// Apply ordering
	if req.OrderBy != "" {
		orderClause := fmt.Sprintf("%s %s", req.OrderBy, req.OrderDir)
		query = query.Order(orderClause)
	}

	// Create slice to hold results
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	sliceType := reflect.SliceOf(modelType)
	results := reflect.New(sliceType).Interface()

	// Execute query
	if err := query.Find(results).Error; err != nil {
		s.Logger.Error("Failed to retrieve records", zap.Error(err))
		return nil, models.PaginationResponse{}, err
	}

	// Calculate pagination
	pagination := models.CalculatePagination(req.Page, req.PageSize, total)

	s.Logger.Info("Records retrieved successfully",
		zap.String("type", reflect.TypeOf(model).String()),
		zap.Int64("total", total),
		zap.Int("returned", reflect.ValueOf(results).Elem().Len()),
	)

	return results, pagination, nil
}

// applySearchFilters applies search filters to the query
func (s *BaseCRUDService) applySearchFilters(query *gorm.DB, searchReq models.SearchRequest) error {
	for _, filter := range searchReq.Filters {
		if err := filter.Validate(); err != nil {
			return err
		}

		switch filter.Operator {
		case models.SearchOperatorEqual:
			query = query.Where(fmt.Sprintf("%s = ?", filter.Field), filter.Value)
		case models.SearchOperatorNotEqual:
			query = query.Where(fmt.Sprintf("%s != ?", filter.Field), filter.Value)
		case models.SearchOperatorLike:
			query = query.Where(fmt.Sprintf("%s LIKE ?", filter.Field), fmt.Sprintf("%%%v%%", filter.Value))
		case models.SearchOperatorILike:
			query = query.Where(fmt.Sprintf("%s ILIKE ?", filter.Field), fmt.Sprintf("%%%v%%", filter.Value))
		case models.SearchOperatorGreaterThan:
			query = query.Where(fmt.Sprintf("%s > ?", filter.Field), filter.Value)
		case models.SearchOperatorGreaterEqual:
			query = query.Where(fmt.Sprintf("%s >= ?", filter.Field), filter.Value)
		case models.SearchOperatorLessThan:
			query = query.Where(fmt.Sprintf("%s < ?", filter.Field), filter.Value)
		case models.SearchOperatorLessEqual:
			query = query.Where(fmt.Sprintf("%s <= ?", filter.Field), filter.Value)
		case models.SearchOperatorIn:
			query = query.Where(fmt.Sprintf("%s IN ?", filter.Field), filter.Value)
		case models.SearchOperatorNotIn:
			query = query.Where(fmt.Sprintf("%s NOT IN ?", filter.Field), filter.Value)
		case models.SearchOperatorIsNull:
			query = query.Where(fmt.Sprintf("%s IS NULL", filter.Field))
		case models.SearchOperatorIsNotNull:
			query = query.Where(fmt.Sprintf("%s IS NOT NULL", filter.Field))
		default:
			return fmt.Errorf("unsupported search operator: %s", filter.Operator)
		}
	}

	return nil
}

// ValidateModel validates that the model has required fields
func (s *BaseCRUDService) ValidateModel(model interface{}) error {
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	// Check if model has ID field
	if _, found := modelType.FieldByName("ID"); !found {
		return fmt.Errorf("model must have an ID field")
	}

	return nil
}

// GetCount returns the total count of records matching the search criteria
func (s *BaseCRUDService) GetCount(model interface{}, searchReq models.SearchRequest) (int64, error) {
	s.Logger.Debug("Counting records", zap.String("type", reflect.TypeOf(model).String()))

	query := s.DB.Model(model)

	// Apply search filters
	if err := s.applySearchFilters(query, searchReq); err != nil {
		s.Logger.Error("Failed to apply search filters for count", zap.Error(err))
		return 0, err
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		s.Logger.Error("Failed to count records", zap.Error(err))
		return 0, err
	}

	s.Logger.Debug("Records counted successfully",
		zap.String("type", reflect.TypeOf(model).String()),
		zap.Int64("count", count),
	)

	return count, nil
}

// Exists checks if a record exists with the given conditions
func (s *BaseCRUDService) Exists(model interface{}, conditions map[string]interface{}) (bool, error) {
	s.Logger.Debug("Checking record existence",
		zap.String("type", reflect.TypeOf(model).String()),
		zap.Any("conditions", conditions),
	)

	query := s.DB.Model(model)
	for field, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", field), value)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		s.Logger.Error("Failed to check record existence", zap.Error(err))
		return false, err
	}

	exists := count > 0
	s.Logger.Debug("Record existence check completed",
		zap.String("type", reflect.TypeOf(model).String()),
		zap.Bool("exists", exists),
	)

	return exists, nil
}

// BatchCreate creates multiple records in a single transaction
func (s *BaseCRUDService) BatchCreate(models interface{}) error {
	s.Logger.Debug("Batch creating records", zap.String("type", reflect.TypeOf(models).String()))

	if err := s.DB.Create(models).Error; err != nil {
		s.Logger.Error("Failed to batch create records",
			zap.Error(err),
			zap.String("type", reflect.TypeOf(models).String()),
		)
		return err
	}

	// Get count of created records
	modelsValue := reflect.ValueOf(models)
	if modelsValue.Kind() == reflect.Ptr {
		modelsValue = modelsValue.Elem()
	}
	count := modelsValue.Len()

	s.Logger.Info("Records batch created successfully",
		zap.String("type", reflect.TypeOf(models).String()),
		zap.Int("count", count),
	)
	return nil
}

// BatchDelete deletes multiple records by IDs
func (s *BaseCRUDService) BatchDelete(model interface{}, ids []uint) error {
	s.Logger.Debug("Batch deleting records",
		zap.String("type", reflect.TypeOf(model).String()),
		zap.Int("count", len(ids)),
	)

	if len(ids) == 0 {
		return fmt.Errorf("no IDs provided for batch delete")
	}

	result := s.DB.Delete(model, ids)
	if result.Error != nil {
		s.Logger.Error("Failed to batch delete records",
			zap.Error(result.Error),
			zap.String("type", reflect.TypeOf(model).String()),
		)
		return result.Error
	}

	s.Logger.Info("Records batch deleted successfully",
		zap.String("type", reflect.TypeOf(model).String()),
		zap.Int64("rows_affected", result.RowsAffected),
	)
	return nil
}

// GetFieldNames returns the field names of a model using reflection
func (s *BaseCRUDService) GetFieldNames(model interface{}) []string {
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	var fields []string
	for i := 0; i < modelType.NumField(); i++ {
		field := modelType.Field(i)

		// Skip unexported fields
		if !field.IsExported() {
			continue
		}

		// Get GORM column name or use field name
		gormTag := field.Tag.Get("gorm")
		if gormTag != "" && strings.Contains(gormTag, "column:") {
			// Extract column name from GORM tag
			parts := strings.Split(gormTag, ";")
			for _, part := range parts {
				if strings.HasPrefix(part, "column:") {
					columnName := strings.TrimPrefix(part, "column:")
					fields = append(fields, columnName)
					break
				}
			}
		} else {
			// Use snake_case conversion of field name
			fields = append(fields, toSnakeCase(field.Name))
		}
	}

	return fields
}

// toSnakeCase converts CamelCase to snake_case
func toSnakeCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}
