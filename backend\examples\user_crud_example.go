package examples

import (
	"jwt-auth-backend/handlers"
	"jwt-auth-backend/models"
	"jwt-auth-backend/middleware"
	"github.com/gin-gonic/gin"
)

// UserCRUDHandler extends the base CRUD handler with User-specific functionality
type UserCRUDHandler struct {
	*handlers.BaseCRUDHandler
}

// NewUserCRUDHandler creates a new User CRUD handler
func NewUserCRUDHandler() *UserCRUDHandler {
	return &UserCRUDHandler{
		BaseCRUDHandler: handlers.NewBaseCRUDHandler(&models.User{}),
	}
}

// SetupUserCRUDRoutes demonstrates how to set up CRUD routes for the User model
// This is an example of how you would integrate the base CRUD system with existing models
func SetupUserCRUDRoutes(r *gin.Engine) {
	// Create the User CRUD handler
	userHandler := NewUserCRUDHandler()
	
	// Setup protected routes (requires authentication)
	v1 := r.Group("/api/v1")
	v1.Use(middleware.AuthMiddleware()) // Apply authentication middleware
	
	// Admin-only routes (requires admin role)
	adminGroup := v1.Group("/admin")
	adminGroup.Use(middleware.RequireRoles("admin"))
	
	// Setup CRUD routes under /admin/users-crud
	// This provides an alternative to the existing /admin/users endpoints
	userHandler.SetupCRUDRoutes(adminGroup, "/users-crud")
	
	// The above creates these endpoints:
	// POST   /api/v1/admin/users-crud          - Create user
	// GET    /api/v1/admin/users-crud/:id      - Get user by ID  
	// PUT    /api/v1/admin/users-crud/:id      - Update user
	// DELETE /api/v1/admin/users-crud/:id      - Delete user
	// GET    /api/v1/admin/users-crud          - List users with pagination
	// POST   /api/v1/admin/users-crud/search   - Advanced user search
	// GET    /api/v1/admin/users-crud/fields   - Get user field names
	// DELETE /api/v1/admin/users-crud/batch    - Batch delete users
}

// Example of how to use the User CRUD endpoints:

/*
1. Create a new user:
POST /api/v1/admin/users-crud
Authorization: Bearer <admin-token>
Content-Type: application/json

{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "securepassword"
}

2. List users with pagination:
GET /api/v1/admin/users-crud?page=1&page_size=10&order_by=username&order_dir=asc
Authorization: Bearer <admin-token>

3. Search users by email domain:
POST /api/v1/admin/users-crud/search
Authorization: Bearer <admin-token>
Content-Type: application/json

{
    "page": 1,
    "page_size": 5,
    "filters": [
        {
            "field": "email",
            "operator": "like",
            "value": "@example.com"
        }
    ],
    "order_by": "created_at",
    "order_dir": "desc"
}

4. Search users created in the last month:
POST /api/v1/admin/users-crud/search
Authorization: Bearer <admin-token>
Content-Type: application/json

{
    "page": 1,
    "page_size": 10,
    "filters": [
        {
            "field": "created_at",
            "operator": "gte",
            "value": "2023-12-01T00:00:00Z"
        }
    ],
    "order_by": "created_at",
    "order_dir": "desc"
}

5. Get available field names for search:
GET /api/v1/admin/users-crud/fields
Authorization: Bearer <admin-token>

Response:
{
    "model": "User",
    "fields": ["id", "username", "email", "created_at", "updated_at"]
}

6. Update a user:
PUT /api/v1/admin/users-crud/123
Authorization: Bearer <admin-token>
Content-Type: application/json

{
    "username": "updatedusername",
    "email": "<EMAIL>"
}

7. Delete multiple users:
DELETE /api/v1/admin/users-crud/batch
Authorization: Bearer <admin-token>
Content-Type: application/json

{
    "ids": [123, 124, 125]
}

8. Get a specific user:
GET /api/v1/admin/users-crud/123
Authorization: Bearer <admin-token>

9. Delete a single user:
DELETE /api/v1/admin/users-crud/123
Authorization: Bearer <admin-token>
*/

// RoleCRUDHandler demonstrates CRUD for the Role model
type RoleCRUDHandler struct {
	*handlers.BaseCRUDHandler
}

func NewRoleCRUDHandler() *RoleCRUDHandler {
	return &RoleCRUDHandler{
		BaseCRUDHandler: handlers.NewBaseCRUDHandler(&models.Role{}),
	}
}

// PermissionCRUDHandler demonstrates CRUD for the Permission model
type PermissionCRUDHandler struct {
	*handlers.BaseCRUDHandler
}

func NewPermissionCRUDHandler() *PermissionCRUDHandler {
	return &PermissionCRUDHandler{
		BaseCRUDHandler: handlers.NewBaseCRUDHandler(&models.Permission{}),
	}
}

// SetupAllCRUDRoutes demonstrates setting up CRUD for all models
func SetupAllCRUDRoutes(r *gin.Engine) {
	// Create handlers
	userHandler := NewUserCRUDHandler()
	roleHandler := NewRoleCRUDHandler()
	permissionHandler := NewPermissionCRUDHandler()
	
	// Setup protected admin routes
	v1 := r.Group("/api/v1")
	v1.Use(middleware.AuthMiddleware())
	
	adminGroup := v1.Group("/admin")
	adminGroup.Use(middleware.RequireRoles("admin"))
	
	// Setup CRUD routes for all models
	userHandler.SetupCRUDRoutes(adminGroup, "/users-crud")
	roleHandler.SetupCRUDRoutes(adminGroup, "/roles-crud")
	permissionHandler.SetupCRUDRoutes(adminGroup, "/permissions-crud")
	
	// This creates comprehensive CRUD endpoints for:
	// - Users: /api/v1/admin/users-crud/*
	// - Roles: /api/v1/admin/roles-crud/*
	// - Permissions: /api/v1/admin/permissions-crud/*
}

// Advanced search examples for different use cases:

/*
Complex User Search Examples:

1. Find users by multiple criteria:
POST /api/v1/admin/users-crud/search
{
    "page": 1,
    "page_size": 20,
    "filters": [
        {
            "field": "username",
            "operator": "ilike",
            "value": "admin"
        },
        {
            "field": "created_at",
            "operator": "gte",
            "value": "2023-01-01T00:00:00Z"
        },
        {
            "field": "email",
            "operator": "not_null",
            "value": null
        }
    ],
    "order_by": "username",
    "order_dir": "asc"
}

2. Find users with specific IDs:
POST /api/v1/admin/users-crud/search
{
    "filters": [
        {
            "field": "id",
            "operator": "in",
            "value": [1, 2, 3, 5, 8]
        }
    ]
}

3. Find users excluding certain IDs:
POST /api/v1/admin/users-crud/search
{
    "filters": [
        {
            "field": "id",
            "operator": "not_in",
            "value": [1, 2, 3]
        }
    ]
}

4. Find users with email containing specific domain:
POST /api/v1/admin/users-crud/search
{
    "filters": [
        {
            "field": "email",
            "operator": "like",
            "value": "@company.com"
        }
    ]
}
*/
