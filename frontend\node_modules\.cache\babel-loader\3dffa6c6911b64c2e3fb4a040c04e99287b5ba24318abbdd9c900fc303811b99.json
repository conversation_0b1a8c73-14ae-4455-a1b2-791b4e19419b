{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Build-Project\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Navbar from './components/Navbar';\nimport AuthForm from './components/AuthForm';\nimport Profile from './components/Profile';\nimport TokenManager from './components/TokenManager';\nimport UserManagement from './components/admin/UserManagement';\nimport UserPermissionManagement from './components/admin/UserPermissionManagement';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const savedUser = localStorage.getItem('user');\n    if (token && savedUser) {\n      try {\n        setUser(JSON.parse(savedUser));\n      } catch (err) {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const handleAuthSuccess = userData => {\n    setUser(userData);\n  };\n  const handleLogout = () => {\n    setUser(null);\n  };\n\n  // Kiểm tra user có phải admin không\n  const isAdmin = user && user.roles && user.roles.includes('admin');\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '50px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      user: user,\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: user ? /*#__PURE__*/_jsxDEV(Profile, {\n          onLogout: handleLogout\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 41\n        }, this) : /*#__PURE__*/_jsxDEV(AuthForm, {\n          onAuthSuccess: handleAuthSuccess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 79\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/tokens\",\n        element: user ? /*#__PURE__*/_jsxDEV(TokenManager, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 47\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 66\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/users\",\n          element: /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/permissions\",\n          element: /*#__PURE__*/_jsxDEV(UserPermissionManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "AuthForm", "Profile", "TokenManager", "UserManagement", "UserPermissionManagement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "savedUser", "JSON", "parse", "err", "removeItem", "handleAuthSuccess", "userData", "handleLogout", "isAdmin", "roles", "includes", "className", "children", "style", "textAlign", "marginTop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLogout", "path", "element", "onAuthSuccess", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Build-Project/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Navbar from './components/Navbar';\r\nimport AuthForm from './components/AuthForm';\r\nimport Profile from './components/Profile';\r\nimport TokenManager from './components/TokenManager';\r\nimport UserManagement from './components/admin/UserManagement';\r\nimport UserPermissionManagement from './components/admin/UserPermissionManagement';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { User } from './types';\r\n\r\nconst App: React.FC = () => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem('token');\r\n    const savedUser = localStorage.getItem('user');\r\n    if (token && savedUser) {\r\n      try {\r\n        setUser(JSON.parse(savedUser));\r\n      } catch (err) {\r\n        localStorage.removeItem('token');\r\n        localStorage.removeItem('user');\r\n      }\r\n    }\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  const handleAuthSuccess = (userData: User) => {\r\n    setUser(userData);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    setUser(null);\r\n  };\r\n\r\n  // Kiểm tra user có phải admin không\r\n  const isAdmin = user && user.roles && user.roles.includes('admin');\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"container\">\r\n        <div style={{ textAlign: 'center', marginTop: '50px' }}>\r\n          <p>Loading...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Router>\r\n      <Navbar user={user} onLogout={handleLogout} />\r\n      <Routes>\r\n        <Route path=\"/\" element={user ? <Profile onLogout={handleLogout} /> : <AuthForm onAuthSuccess={handleAuthSuccess} />} />\r\n        <Route path=\"/tokens\" element={user ? <TokenManager /> : <Navigate to=\"/\" />} />\r\n        {isAdmin && (\r\n          <>\r\n            <Route path=\"/admin/users\" element={<UserManagement />} />\r\n            <Route path=\"/admin/permissions\" element={<UserPermissionManagement />} />\r\n          </>\r\n        )}\r\n        <Route path=\"*\" element={<Navigate to=\"/\" />} />\r\n      </Routes>\r\n    </Router>\r\n  );\r\n};\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,wBAAwB,MAAM,6CAA6C;AAClF,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGpF,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMsB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC9C,IAAIF,KAAK,IAAIG,SAAS,EAAE;MACtB,IAAI;QACFN,OAAO,CAACO,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;QAChCN,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAR,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,iBAAiB,GAAIC,QAAc,IAAK;IAC5CZ,OAAO,CAACY,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBb,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMc,OAAO,GAAGf,IAAI,IAAIA,IAAI,CAACgB,KAAK,IAAIhB,IAAI,CAACgB,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC;EAElE,IAAIf,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKuB,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBxB,OAAA;QAAKyB,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAH,QAAA,eACrDxB,OAAA;UAAAwB,QAAA,EAAG;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/B,OAAA,CAACL,MAAM;IAAA6B,QAAA,gBACLxB,OAAA,CAACZ,MAAM;MAACiB,IAAI,EAAEA,IAAK;MAAC2B,QAAQ,EAAEb;IAAa;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9C/B,OAAA,CAACJ,MAAM;MAAA4B,QAAA,gBACLxB,OAAA,CAACH,KAAK;QAACoC,IAAI,EAAC,GAAG;QAACC,OAAO,EAAE7B,IAAI,gBAAGL,OAAA,CAACV,OAAO;UAAC0C,QAAQ,EAAEb;QAAa;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/B,OAAA,CAACX,QAAQ;UAAC8C,aAAa,EAAElB;QAAkB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxH/B,OAAA,CAACH,KAAK;QAACoC,IAAI,EAAC,SAAS;QAACC,OAAO,EAAE7B,IAAI,gBAAGL,OAAA,CAACT,YAAY;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/B,OAAA,CAACF,QAAQ;UAACsC,EAAE,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC/EX,OAAO,iBACNpB,OAAA,CAAAE,SAAA;QAAAsB,QAAA,gBACExB,OAAA,CAACH,KAAK;UAACoC,IAAI,EAAC,cAAc;UAACC,OAAO,eAAElC,OAAA,CAACR,cAAc;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1D/B,OAAA,CAACH,KAAK;UAACoC,IAAI,EAAC,oBAAoB;UAACC,OAAO,eAAElC,OAAA,CAACP,wBAAwB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eAC1E,CACH,eACD/B,OAAA,CAACH,KAAK;QAACoC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAElC,OAAA,CAACF,QAAQ;UAACsC,EAAE,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAAC3B,EAAA,CAvDID,GAAa;AAAAkC,EAAA,GAAblC,GAAa;AAyDnB,eAAeA,GAAG;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}