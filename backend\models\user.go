package models

import "time"

type User struct {
	BaseModel
	Username string `json:"username" gorm:"unique;not null" binding:"required,min=3,max=50"`
	Email    string `json:"email" gorm:"unique;not null" binding:"required,email"`
	Password string `json:"-" gorm:"not null"` // "-" để không serialize password
	IsActive bool   `json:"is_active" gorm:"default:true"`

	// Associations
	Roles       []Role       `json:"roles,omitempty" gorm:"many2many:user_roles;"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:user_permissions;"`
}

type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// User CRUD DTOs
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	IsActive *bool  `json:"is_active"`
}

type UpdateUserRequest struct {
	Username string `json:"username" binding:"omitempty,min=3,max=50"`
	Email    string `json:"email" binding:"omitempty,email"`
	Password string `json:"password" binding:"omitempty,min=6"`
	IsActive *bool  `json:"is_active"`
}

type UserResponse struct {
	ID        uint      `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// Note: Roles and Permissions will be populated separately to avoid circular imports
}

type AssignRoleToUserRequest struct {
	RoleID uint `json:"role_id" binding:"required"`
}

type AssignPermissionToUserRequest struct {
	PermissionID uint `json:"permission_id" binding:"required"`
}
