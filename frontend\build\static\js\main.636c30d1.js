/*! For license information please see main.636c30d1.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!s.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:o,_owner:i.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var x=b.prototype=new v;x.constructor=b,m(x,y.prototype),x.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function N(e,t,r){var a,o={},l=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(l=""+t.key),t)S.call(t,a)&&!E.hasOwnProperty(a)&&(o[a]=t[a]);var s=arguments.length-2;if(1===s)o.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===o[a]&&(o[a]=s[a]);return{$$typeof:n,type:e,key:l,ref:i,props:o,_owner:k.current}}function _(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var j=/\/+/g;function C(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(e,t,a,o,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return l=l(s=e),e=""===o?"."+C(s,0):o,w(l)?(a="",null!=e&&(a=e.replace(j,"$&/")+"/"),P(l,t,a,"",function(e){return e})):null!=l&&(_(l)&&(l=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(l,a+(!l.key||s&&s.key===l.key?"":(""+l.key).replace(j,"$&/")+"/")+e)),t.push(l)),1;if(s=0,o=""===o?".":o+":",w(e))for(var u=0;u<e.length;u++){var c=o+C(i=e[u],u);s+=P(i,t,a,c,l)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=P(i=i.value,t,a,c=o+C(i,u++),l);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function R(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",function(e){return t.call(n,e,a++)}),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var T={current:null},L={transition:null},U={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:L,ReactCurrentOwner:k};function z(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:R,forEach:function(e,t,n){R(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return R(e,function(){t++}),t},toArray:function(e){return R(e,function(e){return e})||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=l,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=U,t.act=z,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),o=e.key,l=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,i=k.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)S.call(t,u)&&!E.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:o,ref:l,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=N,t.createFactory=function(e){var t=N.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=L.transition;L.transition={};try{e()}finally{L.transition=t}},t.unstable_act=z,t.useCallback=function(e,t){return T.current.useCallback(e,t)},t.useContext=function(e){return T.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return T.current.useDeferredValue(e)},t.useEffect=function(e,t){return T.current.useEffect(e,t)},t.useId=function(){return T.current.useId()},t.useImperativeHandle=function(e,t,n){return T.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return T.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return T.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return T.current.useMemo(e,t)},t.useReducer=function(e,t,n){return T.current.useReducer(e,t,n)},t.useRef=function(e){return T.current.useRef(e)},t.useState=function(e){return T.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return T.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return T.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>o(s,n))u<a&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,L(S);else{var t=r(c);null!==t&&U(w,t.startTime-e)}}function S(e,n){m=!1,g&&(g=!1,v(_),_=-1),h=!0;var o=p;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!P());){var l=f.callback;if("function"===typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(u)&&a(u),x(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&U(w,d.startTime-n),s=!1}return s}finally{f=null,p=o,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,E=!1,N=null,_=-1,j=5,C=-1;function P(){return!(t.unstable_now()-C<j)}function R(){if(null!==N){var e=t.unstable_now();C=e;var n=!0;try{n=N(!0,e)}finally{n?k():(E=!1,N=null)}}else E=!1}if("function"===typeof b)k=function(){b(R)};else if("undefined"!==typeof MessageChannel){var O=new MessageChannel,T=O.port2;O.port1.onmessage=R,k=function(){T.postMessage(null)}}else k=function(){y(R,0)};function L(e){N=e,E||(E=!0,k())}function U(e,n){_=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,L(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var l=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?l+o:l:o=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:i=o+i,sortIndex:-1},o>l?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(g?(v(_),_=-1):g=!0,U(w,o-l))):(e.sortIndex=i,n(u,e),m||h||(m=!0,L(S))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{var r=n(950);t.H=r.createRoot,r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)l.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,o,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),j=Symbol.for("react.context"),C=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),R=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),T=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var L=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var U=Symbol.iterator;function z(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=U&&e[U]||e["@@iterator"])?e:null}var D,F=Object.assign;function A(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var I=!1;function M(e,t){if(!e||I)return"";I=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),o=r.stack.split("\n"),l=a.length-1,i=o.length-1;1<=l&&0<=i&&a[l]!==o[i];)i--;for(;1<=l&&0<=i;l--,i--)if(a[l]!==o[i]){if(1!==l||1!==i)do{if(l--,0>--i||a[l]!==o[i]){var s="\n"+a[l].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=l&&0<=i);break}}}finally{I=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?A(e):""}function B(e){switch(e.tag){case 5:return A(e.type);case 16:return A("Lazy");case 13:return A("Suspense");case 19:return A("SuspenseList");case 0:case 2:case 15:return e=M(e.type,!1);case 11:return e=M(e.type.render,!1);case 1:return e=M(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case N:return"Profiler";case E:return"StrictMode";case P:return"Suspense";case R:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case j:return(e.displayName||"Context")+".Consumer";case _:return(e._context.displayName||"Context")+".Provider";case C:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return W(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function G(e,t){Y(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function oe(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function le(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){he.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ye=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Ee=null;function Ne(e){if(e=ba(e)){if("function"!==typeof Se)throw Error(o(280));var t=e.stateNode;t&&(t=wa(t),Se(e.stateNode,e.type,t))}}function _e(e){ke?Ee?Ee.push(e):Ee=[e]:ke=e}function je(){if(ke){var e=ke,t=Ee;if(Ee=ke=null,Ne(e),t)for(e=0;e<t.length;e++)Ne(t[e])}}function Ce(e,t){return e(t)}function Pe(){}var Re=!1;function Oe(e,t,n){if(Re)return e(t,n);Re=!0;try{return Ce(e,t,n)}finally{Re=!1,(null!==ke||null!==Ee)&&(Pe(),je())}}function Te(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Le=!1;if(c)try{var Ue={};Object.defineProperty(Ue,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Ue,Ue),window.removeEventListener("test",Ue,Ue)}catch(ce){Le=!1}function ze(e,t,n,r,a,o,l,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var De=!1,Fe=null,Ae=!1,Ie=null,Me={onError:function(e){De=!0,Fe=e}};function Be(e,t,n,r,a,o,l,i,s){De=!1,Fe=null,ze.apply(Me,arguments)}function We(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(We(e)!==e)throw Error(o(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=We(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return Ve(a),e;if(l===r)return Ve(a),t;l=l.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=l;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=l;break}if(s===r){i=!0,r=a,n=l;break}s=s.sibling}if(!i){for(s=l.child;s;){if(s===n){i=!0,n=l,r=a;break}if(s===r){i=!0,r=l,n=a;break}s=s.sibling}if(!i)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Je=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ye=a.unstable_now,Ge=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,ot=null;var lt=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/st|0)|0},it=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,l=268435455&n;if(0!==l){var i=l&~a;0!==i?r=dt(i):0!==(o&=l)&&(r=dt(o))}else 0!==(l=n&~a)?r=dt(l):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-lt(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-lt(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-lt(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,St,kt,Et,Nt,_t=!1,jt=[],Ct=null,Pt=null,Rt=null,Ot=new Map,Tt=new Map,Lt=[],Ut="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Rt=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Tt.delete(t.pointerId)}}function Dt(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Ft(e){var t=va(e.target);if(null!==t){var n=We(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Nt(e.priority,function(){kt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function At(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function It(e,t,n){At(e)&&n.delete(t)}function Mt(){_t=!1,null!==Ct&&At(Ct)&&(Ct=null),null!==Pt&&At(Pt)&&(Pt=null),null!==Rt&&At(Rt)&&(Rt=null),Ot.forEach(It),Tt.forEach(It)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,_t||(_t=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Mt)))}function Wt(e){function t(t){return Bt(t,e)}if(0<jt.length){Bt(jt[0],e);for(var n=1;n<jt.length;n++){var r=jt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ct&&Bt(Ct,e),null!==Pt&&Bt(Pt,e),null!==Rt&&Bt(Rt,e),Ot.forEach(t),Tt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)Ft(n),null===n.blockedOn&&Lt.shift()}var Ht=x.ReactCurrentBatchConfig,Vt=!0;function $t(e,t,n,r){var a=bt,o=Ht.transition;Ht.transition=null;try{bt=1,Qt(e,t,n,r)}finally{bt=a,Ht.transition=o}}function qt(e,t,n,r){var a=bt,o=Ht.transition;Ht.transition=null;try{bt=4,Qt(e,t,n,r)}finally{bt=a,Ht.transition=o}}function Qt(e,t,n,r){if(Vt){var a=Jt(e,t,n,r);if(null===a)Vr(e,t,r,Kt,n),zt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Ct=Dt(Ct,e,t,n,r,a),!0;case"dragenter":return Pt=Dt(Pt,e,t,n,r,a),!0;case"mouseover":return Rt=Dt(Rt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return Ot.set(o,Dt(Ot.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Tt.set(o,Dt(Tt.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(zt(e,r),4&t&&-1<Ut.indexOf(e)){for(;null!==a;){var o=ba(a);if(null!==o&&wt(o),null===(o=Jt(e,t,n,r))&&Vr(e,t,r,Kt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Vr(e,t,r,null,n)}}var Kt=null;function Jt(e,t,n,r){if(Kt=null,null!==(e=va(e=we(r))))if(null===(t=We(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ge()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Gt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Gt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[o-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,o){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,ln,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=F({},un,{view:0,detail:0}),fn=an(dn),pn=F({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Nn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(on=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=on=0,sn=e),on)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),hn=an(pn),mn=an(F({},pn,{dataTransfer:0})),gn=an(F({},dn,{relatedTarget:0})),yn=an(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(vn),xn=an(F({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function Nn(){return En}var _n=F({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Nn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),jn=an(_n),Cn=an(F({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=an(F({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Nn})),Rn=an(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=F({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Tn=an(On),Ln=[9,13,27,32],Un=c&&"CompositionEvent"in window,zn=null;c&&"documentMode"in document&&(zn=document.documentMode);var Dn=c&&"TextEvent"in window&&!zn,Fn=c&&(!Un||zn&&8<zn&&11>=zn),An=String.fromCharCode(32),In=!1;function Mn(e,t){switch(e){case"keyup":return-1!==Ln.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Wn=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function $n(e,t,n,r){_e(r),0<(t=qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Qn=null;function Kn(e){Ar(e,0)}function Jn(e){if(Q(xa(e)))return e}function Xn(e,t){if("change"===e)return t}var Yn=!1;if(c){var Gn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Gn=Zn}else Gn=!1;Yn=Gn&&(!document.documentMode||9<document.documentMode)}function tr(){qn&&(qn.detachEvent("onpropertychange",nr),Qn=qn=null)}function nr(e){if("value"===e.propertyName&&Jn(Qn)){var t=[];$n(t,Qn,e,we(e)),Oe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Jn(Qn)}function or(e,t){if("click"===e)return Jn(t)}function lr(e,t){if("input"===e||"change"===e)return Jn(t)}var ir="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(ir(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=cr(n,o);var l=cr(n,r);a&&l&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&sr(vr,r)||(vr=r,0<(r=qr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},kr={},Er={};function Nr(e){if(kr[e])return kr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return kr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var _r=Nr("animationend"),jr=Nr("animationiteration"),Cr=Nr("animationstart"),Pr=Nr("transitionend"),Rr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tr(e,t){Rr.set(e,t),s(t,[e])}for(var Lr=0;Lr<Or.length;Lr++){var Ur=Or[Lr];Tr(Ur.toLowerCase(),"on"+(Ur[0].toUpperCase()+Ur.slice(1)))}Tr(_r,"onAnimationEnd"),Tr(jr,"onAnimationIteration"),Tr(Cr,"onAnimationStart"),Tr("dblclick","onDoubleClick"),Tr("focusin","onFocus"),Tr("focusout","onBlur"),Tr(Pr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,l,i,s,u){if(Be.apply(this,arguments),De){if(!De)throw Error(o(198));var c=Fe;De=!1,Fe=null,Ae||(Ae=!0,Ie=c)}}(r,t,void 0,e),e.currentTarget=null}function Ar(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==o&&a.isPropagationStopped())break e;Fr(a,i,u),o=s}else for(l=0;l<r.length;l++){if(s=(i=r[l]).instance,u=i.currentTarget,i=i.listener,s!==o&&a.isPropagationStopped())break e;Fr(a,i,u),o=s}}}if(Ae)throw e=Ie,Ae=!1,Ie=null,e}function Ir(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Mr(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[Br]){e[Br]=!0,l.forEach(function(t){"selectionchange"!==t&&(Dr.has(t)||Mr(t,!1,e),Mr(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Mr("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Xt(t)){case 1:var a=$t;break;case 4:a=qt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Vr(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===l)for(l=r.return;null!==l;){var s=l.tag;if((3===s||4===s)&&((s=l.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;l=l.return}for(;null!==i;){if(null===(l=va(i)))return;if(5===(s=l.tag)||6===s){r=o=l;continue e}i=i.parentNode}}r=r.return}Oe(function(){var r=o,a=we(n),l=[];e:{var i=Rr.get(e);if(void 0!==i){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=jn;break;case"focusin":u="focus",s=gn;break;case"focusout":u="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Pn;break;case _r:case jr:case Cr:s=yn;break;case Pr:s=Rn;break;case"scroll":s=fn;break;case"wheel":s=Tn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Cn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Te(h,f))&&c.push($r(h,m,p)))),d)break;h=h.return}0<c.length&&(i=new s(i,u,null,n,a),l.push({event:i,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!va(u)&&!u[ha])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?va(u):null)&&(u!==(d=We(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Cn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?i:xa(s),p=null==u?i:xa(u),(i=new c(m,h+"leave",s,n,a)).target=d,i.relatedTarget=p,m=null,va(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=Qr(p))h++;for(p=0,m=f;m;m=Qr(m))p++;for(;0<h-p;)c=Qr(c),h--;for(;0<p-h;)f=Qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==s&&Kr(l,i,s,c,!1),null!==u&&null!==d&&Kr(l,d,u,c,!0)}if("select"===(s=(i=r?xa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=Xn;else if(Vn(i))if(Yn)g=lr;else{g=ar;var y=rr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=or);switch(g&&(g=g(e,r))?$n(l,g,n,a):(y&&y(e,i,r),"focusout"===e&&(y=i._wrapperState)&&y.controlled&&"number"===i.type&&ee(i,"number",i.value)),y=r?xa(r):window,e){case"focusin":(Vn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(l,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(l,n,a)}var v;if(Un)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Wn?Mn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(Wn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Wn&&(v=en()):(Gt="value"in(Yt=a)?Yt.value:Yt.textContent,Wn=!0)),0<(y=qr(r,b)).length&&(b=new xn(b,e,null,n,a),l.push({event:b,listeners:y}),v?b.data=v:null!==(v=Bn(n))&&(b.data=v))),(v=Dn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(In=!0,An);case"textInput":return(e=t.data)===An&&In?null:e;default:return null}}(e,n):function(e,t){if(Wn)return"compositionend"===e||!Un&&Mn(e,t)?(e=en(),Zt=Gt=Yt=null,Wn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),l.push({event:a,listeners:r}),a.data=v))}Ar(l,t)})}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Te(e,n))&&r.unshift($r(e,o,a)),null!=(o=Te(e,t))&&r.push($r(e,o,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var o=t._reactName,l=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Te(n,o))&&l.unshift($r(n,s,i)):a||null!=(s=Te(n,o))&&l.push($r(n,s,i))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Jr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Jr,"\n").replace(Xr,"")}function Gr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(o(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,oa="function"===typeof Promise?Promise:void 0,la="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oa?function(e){return oa.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Wt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Wt(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,ya="__reactHandles$"+da;function va(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function wa(e){return e[pa]||null}var Sa=[],ka=-1;function Ea(e){return{current:e}}function Na(e){0>ka||(e.current=Sa[ka],Sa[ka]=null,ka--)}function _a(e,t){ka++,Sa[ka]=e.current,e.current=t}var ja={},Ca=Ea(ja),Pa=Ea(!1),Ra=ja;function Oa(e,t){var n=e.type.contextTypes;if(!n)return ja;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ta(e){return null!==(e=e.childContextTypes)&&void 0!==e}function La(){Na(Pa),Na(Ca)}function Ua(e,t,n){if(Ca.current!==ja)throw Error(o(168));_a(Ca,t),_a(Pa,n)}function za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,H(e)||"Unknown",a));return F({},n,r)}function Da(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ja,Ra=Ca.current,_a(Ca,e),_a(Pa,Pa.current),!0}function Fa(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=za(e,t,Ra),r.__reactInternalMemoizedMergedChildContext=e,Na(Pa),Na(Ca),_a(Ca,e)):Na(Pa),_a(Pa,n)}var Aa=null,Ia=!1,Ma=!1;function Ba(e){null===Aa?Aa=[e]:Aa.push(e)}function Wa(){if(!Ma&&null!==Aa){Ma=!0;var e=0,t=bt;try{var n=Aa;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Aa=null,Ia=!1}catch(a){throw null!==Aa&&(Aa=Aa.slice(e+1)),Qe(Ze,Wa),a}finally{bt=t,Ma=!1}}return null}var Ha=[],Va=0,$a=null,qa=0,Qa=[],Ka=0,Ja=null,Xa=1,Ya="";function Ga(e,t){Ha[Va++]=qa,Ha[Va++]=$a,$a=e,qa=t}function Za(e,t,n){Qa[Ka++]=Xa,Qa[Ka++]=Ya,Qa[Ka++]=Ja,Ja=e;var r=Xa;e=Ya;var a=32-lt(r)-1;r&=~(1<<a),n+=1;var o=32-lt(t)+a;if(30<o){var l=a-a%5;o=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Xa=1<<32-lt(t)+a|n<<a|r,Ya=o+e}else Xa=1<<o|n<<a|r,Ya=e}function eo(e){null!==e.return&&(Ga(e,1),Za(e,1,0))}function to(e){for(;e===$a;)$a=Ha[--Va],Ha[Va]=null,qa=Ha[--Va],Ha[Va]=null;for(;e===Ja;)Ja=Qa[--Ka],Qa[Ka]=null,Ya=Qa[--Ka],Qa[Ka]=null,Xa=Qa[--Ka],Qa[Ka]=null}var no=null,ro=null,ao=!1,oo=null;function lo(e,t){var n=Ou(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function io(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ja?{id:Xa,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ou(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function so(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function uo(e){if(ao){var t=ro;if(t){var n=t;if(!io(e,t)){if(so(e))throw Error(o(418));t=ua(n.nextSibling);var r=no;t&&io(e,t)?lo(r,n):(e.flags=-4097&e.flags|2,ao=!1,no=e)}}else{if(so(e))throw Error(o(418));e.flags=-4097&e.flags|2,ao=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!ao)return co(e),ao=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ro)){if(so(e))throw po(),Error(o(418));for(;t;)lo(e,t),t=ua(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ua(e.stateNode.nextSibling):null;return!0}function po(){for(var e=ro;e;)e=ua(e.nextSibling)}function ho(){ro=no=null,ao=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var go=x.ReactCurrentBatchConfig;function yo(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,l=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===l?t.ref:(t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e},t._stringRef=l,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function vo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function xo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Lu(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===T&&bo(o)===t.type)?((r=a(t,n.props)).ref=yo(e,t,n),r.return=e,r):((r=Uu(n.type,n.key,n.props,null,e.mode,r)).ref=yo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Au(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=zu(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Uu(t.type,t.key,t.props,null,e.mode,n)).ref=yo(e,null,t),n.return=e,n;case S:return(t=Au(t,e.mode,n)).return=e,t;case T:return f(e,(0,t._init)(t._payload),n)}if(te(t)||z(t))return(t=zu(t,e.mode,n,null)).return=e,t;vo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case S:return n.key===a?c(e,t,n,r):null;case T:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||z(n))return null!==a?null:d(e,t,n,r,null);vo(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case T:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||z(r))return d(t,e=e.get(n)||null,r,a,null);vo(t,r)}return null}function m(a,o,i,s){for(var u=null,c=null,d=o,m=o=0,g=null;null!==d&&m<i.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=p(a,d,i[m],s);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(a,d),o=l(y,o,m),null===c?u=y:c.sibling=y,c=y,d=g}if(m===i.length)return n(a,d),ao&&Ga(a,m),u;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],s))&&(o=l(d,o,m),null===c?u=d:c.sibling=d,c=d);return ao&&Ga(a,m),u}for(d=r(a,d);m<i.length;m++)null!==(g=h(d,a,m,i[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),o=l(g,o,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),ao&&Ga(a,m),u}function g(a,i,s,u){var c=z(s);if("function"!==typeof c)throw Error(o(150));if(null==(s=c.call(s)))throw Error(o(151));for(var d=c=null,m=i,g=i=0,y=null,v=s.next();null!==m&&!v.done;g++,v=s.next()){m.index>g?(y=m,m=null):y=m.sibling;var b=p(a,m,v.value,u);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(a,m),i=l(b,i,g),null===d?c=b:d.sibling=b,d=b,m=y}if(v.done)return n(a,m),ao&&Ga(a,g),c;if(null===m){for(;!v.done;g++,v=s.next())null!==(v=f(a,v.value,u))&&(i=l(v,i,g),null===d?c=v:d.sibling=v,d=v);return ao&&Ga(a,g),c}for(m=r(a,m);!v.done;g++,v=s.next())null!==(v=h(m,a,g,v.value,u))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),i=l(v,i,g),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach(function(e){return t(a,e)}),ao&&Ga(a,g),c}return function e(r,o,l,s){if("object"===typeof l&&null!==l&&l.type===k&&null===l.key&&(l=l.props.children),"object"===typeof l&&null!==l){switch(l.$$typeof){case w:e:{for(var u=l.key,c=o;null!==c;){if(c.key===u){if((u=l.type)===k){if(7===c.tag){n(r,c.sibling),(o=a(c,l.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===T&&bo(u)===c.type){n(r,c.sibling),(o=a(c,l.props)).ref=yo(r,c,l),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}l.type===k?((o=zu(l.props.children,r.mode,s,l.key)).return=r,r=o):((s=Uu(l.type,l.key,l.props,null,r.mode,s)).ref=yo(r,o,l),s.return=r,r=s)}return i(r);case S:e:{for(c=l.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===l.containerInfo&&o.stateNode.implementation===l.implementation){n(r,o.sibling),(o=a(o,l.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Au(l,r.mode,s)).return=r,r=o}return i(r);case T:return e(r,o,(c=l._init)(l._payload),s)}if(te(l))return m(r,o,l,s);if(z(l))return g(r,o,l,s);vo(r,l)}return"string"===typeof l&&""!==l||"number"===typeof l?(l=""+l,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,l)).return=r,r=o):(n(r,o),(o=Fu(l,r.mode,s)).return=r,r=o),i(r)):n(r,o)}}var wo=xo(!0),So=xo(!1),ko=Ea(null),Eo=null,No=null,_o=null;function jo(){_o=No=Eo=null}function Co(e){var t=ko.current;Na(ko),e._currentValue=t}function Po(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ro(e,t){Eo=e,_o=No=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bi=!0),e.firstContext=null)}function Oo(e){var t=e._currentValue;if(_o!==e)if(e={context:e,memoizedValue:t,next:null},null===No){if(null===Eo)throw Error(o(308));No=e,Eo.dependencies={lanes:0,firstContext:e}}else No=No.next=e;return t}var To=null;function Lo(e){null===To?To=[e]:To.push(e)}function Uo(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Lo(t)):(n.next=a.next,a.next=n),t.interleaved=n,zo(e,r)}function zo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Do=!1;function Fo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ao(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Io(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Cs)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,zo(e,n)}return null===(a=r.interleaved)?(t.next=t,Lo(r)):(t.next=a.next,a.next=t),r.interleaved=t,zo(e,n)}function Bo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Wo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=l:o=o.next=l,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ho(e,t,n,r){var a=e.updateQueue;Do=!1;var o=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===l?o=u:l.next=u,l=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==l&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==o){var d=a.baseState;for(l=0,c=u=s=null,i=o;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,m=i;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=F({},d,f);break e;case 2:Do=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,l|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{l|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Ds|=l,e.lanes=l,e.memoizedState=d}}function Vo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(o(191,a));a.call(r)}}}var $o={},qo=Ea($o),Qo=Ea($o),Ko=Ea($o);function Jo(e){if(e===$o)throw Error(o(174));return e}function Xo(e,t){switch(_a(Ko,t),_a(Qo,e),_a(qo,$o),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Na(qo),_a(qo,t)}function Yo(){Na(qo),Na(Qo),Na(Ko)}function Go(e){Jo(Ko.current);var t=Jo(qo.current),n=se(t,e.type);t!==n&&(_a(Qo,e),_a(qo,n))}function Zo(e){Qo.current===e&&(Na(qo),Na(Qo))}var el=Ea(0);function tl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nl=[];function rl(){for(var e=0;e<nl.length;e++)nl[e]._workInProgressVersionPrimary=null;nl.length=0}var al=x.ReactCurrentDispatcher,ol=x.ReactCurrentBatchConfig,ll=0,il=null,sl=null,ul=null,cl=!1,dl=!1,fl=0,pl=0;function hl(){throw Error(o(321))}function ml(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function gl(e,t,n,r,a,l){if(ll=l,il=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,al.current=null===e||null===e.memoizedState?Zl:ei,e=n(r,a),dl){l=0;do{if(dl=!1,fl=0,25<=l)throw Error(o(301));l+=1,ul=sl=null,t.updateQueue=null,al.current=ti,e=n(r,a)}while(dl)}if(al.current=Gl,t=null!==sl&&null!==sl.next,ll=0,ul=sl=il=null,cl=!1,t)throw Error(o(300));return e}function yl(){var e=0!==fl;return fl=0,e}function vl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ul?il.memoizedState=ul=e:ul=ul.next=e,ul}function bl(){if(null===sl){var e=il.alternate;e=null!==e?e.memoizedState:null}else e=sl.next;var t=null===ul?il.memoizedState:ul.next;if(null!==t)ul=t,sl=e;else{if(null===e)throw Error(o(310));e={memoizedState:(sl=e).memoizedState,baseState:sl.baseState,baseQueue:sl.baseQueue,queue:sl.queue,next:null},null===ul?il.memoizedState=ul=e:ul=ul.next=e}return ul}function xl(e,t){return"function"===typeof t?t(e):t}function wl(e){var t=bl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=sl,a=r.baseQueue,l=n.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}r.baseQueue=a=l,n.pending=null}if(null!==a){l=a.next,r=r.baseState;var s=i=null,u=null,c=l;do{var d=c.lane;if((ll&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,il.lanes|=d,Ds|=d}c=c.next}while(null!==c&&c!==l);null===u?i=r:u.next=s,ir(r,t.memoizedState)||(bi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{l=a.lane,il.lanes|=l,Ds|=l,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Sl(e){var t=bl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);ir(l,t.memoizedState)||(bi=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function kl(){}function El(e,t){var n=il,r=bl(),a=t(),l=!ir(r.memoizedState,a);if(l&&(r.memoizedState=a,bi=!0),r=r.queue,Dl(jl.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||null!==ul&&1&ul.memoizedState.tag){if(n.flags|=2048,Ol(9,_l.bind(null,n,r,a,t),void 0,null),null===Ps)throw Error(o(349));0!==(30&ll)||Nl(n,t,a)}return a}function Nl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=il.updateQueue)?(t={lastEffect:null,stores:null},il.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function _l(e,t,n,r){t.value=n,t.getSnapshot=r,Cl(t)&&Pl(e)}function jl(e,t,n){return n(function(){Cl(t)&&Pl(e)})}function Cl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(r){return!0}}function Pl(e){var t=zo(e,1);null!==t&&nu(t,e,1,-1)}function Rl(e){var t=vl();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xl,lastRenderedState:e},t.queue=e,e=e.dispatch=Kl.bind(null,il,e),[t.memoizedState,e]}function Ol(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=il.updateQueue)?(t={lastEffect:null,stores:null},il.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Tl(){return bl().memoizedState}function Ll(e,t,n,r){var a=vl();il.flags|=e,a.memoizedState=Ol(1|t,n,void 0,void 0===r?null:r)}function Ul(e,t,n,r){var a=bl();r=void 0===r?null:r;var o=void 0;if(null!==sl){var l=sl.memoizedState;if(o=l.destroy,null!==r&&ml(r,l.deps))return void(a.memoizedState=Ol(t,n,o,r))}il.flags|=e,a.memoizedState=Ol(1|t,n,o,r)}function zl(e,t){return Ll(8390656,8,e,t)}function Dl(e,t){return Ul(2048,8,e,t)}function Fl(e,t){return Ul(4,2,e,t)}function Al(e,t){return Ul(4,4,e,t)}function Il(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ml(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ul(4,4,Il.bind(null,t,e),n)}function Bl(){}function Wl(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Hl(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vl(e,t,n){return 0===(21&ll)?(e.baseState&&(e.baseState=!1,bi=!0),e.memoizedState=n):(ir(n,t)||(n=mt(),il.lanes|=n,Ds|=n,e.baseState=!0),t)}function $l(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ol.transition;ol.transition={};try{e(!1),t()}finally{bt=n,ol.transition=r}}function ql(){return bl().memoizedState}function Ql(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jl(e))Xl(t,n);else if(null!==(n=Uo(e,t,n,r))){nu(n,e,r,eu()),Yl(n,t,r)}}function Kl(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jl(e))Xl(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=o(l,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,l)){var s=t.interleaved;return null===s?(a.next=a,Lo(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Uo(e,t,a,r))&&(nu(n,e,r,a=eu()),Yl(n,t,r))}}function Jl(e){var t=e.alternate;return e===il||null!==t&&t===il}function Xl(e,t){dl=cl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yl(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Gl={readContext:Oo,useCallback:hl,useContext:hl,useEffect:hl,useImperativeHandle:hl,useInsertionEffect:hl,useLayoutEffect:hl,useMemo:hl,useReducer:hl,useRef:hl,useState:hl,useDebugValue:hl,useDeferredValue:hl,useTransition:hl,useMutableSource:hl,useSyncExternalStore:hl,useId:hl,unstable_isNewReconciler:!1},Zl={readContext:Oo,useCallback:function(e,t){return vl().memoizedState=[e,void 0===t?null:t],e},useContext:Oo,useEffect:zl,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ll(4194308,4,Il.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ll(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ll(4,2,e,t)},useMemo:function(e,t){var n=vl();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vl();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ql.bind(null,il,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vl().memoizedState=e},useState:Rl,useDebugValue:Bl,useDeferredValue:function(e){return vl().memoizedState=e},useTransition:function(){var e=Rl(!1),t=e[0];return e=$l.bind(null,e[1]),vl().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=il,a=vl();if(ao){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Ps)throw Error(o(349));0!==(30&ll)||Nl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,zl(jl.bind(null,r,l,e),[e]),r.flags|=2048,Ol(9,_l.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=vl(),t=Ps.identifierPrefix;if(ao){var n=Ya;t=":"+t+"R"+(n=(Xa&~(1<<32-lt(Xa)-1)).toString(32)+n),0<(n=fl++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ei={readContext:Oo,useCallback:Wl,useContext:Oo,useEffect:Dl,useImperativeHandle:Ml,useInsertionEffect:Fl,useLayoutEffect:Al,useMemo:Hl,useReducer:wl,useRef:Tl,useState:function(){return wl(xl)},useDebugValue:Bl,useDeferredValue:function(e){return Vl(bl(),sl.memoizedState,e)},useTransition:function(){return[wl(xl)[0],bl().memoizedState]},useMutableSource:kl,useSyncExternalStore:El,useId:ql,unstable_isNewReconciler:!1},ti={readContext:Oo,useCallback:Wl,useContext:Oo,useEffect:Dl,useImperativeHandle:Ml,useInsertionEffect:Fl,useLayoutEffect:Al,useMemo:Hl,useReducer:Sl,useRef:Tl,useState:function(){return Sl(xl)},useDebugValue:Bl,useDeferredValue:function(e){var t=bl();return null===sl?t.memoizedState=e:Vl(t,sl.memoizedState,e)},useTransition:function(){return[Sl(xl)[0],bl().memoizedState]},useMutableSource:kl,useSyncExternalStore:El,useId:ql,unstable_isNewReconciler:!1};function ni(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ri(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ai={isMounted:function(e){return!!(e=e._reactInternals)&&We(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=Io(r,a);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Mo(e,o,a))&&(nu(t,e,a,r),Bo(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=Io(r,a);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Mo(e,o,a))&&(nu(t,e,a,r),Bo(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Io(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Mo(e,a,r))&&(nu(t,e,r,n),Bo(t,e,r))}};function oi(e,t,n,r,a,o,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,l):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,o))}function li(e,t,n){var r=!1,a=ja,o=t.contextType;return"object"===typeof o&&null!==o?o=Oo(o):(a=Ta(t)?Ra:Ca.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Oa(e,a):ja),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ai,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function ii(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ai.enqueueReplaceState(t,t.state,null)}function si(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fo(e);var o=t.contextType;"object"===typeof o&&null!==o?a.context=Oo(o):(o=Ta(t)?Ra:Ca.current,a.context=Oa(e,o)),a.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(ri(e,t,o,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ai.enqueueReplaceState(a,a.state,null),Ho(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function ui(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(o){a="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function di(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fi="function"===typeof WeakMap?WeakMap:Map;function pi(e,t,n){(n=Io(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vs||(Vs=!0,$s=r),di(0,t)},n}function hi(e,t,n){(n=Io(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){di(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){di(0,t),"function"!==typeof r&&(null===qs?qs=new Set([this]):qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Nu.bind(null,e,t,n),t.then(e,e))}function gi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Io(-1,1)).tag=2,Mo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var vi=x.ReactCurrentOwner,bi=!1;function xi(e,t,n,r){t.child=null===e?So(t,null,n,r):wo(t,e.child,n,r)}function wi(e,t,n,r,a){n=n.render;var o=t.ref;return Ro(t,a),r=gl(e,t,n,r,o,a),n=yl(),null===e||bi?(ao&&n&&eo(t),t.flags|=1,xi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vi(e,t,a))}function Si(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Tu(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Uu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,ki(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var l=o.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(l,r)&&e.ref===t.ref)return Vi(e,t,a)}return t.flags|=1,(e=Lu(o,r)).ref=t.ref,e.return=t,t.child=e}function ki(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(sr(o,r)&&e.ref===t.ref){if(bi=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,Vi(e,t,a);0!==(131072&e.flags)&&(bi=!0)}}return _i(e,t,n,r,a)}function Ei(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_a(Ls,Ts),Ts|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_a(Ls,Ts),Ts|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,_a(Ls,Ts),Ts|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,_a(Ls,Ts),Ts|=r;return xi(e,t,a,n),t.child}function Ni(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function _i(e,t,n,r,a){var o=Ta(n)?Ra:Ca.current;return o=Oa(t,o),Ro(t,a),n=gl(e,t,n,r,o,a),r=yl(),null===e||bi?(ao&&r&&eo(t),t.flags|=1,xi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vi(e,t,a))}function ji(e,t,n,r,a){if(Ta(n)){var o=!0;Da(t)}else o=!1;if(Ro(t,a),null===t.stateNode)Hi(e,t),li(t,n,r),si(t,n,r,a),r=!0;else if(null===e){var l=t.stateNode,i=t.memoizedProps;l.props=i;var s=l.context,u=n.contextType;"object"===typeof u&&null!==u?u=Oo(u):u=Oa(t,u=Ta(n)?Ra:Ca.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof l.getSnapshotBeforeUpdate;d||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==r||s!==u)&&ii(t,l,r,u),Do=!1;var f=t.memoizedState;l.state=f,Ho(t,r,l,a),s=t.memoizedState,i!==r||f!==s||Pa.current||Do?("function"===typeof c&&(ri(t,n,c,r),s=t.memoizedState),(i=Do||oi(t,n,i,r,f,s,u))?(d||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),l.props=r,l.state=s,l.context=u,r=i):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Ao(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:ni(t.type,i),l.props=u,d=t.pendingProps,f=l.context,"object"===typeof(s=n.contextType)&&null!==s?s=Oo(s):s=Oa(t,s=Ta(n)?Ra:Ca.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||f!==s)&&ii(t,l,r,s),Do=!1,f=t.memoizedState,l.state=f,Ho(t,r,l,a);var h=t.memoizedState;i!==d||f!==h||Pa.current||Do?("function"===typeof p&&(ri(t,n,p,r),h=t.memoizedState),(u=Do||oi(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,h,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),l.props=r,l.state=h,l.context=s,r=u):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ci(e,t,n,r,o,a)}function Ci(e,t,n,r,a,o){Ni(e,t);var l=0!==(128&t.flags);if(!r&&!l)return a&&Fa(t,n,!1),Vi(e,t,o);r=t.stateNode,vi.current=t;var i=l&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&l?(t.child=wo(t,e.child,null,o),t.child=wo(t,null,i,o)):xi(e,t,i,o),t.memoizedState=r.state,a&&Fa(t,n,!0),t.child}function Pi(e){var t=e.stateNode;t.pendingContext?Ua(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ua(0,t.context,!1),Xo(e,t.containerInfo)}function Ri(e,t,n,r,a){return ho(),mo(a),t.flags|=256,xi(e,t,n,r),t.child}var Oi,Ti,Li,Ui,zi={dehydrated:null,treeContext:null,retryLane:0};function Di(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fi(e,t,n){var r,a=t.pendingProps,l=el.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&l)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),_a(el,1&l),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Du(s,a,0,null),e=zu(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Di(n),t.memoizedState=zi,e):Ai(t,s));if(null!==(l=e.memoizedState)&&null!==(r=l.dehydrated))return function(e,t,n,r,a,l,i){if(n)return 256&t.flags?(t.flags&=-257,Ii(e,t,i,r=ci(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=Du({mode:"visible",children:r.children},a,0,null),(l=zu(l,a,i,null)).flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,0!==(1&t.mode)&&wo(t,e.child,null,i),t.child.memoizedState=Di(i),t.memoizedState=zi,l);if(0===(1&t.mode))return Ii(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Ii(e,t,i,r=ci(l=Error(o(419)),r,void 0))}if(s=0!==(i&e.childLanes),bi||s){if(null!==(r=Ps)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==l.retryLane&&(l.retryLane=a,zo(e,a),nu(r,e,a,-1))}return mu(),Ii(e,t,i,r=ci(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=ju.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,ro=ua(a.nextSibling),no=t,ao=!0,oo=null,null!==e&&(Qa[Ka++]=Xa,Qa[Ka++]=Ya,Qa[Ka++]=Ja,Xa=e.id,Ya=e.overflow,Ja=t),t=Ai(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,l,n);if(i){i=a.fallback,s=t.mode,r=(l=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==l?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Lu(l,u)).subtreeFlags=14680064&l.subtreeFlags,null!==r?i=Lu(r,i):(i=zu(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Di(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=zi,a}return e=(i=e.child).sibling,a=Lu(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ai(e,t){return(t=Du({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ii(e,t,n,r){return null!==r&&mo(r),wo(t,e.child,null,n),(e=Ai(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Mi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Po(e.return,t,n)}function Bi(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Wi(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(xi(e,t,r.children,n),0!==(2&(r=el.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Mi(e,n,t);else if(19===e.tag)Mi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(_a(el,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===tl(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bi(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===tl(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bi(t,!0,n,null,o);break;case"together":Bi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ds|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Lu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Lu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $i(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qi(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qi(t),null;case 1:case 17:return Ta(t.type)&&La(),qi(t),null;case 3:return r=t.stateNode,Yo(),Na(Pa),Na(Ca),rl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(lu(oo),oo=null))),Ti(e,t),qi(t),null;case 5:Zo(t);var a=Jo(Ko.current);if(n=t.type,null!==e&&null!=t.stateNode)Li(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return qi(t),null}if(e=Jo(qo.current),fo(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[fa]=t,r[pa]=l,e=0!==(1&t.mode),n){case"dialog":Ir("cancel",r),Ir("close",r);break;case"iframe":case"object":case"embed":Ir("load",r);break;case"video":case"audio":for(a=0;a<zr.length;a++)Ir(zr[a],r);break;case"source":Ir("error",r);break;case"img":case"image":case"link":Ir("error",r),Ir("load",r);break;case"details":Ir("toggle",r);break;case"input":X(r,l),Ir("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Ir("invalid",r);break;case"textarea":ae(r,l),Ir("invalid",r)}for(var s in ve(n,l),a=null,l)if(l.hasOwnProperty(s)){var u=l[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==l.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==l.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Ir("scroll",r)}switch(n){case"input":q(r),Z(r,l,!0);break;case"textarea":q(r),le(r);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Oi(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Ir("cancel",e),Ir("close",e),a=r;break;case"iframe":case"object":case"embed":Ir("load",e),a=r;break;case"video":case"audio":for(a=0;a<zr.length;a++)Ir(zr[a],e);a=r;break;case"source":Ir("error",e),a=r;break;case"img":case"image":case"link":Ir("error",e),Ir("load",e),a=r;break;case"details":Ir("toggle",e),a=r;break;case"input":X(e,r),a=J(e,r),Ir("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=F({},r,{value:void 0}),Ir("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ir("invalid",e)}for(l in ve(n,a),u=a)if(u.hasOwnProperty(l)){var c=u[l];"style"===l?ge(e,c):"dangerouslySetInnerHTML"===l?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===l?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(i.hasOwnProperty(l)?null!=c&&"onScroll"===l&&Ir("scroll",e):null!=c&&b(e,l,c,s))}switch(n){case"input":q(e),Z(e,r,!1);break;case"textarea":q(e),le(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?ne(e,!!r.multiple,l,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qi(t),null;case 6:if(e&&null!=t.stateNode)Ui(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Jo(Ko.current),Jo(qo.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(l=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Gr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Gr(r.nodeValue,n,0!==(1&e.mode))}l&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return qi(t),null;case 13:if(Na(el),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ao&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))po(),ho(),t.flags|=98560,l=!1;else if(l=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(o(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(o(317));l[fa]=t}else ho(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qi(t),l=!1}else null!==oo&&(lu(oo),oo=null),l=!0;if(!l)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&el.current)?0===Us&&(Us=3):mu())),null!==t.updateQueue&&(t.flags|=4),qi(t),null);case 4:return Yo(),Ti(e,t),null===e&&Wr(t.stateNode.containerInfo),qi(t),null;case 10:return Co(t.type._context),qi(t),null;case 19:if(Na(el),null===(l=t.memoizedState))return qi(t),null;if(r=0!==(128&t.flags),null===(s=l.rendering))if(r)$i(l,!1);else{if(0!==Us||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=tl(e))){for(t.flags|=128,$i(l,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=14680066,null===(s=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _a(el,1&el.current|2),t.child}e=e.sibling}null!==l.tail&&Ye()>Ws&&(t.flags|=128,r=!0,$i(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=tl(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$i(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!ao)return qi(t),null}else 2*Ye()-l.renderingStartTime>Ws&&1073741824!==n&&(t.flags|=128,r=!0,$i(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=l.last)?n.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Ye(),t.sibling=null,n=el.current,_a(el,r?1&n|2:1&n),t):(qi(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ts)&&(qi(t),6&t.subtreeFlags&&(t.flags|=8192)):qi(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Ki(e,t){switch(to(t),t.tag){case 1:return Ta(t.type)&&La(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Yo(),Na(Pa),Na(Ca),rl(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zo(t),null;case 13:if(Na(el),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));ho()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Na(el),null;case 4:return Yo(),null;case 10:return Co(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Oi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ti=function(){},Li=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Jo(qo.current);var o,l=null;switch(n){case"input":a=J(e,a),r=J(e,r),l=[];break;case"select":a=F({},a,{value:void 0}),r=F({},r,{value:void 0}),l=[];break;case"textarea":a=re(e,a),r=re(e,r),l=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ve(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(o in s)!s.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&s[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(l||(l=[]),l.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(l=l||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(l=l||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ir("scroll",e),l||s===u||(l=[])):(l=l||[]).push(c,u))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}},Ui=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ji=!1,Xi=!1,Yi="function"===typeof WeakSet?WeakSet:Set,Gi=null;function Zi(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&es(t,n,o)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function os(e){var t=e.alternate;null!==t&&(e.alternate=null,os(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[ga],delete t[ya])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ls(e){return 5===e.tag||3===e.tag||4===e.tag}function is(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ls(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}var cs=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(at,n)}catch(i){}switch(n.tag){case 5:Xi||Zi(n,t);case 6:var r=cs,a=ds;cs=null,fs(e,t,n),ds=a,null!==(cs=r)&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cs.removeChild(n.stateNode));break;case 18:null!==cs&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Wt(e)):sa(cs,n.stateNode));break;case 4:r=cs,a=ds,cs=n.stateNode.containerInfo,ds=!0,fs(e,t,n),cs=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Xi&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,l=o.destroy;o=o.tag,void 0!==l&&(0!==(2&o)||0!==(4&o))&&es(n,t,l),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Xi&&(Zi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){Eu(n,t,i)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Xi=(r=Xi)||null!==n.memoizedState,fs(e,t,n),Xi=r):fs(e,t,n);break;default:fs(e,t,n)}}function hs(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Yi),t.forEach(function(t){var r=Cu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ms(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:cs=s.stateNode,ds=!1;break e;case 3:case 4:cs=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===cs)throw Error(o(160));ps(l,i,a),cs=null,ds=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Eu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),ys(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(g){Eu(e,e.return,g)}try{ns(5,e,e.return)}catch(g){Eu(e,e.return,g)}}break;case 1:ms(t,e),ys(e),512&r&&null!==n&&Zi(n,n.return);break;case 5:if(ms(t,e),ys(e),512&r&&null!==n&&Zi(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Eu(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var l=e.memoizedProps,i=null!==n?n.memoizedProps:l,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===l.type&&null!=l.name&&Y(a,l),be(s,i);var c=be(s,l);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(s){case"input":G(a,l);break;case"textarea":oe(a,l);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var h=l.value;null!=h?ne(a,!!l.multiple,h,!1):p!==!!l.multiple&&(null!=l.defaultValue?ne(a,!!l.multiple,l.defaultValue,!0):ne(a,!!l.multiple,l.multiple?[]:"",!1))}a[pa]=l}catch(g){Eu(e,e.return,g)}}break;case 6:if(ms(t,e),ys(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(g){Eu(e,e.return,g)}}break;case 3:if(ms(t,e),ys(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Wt(t.containerInfo)}catch(g){Eu(e,e.return,g)}break;case 4:default:ms(t,e),ys(e);break;case 13:ms(t,e),ys(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||(Bs=Ye())),4&r&&hs(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xi=(c=Xi)||d,ms(t,e),Xi=c):ms(t,e),ys(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Gi=e,d=e.child;null!==d;){for(f=Gi=d;null!==Gi;){switch(h=(p=Gi).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Zi(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Eu(r,n,g)}}break;case 5:Zi(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==h?(h.return=p,Gi=h):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(s=f.stateNode,i=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",i))}catch(g){Eu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Eu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ms(t,e),ys(e),4&r&&hs(e);case 21:}}function ys(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ls(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),us(e,is(e),a);break;case 3:case 4:var l=r.stateNode.containerInfo;ss(e,is(e),l);break;default:throw Error(o(161))}}catch(i){Eu(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vs(e,t,n){Gi=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Gi;){var a=Gi,o=a.child;if(22===a.tag&&r){var l=null!==a.memoizedState||Ji;if(!l){var i=a.alternate,s=null!==i&&null!==i.memoizedState||Xi;i=Ji;var u=Xi;if(Ji=l,(Xi=s)&&!u)for(Gi=a;null!==Gi;)s=(l=Gi).child,22===l.tag&&null!==l.memoizedState?Ss(a):null!==s?(s.return=l,Gi=s):Ss(a);for(;null!==o;)Gi=o,bs(o,t,n),o=o.sibling;Gi=a,Ji=i,Xi=u}xs(e)}else 0!==(8772&a.subtreeFlags)&&null!==o?(o.return=a,Gi=o):xs(e)}}function xs(e){for(;null!==Gi;){var t=Gi;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xi||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xi)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ni(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Vo(t,l,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Vo(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Wt(f)}}}break;default:throw Error(o(163))}Xi||512&t.flags&&as(t)}catch(p){Eu(t,t.return,p)}}if(t===e){Gi=null;break}if(null!==(n=t.sibling)){n.return=t.return,Gi=n;break}Gi=t.return}}function ws(e){for(;null!==Gi;){var t=Gi;if(t===e){Gi=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Gi=n;break}Gi=t.return}}function Ss(e){for(;null!==Gi;){var t=Gi;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Eu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Eu(t,a,s)}}var o=t.return;try{as(t)}catch(s){Eu(t,o,s)}break;case 5:var l=t.return;try{as(t)}catch(s){Eu(t,l,s)}}}catch(s){Eu(t,t.return,s)}if(t===e){Gi=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Gi=i;break}Gi=t.return}}var ks,Es=Math.ceil,Ns=x.ReactCurrentDispatcher,_s=x.ReactCurrentOwner,js=x.ReactCurrentBatchConfig,Cs=0,Ps=null,Rs=null,Os=0,Ts=0,Ls=Ea(0),Us=0,zs=null,Ds=0,Fs=0,As=0,Is=null,Ms=null,Bs=0,Ws=1/0,Hs=null,Vs=!1,$s=null,qs=null,Qs=!1,Ks=null,Js=0,Xs=0,Ys=null,Gs=-1,Zs=0;function eu(){return 0!==(6&Cs)?Ye():-1!==Gs?Gs:Gs=Ye()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Cs)&&0!==Os?Os&-Os:null!==go.transition?(0===Zs&&(Zs=mt()),Zs):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nu(e,t,n,r){if(50<Xs)throw Xs=0,Ys=null,Error(o(185));yt(e,n,r),0!==(2&Cs)&&e===Ps||(e===Ps&&(0===(2&Cs)&&(Fs|=n),4===Us&&iu(e,Os)),ru(e,r),1===n&&0===Cs&&0===(1&t.mode)&&(Ws=Ye()+500,Ia&&Wa()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var l=31-lt(o),i=1<<l,s=a[l];-1===s?0!==(i&n)&&0===(i&r)||(a[l]=pt(i,t)):s<=t&&(e.expiredLanes|=i),o&=~i}}(e,t);var r=ft(e,e===Ps?Os:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ia=!0,Ba(e)}(su.bind(null,e)):Ba(su.bind(null,e)),la(function(){0===(6&Cs)&&Wa()}),n=null;else{switch(xt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Pu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Gs=-1,Zs=0,0!==(6&Cs))throw Error(o(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===Ps?Os:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=Cs;Cs|=2;var l=hu();for(Ps===e&&Os===t||(Hs=null,Ws=Ye()+500,fu(e,t));;)try{vu();break}catch(s){pu(e,s)}jo(),Ns.current=l,Cs=a,null!==Rs?t=0:(Ps=null,Os=0,t=Us)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=ou(e,a))),1===t)throw n=zs,fu(e,0),iu(e,r),ru(e,Ye()),n;if(6===t)iu(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!ir(o(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gu(e,r))&&(0!==(l=ht(e))&&(r=l,t=ou(e,l))),1===t))throw n=zs,fu(e,0),iu(e,r),ru(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:wu(e,Ms,Hs);break;case 3:if(iu(e,r),(130023424&r)===r&&10<(t=Bs+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,Ms,Hs),t);break}wu(e,Ms,Hs);break;case 4:if(iu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-lt(r);l=1<<i,(i=t[i])>a&&(a=i),r&=~l}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Es(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,Ms,Hs),r);break}wu(e,Ms,Hs);break;default:throw Error(o(329))}}}return ru(e,Ye()),e.callbackNode===n?au.bind(null,e):null}function ou(e,t){var n=Is;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Ms,Ms=n,null!==t&&lu(t)),e}function lu(e){null===Ms?Ms=e:Ms.push.apply(Ms,e)}function iu(e,t){for(t&=~As,t&=~Fs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-lt(t),r=1<<n;e[n]=-1,t&=~r}}function su(e){if(0!==(6&Cs))throw Error(o(327));Su();var t=ft(e,0);if(0===(1&t))return ru(e,Ye()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=ou(e,r))}if(1===n)throw n=zs,fu(e,0),iu(e,t),ru(e,Ye()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Ms,Hs),ru(e,Ye()),null}function uu(e,t){var n=Cs;Cs|=1;try{return e(t)}finally{0===(Cs=n)&&(Ws=Ye()+500,Ia&&Wa())}}function cu(e){null!==Ks&&0===Ks.tag&&0===(6&Cs)&&Su();var t=Cs;Cs|=1;var n=js.transition,r=bt;try{if(js.transition=null,bt=1,e)return e()}finally{bt=r,js.transition=n,0===(6&(Cs=t))&&Wa()}}function du(){Ts=Ls.current,Na(Ls)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Rs)for(n=Rs.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&La();break;case 3:Yo(),Na(Pa),Na(Ca),rl();break;case 5:Zo(r);break;case 4:Yo();break;case 13:case 19:Na(el);break;case 10:Co(r.type._context);break;case 22:case 23:du()}n=n.return}if(Ps=e,Rs=e=Lu(e.current,null),Os=Ts=t,Us=0,zs=null,As=Fs=Ds=0,Ms=Is=null,null!==To){for(t=0;t<To.length;t++)if(null!==(r=(n=To[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var l=o.next;o.next=a,r.next=l}n.pending=r}To=null}return e}function pu(e,t){for(;;){var n=Rs;try{if(jo(),al.current=Gl,cl){for(var r=il.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}cl=!1}if(ll=0,ul=sl=il=null,dl=!1,fl=0,_s.current=null,null===n||null===n.return){Us=1,zs=t,Rs=null;break}e:{var l=e,i=n.return,s=n,u=t;if(t=Os,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gi(i);if(null!==h){h.flags&=-257,yi(h,i,s,0,t),1&h.mode&&mi(l,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){mi(l,c,t),mu();break e}u=Error(o(426))}else if(ao&&1&s.mode){var y=gi(i);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),yi(y,i,s,0,t),mo(ui(u,s));break e}}l=u=ui(u,s),4!==Us&&(Us=2),null===Is?Is=[l]:Is.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,Wo(l,pi(0,u,t));break e;case 1:s=u;var v=l.type,b=l.stateNode;if(0===(128&l.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===qs||!qs.has(b)))){l.flags|=65536,t&=-t,l.lanes|=t,Wo(l,hi(l,s,t));break e}}l=l.return}while(null!==l)}xu(n)}catch(x){t=x,Rs===n&&null!==n&&(Rs=n=n.return);continue}break}}function hu(){var e=Ns.current;return Ns.current=Gl,null===e?Gl:e}function mu(){0!==Us&&3!==Us&&2!==Us||(Us=4),null===Ps||0===(268435455&Ds)&&0===(268435455&Fs)||iu(Ps,Os)}function gu(e,t){var n=Cs;Cs|=2;var r=hu();for(Ps===e&&Os===t||(Hs=null,fu(e,t));;)try{yu();break}catch(a){pu(e,a)}if(jo(),Cs=n,Ns.current=r,null!==Rs)throw Error(o(261));return Ps=null,Os=0,Us}function yu(){for(;null!==Rs;)bu(Rs)}function vu(){for(;null!==Rs&&!Je();)bu(Rs)}function bu(e){var t=ks(e.alternate,e,Ts);e.memoizedProps=e.pendingProps,null===t?xu(e):Rs=t,_s.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qi(n,t,Ts)))return void(Rs=n)}else{if(null!==(n=Ki(n,t)))return n.flags&=32767,void(Rs=n);if(null===e)return Us=6,void(Rs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Rs=t);Rs=t=e}while(null!==t);0===Us&&(Us=5)}function wu(e,t,n){var r=bt,a=js.transition;try{js.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==Ks);if(0!==(6&Cs))throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-lt(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,l),e===Ps&&(Rs=Ps=null,Os=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qs||(Qs=!0,Pu(tt,function(){return Su(),null})),l=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||l){l=js.transition,js.transition=null;var i=bt;bt=1;var s=Cs;Cs|=4,_s.current=null,function(e,t){if(ea=Vt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(w){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==l||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===l&&++d===r&&(u=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Vt=!1,Gi=t;null!==Gi;)if(e=(t=Gi).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Gi=e;else for(;null!==Gi;){t=Gi;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:ni(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(o(163))}}catch(w){Eu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Gi=e;break}Gi=t.return}m=ts,ts=!1}(e,n),gs(n,e),hr(ta),Vt=!!ea,ta=ea=null,e.current=n,vs(n,e,a),Xe(),Cs=s,bt=i,js.transition=l}else e.current=n;if(Qs&&(Qs=!1,Ks=e,Js=a),l=e.pendingLanes,0===l&&(qs=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Vs)throw Vs=!1,e=$s,$s=null,e;0!==(1&Js)&&0!==e.tag&&Su(),l=e.pendingLanes,0!==(1&l)?e===Ys?Xs++:(Xs=0,Ys=e):Xs=0,Wa()}(e,t,n,r)}finally{js.transition=a,bt=r}return null}function Su(){if(null!==Ks){var e=xt(Js),t=js.transition,n=bt;try{if(js.transition=null,bt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Js=0,0!==(6&Cs))throw Error(o(331));var a=Cs;for(Cs|=4,Gi=e.current;null!==Gi;){var l=Gi,i=l.child;if(0!==(16&Gi.flags)){var s=l.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Gi=c;null!==Gi;){var d=Gi;switch(d.tag){case 0:case 11:case 15:ns(8,d,l)}var f=d.child;if(null!==f)f.return=d,Gi=f;else for(;null!==Gi;){var p=(d=Gi).sibling,h=d.return;if(os(d),d===c){Gi=null;break}if(null!==p){p.return=h,Gi=p;break}Gi=h}}}var m=l.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Gi=l}}if(0!==(2064&l.subtreeFlags)&&null!==i)i.return=l,Gi=i;else e:for(;null!==Gi;){if(0!==(2048&(l=Gi).flags))switch(l.tag){case 0:case 11:case 15:ns(9,l,l.return)}var v=l.sibling;if(null!==v){v.return=l.return,Gi=v;break e}Gi=l.return}}var b=e.current;for(Gi=b;null!==Gi;){var x=(i=Gi).child;if(0!==(2064&i.subtreeFlags)&&null!==x)x.return=i,Gi=x;else e:for(i=b;null!==Gi;){if(0!==(2048&(s=Gi).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(S){Eu(s,s.return,S)}if(s===i){Gi=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Gi=w;break e}Gi=s.return}}if(Cs=a,Wa(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(at,e)}catch(S){}r=!0}return r}finally{bt=n,js.transition=t}}return!1}function ku(e,t,n){e=Mo(e,t=pi(0,t=ui(n,t),1),1),t=eu(),null!==e&&(yt(e,1,t),ru(e,t))}function Eu(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qs||!qs.has(r))){t=Mo(t,e=hi(t,e=ui(n,e),1),1),e=eu(),null!==t&&(yt(t,1,e),ru(t,e));break}}t=t.return}}function Nu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Ps===e&&(Os&n)===n&&(4===Us||3===Us&&(130023424&Os)===Os&&500>Ye()-Bs?fu(e,0):As|=n),ru(e,t)}function _u(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=zo(e,t))&&(yt(e,t,n),ru(e,n))}function ju(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),_u(e,n)}function Cu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),_u(e,n)}function Pu(e,t){return Qe(e,t)}function Ru(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ou(e,t,n,r){return new Ru(e,t,n,r)}function Tu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Lu(e,t){var n=e.alternate;return null===n?((n=Ou(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Uu(e,t,n,r,a,l){var i=2;if(r=e,"function"===typeof e)Tu(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case k:return zu(n.children,a,l,t);case E:i=8,a|=8;break;case N:return(e=Ou(12,n,t,2|a)).elementType=N,e.lanes=l,e;case P:return(e=Ou(13,n,t,a)).elementType=P,e.lanes=l,e;case R:return(e=Ou(19,n,t,a)).elementType=R,e.lanes=l,e;case L:return Du(n,a,l,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case _:i=10;break e;case j:i=9;break e;case C:i=11;break e;case O:i=14;break e;case T:i=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Ou(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function zu(e,t,n,r){return(e=Ou(7,e,r,t)).lanes=n,e}function Du(e,t,n,r){return(e=Ou(22,e,r,t)).elementType=L,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Ou(6,e,null,t)).lanes=n,e}function Au(e,t,n){return(t=Ou(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Iu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Mu(e,t,n,r,a,o,l,i,s){return e=new Iu(e,t,n,i,s),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Ou(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fo(o),e}function Bu(e){if(!e)return ja;e:{if(We(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ta(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Ta(n))return za(e,n,t)}return t}function Wu(e,t,n,r,a,o,l,i,s){return(e=Mu(n,r,!0,e,0,o,0,i,s)).context=Bu(null),n=e.current,(o=Io(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Mo(n,o,a),e.current.lanes=a,yt(e,a,r),ru(e,r),e}function Hu(e,t,n,r){var a=t.current,o=eu(),l=tu(a);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Io(o,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Mo(a,t,l))&&(nu(e,a,l,o),Bo(e,a,l)),l}function Vu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $u(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qu(e,t){$u(e,t),(e=e.alternate)&&$u(e,t)}ks=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pa.current)bi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bi=!1,function(e,t,n){switch(t.tag){case 3:Pi(t),ho();break;case 5:Go(t);break;case 1:Ta(t.type)&&Da(t);break;case 4:Xo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;_a(ko,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(_a(el,1&el.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fi(e,t,n):(_a(el,1&el.current),null!==(e=Vi(e,t,n))?e.sibling:null);_a(el,1&el.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Wi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),_a(el,el.current),r)break;return null;case 22:case 23:return t.lanes=0,Ei(e,t,n)}return Vi(e,t,n)}(e,t,n);bi=0!==(131072&e.flags)}else bi=!1,ao&&0!==(1048576&t.flags)&&Za(t,qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Hi(e,t),e=t.pendingProps;var a=Oa(t,Ca.current);Ro(t,n),a=gl(null,t,r,e,a,n);var l=yl();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ta(r)?(l=!0,Da(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fo(t),a.updater=ai,t.stateNode=a,a._reactInternals=t,si(t,r,e,n),t=Ci(null,t,r,!0,l,n)):(t.tag=0,ao&&l&&eo(t),xi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Hi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Tu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===C)return 11;if(e===O)return 14}return 2}(r),e=ni(r,e),a){case 0:t=_i(null,t,r,e,n);break e;case 1:t=ji(null,t,r,e,n);break e;case 11:t=wi(null,t,r,e,n);break e;case 14:t=Si(null,t,r,ni(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,_i(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 1:return r=t.type,a=t.pendingProps,ji(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 3:e:{if(Pi(t),null===e)throw Error(o(387));r=t.pendingProps,a=(l=t.memoizedState).element,Ao(e,t),Ho(t,r,null,n);var i=t.memoizedState;if(r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Ri(e,t,r,n,a=ui(Error(o(423)),t));break e}if(r!==a){t=Ri(e,t,r,n,a=ui(Error(o(424)),t));break e}for(ro=ua(t.stateNode.containerInfo.firstChild),no=t,ao=!0,oo=null,n=So(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ho(),r===a){t=Vi(e,t,n);break e}xi(e,t,r,n)}t=t.child}return t;case 5:return Go(t),null===e&&uo(t),r=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==l&&na(r,l)&&(t.flags|=32),Ni(e,t),xi(e,t,i,n),t.child;case 6:return null===e&&uo(t),null;case 13:return Fi(e,t,n);case 4:return Xo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wo(t,null,r,n):xi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,wi(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 7:return xi(e,t,t.pendingProps,n),t.child;case 8:case 12:return xi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,i=a.value,_a(ko,r._currentValue),r._currentValue=i,null!==l)if(ir(l.value,i)){if(l.children===a.children&&!Pa.current){t=Vi(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var s=l.dependencies;if(null!==s){i=l.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===l.tag){(u=Io(-1,n&-n)).tag=2;var c=l.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}l.lanes|=n,null!==(u=l.alternate)&&(u.lanes|=n),Po(l.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===l.tag)i=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(i=l.return))throw Error(o(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Po(i,n,t),i=l.sibling}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}xi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ro(t,n),r=r(a=Oo(a)),t.flags|=1,xi(e,t,r,n),t.child;case 14:return a=ni(r=t.type,t.pendingProps),Si(e,t,r,a=ni(r.type,a),n);case 15:return ki(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ni(r,a),Hi(e,t),t.tag=1,Ta(r)?(e=!0,Da(t)):e=!1,Ro(t,n),li(t,r,a),si(t,r,a,n),Ci(null,t,r,!0,e,n);case 19:return Wi(e,t,n);case 22:return Ei(e,t,n)}throw Error(o(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Ju(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gu(){}function Zu(e,t,n,r,a){var o=n._reactRootContainer;if(o){var l=o;if("function"===typeof a){var i=a;a=function(){var e=Vu(l);i.call(e)}}Hu(t,l,e,a)}else l=function(e,t,n,r,a){if(a){if("function"===typeof r){var o=r;r=function(){var e=Vu(l);o.call(e)}}var l=Wu(t,r,e,0,null,!1,0,"",Gu);return e._reactRootContainer=l,e[ha]=l.current,Wr(8===e.nodeType?e.parentNode:e),cu(),l}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=Vu(s);i.call(e)}}var s=Mu(e,0,!1,null,0,!1,0,"",Gu);return e._reactRootContainer=s,e[ha]=s.current,Wr(8===e.nodeType?e.parentNode:e),cu(function(){Hu(t,s,n,r)}),s}(n,t,e,a,r);return Vu(l)}Ju.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Hu(e,t,null,null)},Ju.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Hu(null,e,null,null)}),t[ha]=null}},Ju.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&Ft(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),ru(t,Ye()),0===(6&Cs)&&(Ws=Ye()+500,Wa()))}break;case 13:cu(function(){var t=zo(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),qu(e,1)}},St=function(e){if(13===e.tag){var t=zo(e,134217728);if(null!==t)nu(t,e,134217728,eu());qu(e,134217728)}},kt=function(e){if(13===e.tag){var t=tu(e),n=zo(e,t);if(null!==n)nu(n,e,t,eu());qu(e,t)}},Et=function(){return bt},Nt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(o(90));Q(r),G(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ce=uu,Pe=cu;var ec={usingClientEntryPoint:!1,Events:[ba,xa,wa,_e,je,uu]},tc={findFiberByHostInstance:va,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),ot=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xu(e))throw Error(o(299));var n=!1,r="",a=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Mu(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,Wr(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Yu(t))throw Error(o(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xu(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,l="",i=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Wu(t,null,e,1,null!=n?n:null,a,0,l,i),e[ha]=t.current,Wr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Ju(t)},t.render=function(e,t,n){if(!Yu(t))throw Error(o(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yu(e))throw Error(o(40));return!!e._reactRootContainer&&(cu(function(){Zu(null,null,e,!1,function(){e._reactRootContainer=null,e[ha]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var o=Object.create(null);n.r(o);var l={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach(e=>l[e]=()=>r[e]);return l.default=()=>r,n.d(o,l),o}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Vt,hasStandardBrowserEnv:()=>qt,hasStandardBrowserWebWorkerEnv:()=>Qt,navigator:()=>$t,origin:()=>Kt});var a,o=n(43),l=n.t(o,2),i=n(391),s=n(950),u=n.t(s,2);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(a||(a={}));const d="popstate";function f(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function p(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function h(e,t){return{usr:e.state,key:e.key,idx:t}}function m(e,t,n,r){return void 0===n&&(n=null),c({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?y(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function g(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function y(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function v(e,t,n,r){void 0===r&&(r={});let{window:o=document.defaultView,v5Compat:l=!1}=r,i=o.history,s=a.Pop,u=null,p=y();function y(){return(i.state||{idx:null}).idx}function v(){s=a.Pop;let e=y(),t=null==e?null:e-p;p=e,u&&u({action:s,location:x.location,delta:t})}function b(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,n="string"===typeof e?e:g(e);return n=n.replace(/ $/,"%20"),f(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==p&&(p=0,i.replaceState(c({},i.state,{idx:p}),""));let x={get action(){return s},get location(){return e(o,i)},listen(e){if(u)throw new Error("A history only accepts one active listener");return o.addEventListener(d,v),u=e,()=>{o.removeEventListener(d,v),u=null}},createHref:e=>t(o,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s=a.Push;let r=m(x.location,e,t);n&&n(r,e),p=y()+1;let c=h(r,p),d=x.createHref(r);try{i.pushState(c,"",d)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;o.location.assign(d)}l&&u&&u({action:s,location:x.location,delta:1})},replace:function(e,t){s=a.Replace;let r=m(x.location,e,t);n&&n(r,e),p=y();let o=h(r,p),c=x.createHref(r);i.replaceState(o,"",c),l&&u&&u({action:s,location:x.location,delta:0})},go:e=>i.go(e)};return x}var b;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(b||(b={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function x(e,t,n){return void 0===n&&(n="/"),w(e,t,n,!1)}function w(e,t,n,r){let a=z(("string"===typeof t?y(t):t).pathname||"/",n);if(null==a)return null;let o=S(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(o);let l=null;for(let i=0;null==l&&i<o.length;++i){let e=U(a);l=T(o[i],e,r)}return l}function S(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(f(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),l.relativePath=l.relativePath.slice(r.length));let i=M([r,l.relativePath]),s=n.concat(l);e.children&&e.children.length>0&&(f(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),S(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:O(i,e.index),routesMeta:s})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of k(e.path))a(e,t,r);else a(e,t)}),t}function k(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let l=k(r.join("/")),i=[];return i.push(...l.map(e=>""===e?o:[o,e].join("/"))),a&&i.push(...l),i.map(t=>e.startsWith("/")&&""===t?"/":t)}const E=/^:[\w-]+$/,N=3,_=2,j=1,C=10,P=-2,R=e=>"*"===e;function O(e,t){let n=e.split("/"),r=n.length;return n.some(R)&&(r+=P),t&&(r+=_),n.filter(e=>!R(e)).reduce((e,t)=>e+(E.test(t)?N:""===t?j:C),r)}function T(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},o="/",l=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=L({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=L({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:M([o,c.pathname]),pathnameBase:B(M([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=M([o,c.pathnameBase]))}return l}function L(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);p("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:l,pattern:e}}function U(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return p(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function z(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function D(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function F(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function A(e,t){let n=F(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function I(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=y(e):(a=c({},e),f(!a.pathname||!a.pathname.includes("?"),D("?","pathname","search",a)),f(!a.pathname||!a.pathname.includes("#"),D("#","pathname","hash",a)),f(!a.search||!a.search.includes("#"),D("#","search","hash",a)));let o,l=""===e||""===a.pathname,i=l?"/":a.pathname;if(null==i)o=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?y(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:W(r),hash:H(a)}}(a,o),u=i&&"/"!==i&&i.endsWith("/"),d=(l||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!d||(s.pathname+="/"),s}const M=e=>e.join("/").replace(/\/\/+/g,"/"),B=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),W=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",H=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function V(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const $=["post","put","patch","delete"],q=(new Set($),["get",...$]);new Set(q),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Q.apply(this,arguments)}const K=o.createContext(null);const J=o.createContext(null);const X=o.createContext(null);const Y=o.createContext(null);const G=o.createContext({outlet:null,matches:[],isDataRoute:!1});const Z=o.createContext(null);function ee(){return null!=o.useContext(Y)}function te(){return ee()||f(!1),o.useContext(Y).location}function ne(e){o.useContext(X).static||o.useLayoutEffect(e)}function re(){let{isDataRoute:e}=o.useContext(G);return e?function(){let{router:e}=pe(de.UseNavigateStable),t=me(fe.UseNavigateStable),n=o.useRef(!1);return ne(()=>{n.current=!0}),o.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,Q({fromRouteId:t},a)))},[e,t])}():function(){ee()||f(!1);let e=o.useContext(K),{basename:t,future:n,navigator:r}=o.useContext(X),{matches:a}=o.useContext(G),{pathname:l}=te(),i=JSON.stringify(A(a,n.v7_relativeSplatPath)),s=o.useRef(!1);return ne(()=>{s.current=!0}),o.useCallback(function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"===typeof n)return void r.go(n);let o=I(n,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:M([t,o.pathname])),(a.replace?r.replace:r.push)(o,a.state,a)},[t,r,i,l,e])}()}function ae(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=o.useContext(X),{matches:a}=o.useContext(G),{pathname:l}=te(),i=JSON.stringify(A(a,r.v7_relativeSplatPath));return o.useMemo(()=>I(e,JSON.parse(i),l,"path"===n),[e,i,l,n])}function oe(e,t,n,r){ee()||f(!1);let{navigator:l}=o.useContext(X),{matches:i}=o.useContext(G),s=i[i.length-1],u=s?s.params:{},c=(s&&s.pathname,s?s.pathnameBase:"/");s&&s.route;let d,p=te();if(t){var h;let e="string"===typeof t?y(t):t;"/"===c||(null==(h=e.pathname)?void 0:h.startsWith(c))||f(!1),d=e}else d=p;let m=d.pathname||"/",g=m;if("/"!==c){let e=c.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let v=x(e,{pathname:g});let b=ce(v&&v.map(e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:M([c,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:M([c,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,r);return t&&b?o.createElement(Y.Provider,{value:{location:Q({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:a.Pop}},b):b}function le(){let e=function(){var e;let t=o.useContext(Z),n=he(fe.UseRouteError),r=me(fe.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=V(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r};return o.createElement(o.Fragment,null,o.createElement("h2",null,"Unexpected Application Error!"),o.createElement("h3",{style:{fontStyle:"italic"}},t),n?o.createElement("pre",{style:a},n):null,null)}const ie=o.createElement(le,null);class se extends o.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?o.createElement(G.Provider,{value:this.props.routeContext},o.createElement(Z.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ue(e){let{routeContext:t,match:n,children:r}=e,a=o.useContext(K);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),o.createElement(G.Provider,{value:t},r)}function ce(e,t,n,r){var a;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=r)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(a=n)?void 0:a.errors;if(null!=s){let e=i.findIndex(e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id]));e>=0||f(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let o=0;o<i.length;o++){let e=i[o];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=o),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight((e,r,a)=>{let l,d=!1,f=null,p=null;var h;n&&(l=s&&r.route.id?s[r.route.id]:void 0,f=r.route.errorElement||ie,u&&(c<0&&0===a?(h="route-fallback",!1||ge[h]||(ge[h]=!0),d=!0,p=null):c===a&&(d=!0,p=r.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,a+1)),g=()=>{let t;return t=l?f:d?p:r.route.Component?o.createElement(r.route.Component,null):r.route.element?r.route.element:e,o.createElement(ue,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?o.createElement(se,{location:n.location,revalidation:n.revalidation,component:f,error:l,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()},null)}var de=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(de||{}),fe=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(fe||{});function pe(e){let t=o.useContext(K);return t||f(!1),t}function he(e){let t=o.useContext(J);return t||f(!1),t}function me(e){let t=function(){let e=o.useContext(G);return e||f(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||f(!1),n.route.id}const ge={};function ye(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}l.startTransition;function ve(e){let{to:t,replace:n,state:r,relative:a}=e;ee()||f(!1);let{future:l,static:i}=o.useContext(X),{matches:s}=o.useContext(G),{pathname:u}=te(),c=re(),d=I(t,A(s,l.v7_relativeSplatPath),u,"path"===a),p=JSON.stringify(d);return o.useEffect(()=>c(JSON.parse(p),{replace:n,state:r,relative:a}),[c,p,a,n,r]),null}function be(e){f(!1)}function xe(e){let{basename:t="/",children:n=null,location:r,navigationType:l=a.Pop,navigator:i,static:s=!1,future:u}=e;ee()&&f(!1);let c=t.replace(/^\/*/,"/"),d=o.useMemo(()=>({basename:c,navigator:i,static:s,future:Q({v7_relativeSplatPath:!1},u)}),[c,u,i,s]);"string"===typeof r&&(r=y(r));let{pathname:p="/",search:h="",hash:m="",state:g=null,key:v="default"}=r,b=o.useMemo(()=>{let e=z(p,c);return null==e?null:{location:{pathname:e,search:h,hash:m,state:g,key:v},navigationType:l}},[c,p,h,m,g,v,l]);return null==b?null:o.createElement(X.Provider,{value:d},o.createElement(Y.Provider,{children:n,value:b}))}function we(e){let{children:t,location:n}=e;return oe(Se(t),n)}new Promise(()=>{});o.Component;function Se(e,t){void 0===t&&(t=[]);let n=[];return o.Children.forEach(e,(e,r)=>{if(!o.isValidElement(e))return;let a=[...t,r];if(e.type===o.Fragment)return void n.push.apply(n,Se(e.props.children,a));e.type!==be&&f(!1),e.props.index&&e.props.children&&f(!1);let l={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=Se(e.props.children,a)),n.push(l)}),n}function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ke.apply(this,arguments)}function Ee(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ne=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(Zr){}new Map;const _e=l.startTransition;u.flushSync,l.useId;function je(e){let{basename:t,children:n,future:r,window:a}=e,l=o.useRef();var i;null==l.current&&(l.current=(void 0===(i={window:a,v5Compat:!0})&&(i={}),v(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return m("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:g(t)},null,i)));let s=l.current,[u,c]=o.useState({action:s.action,location:s.location}),{v7_startTransition:d}=r||{},f=o.useCallback(e=>{d&&_e?_e(()=>c(e)):c(e)},[c,d]);return o.useLayoutEffect(()=>s.listen(f),[s,f]),o.useEffect(()=>ye(r),[r]),o.createElement(xe,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:r})}const Ce="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Pe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Re=o.forwardRef(function(e,t){let n,{onClick:r,relative:a,reloadDocument:l,replace:i,state:s,target:u,to:c,preventScrollReset:d,viewTransition:p}=e,h=Ee(e,Ne),{basename:m}=o.useContext(X),y=!1;if("string"===typeof c&&Pe.test(c)&&(n=c,Ce))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=z(t.pathname,m);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:y=!0}catch(Zr){}let v=function(e,t){let{relative:n}=void 0===t?{}:t;ee()||f(!1);let{basename:r,navigator:a}=o.useContext(X),{hash:l,pathname:i,search:s}=ae(e,{relative:n}),u=i;return"/"!==r&&(u="/"===i?r:M([r,i])),a.createHref({pathname:u,search:s,hash:l})}(c,{relative:a}),b=function(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:s}=void 0===t?{}:t,u=re(),c=te(),d=ae(e,{relative:i});return o.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:g(c)===g(d);u(e,{replace:n,state:a,preventScrollReset:l,relative:i,viewTransition:s})}},[c,u,d,r,a,n,e,l,i,s])}(c,{replace:i,state:s,target:u,preventScrollReset:d,relative:a,viewTransition:p});return o.createElement("a",ke({},h,{href:n||v,onClick:y||l?r:function(e){r&&r(e),e.defaultPrevented||b(e)},ref:t,target:u}))});var Oe,Te;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Oe||(Oe={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Te||(Te={}));var Le=n(579);const Ue=e=>{let{user:t,onLogout:n}=e;const r=t&&t.roles&&t.roles.includes("admin");return(0,Le.jsx)("nav",{className:"navbar",children:(0,Le.jsxs)("div",{className:"container",children:[(0,Le.jsx)("h1",{children:"JWT Auth App"}),(0,Le.jsx)("div",{children:t&&(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)(Re,{to:"/tokens",style:{color:"white",marginRight:20,textDecoration:"underline"},children:"Tokens"}),r&&(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)(Re,{to:"/admin/users",style:{color:"white",marginRight:20,textDecoration:"underline"},children:"Users"}),(0,Le.jsx)(Re,{to:"/admin/permissions",style:{color:"white",marginRight:20,textDecoration:"underline"},children:"Permissions"})]}),(0,Le.jsxs)("span",{style:{marginRight:"15px"},children:["Welcome, ",t.username,"!"]}),(0,Le.jsx)("button",{onClick:n,children:"Logout"})]})})]})})};function ze(e){return ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(e)}function De(e){var t=function(e,t){if("object"!=ze(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=ze(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ze(t)?t:t+""}function Fe(e,t,n){return(t=De(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ae(Object(n),!0).forEach(function(t){Fe(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ae(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Me(e,t){return function(){return e.apply(t,arguments)}}const{toString:Be}=Object.prototype,{getPrototypeOf:We}=Object,{iterator:He,toStringTag:Ve}=Symbol,$e=(qe=Object.create(null),e=>{const t=Be.call(e);return qe[t]||(qe[t]=t.slice(8,-1).toLowerCase())});var qe;const Qe=e=>(e=e.toLowerCase(),t=>$e(t)===e),Ke=e=>t=>typeof t===e,{isArray:Je}=Array,Xe=Ke("undefined");const Ye=Qe("ArrayBuffer");const Ge=Ke("string"),Ze=Ke("function"),et=Ke("number"),tt=e=>null!==e&&"object"===typeof e,nt=e=>{if("object"!==$e(e))return!1;const t=We(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Ve in e)&&!(He in e)},rt=Qe("Date"),at=Qe("File"),ot=Qe("Blob"),lt=Qe("FileList"),it=Qe("URLSearchParams"),[st,ut,ct,dt]=["ReadableStream","Request","Response","Headers"].map(Qe);function ft(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Je(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let l;for(n=0;n<o;n++)l=r[n],t.call(null,e[l],l,e)}}function pt(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const ht="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,mt=e=>!Xe(e)&&e!==ht;const gt=(yt="undefined"!==typeof Uint8Array&&We(Uint8Array),e=>yt&&e instanceof yt);var yt;const vt=Qe("HTMLFormElement"),bt=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),xt=Qe("RegExp"),wt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ft(n,(n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)}),Object.defineProperties(e,r)};const St=Qe("AsyncFunction"),kt=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],ht.addEventListener("message",e=>{let{source:t,data:a}=e;t===ht&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),ht.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,Ze(ht.postMessage)),Et="undefined"!==typeof queueMicrotask?queueMicrotask.bind(ht):"undefined"!==typeof process&&process.nextTick||kt,Nt={isArray:Je,isArrayBuffer:Ye,isBuffer:function(e){return null!==e&&!Xe(e)&&null!==e.constructor&&!Xe(e.constructor)&&Ze(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Ze(e.append)&&("formdata"===(t=$e(e))||"object"===t&&Ze(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Ye(e.buffer),t},isString:Ge,isNumber:et,isBoolean:e=>!0===e||!1===e,isObject:tt,isPlainObject:nt,isReadableStream:st,isRequest:ut,isResponse:ct,isHeaders:dt,isUndefined:Xe,isDate:rt,isFile:at,isBlob:ot,isRegExp:xt,isFunction:Ze,isStream:e=>tt(e)&&Ze(e.pipe),isURLSearchParams:it,isTypedArray:gt,isFileList:lt,forEach:ft,merge:function e(){const{caseless:t}=mt(this)&&this||{},n={},r=(r,a)=>{const o=t&&pt(n,a)||a;nt(n[o])&&nt(r)?n[o]=e(n[o],r):nt(r)?n[o]=e({},r):Je(r)?n[o]=r.slice():n[o]=r};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&ft(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return ft(t,(t,r)=>{n&&Ze(t)?e[r]=Me(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,l;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)l=a[o],r&&!r(l,e,t)||i[l]||(t[l]=e[l],i[l]=!0);e=!1!==n&&We(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:$e,kindOfTest:Qe,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Je(e))return e;let t=e.length;if(!et(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[He]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:vt,hasOwnProperty:bt,hasOwnProp:bt,reduceDescriptors:wt,freezeMethods:e=>{wt(e,(t,n)=>{if(Ze(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Ze(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return Je(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:pt,global:ht,isContextDefined:mt,isSpecCompliantForm:function(e){return!!(e&&Ze(e.append)&&"FormData"===e[Ve]&&e[He])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(tt(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=Je(e)?[]:{};return ft(e,(e,t)=>{const o=n(e,r+1);!Xe(o)&&(a[t]=o)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:St,isThenable:e=>e&&(tt(e)||Ze(e))&&Ze(e.then)&&Ze(e.catch),setImmediate:kt,asap:Et,isIterable:e=>null!=e&&Ze(e[He])};function _t(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}Nt.inherits(_t,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Nt.toJSONObject(this.config),code:this.code,status:this.status}}});const jt=_t.prototype,Ct={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ct[e]={value:e}}),Object.defineProperties(_t,Ct),Object.defineProperty(jt,"isAxiosError",{value:!0}),_t.from=(e,t,n,r,a,o)=>{const l=Object.create(jt);return Nt.toFlatObject(e,l,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),_t.call(l,e.message,t,n,r,a),l.cause=e,l.name=e.name,o&&Object.assign(l,o),l};const Pt=_t;function Rt(e){return Nt.isPlainObject(e)||Nt.isArray(e)}function Ot(e){return Nt.endsWith(e,"[]")?e.slice(0,-2):e}function Tt(e,t,n){return e?e.concat(t).map(function(e,t){return e=Ot(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Lt=Nt.toFlatObject(Nt,{},null,function(e){return/^is[A-Z]/.test(e)});const Ut=function(e,t,n){if(!Nt.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Nt.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!Nt.isUndefined(t[e])})).metaTokens,a=n.visitor||u,o=n.dots,l=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Nt.isSpecCompliantForm(t);if(!Nt.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(Nt.isDate(e))return e.toISOString();if(Nt.isBoolean(e))return e.toString();if(!i&&Nt.isBlob(e))throw new Pt("Blob is not supported. Use a Buffer instead.");return Nt.isArrayBuffer(e)||Nt.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(Nt.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Nt.isArray(e)&&function(e){return Nt.isArray(e)&&!e.some(Rt)}(e)||(Nt.isFileList(e)||Nt.endsWith(n,"[]"))&&(i=Nt.toArray(e)))return n=Ot(n),i.forEach(function(e,r){!Nt.isUndefined(e)&&null!==e&&t.append(!0===l?Tt([n],r,o):null===l?n:n+"[]",s(e))}),!1;return!!Rt(e)||(t.append(Tt(a,n,o),s(e)),!1)}const c=[],d=Object.assign(Lt,{defaultVisitor:u,convertValue:s,isVisitable:Rt});if(!Nt.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Nt.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Nt.forEach(n,function(n,o){!0===(!(Nt.isUndefined(n)||null===n)&&a.call(t,n,Nt.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])}),c.pop()}}(e),t};function zt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function Dt(e,t){this._pairs=[],e&&Ut(e,this,t)}const Ft=Dt.prototype;Ft.append=function(e,t){this._pairs.push([e,t])},Ft.toString=function(e){const t=e?function(t){return e.call(this,t,zt)}:zt;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const At=Dt;function It(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Mt(e,t,n){if(!t)return e;const r=n&&n.encode||It;Nt.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):Nt.isURLSearchParams(t)?t.toString():new At(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const Bt=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Nt.forEach(this.handlers,function(t){null!==t&&e(t)})}},Wt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ht={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:At,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Vt="undefined"!==typeof window&&"undefined"!==typeof document,$t="object"===typeof navigator&&navigator||void 0,qt=Vt&&(!$t||["ReactNative","NativeScript","NS"].indexOf($t.product)<0),Qt="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Kt=Vt&&window.location.href||"http://localhost",Jt=Ie(Ie({},r),Ht);const Xt=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const l=Number.isFinite(+o),i=a>=e.length;if(o=!o&&Nt.isArray(r)?r.length:o,i)return Nt.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!l;r[o]&&Nt.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],a)&&Nt.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!l}if(Nt.isFormData(e)&&Nt.isFunction(e.entries)){const n={};return Nt.forEachEntry(e,(e,r)=>{t(function(e){return Nt.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const Yt={transitional:Wt,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=Nt.isObject(e);a&&Nt.isHTMLForm(e)&&(e=new FormData(e));if(Nt.isFormData(e))return r?JSON.stringify(Xt(e)):e;if(Nt.isArrayBuffer(e)||Nt.isBuffer(e)||Nt.isStream(e)||Nt.isFile(e)||Nt.isBlob(e)||Nt.isReadableStream(e))return e;if(Nt.isArrayBufferView(e))return e.buffer;if(Nt.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Ut(e,new Jt.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Jt.isNode&&Nt.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=Nt.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Ut(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(Nt.isString(e))try{return(t||JSON.parse)(e),Nt.trim(e)}catch(Zr){if("SyntaxError"!==Zr.name)throw Zr}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Yt.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Nt.isResponse(e)||Nt.isReadableStream(e))return e;if(e&&Nt.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(Zr){if(n){if("SyntaxError"===Zr.name)throw Pt.from(Zr,Pt.ERR_BAD_RESPONSE,this,null,this.response);throw Zr}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Jt.classes.FormData,Blob:Jt.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Nt.forEach(["delete","get","head","post","put","patch"],e=>{Yt.headers[e]={}});const Gt=Yt,Zt=Nt.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),en=Symbol("internals");function tn(e){return e&&String(e).trim().toLowerCase()}function nn(e){return!1===e||null==e?e:Nt.isArray(e)?e.map(nn):String(e)}function rn(e,t,n,r,a){return Nt.isFunction(r)?r.call(this,t,n):(a&&(t=n),Nt.isString(t)?Nt.isString(r)?-1!==t.indexOf(r):Nt.isRegExp(r)?r.test(t):void 0:void 0)}class an{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=tn(t);if(!a)throw new Error("header name must be a non-empty string");const o=Nt.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=nn(e))}const o=(e,t)=>Nt.forEach(e,(e,n)=>a(e,n,t));if(Nt.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(Nt.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Zt[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(Nt.isObject(e)&&Nt.isIterable(e)){let n,r,a={};for(const t of e){if(!Nt.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?Nt.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=tn(e)){const n=Nt.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Nt.isFunction(t))return t.call(this,e,n);if(Nt.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=tn(e)){const n=Nt.findKey(this,e);return!(!n||void 0===this[n]||t&&!rn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=tn(e)){const a=Nt.findKey(n,e);!a||t&&!rn(0,n[a],a,t)||(delete n[a],r=!0)}}return Nt.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!rn(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return Nt.forEach(this,(r,a)=>{const o=Nt.findKey(n,a);if(o)return t[o]=nn(r),void delete t[a];const l=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();l!==a&&delete t[a],t[l]=nn(r),n[l]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Nt.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Nt.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[en]=this[en]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=tn(e);t[r]||(!function(e,t){const n=Nt.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return Nt.isArray(e)?e.forEach(r):r(e),this}}an.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Nt.reduceDescriptors(an.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),Nt.freezeMethods(an);const on=an;function ln(e,t){const n=this||Gt,r=t||n,a=on.from(r.headers);let o=r.data;return Nt.forEach(e,function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function sn(e){return!(!e||!e.__CANCEL__)}function un(e,t,n){Pt.call(this,null==e?"canceled":e,Pt.ERR_CANCELED,t,n),this.name="CanceledError"}Nt.inherits(un,Pt,{__CANCEL__:!0});const cn=un;function dn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Pt("Request failed with status code "+n.status,[Pt.ERR_BAD_REQUEST,Pt.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const fn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,l=0;return t=void 0!==t?t:1e3,function(i){const s=Date.now(),u=r[l];a||(a=s),n[o]=i,r[o]=s;let c=l,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===l&&(l=(l+1)%e),s-a<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}};const pn=function(e,t){let n,r,a=0,o=1e3/t;const l=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,s=new Array(i),u=0;u<i;u++)s[u]=arguments[u];t>=o?l(s,e):(n=s,r||(r=setTimeout(()=>{r=null,l(n)},o-t)))},()=>n&&l(n)]},hn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=fn(50,250);return pn(n=>{const o=n.loaded,l=n.lengthComputable?n.total:void 0,i=o-r,s=a(i);r=o;e({loaded:o,total:l,progress:l?o/l:void 0,bytes:i,rate:s||void 0,estimated:s&&l&&o<=l?(l-o)/s:void 0,event:n,lengthComputable:null!=l,[t?"download":"upload"]:!0})},n)},mn=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},gn=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Nt.asap(()=>e(...n))},yn=Jt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Jt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Jt.origin),Jt.navigator&&/(msie|trident)/i.test(Jt.navigator.userAgent)):()=>!0,vn=Jt.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const l=[e+"="+encodeURIComponent(t)];Nt.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),Nt.isString(r)&&l.push("path="+r),Nt.isString(a)&&l.push("domain="+a),!0===o&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function bn(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const xn=e=>e instanceof on?Ie({},e):e;function wn(e,t){t=t||{};const n={};function r(e,t,n,r){return Nt.isPlainObject(e)&&Nt.isPlainObject(t)?Nt.merge.call({caseless:r},e,t):Nt.isPlainObject(t)?Nt.merge({},t):Nt.isArray(t)?t.slice():t}function a(e,t,n,a){return Nt.isUndefined(t)?Nt.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!Nt.isUndefined(t))return r(void 0,t)}function l(e,t){return Nt.isUndefined(t)?Nt.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const s={url:o,method:o,data:o,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:i,headers:(e,t,n)=>a(xn(e),xn(t),0,!0)};return Nt.forEach(Object.keys(Object.assign({},e,t)),function(r){const o=s[r]||a,l=o(e[r],t[r],r);Nt.isUndefined(l)&&o!==i||(n[r]=l)}),n}const Sn=e=>{const t=wn({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:l,headers:i,auth:s}=t;if(t.headers=i=on.from(i),t.url=Mt(bn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),Nt.isFormData(r))if(Jt.hasStandardBrowserEnv||Jt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(Jt.hasStandardBrowserEnv&&(a&&Nt.isFunction(a)&&(a=a(t)),a||!1!==a&&yn(t.url))){const e=o&&l&&vn.read(l);e&&i.set(o,e)}return t},kn="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Sn(e);let a=r.data;const o=on.from(r.headers).normalize();let l,i,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=on.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());dn(function(e){t(e),h()},function(e){n(e),h()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Pt("Request aborted",Pt.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Pt("Network Error",Pt.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Wt;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Pt(t,a.clarifyTimeoutError?Pt.ETIMEDOUT:Pt.ECONNABORTED,e,m)),m=null},void 0===a&&o.setContentType(null),"setRequestHeader"in m&&Nt.forEach(o.toJSON(),function(e,t){m.setRequestHeader(t,e)}),Nt.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([s,c]=hn(p,!0),m.addEventListener("progress",s)),f&&m.upload&&([i,u]=hn(f),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(l=t=>{m&&(n(!t||t.type?new cn(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===Jt.protocols.indexOf(y)?n(new Pt("Unsupported protocol "+y+":",Pt.ERR_BAD_REQUEST,e)):m.send(a||null)})},En=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,l();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Pt?t:new cn(t instanceof Error?t.message:t))}};let o=t&&setTimeout(()=>{o=null,a(new Pt("timeout ".concat(t," of ms exceeded"),Pt.ETIMEDOUT))},t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:i}=r;return i.unsubscribe=()=>Nt.asap(l),i}};function Nn(e,t){this.v=e,this.k=t}function _n(e){return function(){return new jn(e.apply(this,arguments))}}function jn(e){var t,n;function r(t,n){try{var o=e[t](n),l=o.value,i=l instanceof Nn;Promise.resolve(i?l.v:l).then(function(n){if(i){var s="return"===t?"return":"next";if(!l.k||n.done)return r(s,n);n=e[s](n).value}a(o.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(o,l){var i={key:e,arg:a,resolve:o,reject:l,next:null};n?n=n.next=i:(t=n=i,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function Cn(e){return new Nn(e,0)}function Pn(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new Nn(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Rn(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new On(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function On(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return On=function(e){this.s=e,this.n=e.next},On.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new On(e)}jn.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},jn.prototype.next=function(e){return this._invoke("next",e)},jn.prototype.throw=function(e){return this._invoke("throw",e)},jn.prototype.return=function(e){return this._invoke("return",e)};const Tn=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Ln=function(){var e=_n(function*(e,t){var n,r=!1,a=!1;try{for(var o,l=Rn(Un(e));r=!(o=yield Cn(l.next())).done;r=!1){const e=o.value;yield*Pn(Rn(Tn(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=l.return&&(yield Cn(l.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),Un=function(){var e=_n(function*(e){if(e[Symbol.asyncIterator])return void(yield*Pn(Rn(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Cn(t.read());if(e)break;yield n}}finally{yield Cn(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),zn=(e,t,n,r)=>{const a=Ln(e,t);let o,l=0,i=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let o=r.byteLength;if(n){let e=l+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},Dn="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Fn=Dn&&"function"===typeof ReadableStream,An=Dn&&("function"===typeof TextEncoder?(In=new TextEncoder,e=>In.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var In;const Mn=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(Zr){return!1}},Bn=Fn&&Mn(()=>{let e=!1;const t=new Request(Jt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Wn=Fn&&Mn(()=>Nt.isReadableStream(new Response("").body)),Hn={stream:Wn&&(e=>e.body)};var Vn;Dn&&(Vn=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Hn[e]&&(Hn[e]=Nt.isFunction(Vn[e])?t=>t[e]():(t,n)=>{throw new Pt("Response type '".concat(e,"' is not supported"),Pt.ERR_NOT_SUPPORT,n)})}));const $n=async(e,t)=>{const n=Nt.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Nt.isBlob(e))return e.size;if(Nt.isSpecCompliantForm(e)){const t=new Request(Jt.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Nt.isArrayBufferView(e)||Nt.isArrayBuffer(e)?e.byteLength:(Nt.isURLSearchParams(e)&&(e+=""),Nt.isString(e)?(await An(e)).byteLength:void 0)})(t):n},qn=Dn&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:l,onDownloadProgress:i,onUploadProgress:s,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Sn(e);u=u?(u+"").toLowerCase():"text";let p,h=En([a,o&&o.toAbortSignal()],l);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(s&&Bn&&"get"!==n&&"head"!==n&&0!==(g=await $n(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Nt.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=mn(g,hn(gn(s)));r=zn(n.body,65536,e,t)}}Nt.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,Ie(Ie({},f),{},{signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let o=await fetch(p,f);const l=Wn&&("stream"===u||"response"===u);if(Wn&&(i||l&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=o[t]});const t=Nt.toFiniteNumber(o.headers.get("content-length")),[n,r]=i&&mn(t,hn(gn(i),!0))||[];o=new Response(zn(o.body,65536,n,()=>{r&&r(),m&&m()}),e)}u=u||"text";let y=await Hn[Nt.findKey(Hn,u)||"text"](o,e);return!l&&m&&m(),await new Promise((t,n)=>{dn(t,n,{data:y,headers:on.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})})}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new Pt("Network Error",Pt.ERR_NETWORK,e,p),{cause:y.cause||y});throw Pt.from(y,y&&y.code,e,p)}}),Qn={http:null,xhr:kn,fetch:qn};Nt.forEach(Qn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(Zr){}Object.defineProperty(e,"adapterName",{value:t})}});const Kn=e=>"- ".concat(e),Jn=e=>Nt.isFunction(e)||null===e||!1===e,Xn=e=>{e=Nt.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!Jn(n)&&(r=Qn[(t=String(n)).toLowerCase()],void 0===r))throw new Pt("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(Kn).join("\n"):" "+Kn(e[0]):"as no adapter specified";throw new Pt("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Yn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new cn(null,e)}function Gn(e){Yn(e),e.headers=on.from(e.headers),e.data=ln.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Xn(e.adapter||Gt.adapter)(e).then(function(t){return Yn(e),t.data=ln.call(e,e.transformResponse,t),t.headers=on.from(t.headers),t},function(t){return sn(t)||(Yn(e),t&&t.response&&(t.response.data=ln.call(e,e.transformResponse,t.response),t.response.headers=on.from(t.response.headers))),Promise.reject(t)})}const Zn="1.10.0",er={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{er[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const tr={};er.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Zn+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new Pt(r(a," has been removed"+(t?" in "+t:"")),Pt.ERR_DEPRECATED);return t&&!tr[a]&&(tr[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},er.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const nr={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Pt("options must be an object",Pt.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],l=t[o];if(l){const t=e[o],n=void 0===t||l(t,o,e);if(!0!==n)throw new Pt("option "+o+" must be "+n,Pt.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new Pt("Unknown option "+o,Pt.ERR_BAD_OPTION)}},validators:er},rr=nr.validators;class ar{constructor(e){this.defaults=e||{},this.interceptors={request:new Bt,response:new Bt}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(Zr){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=wn(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&nr.assertOptions(n,{silentJSONParsing:rr.transitional(rr.boolean),forcedJSONParsing:rr.transitional(rr.boolean),clarifyTimeoutError:rr.transitional(rr.boolean)},!1),null!=r&&(Nt.isFunction(r)?t.paramsSerializer={serialize:r}:nr.assertOptions(r,{encode:rr.function,serialize:rr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),nr.assertOptions(t,{baseUrl:rr.spelling("baseURL"),withXsrfToken:rr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&Nt.merge(a.common,a[t.method]);a&&Nt.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=on.concat(o,a);const l=[];let i=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,l.unshift(e.fulfilled,e.rejected))});const s=[];let u;this.interceptors.response.forEach(function(e){s.push(e.fulfilled,e.rejected)});let c,d=0;if(!i){const e=[Gn.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=l.length;let f=t;for(d=0;d<c;){const e=l[d++],t=l[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=Gn.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return Mt(bn((e=wn(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Nt.forEach(["delete","get","head","options"],function(e){ar.prototype[e]=function(t,n){return this.request(wn(n||{},{method:e,url:t,data:(n||{}).data}))}}),Nt.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(wn(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ar.prototype[e]=t(),ar.prototype[e+"Form"]=t(!0)});const or=ar;class lr{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new cn(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new lr(function(t){e=t}),cancel:e}}}const ir=lr;const sr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(sr).forEach(e=>{let[t,n]=e;sr[n]=t});const ur=sr;const cr=function e(t){const n=new or(t),r=Me(or.prototype.request,n);return Nt.extend(r,or.prototype,n,{allOwnKeys:!0}),Nt.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(wn(t,n))},r}(Gt);cr.Axios=or,cr.CanceledError=cn,cr.CancelToken=ir,cr.isCancel=sn,cr.VERSION=Zn,cr.toFormData=Ut,cr.AxiosError=Pt,cr.Cancel=cr.CanceledError,cr.all=function(e){return Promise.all(e)},cr.spread=function(e){return function(t){return e.apply(null,t)}},cr.isAxiosError=function(e){return Nt.isObject(e)&&!0===e.isAxiosError},cr.mergeConfig=wn,cr.AxiosHeaders=on,cr.formToJSON=e=>Xt(Nt.isHTMLForm(e)?new FormData(e):e),cr.getAdapter=Xn,cr.HttpStatusCode=ur,cr.default=cr;const dr=cr,fr={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:8080/api/v1",pr=dr.create({baseURL:fr,headers:{"Content-Type":"application/json"}});function hr(e,t){localStorage.setItem("token",e),localStorage.setItem("refresh_token",t)}function mr(){localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user")}pr.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e));let gr=!1,yr=[];function vr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;yr.forEach(n=>{e?n.reject(e):n.resolve(t)}),yr=[]}pr.interceptors.response.use(e=>e,async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;const e=localStorage.getItem("refresh_token"),t=localStorage.getItem("user");if(e&&t)try{if(gr)return new Promise((e,t)=>{yr.push({resolve:e,reject:t})}).then(e=>{const t=e;return n.headers=n.headers||{},n.headers.Authorization="Bearer "+t,pr(n)}).catch(e=>Promise.reject(e));gr=!0;const{id:r}=JSON.parse(t),a=await pr.post("/auth/refresh",{user_id:r,refresh_token:e});return hr(a.data.token,e),vr(null,a.data.token),n.headers=n.headers||{},n.headers.Authorization="Bearer "+a.data.token,pr(n)}catch(r){return vr(r,null),mr(),window.location.href="/login",Promise.reject(r)}finally{gr=!1}else mr(),window.location.href="/login"}return Promise.reject(e)});const br=e=>pr.post("/auth/register",e),xr=async e=>{const t=await pr.post("/auth/login",e);hr(t.data.token,t.data.refresh_token);const n=Ie(Ie({},t.data.user),{},{roles:t.data.roles});return localStorage.setItem("user",JSON.stringify(n)),t.data.user=n,t},wr=(e,t)=>pr.post("/auth/refresh",{user_id:e,refresh_token:t}),Sr=()=>{const e=localStorage.getItem("token"),t=localStorage.getItem("refresh_token"),n=localStorage.getItem("user");let r;if(n)try{r=JSON.parse(n).id}catch(a){}return mr(),pr.post("/auth/logout",{user_id:r,refresh_token:t,access_token:e})},kr=()=>pr.post("/auth/revoke-all"),Er=()=>pr.get("/user/profile"),Nr=e=>pr.post("/admin/user-management",e),_r=(e,t)=>pr.put("/admin/user-management/".concat(e),t),jr=e=>pr.post("/admin/role-management",e),Cr=(e,t)=>pr.put("/admin/role-management/".concat(e),t),Pr=e=>pr.post("/admin/permission-management",e),Rr=(e,t)=>pr.put("/admin/permission-management/".concat(e),t),Or=()=>pr.get("/admin/users"),Tr=e=>pr.get("/admin/user/".concat(e,"/permissions")),Lr=e=>pr.get("/admin/user/".concat(e,"/direct-permissions")),Ur=e=>{let{onAuthSuccess:t}=e;const[n,r]=(0,o.useState)(!0),[a,l]=(0,o.useState)({username:"",email:"",password:""}),[i,s]=(0,o.useState)(""),[u,c]=(0,o.useState)(!1),d=e=>{l(Ie(Ie({},a),{},{[e.target.name]:e.target.value}))};return(0,Le.jsxs)("div",{className:"auth-container",children:[(0,Le.jsx)("h2",{children:n?"Login":"Register"}),i&&(0,Le.jsx)("div",{className:"error",children:i}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),s(""),c(!0);try{let e;e=n?await xr({email:a.email,password:a.password}):await br(a);const{token:r,refresh_token:o,user:l,roles:i}=e.data;hr(r,o);const s=Ie(Ie({},l),{},{roles:i});localStorage.setItem("user",JSON.stringify(s)),t(s)}catch(l){var r,o;s((null===(r=l.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.error)||"An error occurred")}finally{c(!1)}},children:[!n&&(0,Le.jsxs)("div",{className:"form-group",children:[(0,Le.jsx)("label",{htmlFor:"username",children:"Username"}),(0,Le.jsx)("input",{type:"text",id:"username",name:"username",value:a.username,onChange:d,required:!0,minLength:3,maxLength:50})]}),(0,Le.jsxs)("div",{className:"form-group",children:[(0,Le.jsx)("label",{htmlFor:"email",children:"Email"}),(0,Le.jsx)("input",{type:"email",id:"email",name:"email",value:a.email,onChange:d,required:!0})]}),(0,Le.jsxs)("div",{className:"form-group",children:[(0,Le.jsx)("label",{htmlFor:"password",children:"Password"}),(0,Le.jsx)("input",{type:"password",id:"password",name:"password",value:a.password,onChange:d,required:!0,minLength:6})]}),(0,Le.jsx)("button",{type:"submit",className:"btn",disabled:u,children:u?"Loading...":n?"Login":"Register"})]}),(0,Le.jsx)("div",{className:"toggle-form",children:(0,Le.jsx)("button",{type:"button",onClick:()=>{r(!n),s(""),l({username:"",email:"",password:""})},children:n?"Don't have an account? Register":"Already have an account? Login"})})]})},zr=e=>{let{onLogout:t}=e;const[n,r]=(0,o.useState)(null),[a,l]=(0,o.useState)(!0),[i,s]=(0,o.useState)(""),[u,c]=(0,o.useState)("");(0,o.useEffect)(()=>{d()},[]);const d=async()=>{l(!0),s(""),c("");try{const e=await Er(),{user:t}=e.data;localStorage.setItem("user",JSON.stringify(t)),r(t)}catch(e){s("Failed to load profile")}finally{l(!1)}};n&&n.roles&&n.roles.includes("admin");return a?(0,Le.jsx)("div",{className:"container",children:(0,Le.jsx)("div",{className:"profile-card",children:(0,Le.jsx)("p",{children:"Loading profile..."})})}):i?(0,Le.jsx)("div",{className:"container",children:(0,Le.jsxs)("div",{className:"profile-card",children:[(0,Le.jsx)("div",{className:"error",children:i}),(0,Le.jsx)("button",{className:"btn",onClick:d,children:"Retry"})]})}):(0,Le.jsx)("div",{className:"container",children:(0,Le.jsxs)("div",{className:"profile-card",children:[(0,Le.jsx)("h2",{children:"User Profile"}),u&&(0,Le.jsx)("div",{className:"success",children:u}),n&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"ID:"})," ",n.id]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Username:"})," ",n.username]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Email:"})," ",n.email]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Created:"})," ",new Date(n.created_at).toLocaleDateString()]}),(0,Le.jsxs)("div",{className:"profile-info",children:[(0,Le.jsx)("strong",{children:"Roles:"})," ",n.roles&&n.roles.length>0?n.roles.join(", "):"None"]})]}),(0,Le.jsx)("button",{className:"btn btn-secondary",onClick:async()=>{try{await Sr(),c("Logged out successfully!")}catch(e){s("Logout error")}finally{mr(),t()}},style:{marginTop:"20px"},children:"Logout"})]})})},Dr=()=>{const[e,t]=(0,o.useState)(null),[n,r]=(0,o.useState)(!1),[a,l]=(0,o.useState)("");(0,o.useEffect)(()=>{i()},[]);const i=()=>{const e=localStorage.getItem("token"),n=localStorage.getItem("refresh_token"),r=localStorage.getItem("user");if(e&&n&&r)try{const a=JSON.parse(atob(e.split(".")[1])),o=new Date(1e3*a.exp);t({token:e.substring(0,20)+"...",refreshToken:n.substring(0,20)+"...",user:JSON.parse(r),tokenExpiry:o})}catch(a){console.error("Error parsing token:",a)}},s=()=>!(null===e||void 0===e||!e.tokenExpiry)&&new Date>e.tokenExpiry;return e?(0,Le.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,Le.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Token Manager"}),(0,Le.jsxs)("div",{className:"space-y-4",children:[(0,Le.jsxs)("div",{children:[(0,Le.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User:"}),(0,Le.jsxs)("p",{className:"text-sm text-gray-900",children:[e.user.username," (",e.user.email,")"]})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Access Token:"}),(0,Le.jsx)("p",{className:"text-sm text-gray-900 font-mono",children:e.token}),(0,Le.jsxs)("div",{className:"flex items-center mt-1",children:[(0,Le.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(s()?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:s()?"Expired":"Valid"}),(0,Le.jsx)("span",{className:"ml-2 text-xs text-gray-500",children:s()?"Token has expired":"Expires in ".concat((()=>{if(null===e||void 0===e||!e.tokenExpiry)return"";const t=new Date,n=e.tokenExpiry.getTime()-t.getTime();if(n<=0)return"Expired";const r=Math.floor(n/6e4),a=Math.floor(r/60);return a>0?"".concat(a,"h ").concat(r%60,"m"):"".concat(r,"m")})())})]})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Refresh Token:"}),(0,Le.jsx)("p",{className:"text-sm text-gray-900 font-mono",children:e.refreshToken})]}),(0,Le.jsxs)("div",{className:"flex space-x-3",children:[(0,Le.jsx)("button",{onClick:async()=>{var t;if(null!==e&&void 0!==e&&null!==(t=e.user)&&void 0!==t&&t.id){r(!0),l("");try{const t=localStorage.getItem("refresh_token");if(!t)throw new Error("No refresh token found");await wr(e.user.id,t);l("\u2705 Token refreshed successfully!"),i()}catch(o){var n,a;l("\u274c Failed to refresh token: ".concat((null===(n=o.response)||void 0===n||null===(a=n.data)||void 0===a?void 0:a.error)||o.message))}finally{r(!1)}}},disabled:n,className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50",children:n?"Refreshing...":"Refresh Token"}),(0,Le.jsx)("button",{onClick:async()=>{try{await kr(),l("\u2705 All refresh tokens revoked successfully!"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user"),t(null)}catch(r){var e,n;l("\u274c Failed to revoke tokens: ".concat((null===(e=r.response)||void 0===e||null===(n=e.data)||void 0===n?void 0:n.error)||r.message))}},className:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded",children:"Revoke All Tokens"}),(0,Le.jsx)("button",{onClick:i,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Reload Info"})]}),a&&(0,Le.jsx)("div",{className:"p-3 rounded ".concat(a.startsWith("\u2705")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:a})]})]}):(0,Le.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,Le.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Token Manager"}),(0,Le.jsx)("p",{className:"text-gray-600",children:"No active session found. Please login first."})]})},Fr=()=>{const[e,t]=(0,o.useState)([]),[n,r]=(0,o.useState)(!0),[a,l]=(0,o.useState)(null),[i,s]=(0,o.useState)(!1),[u,c]=(0,o.useState)("");(0,o.useEffect)(()=>{d()},[]);const d=async()=>{r(!0),c("");try{const e=(await Or()).data,n=await Promise.all(e.map(async e=>{const[t,n]=await Promise.all([Tr(e.id),Lr(e.id)]);return Ie(Ie({},e),{},{permissions:t.data,directPermissions:n.data})}));t(n)}catch(e){c("Failed to load users")}finally{r(!1)}};return(0,Le.jsxs)("div",{className:"container",children:[(0,Le.jsx)("h2",{children:"User Management"}),u&&(0,Le.jsx)("div",{className:"error",children:u}),n?(0,Le.jsx)("p",{children:"Loading users..."}):(0,Le.jsxs)("table",{className:"user-table",style:{width:"100%",borderCollapse:"collapse"},children:[(0,Le.jsx)("thead",{children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{children:"ID"}),(0,Le.jsx)("th",{children:"Username"}),(0,Le.jsx)("th",{children:"Email"}),(0,Le.jsx)("th",{children:"Role"}),(0,Le.jsx)("th",{children:"Actions"})]})}),(0,Le.jsx)("tbody",{children:e.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{children:e.id}),(0,Le.jsx)("td",{children:e.username}),(0,Le.jsx)("td",{children:e.email}),(0,Le.jsx)("td",{children:e.roles&&e.roles.length>0?e.roles.join(", "):"None"}),(0,Le.jsx)("td",{children:(0,Le.jsx)("button",{className:"btn",onClick:()=>(e=>{l(e),s(!0)})(e),children:"Xem quy\u1ec1n"})})]},e.id))})]}),i&&a&&(0,Le.jsx)("div",{className:"modal-overlay",style:{position:"fixed",top:0,left:0,width:"100vw",height:"100vh",background:"rgba(0,0,0,0.3)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,Le.jsxs)("div",{className:"modal",style:{background:"white",padding:30,borderRadius:10,minWidth:350},children:[(0,Le.jsxs)("h3",{children:["Quy\u1ec1n c\u1ee7a user: ",a.username]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("strong",{children:"Quy\u1ec1n t\u1ed5ng h\u1ee3p (qua role):"}),(0,Le.jsx)("ul",{children:a.permissions&&0!==a.permissions.length?a.permissions.map(e=>(0,Le.jsx)("li",{children:e},e)):(0,Le.jsx)("li",{children:"Kh\xf4ng c\xf3"})})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsx)("strong",{children:"Quy\u1ec1n tr\u1ef1c ti\u1ebfp:"}),(0,Le.jsx)("ul",{children:a.directPermissions&&0!==a.directPermissions.length?a.directPermissions.map(e=>(0,Le.jsx)("li",{children:e},e)):(0,Le.jsx)("li",{children:"Kh\xf4ng c\xf3"})})]}),(0,Le.jsx)("button",{className:"btn btn-secondary",onClick:()=>{l(null),s(!1)},style:{marginTop:20},children:"\u0110\xf3ng"})]})})]})},Ar={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:8080/api/v1",Ir=dr.create({baseURL:Ar,headers:{"Content-Type":"application/json"}});Ir.interceptors.request.use(e=>{const t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),Ir.interceptors.response.use(e=>e,async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;try{const e=localStorage.getItem("refresh_token");if(!e)throw new Error("No refresh token available");const t=await dr.post("".concat(Ar,"/auth/refresh"),{refresh_token:e}),{token:r,refresh_token:a}=t.data;return localStorage.setItem("access_token",r),localStorage.setItem("refresh_token",a),n.headers.Authorization="Bearer ".concat(r),Ir(n)}catch(r){return localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user"),window.location.href="/login",Promise.reject(r)}}return Promise.reject(e)});const Mr={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:8080/api/v1",Br=dr.create({baseURL:Mr,headers:{"Content-Type":"application/json"}});Br.interceptors.request.use(e=>{const t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),Br.interceptors.response.use(e=>e,async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;try{const e=localStorage.getItem("refresh_token");if(!e)throw new Error("No refresh token available");const t=await dr.post("".concat(Mr,"/auth/refresh"),{refresh_token:e}),{token:r,refresh_token:a}=t.data;return localStorage.setItem("access_token",r),localStorage.setItem("refresh_token",a),n.headers.Authorization="Bearer ".concat(r),Br(n)}catch(r){return localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user"),window.location.href="/login",Promise.reject(r)}}return Promise.reject(e)});class Wr{constructor(e){this.baseUrl=void 0,this.baseUrl=e}list(e){return Br.get(this.baseUrl,{params:e})}search(e){return Br.post("".concat(this.baseUrl,"/search"),e)}getById(e){return Br.get("".concat(this.baseUrl,"/").concat(e))}create(e){return Br.post(this.baseUrl,e)}update(e,t){return Br.put("".concat(this.baseUrl,"/").concat(e),t)}delete(e){return Br.delete("".concat(this.baseUrl,"/").concat(e))}}const Hr=new class extends Wr{constructor(){super("/admin/user-management")}getUsersWithRoles(){return Br.get("/admin/users")}getUserRoles(e){return Br.get("/admin/user/".concat(e,"/roles"))}getUserPermissions(e){return Br.get("/admin/user/".concat(e,"/permissions"))}getUserDirectPermissions(e){return Br.get("/admin/user/".concat(e,"/direct-permissions"))}assignRole(e,t){return Br.post("/admin/assign-role",{user_id:e,role:t})}removeRole(e,t){return Br.post("/admin/remove-role",{user_id:e,role:t})}assignPermission(e,t){return Br.post("/admin/assign-user-permission",{user_id:e,permission:t})}removePermission(e,t){return Br.post("/admin/remove-user-permission",{user_id:e,permission:t})}assignRoleCRUD(e,t){return Br.post("".concat(this.baseUrl,"/").concat(e,"/roles"),{role_id:t})}removeRoleCRUD(e,t){return Br.delete("".concat(this.baseUrl,"/").concat(e,"/roles/").concat(t))}assignPermissionCRUD(e,t){return Br.post("".concat(this.baseUrl,"/").concat(e,"/permissions"),{permission_id:t})}removePermissionCRUD(e,t){return Br.delete("".concat(this.baseUrl,"/").concat(e,"/permissions/").concat(t))}};const Vr=new class extends Wr{constructor(){super("/admin/role-management")}getAllRoles(){return Br.get("/admin/roles")}getActiveRoles(){return this.list({active_only:!0})}getRolePermissions(e){return Br.get("/admin/role/".concat(e,"/permissions"))}assignPermission(e,t){return Br.post("/admin/assign-permission",{role_id:e,permission:t})}removePermission(e,t){return Br.post("/admin/remove-permission",{role_id:e,permission:t})}assignPermissionCRUD(e,t){return Br.post("".concat(this.baseUrl,"/").concat(e,"/permissions"),{permission_id:t})}removePermissionCRUD(e,t){return Br.delete("".concat(this.baseUrl,"/").concat(e,"/permissions/").concat(t))}searchRoles(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return this.search({page:t,page_size:n,search_term:e,filters:{is_active:!0}})}getRolesByName(e){return this.list({page:1,page_size:100,name_like:e})}toggleActiveStatus(e,t){return this.update(e,{is_active:t})}bulkDelete(e){return Br.post("".concat(this.baseUrl,"/bulk-delete"),{ids:e})}bulkToggleStatus(e,t){return Br.post("".concat(this.baseUrl,"/bulk-toggle-status"),{ids:e,is_active:t})}};const $r=new class extends Wr{constructor(){super("/admin/permission-management")}getAllPermissions(){return Br.get("/admin/permissions")}getActivePermissions(){return this.list({active_only:!0})}searchPermissions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return this.search({page:t,page_size:n,search_term:e,filters:{is_active:!0}})}getPermissionsByName(e){return this.list({page:1,page_size:100,name_like:e})}toggleActiveStatus(e,t){return this.update(e,{is_active:t})}getPermissionsByCategory(e){return this.list({page:1,page_size:100,category:e})}bulkDelete(e){return Br.post("".concat(this.baseUrl,"/bulk-delete"),{ids:e})}bulkToggleStatus(e,t){return Br.post("".concat(this.baseUrl,"/bulk-toggle-status"),{ids:e,is_active:t})}getUnassignedPermissions(e){return this.list({page:1,page_size:100,exclude_role:e,is_active:!0})}getUnassignedUserPermissions(e){return this.list({page:1,page_size:100,exclude_user:e,is_active:!0})}advancedSearch(e){return this.search({page:e.page||1,page_size:e.page_size||10,search_term:e.search_term,filters:{is_active:e.is_active,category:e.category,created_after:e.created_after,created_before:e.created_before}})}};const qr=new class{constructor(){this.baseUrl="/admin"}getDashboard(){return Br.get("".concat(this.baseUrl,"/dashboard"))}getUsers(){return Br.get("".concat(this.baseUrl,"/users"))}getUserRoles(e){return Br.get("".concat(this.baseUrl,"/user/").concat(e,"/roles"))}getUserPermissions(e){return Br.get("".concat(this.baseUrl,"/user/").concat(e,"/permissions"))}getUserDirectPermissions(e){return Br.get("".concat(this.baseUrl,"/user/").concat(e,"/direct-permissions"))}getRoles(){return Br.get("".concat(this.baseUrl,"/roles"))}getRolePermissions(e){return Br.get("".concat(this.baseUrl,"/role/").concat(e,"/permissions"))}getPermissions(){return Br.get("".concat(this.baseUrl,"/permissions"))}assignRole(e){return Br.post("".concat(this.baseUrl,"/assign-role"),e)}removeRole(e){return Br.post("".concat(this.baseUrl,"/remove-role"),e)}assignPermission(e){return Br.post("".concat(this.baseUrl,"/assign-permission"),e)}removePermission(e){return Br.post("".concat(this.baseUrl,"/remove-permission"),e)}assignUserPermission(e){return Br.post("".concat(this.baseUrl,"/assign-user-permission"),e)}removeUserPermission(e){return Br.post("".concat(this.baseUrl,"/remove-user-permission"),e)}assignRoleToUser(e,t){return this.assignRole({user_id:e,role:t})}removeRoleFromUser(e,t){return this.removeRole({user_id:e,role:t})}assignPermissionToRole(e,t){return this.assignPermission({role_id:e,permission:t})}removePermissionFromRole(e,t){return this.removePermission({role_id:e,permission:t})}assignPermissionToUser(e,t){return this.assignUserPermission({user_id:e,permission:t})}removePermissionFromUser(e,t){return this.removeUserPermission({user_id:e,permission:t})}},Qr=e=>{let{isOpen:t,onClose:n,user:r,onSuccess:a}=e;const[l,i]=(0,o.useState)({username:"",email:"",password:"",is_active:!0}),[s,u]=(0,o.useState)(!1),[c,d]=(0,o.useState)("");(0,o.useEffect)(()=>{i(r?{username:r.username,email:r.email,password:"",is_active:r.is_active}:{username:"",email:"",password:"",is_active:!0}),d("")},[r,t]);const f=e=>{const{name:t,value:n,type:r,checked:a}=e.target;i(e=>Ie(Ie({},e),{},{[t]:"checkbox"===r?a:n}))};return t?(0,Le.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Le.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,Le.jsxs)("div",{className:"mt-3",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:r?"Edit User":"Add New User"}),c&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),u(!0),d("");try{if(r){const e={username:l.username,email:l.email,is_active:l.is_active};l.password.trim()&&(e.password=l.password),await _r(r.id,e)}else{if(!l.password.trim())return void d("Password is required for new users");await Nr(l)}a(),n()}catch(i){var t,o;d((null===(t=i.response)||void 0===t||null===(o=t.data)||void 0===o?void 0:o.error)||i.message)}finally{u(!1)}},children:[(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),(0,Le.jsx)("input",{type:"text",id:"username",name:"username",value:l.username,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,Le.jsx)("input",{type:"email",id:"email",name:"email",value:l.email,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsxs)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:["Password ",r&&"(leave blank to keep current)"]}),(0,Le.jsx)("input",{type:"password",id:"password",name:"password",value:l.password,onChange:f,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,Le.jsx)("div",{className:"mb-6",children:(0,Le.jsxs)("label",{className:"flex items-center",children:[(0,Le.jsx)("input",{type:"checkbox",name:"is_active",checked:l.is_active,onChange:f,className:"mr-2"}),(0,Le.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),(0,Le.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,Le.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",disabled:s,className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50",children:s?"Saving...":r?"Update":"Create"})]})]})]})})}):null},Kr=e=>{let{isOpen:t,onClose:n,role:r,onSuccess:a}=e;const[l,i]=(0,o.useState)({name:"",description:"",is_active:!0}),[s,u]=(0,o.useState)(!1),[c,d]=(0,o.useState)("");(0,o.useEffect)(()=>{i(r?{name:r.name,description:r.description||"",is_active:r.is_active}:{name:"",description:"",is_active:!0}),d("")},[r,t]);const f=e=>{const{name:t,value:n,type:r}=e.target,a=e.target.checked;i(e=>Ie(Ie({},e),{},{[t]:"checkbox"===r?a:n}))};return t?(0,Le.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Le.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,Le.jsxs)("div",{className:"mt-3",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:r?"Edit Role":"Add New Role"}),c&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),u(!0),d("");try{r?await Cr(r.id,l):await jr(l),a(),n()}catch(i){var t,o;d((null===(t=i.response)||void 0===t||null===(o=t.data)||void 0===o?void 0:o.error)||i.message)}finally{u(!1)}},children:[(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Role Name"}),(0,Le.jsx)("input",{type:"text",id:"name",name:"name",value:l.name,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., admin, manager, user"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,Le.jsx)("textarea",{id:"description",name:"description",value:l.description,onChange:f,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe the role's purpose and responsibilities"})]}),(0,Le.jsx)("div",{className:"mb-6",children:(0,Le.jsxs)("label",{className:"flex items-center",children:[(0,Le.jsx)("input",{type:"checkbox",name:"is_active",checked:l.is_active,onChange:f,className:"mr-2"}),(0,Le.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),(0,Le.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,Le.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",disabled:s,className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50",children:s?"Saving...":r?"Update":"Create"})]})]})]})})}):null},Jr=e=>{let{isOpen:t,onClose:n,permission:r,onSuccess:a}=e;const[l,i]=(0,o.useState)({name:"",description:"",is_active:!0}),[s,u]=(0,o.useState)(!1),[c,d]=(0,o.useState)("");(0,o.useEffect)(()=>{i(r?{name:r.name,description:r.description||"",is_active:r.is_active}:{name:"",description:"",is_active:!0}),d("")},[r,t]);const f=e=>{const{name:t,value:n,type:r}=e.target,a=e.target.checked;i(e=>Ie(Ie({},e),{},{[t]:"checkbox"===r?a:n}))};return t?(0,Le.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Le.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,Le.jsxs)("div",{className:"mt-3",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:r?"Edit Permission":"Add New Permission"}),c&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,Le.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),u(!0),d("");try{r?await Rr(r.id,l):await Pr(l),a(),n()}catch(i){var t,o;d((null===(t=i.response)||void 0===t||null===(o=t.data)||void 0===o?void 0:o.error)||i.message)}finally{u(!1)}},children:[(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Permission Name"}),(0,Le.jsx)("input",{type:"text",id:"name",name:"name",value:l.name,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., read_users, edit_users, delete_users"})]}),(0,Le.jsxs)("div",{className:"mb-4",children:[(0,Le.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,Le.jsx)("textarea",{id:"description",name:"description",value:l.description,onChange:f,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe what this permission allows users to do"})]}),(0,Le.jsx)("div",{className:"mb-6",children:(0,Le.jsxs)("label",{className:"flex items-center",children:[(0,Le.jsx)("input",{type:"checkbox",name:"is_active",checked:l.is_active,onChange:f,className:"mr-2"}),(0,Le.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),(0,Le.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,Le.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"Cancel"}),(0,Le.jsx)("button",{type:"submit",disabled:s,className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50",children:s?"Saving...":r?"Update":"Create"})]})]})]})})}):null},Xr=e=>{let{users:t,roles:n,onMessage:r,onError:a}=e;const[l,i]=(0,o.useState)(null),[s,u]=(0,o.useState)([]),[c,d]=(0,o.useState)([]),[f,p]=(0,o.useState)([]),[h,m]=(0,o.useState)([]),[g,y]=(0,o.useState)(!1),[v,b]=(0,o.useState)("roles");(0,o.useEffect)(()=>{l&&x()},[l]);const x=async()=>{if(l){y(!0);try{const e=await qr.getUserRoles(l.id);u(e.data);const t=await qr.getUserPermissions(l.id);d(t.data);const r=e.data;p(n.filter(e=>!r.includes(e.name)&&e.is_active)),m([])}catch(r){var e,t;a("Failed to load user details: ".concat((null===(e=r.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.error)||r.message))}finally{y(!1)}}};return(0,Le.jsxs)("div",{children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold mb-6",children:"User Role & Permission Assignments"}),(0,Le.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,Le.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,Le.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Select User"}),(0,Le.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:t.map(e=>(0,Le.jsxs)("button",{onClick:()=>i(e),className:"w-full text-left p-3 rounded border ".concat((null===l||void 0===l?void 0:l.id)===e.id?"bg-blue-100 border-blue-500":"bg-gray-50 border-gray-200 hover:bg-gray-100"),children:[(0,Le.jsx)("div",{className:"font-medium",children:e.username}),(0,Le.jsx)("div",{className:"text-sm text-gray-600",children:e.email})]},e.id))})]}),l&&(0,Le.jsx)("div",{className:"lg:col-span-2",children:(0,Le.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,Le.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,Le.jsxs)("h3",{className:"text-lg font-medium",children:["Managing: ",l.username]}),(0,Le.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,Le.jsx)("button",{onClick:()=>b("roles"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("roles"===v?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"Roles"}),(0,Le.jsx)("button",{onClick:()=>b("permissions"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("permissions"===v?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"Permissions"})]})]}),g?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading..."}):(0,Le.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("h4",{className:"font-medium mb-3",children:["Assigned ","roles"===v?"Roles":"Permissions"]}),(0,Le.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:"roles"===v?s.length>0?s.map(e=>(0,Le.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded",children:[(0,Le.jsx)("div",{children:(0,Le.jsx)("div",{className:"font-medium text-green-800",children:e})}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(l)try{await qr.removeRoleFromUser(l.id,e),r("Role removed successfully"),x()}catch(o){var t,n;a("Failed to remove role: ".concat((null===(t=o.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||o.message))}})(e),className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},e)):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No roles assigned"}):c.length>0?c.map(e=>(0,Le.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded",children:[(0,Le.jsx)("div",{children:(0,Le.jsx)("div",{className:"font-medium text-green-800",children:e})}),(0,Le.jsx)("button",{onClick:()=>(async()=>{if(l)try{a("Direct permission removal not yet implemented")}catch(n){var e,t;a("Failed to remove permission: ".concat((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.error)||n.message))}})(),className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},e)):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No permissions assigned"})})]}),(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("h4",{className:"font-medium mb-3",children:["Available ","roles"===v?"Roles":"Permissions"]}),(0,Le.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:"roles"===v?f.length>0?f.map(e=>(0,Le.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded",children:[(0,Le.jsxs)("div",{children:[(0,Le.jsx)("div",{className:"font-medium",children:e.name}),e.description&&(0,Le.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(l)try{await qr.assignRoleToUser(l.id,e),r("Role assigned successfully"),x()}catch(o){var t,n;a("Failed to assign role: ".concat((null===(t=o.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||o.message))}})(e.name),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Assign"})]},e.id)):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No available roles"}):(0,Le.jsx)("div",{className:"text-gray-500 text-center py-4",children:"Permission assignment feature coming soon"})})]})]})]})})]}),!l&&(0,Le.jsx)("div",{className:"text-center py-12 text-gray-500",children:(0,Le.jsx)("p",{children:"Select a user to manage their roles and permissions"})})]})},Yr=()=>{const[e,t]=(0,o.useState)("users"),[n,r]=(0,o.useState)([]),[a,l]=(0,o.useState)({page:1,page_size:10,total_items:0,total_pages:0}),[i,s]=(0,o.useState)(!1),[u,c]=(0,o.useState)(""),[d,f]=(0,o.useState)([]),[p,h]=(0,o.useState)({page:1,page_size:10,total_items:0,total_pages:0}),[m,g]=(0,o.useState)(!1),[y,v]=(0,o.useState)(""),[b,x]=(0,o.useState)([]),[w,S]=(0,o.useState)({page:1,page_size:10,total_items:0,total_pages:0}),[k,E]=(0,o.useState)(!1),[N,_]=(0,o.useState)(""),[j,C]=(0,o.useState)(""),[P,R]=(0,o.useState)(""),[O,T]=(0,o.useState)(!1),[L,U]=(0,o.useState)(!1),[z,D]=(0,o.useState)(!1),[F,A]=(0,o.useState)(null),[I,M]=(0,o.useState)(null),[B,W]=(0,o.useState)(null);(0,o.useEffect)(()=>{switch(e){case"users":H();break;case"roles":$();break;case"permissions":Q();break;case"assignments":H(),$()}},[e]);const H=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";s(!0),R("");try{let n;if(t.trim()){const r={page:e,page_size:a.page_size,search_term:t,filters:{is_active:!0}};n=await Hr.search(r)}else n=await Hr.list({page:e,page_size:a.page_size});r(n.data.data),l(n.data.pagination)}catch(i){var n,o;R("Failed to load users: ".concat((null===(n=i.response)||void 0===n||null===(o=n.data)||void 0===o?void 0:o.error)||i.message))}finally{s(!1)}},V=()=>{l(e=>Ie(Ie({},e),{},{page:1})),H(1,u)},$=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";g(!0),R("");try{let n;n=t.trim()?await Vr.searchRoles(t,e,p.page_size):await Vr.list({page:e,page_size:p.page_size}),f(n.data.data),h(n.data.pagination)}catch(a){var n,r;R("Failed to load roles: ".concat((null===(n=a.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.error)||a.message))}finally{g(!1)}},q=()=>{h(e=>Ie(Ie({},e),{},{page:1})),$(1,y)},Q=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";E(!0),R("");try{let n;n=t.trim()?await $r.searchPermissions(t,e,w.page_size):await $r.list({page:e,page_size:w.page_size}),x(n.data.data),S(n.data.pagination)}catch(a){var n,r;R("Failed to load permissions: ".concat((null===(n=a.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.error)||a.message))}finally{E(!1)}},K=()=>{S(e=>Ie(Ie({},e),{},{page:1})),Q(1,N)},J=(e,t)=>{const n=[];for(let r=1;r<=e.total_pages;r++)n.push((0,Le.jsx)("button",{onClick:()=>t(r),className:"px-3 py-1 mx-1 rounded ".concat(r===e.page?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:r},r));return(0,Le.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,Le.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(e.page-1)*e.page_size+1," to"," ",Math.min(e.page*e.page_size,e.total_items)," of"," ",e.total_items," entries"]}),(0,Le.jsxs)("div",{className:"flex items-center",children:[(0,Le.jsx)("button",{onClick:()=>t(e.page-1),disabled:e.page<=1,className:"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50",children:"Previous"}),n,(0,Le.jsx)("button",{onClick:()=>t(e.page+1),disabled:e.page>=e.total_pages,className:"px-3 py-1 mx-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50",children:"Next"})]})]})};return(0,Le.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,Le.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"User Permission Management"}),j&&(0,Le.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:j}),P&&(0,Le.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:P}),(0,Le.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,Le.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{key:"users",label:"Users"},{key:"roles",label:"Roles"},{key:"permissions",label:"Permissions"},{key:"assignments",label:"Assignments"}].map(n=>(0,Le.jsx)("button",{onClick:()=>t(n.key),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(e===n.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:n.label},n.key))})}),"users"===e&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold",children:"User Management"}),(0,Le.jsx)("button",{onClick:()=>{A(null),T(!0)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"Add User"})]}),(0,Le.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,Le.jsx)("input",{type:"text",placeholder:"Search users by username or email...",value:u,onChange:e=>c(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&V()}),(0,Le.jsx)("button",{onClick:V,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Search"}),(0,Le.jsx)("button",{onClick:()=>{c(""),H(1,"")},className:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded",children:"Clear"})]}),i?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading users..."}):(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)("div",{className:"overflow-x-auto",children:(0,Le.jsxs)("table",{className:"min-w-full bg-white border border-gray-200",children:[(0,Le.jsx)("thead",{className:"bg-gray-50",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Username"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Le.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:n.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.id}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.username}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.email}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Le.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Active":"Inactive"})}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,Le.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,Le.jsx)("button",{onClick:()=>{A(e),T(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"Edit"}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this user?"))try{await Hr.delete(e),C("User deleted successfully"),H(a.page,u)}catch(r){var t,n;R("Failed to delete user: ".concat((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||r.message))}})(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})}),J(a,e=>{l(t=>Ie(Ie({},t),{},{page:e})),H(e,u)})]})]}),"roles"===e&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold",children:"Role Management"}),(0,Le.jsx)("button",{onClick:()=>{M(null),U(!0)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"Add Role"})]}),(0,Le.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,Le.jsx)("input",{type:"text",placeholder:"Search roles by name...",value:y,onChange:e=>v(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&q()}),(0,Le.jsx)("button",{onClick:q,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Search"}),(0,Le.jsx)("button",{onClick:()=>{v(""),$(1,"")},className:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded",children:"Clear"})]}),m?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading roles..."}):(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)("div",{className:"overflow-x-auto",children:(0,Le.jsxs)("table",{className:"min-w-full bg-white border border-gray-200",children:[(0,Le.jsx)("thead",{className:"bg-gray-50",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Le.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.id}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.name}),(0,Le.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.description||"No description"}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Le.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Active":"Inactive"})}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,Le.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,Le.jsx)("button",{onClick:()=>{M(e),U(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"Edit"}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this role?"))try{await Vr.delete(e),C("Role deleted successfully"),$(p.page,y)}catch(r){var t,n;R("Failed to delete role: ".concat((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||r.message))}})(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})}),J(p,e=>{h(t=>Ie(Ie({},t),{},{page:e})),$(e,y)})]})]}),"permissions"===e&&(0,Le.jsxs)("div",{children:[(0,Le.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Le.jsx)("h2",{className:"text-xl font-semibold",children:"Permission Management"}),(0,Le.jsx)("button",{onClick:()=>{W(null),D(!0)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"Add Permission"})]}),(0,Le.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,Le.jsx)("input",{type:"text",placeholder:"Search permissions by name...",value:N,onChange:e=>_(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&K()}),(0,Le.jsx)("button",{onClick:K,className:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded",children:"Search"}),(0,Le.jsx)("button",{onClick:()=>{_(""),Q(1,"")},className:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded",children:"Clear"})]}),k?(0,Le.jsx)("div",{className:"text-center py-8",children:"Loading permissions..."}):(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)("div",{className:"overflow-x-auto",children:(0,Le.jsxs)("table",{className:"min-w-full bg-white border border-gray-200",children:[(0,Le.jsx)("thead",{className:"bg-gray-50",children:(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Le.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Le.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(e=>(0,Le.jsxs)("tr",{children:[(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.id}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.name}),(0,Le.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.description||"No description"}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Le.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Active":"Inactive"})}),(0,Le.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,Le.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,Le.jsx)("button",{onClick:()=>{W(e),D(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"Edit"}),(0,Le.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this permission?"))try{await $r.delete(e),C("Permission deleted successfully"),Q(w.page,N)}catch(r){var t,n;R("Failed to delete permission: ".concat((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.error)||r.message))}})(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})}),J(w,e=>{S(t=>Ie(Ie({},t),{},{page:e})),Q(e,N)})]})]}),"assignments"===e&&(0,Le.jsx)(Xr,{users:n,roles:d,onMessage:C,onError:R}),(0,Le.jsx)(Qr,{isOpen:O,onClose:()=>T(!1),user:F,onSuccess:()=>{C(F?"User updated successfully":"User created successfully"),H(a.page,u)}}),(0,Le.jsx)(Kr,{isOpen:L,onClose:()=>U(!1),role:I,onSuccess:()=>{C(I?"Role updated successfully":"Role created successfully"),$(p.page,y)}}),(0,Le.jsx)(Jr,{isOpen:z,onClose:()=>D(!1),permission:B,onSuccess:()=>{C(B?"Permission updated successfully":"Permission created successfully"),Q(w.page,N)}})]})},Gr=()=>{const[e,t]=(0,o.useState)(null),[n,r]=(0,o.useState)(!0);(0,o.useEffect)(()=>{const e=localStorage.getItem("token"),n=localStorage.getItem("user");if(e&&n)try{t(JSON.parse(n))}catch(a){localStorage.removeItem("token"),localStorage.removeItem("user")}r(!1)},[]);const a=()=>{t(null)},l=e&&e.roles&&e.roles.includes("admin");return n?(0,Le.jsx)("div",{className:"container",children:(0,Le.jsx)("div",{style:{textAlign:"center",marginTop:"50px"},children:(0,Le.jsx)("p",{children:"Loading..."})})}):(0,Le.jsxs)(je,{children:[(0,Le.jsx)(Ue,{user:e,onLogout:a}),(0,Le.jsxs)(we,{children:[(0,Le.jsx)(be,{path:"/",element:e?(0,Le.jsx)(zr,{onLogout:a}):(0,Le.jsx)(Ur,{onAuthSuccess:e=>{t(e)}})}),(0,Le.jsx)(be,{path:"/tokens",element:e?(0,Le.jsx)(Dr,{}):(0,Le.jsx)(ve,{to:"/"})}),l&&(0,Le.jsxs)(Le.Fragment,{children:[(0,Le.jsx)(be,{path:"/admin/users",element:(0,Le.jsx)(Fr,{})}),(0,Le.jsx)(be,{path:"/admin/permissions",element:(0,Le.jsx)(Yr,{})})]}),(0,Le.jsx)(be,{path:"*",element:(0,Le.jsx)(ve,{to:"/"})})]})]})};(0,i.H)(document.getElementById("root")).render((0,Le.jsx)(o.StrictMode,{children:(0,Le.jsx)(Gr,{})}))})();
//# sourceMappingURL=main.636c30d1.js.map