import React, { useState, useEffect } from 'react';
import { User, Role, Permission } from '../../types';
import { adminService } from '../../services';

interface AssignmentsTabProps {
  users: User[];
  roles: Role[];
  onMessage: (message: string) => void;
  onError: (error: string) => void;
}

const AssignmentsTab: React.FC<AssignmentsTabProps> = ({ users, roles, onMessage, onError }) => {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userRoleNames, setUserRoleNames] = useState<string[]>([]);
  const [userPermissionNames, setUserPermissionNames] = useState<string[]>([]);
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [assignmentType, setAssignmentType] = useState<'roles' | 'permissions'>('roles');

  // Load user details when selected
  useEffect(() => {
    if (selectedUser) {
      loadUserDetails();
    }
  }, [selectedUser]);

  const loadUserDetails = async () => {
    if (!selectedUser) return;

    setLoading(true);
    try {
      // Load user roles (returns string[])
      const rolesResponse = await adminService.getUserRoles(selectedUser.id);
      setUserRoleNames(rolesResponse.data);

      // Load user permissions (returns string[])
      const permissionsResponse = await adminService.getUserPermissions(selectedUser.id);
      setUserPermissionNames(permissionsResponse.data);

      // Set available roles (roles not assigned to user)
      const assignedRoleNames = rolesResponse.data;
      setAvailableRoles(roles.filter(role => !assignedRoleNames.includes(role.name) && role.is_active));

      // Load all permissions for available permissions list
      // Note: This would need to be implemented in the backend to get all permissions
      // For now, we'll use an empty array
      setAvailablePermissions([]);

    } catch (err: any) {
      onError(`Failed to load user details: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRole = async (roleName: string) => {
    if (!selectedUser) return;

    try {
      await adminService.assignRoleToUser(selectedUser.id, roleName);
      onMessage('Role assigned successfully');
      loadUserDetails(); // Reload to update the lists
    } catch (err: any) {
      onError(`Failed to assign role: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleRemoveRole = async (roleName: string) => {
    if (!selectedUser) return;

    try {
      await adminService.removeRoleFromUser(selectedUser.id, roleName);
      onMessage('Role removed successfully');
      loadUserDetails(); // Reload to update the lists
    } catch (err: any) {
      onError(`Failed to remove role: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleAssignPermission = async (permissionName: string) => {
    if (!selectedUser) return;

    try {
      // Note: This would need a different API endpoint for direct user permission assignment
      // For now, we'll show an error message
      onError('Direct permission assignment not yet implemented');
    } catch (err: any) {
      onError(`Failed to assign permission: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleRemovePermission = async (permissionName: string) => {
    if (!selectedUser) return;

    try {
      // Note: This would need a different API endpoint for direct user permission removal
      // For now, we'll show an error message
      onError('Direct permission removal not yet implemented');
    } catch (err: any) {
      onError(`Failed to remove permission: ${err.response?.data?.error || err.message}`);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">User Role & Permission Assignments</h2>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Selection */}
        <div className="bg-white p-4 rounded-lg border">
          <h3 className="text-lg font-medium mb-4">Select User</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {users.map((user) => (
              <button
                key={user.id}
                onClick={() => setSelectedUser(user)}
                className={`w-full text-left p-3 rounded border ${selectedUser?.id === user.id
                  ? 'bg-blue-100 border-blue-500'
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}
              >
                <div className="font-medium">{user.username}</div>
                <div className="text-sm text-gray-600">{user.email}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Assignment Type Toggle */}
        {selectedUser && (
          <div className="lg:col-span-2">
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">
                  Managing: {selectedUser.username}
                </h3>
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setAssignmentType('roles')}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'roles'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                      }`}
                  >
                    Roles
                  </button>
                  <button
                    onClick={() => setAssignmentType('permissions')}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${assignmentType === 'permissions'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                      }`}
                  >
                    Permissions
                  </button>
                </div>
              </div>

              {loading ? (
                <div className="text-center py-8">Loading...</div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Assigned Items */}
                  <div>
                    <h4 className="font-medium mb-3">
                      Assigned {assignmentType === 'roles' ? 'Roles' : 'Permissions'}
                    </h4>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {assignmentType === 'roles' ? (
                        userRoleNames.length > 0 ? (
                          userRoleNames.map((roleName) => (
                            <div key={roleName} className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded">
                              <div>
                                <div className="font-medium text-green-800">{roleName}</div>
                              </div>
                              <button
                                onClick={() => handleRemoveRole(roleName)}
                                className="text-red-600 hover:text-red-800 text-sm"
                              >
                                Remove
                              </button>
                            </div>
                          ))
                        ) : (
                          <div className="text-gray-500 text-center py-4">No roles assigned</div>
                        )
                      ) : (
                        userPermissionNames.length > 0 ? (
                          userPermissionNames.map((permissionName) => (
                            <div key={permissionName} className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded">
                              <div>
                                <div className="font-medium text-green-800">{permissionName}</div>
                              </div>
                              <button
                                onClick={() => handleRemovePermission(permissionName)}
                                className="text-red-600 hover:text-red-800 text-sm"
                              >
                                Remove
                              </button>
                            </div>
                          ))
                        ) : (
                          <div className="text-gray-500 text-center py-4">No permissions assigned</div>
                        )
                      )}
                    </div>
                  </div>

                  {/* Available Items */}
                  <div>
                    <h4 className="font-medium mb-3">
                      Available {assignmentType === 'roles' ? 'Roles' : 'Permissions'}
                    </h4>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {assignmentType === 'roles' ? (
                        availableRoles.length > 0 ? (
                          availableRoles.map((role) => (
                            <div key={role.id} className="flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded">
                              <div>
                                <div className="font-medium">{role.name}</div>
                                {role.description && (
                                  <div className="text-sm text-gray-600">{role.description}</div>
                                )}
                              </div>
                              <button
                                onClick={() => handleAssignRole(role.name)}
                                className="text-blue-600 hover:text-blue-800 text-sm"
                              >
                                Assign
                              </button>
                            </div>
                          ))
                        ) : (
                          <div className="text-gray-500 text-center py-4">No available roles</div>
                        )
                      ) : (
                        <div className="text-gray-500 text-center py-4">
                          Permission assignment feature coming soon
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {!selectedUser && (
        <div className="text-center py-12 text-gray-500">
          <p>Select a user to manage their roles and permissions</p>
        </div>
      )}
    </div>
  );
};

export default AssignmentsTab;
