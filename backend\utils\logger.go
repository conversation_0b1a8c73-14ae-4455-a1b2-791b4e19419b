package utils

import (
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var Logger *zap.Logger

// InitLogger initializes the Zap logger
func InitLogger() {
	// Get log level from environment variable
	logLevel := os.Getenv("LOG_LEVEL")
	if logLevel == "" {
		logLevel = "info"
	}

	// Get log file path from environment variable
	logFile := os.Getenv("LOG_FILE")
	if logFile == "" {
		logFile = "logs/app.log"
	}

	// Parse log level
	var level zapcore.Level
	switch logLevel {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// Create encoder config
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// Create cores for different outputs
	var cores []zapcore.Core

	// Console output (always enabled)
	consoleCore := zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		zapcore.AddSync(os.Stdout),
		level,
	)
	cores = append(cores, consoleCore)

	// File output (if log file is specified)
	if logFile != "" {
		// Create logs directory if it doesn't exist
		logDir := filepath.Dir(logFile)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			// If we can't create the directory, just log to console
			Logger = zap.New(consoleCore, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
			return
		}

		// Open log file
		file, err := os.OpenFile(logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err == nil {
			fileCore := zapcore.NewCore(
				zapcore.NewJSONEncoder(encoderConfig),
				zapcore.AddSync(file),
				level,
			)
			cores = append(cores, fileCore)
		}
	}

	// Create multi-core logger
	core := zapcore.NewTee(cores...)
	Logger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
}

// GetLogger returns the logger instance
func GetLogger() *zap.Logger {
	return Logger
}

// LogRequest logs HTTP request information
func LogRequest(method, path, remoteAddr string, statusCode int, duration float64) {
	Logger.Info("HTTP Request",
		zap.String("method", method),
		zap.String("path", path),
		zap.String("remote_addr", remoteAddr),
		zap.Int("status_code", statusCode),
		zap.Float64("duration_ms", duration),
	)
}

// LogError logs error with context
func LogError(msg string, err error, fields ...zap.Field) {
	if err != nil {
		fields = append(fields, zap.Error(err))
	}
	Logger.Error(msg, fields...)
}

// LogInfo logs info message with context
func LogInfo(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// LogDebug logs debug message with context
func LogDebug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// LogWarn logs warning message with context
func LogWarn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
} 